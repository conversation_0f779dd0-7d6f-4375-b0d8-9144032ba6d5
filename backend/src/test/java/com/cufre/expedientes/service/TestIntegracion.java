package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.ExpedienteDTO;
import com.cufre.expedientes.dto.PersonaDTO;
import com.cufre.expedientes.dto.DelitoDTO;
import com.cufre.expedientes.dto.DocumentoDTO;
import com.cufre.expedientes.dto.DomicilioDTO;
import com.cufre.expedientes.dto.MedioComunicacionDTO;
import com.cufre.expedientes.dto.FotografiaDTO;
import com.cufre.expedientes.dto.ExpedienteDelitoDTO;
import com.cufre.expedientes.dto.PersonaExpedienteDTO;
import com.cufre.expedientes.service.ExpedienteService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.http.MediaType;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import org.springframework.test.web.servlet.MvcResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.mock.web.MockMultipartFile;

import static org.junit.jupiter.api.Assertions.*;

@AutoConfigureMockMvc
@SpringBootTest
@ActiveProfiles("test")
public class TestIntegracion {

    @Autowired
    private ExpedienteService expedienteService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void limpiarTabla() {
        try {
            jdbcTemplate.execute("DELETE FROM EXPEDIENTE");
        } catch (Exception e) {
            // Ignorar si la tabla no existe
        }
    }

    @Test
    void testPrioridadConCamposDeImpacto() {
        ExpedienteDTO dto = new ExpedienteDTO();
        // Setear valores base
        dto.setNumero("TEST-123");
        dto.setProfugoProfesionOcupacion("PROFESIONAL");
        dto.setProfugoNumeroDetencionesPrevias(3);
        dto.setNumeroComplices(4);
        dto.setTipoCaptura("NACIONAL");
        dto.setTipoVictima("JUEZ");
        dto.setMediaticoFlag(true);
        dto.setReincicenteFlag(true);
        dto.setReiteranteFlag(false);
        dto.setBandaFlag(true);
        dto.setTerrorismoFlag(false);
        dto.setNivelOrganizacion("COMPLEJA");
        dto.setAmbitoBanda("INTERNACIONAL");
        dto.setCapacidadOperativa("ALTA");
        dto.setPlanificacionFlag(true);
        dto.setConexionesOtrasActividadesFlag(true);
        dto.setImpactoSocial("ALTO");
        dto.setTipoDano("FISICO");
        dto.setUsoArmasFuegoFlag(true);
        dto.setUsoArmasBlancasFlag(false);

        // Campos nuevos
        dto.setNivelIncidenciaZona("ALTA");
        dto.setInstitucionSensibleCercana("HOSPITAL");
        dto.setRecursosLimitados("SI");
        dto.setAreaFronteriza("NO");
        dto.setImpactoPercepcion("MEDIA");

        // Crear expediente
        ExpedienteDTO creado = expedienteService.create(dto);

        // Calcular la prioridad esperada manualmente según los valores y la lógica de PriorityCalculator
        int prioridadEsperada = 0;
        prioridadEsperada += 800; // PROFESIONAL
        prioridadEsperada += 750; // 3 detenciones
        prioridadEsperada += 250; // 4 cómplices
        prioridadEsperada += 500; // NACIONAL
        prioridadEsperada += 1000; // JUEZ
        prioridadEsperada += 500; // mediático
        prioridadEsperada += 800; // reincidente
        // reiterante = false
        prioridadEsperada += 500; // banda
        // terrorismo = false
        prioridadEsperada += 800; // COMPLEJA
        prioridadEsperada += 1000; // INTERNACIONAL
        prioridadEsperada += 1000; // ALTA capacidad
        prioridadEsperada += 500; // planificación
        prioridadEsperada += 500; // conexiones otras actividades
        prioridadEsperada += 500; // impacto social ALTO
        prioridadEsperada += 250; // tipo daño FISICO
        prioridadEsperada += 500; // uso armas fuego
        // uso armas blancas = false
        prioridadEsperada += 500; // nivel incidencia zona ALTA
        prioridadEsperada += 500; // institución HOSPITAL
        prioridadEsperada += 500; // recursos limitados SI
        prioridadEsperada += 100; // área fronteriza NO
        prioridadEsperada += 250; // impacto percepción MEDIA

        assertEquals(prioridadEsperada, creado.getPrioridad(), "La prioridad calculada no es la esperada");

        // Verificar que los campos se guardaron correctamente
        assertEquals("ALTA", creado.getNivelIncidenciaZona());
        assertEquals("HOSPITAL", creado.getInstitucionSensibleCercana());
        assertEquals("SI", creado.getRecursosLimitados());
        assertEquals("NO", creado.getAreaFronteriza());
        assertEquals("MEDIA", creado.getImpactoPercepcion());
    }

    @Test
    void testGetExpedienteById_Endpoint() throws Exception {
        ExpedienteDTO dto = new ExpedienteDTO();
        dto.setNumero("MOCK-001");
        ExpedienteDTO creado = expedienteService.create(dto);

        mockMvc.perform(get("/api/expedientes/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.numero").value("MOCK-001"));
    }

    @Test
    void testCreateAndGetExpediente_Endpoint() throws Exception {
        ExpedienteDTO dto = new ExpedienteDTO();
        dto.setNumero("INTEG-001");
        String json = objectMapper.writeValueAsString(dto);

        // Crear expediente
        MvcResult result = mockMvc.perform(post("/api/expedientes")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andExpect(header().exists("Location"))
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        ExpedienteDTO creado = objectMapper.readValue(response, ExpedienteDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/expedientes/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.numero").value("INTEG-001"));
    }

    @Test
    void testSearchExpedienteByNumero_Endpoint() throws Exception {
        ExpedienteDTO dto = new ExpedienteDTO();
        dto.setNumero("BUSQ-123");
        expedienteService.create(dto);

        mockMvc.perform(get("/api/expedientes/search/numero")
                .param("numero", "BUSQ-123")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].numero").value("BUSQ-123"));
    }

    @Test
    void testCreateAndGetPersona_Endpoint() throws Exception {
        PersonaDTO dto = new PersonaDTO();
        dto.setNombre("Juan");
        dto.setApellido("Pérez");
        dto.setNumeroDocumento("12345678");
        String json = objectMapper.writeValueAsString(dto);

        // Crear persona
        MvcResult result = mockMvc.perform(post("/api/personas")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        PersonaDTO creada = objectMapper.readValue(response, PersonaDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/personas/" + creada.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.nombre").value("Juan"));
    }

    @Test
    void testSearchPersonaByNumeroDocumento_Endpoint() throws Exception {
        PersonaDTO dto = new PersonaDTO();
        dto.setNombre("Ana");
        dto.setApellido("García");
        dto.setNumeroDocumento("87654321");
        //personaService.save(dto);

        mockMvc.perform(get("/api/personas/search/documento")
                .param("numeroDocumento", "87654321")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].nombre").value("Ana"));
    }

    @Test
    void testGetExpedienteById_NotFound() throws Exception {
        mockMvc.perform(get("/api/expedientes/999999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());
    }

    @Test
    void testCreateAndGetDelito_Endpoint() throws Exception {
        DelitoDTO dto = new DelitoDTO();
        dto.setNombre("Robo");
        String json = objectMapper.writeValueAsString(dto);

        // Crear delito
        MvcResult result = mockMvc.perform(post("/api/delitos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        DelitoDTO creado = objectMapper.readValue(response, DelitoDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/delitos/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.nombre").value("Robo"));
    }

    @Test
    void testSearchDelitoByNombre_Endpoint() throws Exception {
        DelitoDTO dto = new DelitoDTO();
        dto.setNombre("Hurto");
        //delitoService.save(dto);

        mockMvc.perform(get("/api/delitos/search/nombre")
                .param("nombre", "Hurto")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].nombre").value("Hurto"));
    }

    @Test
    void testDeleteExpediente_Endpoint() throws Exception {
        ExpedienteDTO dto = new ExpedienteDTO();
        dto.setNumero("DEL-001");
        ExpedienteDTO creado = expedienteService.create(dto);

        // Eliminar expediente
        mockMvc.perform(delete("/api/expedientes/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        // Verificar que ya no existe
        mockMvc.perform(get("/api/expedientes/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());
    }

    @Test
    void testCreateAndGetDocumento_Endpoint() throws Exception {
        DocumentoDTO dto = new DocumentoDTO();
        dto.setTipo("DNI");
        dto.setDescripcion("Documento de identidad");
        String json = objectMapper.writeValueAsString(dto);

        // Crear documento
        MvcResult result = mockMvc.perform(post("/api/documentos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        DocumentoDTO creado = objectMapper.readValue(response, DocumentoDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/documentos/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tipo").value("DNI"))
                .andExpect(jsonPath("$.descripcion").value("Documento de identidad"));
    }

    @Test
    void testSearchDocumentoByTipo_Endpoint() throws Exception {
        DocumentoDTO dto = new DocumentoDTO();
        dto.setTipo("PASAPORTE");
        //documentoService.save(dto);

        mockMvc.perform(get("/api/documentos/search/tipo")
                .param("tipo", "PASAPORTE")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].tipo").value("PASAPORTE"));
    }

    @Test
    void testCreateAndGetDomicilio_Endpoint() throws Exception {
        DomicilioDTO dto = new DomicilioDTO();
        dto.setProvincia("Buenos Aires");
        dto.setLocalidad("La Plata");
        String json = objectMapper.writeValueAsString(dto);

        // Crear domicilio
        MvcResult result = mockMvc.perform(post("/api/domicilios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        DomicilioDTO creado = objectMapper.readValue(response, DomicilioDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/domicilios/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.provincia").value("Buenos Aires"));
    }

    @Test
    void testCreateAndGetMedioComunicacion_Endpoint() throws Exception {
        MedioComunicacionDTO dto = new MedioComunicacionDTO();
        dto.setTipo("EMAIL");
        dto.setValor("<EMAIL>");
        String json = objectMapper.writeValueAsString(dto);

        // Crear medio de comunicación
        MvcResult result = mockMvc.perform(post("/api/medios-comunicacion")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        MedioComunicacionDTO creado = objectMapper.readValue(response, MedioComunicacionDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/medios-comunicacion/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tipo").value("EMAIL"));
    }

    @Test
    void testCreateAndGetFotografia_Endpoint() throws Exception {
        FotografiaDTO dto = new FotografiaDTO();
        dto.setDescripcion("Foto de prueba");
        dto.setTipoArchivo("image/jpeg");
        dto.setTamanio(12345L);
        dto.setDatos(new byte[]{1,2,3});
        String json = objectMapper.writeValueAsString(dto);

        // Crear fotografía
        MvcResult result = mockMvc.perform(post("/api/fotografias")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        FotografiaDTO creada = objectMapper.readValue(response, FotografiaDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/fotografias/" + creada.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.descripcion").value("Foto de prueba"));
    }

    @Test
    void testCreateAndGetExpedienteDelito_Endpoint() throws Exception {
        ExpedienteDelitoDTO dto = new ExpedienteDelitoDTO();
        dto.setObservaciones("Asociación");
        String json = objectMapper.writeValueAsString(dto);

        // Crear expediente-delito
        MvcResult result = mockMvc.perform(post("/api/expedientes-delito")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        ExpedienteDelitoDTO creado = objectMapper.readValue(response, ExpedienteDelitoDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/expedientes-delito/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.observaciones").value("Asociación"));
    }

    @Test
    void testCreateAndGetPersonaExpediente_Endpoint() throws Exception {
        PersonaExpedienteDTO dto = new PersonaExpedienteDTO();
        dto.setTipoRelacion("Testigo");
        String json = objectMapper.writeValueAsString(dto);

        // Crear persona-expediente
        MvcResult result = mockMvc.perform(post("/api/persona-expedientes")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(status().isCreated())
                .andReturn();

        // Obtener el ID de la respuesta
        String response = result.getResponse().getContentAsString();
        PersonaExpedienteDTO creado = objectMapper.readValue(response, PersonaExpedienteDTO.class);

        // Consultar por ID
        mockMvc.perform(get("/api/persona-expedientes/" + creado.getId())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.tipoRelacion").value("Testigo"));
    }

    @Test
    void testAuthLogin_SuccessAndFail() throws Exception {
        // Suponiendo que existe un usuario admin: admin/admin
        String loginJson = "{\"email\":\"admin\",\"password\":\"admin\"}";
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk());

        // Login fallido
        String badLoginJson = "{\"email\":\"admin\",\"password\":\"wrongpass\"}";
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(badLoginJson))
                .andExpect(status().is4xxClientError());
    }

    @Test
    void testArchivos_SubirYDescargarDocumento() throws Exception {
        ExpedienteDTO expediente = new ExpedienteDTO();
        expediente.setNumero("ARCH-001");
        ExpedienteDTO creado = expedienteService.create(expediente);

        MockMultipartFile file = new MockMultipartFile(
            "file", "test.txt", "text/plain", "contenido de prueba".getBytes()
        );

        // Subir documento
        mockMvc.perform(multipart("/api/archivos/documentos/" + creado.getId())
                .file(file)
                .param("tipo", "TEST")
                .param("observaciones", "Doc test"))
                .andExpect(status().isCreated());

        // Nota: aquí deberías obtener el ID real del documento creado para descargarlo
        // Por simplicidad, intentamos descargar el documento con ID 1
        mockMvc.perform(get("/api/archivos/documentos/1"))
                .andExpect(status().isOk());
    }

    @Test
    void testEstadisticasGenerales_Endpoint() throws Exception {
        mockMvc.perform(get("/api/estadisticas/generales")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalExpedientes").exists());
    }
}