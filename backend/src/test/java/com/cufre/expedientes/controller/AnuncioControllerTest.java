package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.CrearAnuncioDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Prueba de integración para AnuncioController
 * Verifica que el endpoint POST /api/anuncios funcione correctamente
 * y no sea interceptado por ResourceHttpRequestHandler
 *
 * OBJETIVO PRINCIPAL: Confirmar que la corrección del WebMvcConfig funciona
 * y que POST /api/anuncios llega al controlador correcto, no al ResourceHttpRequestHandler
 */
@AutoConfigureMockMvc
@SpringBootTest
@ActiveProfiles("test")
public class AnuncioControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * PRUEBA PRINCIPAL: Verifica que POST /api/anuncios llegue al AnuncioController
     * y no sea interceptado por ResourceHttpRequestHandler (que causaba error 500)
     *
     * Esta prueba confirma que la corrección en WebMvcConfig.setOrder(2) funciona correctamente.
     */
    @Test
    void testCrearAnuncio_LlegaAlControlador_NoResourceHandler() throws Exception {
        // Preparar datos de prueba
        CrearAnuncioDTO anuncioDTO = CrearAnuncioDTO.builder()
                .titulo("Anuncio de Prueba")
                .contenido("Mensaje de prueba para verificar routing correcto")
                .activo(true)
                .build();

        String json = objectMapper.writeValueAsString(anuncioDTO);

        // Ejecutar petición POST
        // IMPORTANTE: No esperamos 500 (ResourceHttpRequestHandler)
        // Esperamos que llegue al controlador (puede fallar por otros motivos como autenticación)
        mockMvc.perform(post("/api/anuncios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(result -> {
                    int status = result.getResponse().getStatus();
                    // Lo importante: NO debe ser 500 (ResourceHttpRequestHandler) ni 405 (Method Not Allowed)
                    if (status == 500 || status == 405) {
                        throw new AssertionError("El endpoint fue interceptado por ResourceHttpRequestHandler. Status: " + status);
                    }
                });
        
        // Si llegamos aquí, significa que el routing funciona correctamente
        // La petición llegó al AnuncioController, no al ResourceHttpRequestHandler
    }

    /**
     * Prueba que verifica que POST /api/anuncios con datos inválidos
     * llegue al controlador y retorne error de validación, no error 500 de routing
     */
    @Test
    void testCrearAnuncio_DatosInvalidos_LlegaAlControlador() throws Exception {
        // Preparar datos inválidos (título vacío)
        CrearAnuncioDTO anuncioDTO = CrearAnuncioDTO.builder()
                .titulo("") // Título vacío - debería fallar validación
                .contenido("Contenido válido")
                .activo(false)
                .build();

        String json = objectMapper.writeValueAsString(anuncioDTO);

        // Ejecutar petición POST
        mockMvc.perform(post("/api/anuncios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(result -> {
                    int status = result.getResponse().getStatus();
                    // Lo importante: NO debe ser 500 (ResourceHttpRequestHandler) ni 405 (Method Not Allowed)
                    if (status == 500 || status == 405) {
                        throw new AssertionError("El endpoint fue interceptado por ResourceHttpRequestHandler. Status: " + status);
                    }
                });
    }

    /**
     * Prueba que verifica que POST /api/anuncios sin autenticación
     * llegue al controlador y retorne error de autorización, no error 500 de routing
     */
    @Test
    void testCrearAnuncio_SinAutenticacion_LlegaAlControlador() throws Exception {
        // Preparar datos válidos
        CrearAnuncioDTO anuncioDTO = CrearAnuncioDTO.builder()
                .titulo("Anuncio de Prueba")
                .contenido("Contenido de prueba")
                .activo(true)
                .build();

        String json = objectMapper.writeValueAsString(anuncioDTO);

        // Ejecutar petición POST sin autenticación
        mockMvc.perform(post("/api/anuncios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(json))
                .andExpect(result -> {
                    int status = result.getResponse().getStatus();
                    // Lo importante: NO debe ser 500 (ResourceHttpRequestHandler) ni 405 (Method Not Allowed)
                    if (status == 500 || status == 405) {
                        throw new AssertionError("El endpoint fue interceptado por ResourceHttpRequestHandler. Status: " + status);
                    }
                });
    }

    /**
     * Prueba adicional: Verifica que el endpoint GET /api/anuncios también funcione
     * (confirma que el routing general de /api/anuncios/* funciona correctamente)
     */
    @Test
    void testGetAnuncios_LlegaAlControlador_NoResourceHandler() throws Exception {
        // Ejecutar petición GET
        mockMvc.perform(post("/api/anuncios")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(result -> {
                    int status = result.getResponse().getStatus();
                    // Lo importante: NO debe ser 500 (ResourceHttpRequestHandler) ni 405 (Method Not Allowed)
                    if (status == 500 || status == 405) {
                        throw new AssertionError("El endpoint fue interceptado por ResourceHttpRequestHandler. Status: " + status);
                    }
                });
    }
}