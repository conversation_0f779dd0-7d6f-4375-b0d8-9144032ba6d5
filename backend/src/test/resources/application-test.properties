# Usar H2 en memoria para tests de integración, en modo Oracle y forzando mayúsculas
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=Oracle;DATABASE_TO_UPPER=true
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop

# Desactivar Flyway y Liquibase
spring.flyway.enabled=false
spring.liquibase.enabled=false

# Otros ajustes de logs y seguridad
logging.level.org.hibernate.SQL=DEBUG
spring.security.user.password=test
security.basic.enabled=false
spring.security.enabled=false 