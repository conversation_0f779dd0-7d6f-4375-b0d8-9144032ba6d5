<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CUFRE - Sistema de Expedientes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .api-link {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .api-link:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ CUFRE - Sistema de Expedientes</h1>
        
        <div class="status">
            <strong>✅ Backend funcionando correctamente</strong><br>
            Servidor iniciado en puerto 8080
        </div>

        <div class="api-section">
            <h3>📋 APIs Principales</h3>
            <a href="/api/parametros-prioridad" class="api-link">Parámetros de Prioridad</a>
            <a href="/api/anuncios" class="api-link">Anuncios</a>
            <a href="/api/anuncios/activo" class="api-link">Anuncios Activos</a>
        </div>

        <div class="api-section">
            <h3>🔧 APIs de Sistema</h3>
            <a href="/api/health" class="api-link">Health Check</a>
            <a href="/actuator/health" class="api-link">Actuator Health</a>
        </div>

        <div class="api-section">
            <h3>📚 Documentación</h3>
            <p>Esta es la página de inicio del sistema CUFRE. El backend está configurado para servir tanto la API REST como esta interfaz web básica.</p>
            <p><strong>Nota:</strong> Esta página se sirve automáticamente para rutas no encontradas (SPA fallback).</p>
        </div>
    </div>

    <script>
        // Verificar estado de las APIs principales
        const checkApiStatus = async () => {
            const apis = [
                { name: 'Parámetros de Prioridad', url: '/api/parametros-prioridad' },
                { name: 'Anuncios', url: '/api/anuncios' },
                { name: 'Anuncios Activos', url: '/api/anuncios/activo' }
            ];

            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    console.log(`${api.name}: ${response.status} ${response.statusText}`);
                } catch (error) {
                    console.error(`${api.name}: Error -`, error.message);
                }
            }
        };

        // Ejecutar verificación después de cargar la página
        window.addEventListener('load', () => {
            setTimeout(checkApiStatus, 1000);
        });
    </script>
</body>
</html>