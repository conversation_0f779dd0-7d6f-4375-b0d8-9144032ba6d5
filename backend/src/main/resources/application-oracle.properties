# Configuración de conexión a Oracle
spring.datasource.url=****************************************************************
spring.datasource.username=CUFRE-TEST
spring.datasource.password=C5fr3T3st!
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# Configuración de HikariCP (pool de conexiones)
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.auto-commit=false
spring.datasource.hikari.pool-name=CufreOraclePool
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL

# Propiedades de conexión adicionales
spring.datasource.hikari.data-source-properties.oracle.net.CONNECT_TIMEOUT=10000
spring.datasource.hikari.data-source-properties.oracle.jdbc.ReadTimeout=30000
spring.datasource.hikari.initialization-fail-timeout=30000

# Configuración de Hibernate
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.type=trace
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=false
# Desactivar uso del esquema predeterminado para las consultas
spring.jpa.properties.hibernate.default_schema=

# Configuración de transacciones
spring.transaction.default-timeout=60
spring.jpa.properties.hibernate.connection.isolation=2
spring.jpa.open-in-view=false

# Configuración para prefijos de tablas
# Configurar uso de identificadores sin comillas
spring.jpa.properties.hibernate.globally_quoted_identifiers=false

# Configuración específica de Oracle
spring.jpa.properties.hibernate.dialect.oracle.prefer_long_raw=true

# Transformar nombres de tablas a mayúsculas para Oracle
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.globally_quoted_identifiers_skip_column_definitions=true
spring.jpa.properties.hibernate.physical_naming_strategy=com.cufre.expedientes.config.UpperCaseNamingStrategy

# Configuración de Flyway
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.oracle.sqlplus=true
# Configuración precisa para Flyway - especificar el esquema real
spring.flyway.schemas=CUFRE-TEST
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}

# Configuración de logs detallados para diagnóstico
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.com.zaxxer.hikari=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.org.flywaydb=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.springframework.orm=DEBUG
logging.level.org.springframework.transaction=DEBUG
logging.level.com.cufre.expedientes=DEBUG

# Servidor
server.port=8080
server.servlet.context-path=/api

# Configuración de seguridad
spring.security.user.name=admin
spring.security.user.password=admin

# Configuración de Spring Boot Actuator para monitoreo
management.endpoints.web.exposure.include=health,info,env,metrics
management.endpoint.health.show-details=always
management.health.db.enabled=true
management.endpoint.health.probes.enabled=true

# Configuración JWT
app.jwt.secret=cufre-expedientes-jwt-secret-key-for-signing-tokens-change-in-production
app.jwt.expiration-ms=86400000
app.jwt.authorities-key=roles

# Configuración para subida de archivos
app.uploads.dir=uploads
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB 

# Configuración de correo SMTP para Gmail (<EMAIL>)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=olfa qcwg cgwk dvyx
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.connectiontimeout=30000
spring.mail.properties.mail.smtp.timeout=30000
spring.mail.properties.mail.smtp.writetimeout=30000
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false
# Deshabilitar la validación estricta
spring.mail.properties.mail.smtp.ssl.checkserveridentity=false
