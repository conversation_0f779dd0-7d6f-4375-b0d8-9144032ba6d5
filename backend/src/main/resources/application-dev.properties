# Configuración de Oracle Database para desarrollo
spring.datasource.url=***********************************
spring.datasource.username=C##CUFRE_USER
spring.datasource.password=Cufre-2025
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# --- Configuración de H2 deshabilitada ---
# spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
# spring.datasource.driver-class-name=org.h2.Driver
# spring.datasource.username=sa
# spring.datasource.password=
# spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
# spring.jpa.hibernate.ddl-auto=update
# spring.h2.console.enabled=true
# spring.h2.console.path=/h2-console

# Habilitar Flyway para desarrollo
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.validate-on-migrate=false

# Servidor
server.port=8080

# Configuración de seguridad
spring.security.user.name=admin
spring.security.user.password=admin

# Configuración JWT
app.jwt.secret=cufre-expedientes-jwt-secret-key-for-signing-tokens-change-in-production
app.jwt.expiration-ms=86400000
app.jwt.authorities-key=roles

# Configuración para subida de archivos
app.uploads.dir=/Users/<USER>/Documents/CodigoFuente/cufre/backend/uploads
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configuración de correo SMTP para Gmail (<EMAIL>)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=olfa qcwg cgwk dvyx
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.connectiontimeout=30000
spring.mail.properties.mail.smtp.timeout=30000
spring.mail.properties.mail.smtp.writetimeout=30000
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false
# Deshabilitar la validación estricta
spring.mail.properties.mail.smtp.ssl.checkserveridentity=false

server.servlet.context-path=/api 