# Configuración de conexión a Oracle
spring.datasource.url=****************************************************************
spring.datasource.username=CUFRE-TEST
spring.datasource.password=C5fr3T3st!
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# Configuración de HikariCP (pool de conexiones)
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=12
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.pool-name=CufreHikariPool
spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL

# Configuración de Hibernate
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.type=trace
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Configuración de Flyway
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.oracle.sqlplus=true
# spring.flyway.schemas=C##CUFRE_USER
#spring.flyway.user=C##CUFRE_USER
#spring.flyway.password=Cufre-2025

# Configuración de logs
logging.level.root=INFO
logging.level.com.cufre.expedientes=DEBUG
logging.level.org.springframework.security=TRACE
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Servidor
server.port=8080

# Configuración de seguridad
spring.security.user.name=admin
spring.security.user.password=admin

# Configuración de Spring Boot Actuator
management.endpoints.web.exposure.include=health,info,metrics,env
management.endpoint.health.show-details=always
management.health.db.enabled=true
management.endpoint.health.probes.enabled=true

# Configuración JWT
app.jwt.secret=ClaveSecretaParaGenerarTokenJwtDebeSerLargaYSeguraParaAplicacionCUFRE2024
app.jwt.expiration-ms=7200000
app.jwt.authorities-key=roles

# Configuración para subida de archivos
app.uploads.dir=uploads
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configuración de recursos estáticos
spring.web.resources.add-mappings=true
spring.mvc.throw-exception-if-no-handler-found=false
spring.web.resources.static-locations=classpath:/static/

# Configuración del servidor
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=never
server.error.include-exception=false

# Permitir sobrescritura de definición de beans (para solucionar error de duplicación)
spring.main.allow-bean-definition-overriding=true

# Configuración de correo
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=sqyd hzte bowb grsp
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.smtp.connectiontimeout=30000
spring.mail.properties.mail.smtp.timeout=30000
spring.mail.properties.mail.smtp.writetimeout=30000
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false
# Deshabilitar la validación estricta
spring.mail.properties.mail.smtp.ssl.checkserveridentity=false
spring.mail.default-encoding=UTF-8

server.servlet.context-path=/api

# Perfil activo por defecto para desarrollo
spring.profiles.active=dev
