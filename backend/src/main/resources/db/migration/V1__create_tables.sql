-- Creación de tablas del sistema de gestión de expedientes
-- Convención: Nombres de tablas y columnas en mayúsculas

-- Tabla de usuarios
CREATE TABLE USUARIO (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    ROL VARCHAR2(50) NOT NULL,
    NOMBRE VARCHAR2(100) NOT NULL,
    APELLIDO VARCHAR2(100) NOT NULL,
    CONTRASENA VARCHAR2(200) NOT NULL,
    DEPENDENCIA VARCHAR2(100),
    CREADO_POR NUMBER,
    FOREIGN KEY (CREADO_POR) REFERENCES USUARIO(ID)
);

-- Tabla de expedientes
CREATE TABLE EXPEDIENTE (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    NUMERO VARCHAR2(50) UNIQUE NOT NULL,
    FECHA_INGRESO DATE,
    ESTADO_SITUACION VARCHAR2(50),
    FUERZA_ASIGNADA VARCHAR2(100),
    FECHA_ASIGNACION DATE,
    AUTORIZACION_TAREAS VARCHAR2(200),
    FECHA_AUTORIZACION_TAREAS DATE,
    DESCRIPCION VARCHAR2(1000),
    PRIORIDAD NUMBER,
    RECOMPENSA VARCHAR2(200),
    FECHA_OFICIO DATE,
    NUMERO_CAUSA VARCHAR2(50),
    CARATULA VARCHAR2(200),
    JUZGADO VARCHAR2(100),
    SECRETARIA VARCHAR2(100),
    FISCALIA VARCHAR2(100),
    JURISDICCION VARCHAR2(100),
    PROVINCIA VARCHAR2(100),
    TIPO_CAPTURA VARCHAR2(20) CHECK (TIPO_CAPTURA IN ('NACIONAL','INTERNACIONAL','OTRO')),
    PAIS VARCHAR2(100),
    MOTIVO_CAPTURA VARCHAR2(200),
    DISPOSICION_JUZGADO VARCHAR2(200),
    
    -- Datos del Prófugo
    PROFUGO_TEZ VARCHAR2(50),
    PROFUGO_CONTEXTURA_FISICA VARCHAR2(50),
    PROFUGO_CABELLO VARCHAR2(50),
    PROFUGO_OJOS VARCHAR2(50),
    PROFUGO_ESTATURA NUMBER(5,2),
    PROFUGO_PESO NUMBER(5,2),
    PROFUGO_MARCAS_VISIBLES VARCHAR2(200),
    PROFUGO_NIVEL_ESTUDIOS VARCHAR2(50),
    PROFUGO_PROFESION_OCUPACION VARCHAR2(50),
    PROFUGO_GRUPO_SANGUINEO VARCHAR2(10),
    PROFUGO_TELEFONO VARCHAR2(30),
    PROFUGO_ANTECEDENTES_PENALES NUMBER(1),
    PROFUGO_DETALLE_ANTECEDENTES VARCHAR2(200),
    PROFUGO_SITUACION_PROCESAL VARCHAR2(100),
    PROFUGO_NUMERO_PRONTUARIO VARCHAR2(50),
    PROFUGO_ULTIMA_VEZ_VISTO VARCHAR2(200),
    PROFUGO_ESTABA_DETENIDO NUMBER(1),
    PROFUGO_NUMERO_DETENCIONES_PREVIAS NUMBER,
    
    -- Datos del Hecho
    FECHA_HECHO DATE,
    LUGAR_HECHO VARCHAR2(200),
    DESCRIPCION_HECHO VARCHAR2(1000),
    MEDIATICO_FLAG NUMBER(1),
    NUMERO_COMPLICES NUMBER,
    TIPO_DANO VARCHAR2(50),
    USO_ARMAS_FUEGO_FLAG NUMBER(1),
    USO_ARMAS_BLANCAS_FLAG NUMBER(1),
    PELIGROSIDAD_FLAG NUMBER(1),
    ANTECEDENTES_FLAG NUMBER(1),
    DETALLE_ANTECEDENTES VARCHAR2(200),
    REINCIDENTE_FLAG NUMBER(1),
    REITERANTE_FLAG NUMBER(1),
    
    -- Datos de la detención
    FECHA_DETENCION DATE,
    LUGAR_DETENCION VARCHAR2(200),
    FUERZA_DETENCION VARCHAR2(100),
    DESCRIPCION_PROCEDIMIENTO VARCHAR2(500),
    
    -- Organización Criminal
    BANDA_FLAG NUMBER(1),
    TERRORISMO_FLAG NUMBER(1),
    NOMBRE_BANDA VARCHAR2(100),
    NIVEL_ORGANIZACION VARCHAR2(20) CHECK (NIVEL_ORGANIZACION IN ('SIMPLE','COMPLEJA')),
    AMBITO_BANDA VARCHAR2(20) CHECK (AMBITO_BANDA IN ('NACIONAL','PROVINCIAL','BARRIAL','INTERNACIONAL')),
    CAPACIDAD_OPERATIVA VARCHAR2(10) CHECK (CAPACIDAD_OPERATIVA IN ('ALTA','BAJA')),
    PLANIFICACION_FLAG NUMBER(1),
    PATRONES_REPETITIVOS NUMBER(1),
    CONEXIONES_OTRAS_ACTIVIDADES_FLAG NUMBER(1),
    
    -- Impacto y Contexto
    IMPACTO_SOCIAL VARCHAR2(10) CHECK (IMPACTO_SOCIAL IN ('ALTO','BAJO')),
    NIVEL_INCIDENCIA_ZONA VARCHAR2(10) CHECK (NIVEL_INCIDENCIA_ZONA IN ('ALTA','MEDIA','BAJA')),
    INSTITUCION_SENSIBLE_CERCANA VARCHAR2(20) CHECK (INSTITUCION_SENSIBLE_CERCANA IN ('ESCUELA','HOSPITAL','IGLESIA','SINAGOGA','MEZQUITA','OTRO','NINGUNA','SIN_DATO')),
    IMPACTO_PERCEPCION VARCHAR2(10) CHECK (IMPACTO_PERCEPCION IN ('ALTA','MEDIA','BAJA')),
    RECURSOS_LIMITADOS VARCHAR2(50),
    AREA_FRONTERIZA VARCHAR2(50),
    TIPO_VICTIMA VARCHAR2(20) CHECK (TIPO_VICTIMA IN ('MENOR','MUJER','ANCIANO_JUBILADO','POLITICO','JUEZ','FISCAL','OTROS'))
);

-- Tabla de personas
CREATE TABLE PERSONA (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    TIPO_DOCUMENTO VARCHAR2(20),
    NUMERO_DOCUMENTO VARCHAR2(20),
    NOMBRE VARCHAR2(100),
    APELLIDO VARCHAR2(100),
    ALIAS VARCHAR2(100),
    FECHA_NACIMIENTO DATE,
    EDAD NUMBER,
    NACIONALIDAD VARCHAR2(50),
    GENERO VARCHAR2(20),
    ESTADO_CIVIL VARCHAR2(20)
);

-- Tabla de relación personas-expediente
CREATE TABLE PERSONA_EXPEDIENTE (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    PERSONA_ID NUMBER NOT NULL,
    EXPEDIENTE_ID NUMBER NOT NULL,
    TIPO_RELACION VARCHAR2(50) NOT NULL, -- 'PROFUGO', 'FAMILIAR', 'ASOCIADO', etc.
    OBSERVACIONES VARCHAR2(500),
    FOREIGN KEY (PERSONA_ID) REFERENCES PERSONA(ID),
    FOREIGN KEY (EXPEDIENTE_ID) REFERENCES EXPEDIENTE(ID),
    CONSTRAINT UK_PERSONA_EXPEDIENTE UNIQUE (PERSONA_ID, EXPEDIENTE_ID, TIPO_RELACION)
);

-- Tabla de domicilios
CREATE TABLE DOMICILIO (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    PERSONA_ID NUMBER,
    CALLE VARCHAR2(100),
    NUMERO VARCHAR2(20),
    PISO VARCHAR2(10),
    DEPARTAMENTO VARCHAR2(10),
    BARRIO VARCHAR2(50),
    CODIGO_POSTAL VARCHAR2(20),
    LOCALIDAD VARCHAR2(100),
    PROVINCIA VARCHAR2(100),
    PAIS VARCHAR2(100) DEFAULT 'Argentina',
    TIPO VARCHAR2(20),
    ES_PRINCIPAL NUMBER(1),
    DESCRIPCION VARCHAR2(200),
    FOREIGN KEY (PERSONA_ID) REFERENCES PERSONA(ID)
);

-- Tabla de medios de comunicación
CREATE TABLE MEDIOS_DE_COMUNICACION (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    PERSONA_ID NUMBER NOT NULL,
    TIPO VARCHAR2(20),
    VALOR VARCHAR2(100),
    OBSERVACIONES VARCHAR2(200),
    FOREIGN KEY (PERSONA_ID) REFERENCES PERSONA(ID)
);

-- Tabla de fotografías
CREATE TABLE FOTOGRAFIA (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    EXPEDIENTE_ID NUMBER NOT NULL,
    RUTA_ARCHIVO VARCHAR2(200),
    DESCRIPCION VARCHAR2(200),
    FECHA DATE,
    FOREIGN KEY (EXPEDIENTE_ID) REFERENCES EXPEDIENTE(ID)
);

-- Tabla de documentos
CREATE TABLE DOCUMENTO (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    EXPEDIENTE_ID NUMBER NOT NULL,
    TIPO VARCHAR2(50),
    RUTA_ARCHIVO VARCHAR2(200),
    DESCRIPCION VARCHAR2(200),
    FECHA DATE,
    FOREIGN KEY (EXPEDIENTE_ID) REFERENCES EXPEDIENTE(ID)
);

-- Tabla de delitos
CREATE TABLE DELITO (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    NOMBRE VARCHAR2(200) NOT NULL,
    DESCRIPCION VARCHAR2(1000),
    CODIGO_PENAL VARCHAR2(100),
    TIPO_PENA VARCHAR2(50),
    PENA_MINIMA VARCHAR2(50),
    PENA_MAXIMA VARCHAR2(50),
    VALORACION NUMBER,
    CREADO_EN DATE DEFAULT SYSDATE,
    ACTUALIZADO_EN DATE
);

-- Tabla de relación expediente-delito
CREATE TABLE EXPEDIENTE_DELITO (
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    EXPEDIENTE_ID NUMBER NOT NULL,
    DELITO_ID NUMBER NOT NULL,
    OBSERVACIONES VARCHAR2(500),
    FOREIGN KEY (EXPEDIENTE_ID) REFERENCES EXPEDIENTE(ID),
    FOREIGN KEY (DELITO_ID) REFERENCES DELITO(ID),
    CONSTRAINT UK_EXPEDIENTE_DELITO UNIQUE (EXPEDIENTE_ID, DELITO_ID)
);

-- Secuencias para generar IDs
CREATE SEQUENCE SEQ_USUARIO START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_EXPEDIENTE START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_PERSONA START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_DOMICILIO START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_MEDIOS_COMUNICACION START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_FOTOGRAFIA START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_DOCUMENTO START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_DELITO START WITH 1 INCREMENT BY 1;

-- Insertar un usuario administrador inicial
INSERT INTO USUARIO (ID, ROL, NOMBRE, APELLIDO, CONTRASENA, DEPENDENCIA)
VALUES (SEQ_USUARIO.NEXTVAL, 'ADMIN', 'Administrador', 'Sistema', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'SISTEMA'); -- Contraseña: password

-- Commit para aplicar los cambios
COMMIT; 