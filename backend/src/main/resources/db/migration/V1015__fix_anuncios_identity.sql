-- Migración para corregir el problema ORA-32795 en tabla ANUNCIOS
-- Cambia la columna ID de GENERATED ALWAYS a GENERATED BY DEFAULT ON NULL AS IDENTITY
-- Autor: Sistema CUFRE
-- Fecha: 2025-06-21
-- Problema: ORA-32795: no se puede insertar una columna de identidad siempre generada

-- Verificar si la tabla ANUNCIOS existe y corregir la configuración de identidad
DECLARE
    table_exists NUMBER;
    identity_config VARCHAR2(4000);
    always_count NUMBER;
BEGIN
    -- Verificar si la tabla existe
    SELECT COUNT(*) INTO table_exists
    FROM user_tables
    WHERE table_name = 'ANUNCIOS';
    
    IF table_exists > 0 THEN
        -- Verificar si existe configuración de identidad ALWAYS
        BEGIN
            SELECT COUNT(*) INTO always_count
            FROM user_tab_identity_cols
            WHERE table_name = 'ANUNCIOS'
            AND column_name = 'ID'
            AND identity_options LIKE '%ALWAYS%';
            
            -- Si hay configuración ALWAYS, cambiarla a BY DEFAULT ON NULL
            IF always_count > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE ANUNCIOS MODIFY (ID GENERATED BY DEFAULT ON NULL AS IDENTITY)';
                DBMS_OUTPUT.PUT_LINE('Columna ID de ANUNCIOS modificada exitosamente a GENERATED BY DEFAULT ON NULL AS IDENTITY');
            ELSE
                DBMS_OUTPUT.PUT_LINE('Columna ID de ANUNCIOS ya tiene la configuración correcta o no usa identidad');
            END IF;
            
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                DBMS_OUTPUT.PUT_LINE('La columna ID de ANUNCIOS no tiene configuración de identidad');
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error al verificar/modificar la configuración de identidad: ' || SQLERRM);
                RAISE;
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('La tabla ANUNCIOS no existe, no se requiere corrección');
    END IF;
END;
/

-- Verificar el resultado final
DECLARE
    identity_config VARCHAR2(4000);
    config_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO config_count
    FROM user_tab_identity_cols
    WHERE table_name = 'ANUNCIOS'
    AND column_name = 'ID';
    
    IF config_count > 0 THEN
        SELECT identity_options INTO identity_config
        FROM user_tab_identity_cols
        WHERE table_name = 'ANUNCIOS'
        AND column_name = 'ID';
        
        DBMS_OUTPUT.PUT_LINE('Configuración final de identidad para ANUNCIOS.ID: ' || SUBSTR(identity_config, 1, 200));
    ELSE
        DBMS_OUTPUT.PUT_LINE('No se encontró configuración de identidad para ANUNCIOS.ID');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error al verificar configuración final: ' || SQLERRM);
END;
/