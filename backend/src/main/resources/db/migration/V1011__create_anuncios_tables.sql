-- Migración para crear las tablas del sistema de anuncios globales (IDEMPOTENTE)
-- Autor: Siste<PERSON> CUFRE
-- Fecha: 2025-01-20

-- Crear tabla ANUNCIOS (solo si no existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'ANUNCIOS';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE ANUNCIOS (
            ID NUMBER(19,0) GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            TITULO VARCHAR2(255) NOT NULL,
            CONTENIDO CLOB NOT NULL,
            ACTIVO NUMBER(1,0) DEFAULT 0 NOT NULL,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CREADO_POR_ID NUMBER(19,0) NOT NULL,
            CONSTRAINT FK_ANUNCIOS_USUARIO FOREIGN KEY (CREADO_POR_ID) REFERENCES USUARIO(ID)
        )';
    END IF;
END;
/

-- Crear tabla ANUNCIOS_VISTOS (solo si no existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'ANUNCIOS_VISTOS';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE ANUNCIOS_VISTOS (
            USUARIO_ID NUMBER(19,0) NOT NULL,
            ANUNCIO_ID NUMBER(19,0) NOT NULL,
            FECHA_VISTO TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (USUARIO_ID, ANUNCIO_ID),
            CONSTRAINT FK_VISTOS_USUARIO FOREIGN KEY (USUARIO_ID) REFERENCES USUARIO(ID) ON DELETE CASCADE,
            CONSTRAINT FK_VISTOS_ANUNCIO FOREIGN KEY (ANUNCIO_ID) REFERENCES ANUNCIOS(ID) ON DELETE CASCADE
        )';
    END IF;
END;
/

-- Crear índice IDX_ANUNCIOS_ACTIVO (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ANUNCIOS_ACTIVO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ANUNCIOS_ACTIVO ON ANUNCIOS(ACTIVO)';
    END IF;
END;
/

-- Crear índice IDX_ANUNCIOS_FECHA_CREACION (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ANUNCIOS_FECHA_CREACION';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ANUNCIOS_FECHA_CREACION ON ANUNCIOS(FECHA_CREACION)';
    END IF;
END;
/

-- Crear índice IDX_ANUNCIOS_VISTOS_USUARIO (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ANUNCIOS_VISTOS_USUARIO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ANUNCIOS_VISTOS_USUARIO ON ANUNCIOS_VISTOS(USUARIO_ID)';
    END IF;
END;
/

-- Crear índice IDX_ANUNCIOS_VISTOS_ANUNCIO (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ANUNCIOS_VISTOS_ANUNCIO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ANUNCIOS_VISTOS_ANUNCIO ON ANUNCIOS_VISTOS(ANUNCIO_ID)';
    END IF;
END;
/

-- Agregar comentarios para documentar las tablas (solo si las tablas existen)
DECLARE
    table_exists NUMBER;
BEGIN
    -- Comentarios para tabla ANUNCIOS
    SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'ANUNCIOS';
    IF table_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON TABLE ANUNCIOS IS ''Tabla para almacenar anuncios globales del sistema''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.ID IS ''Identificador único del anuncio''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.TITULO IS ''Título del anuncio que se mostrará en el modal''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.CONTENIDO IS ''Contenido HTML del anuncio''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.ACTIVO IS ''Indica si este es el anuncio activo (solo uno puede estar activo a la vez)''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.FECHA_CREACION IS ''Fecha y hora de creación del anuncio''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS.CREADO_POR_ID IS ''ID del usuario administrador que creó el anuncio''';
    END IF;
    
    -- Comentarios para tabla ANUNCIOS_VISTOS
    SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'ANUNCIOS_VISTOS';
    IF table_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON TABLE ANUNCIOS_VISTOS IS ''Tabla para rastrear qué usuarios han visto qué anuncios''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS_VISTOS.USUARIO_ID IS ''ID del usuario que vio el anuncio''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS_VISTOS.ANUNCIO_ID IS ''ID del anuncio que fue visto''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN ANUNCIOS_VISTOS.FECHA_VISTO IS ''Fecha y hora en que el usuario vio el anuncio''';
    END IF;
END;
/