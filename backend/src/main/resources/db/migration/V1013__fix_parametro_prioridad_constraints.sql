-- Migración para corregir las restricciones de la tabla PARAMETRO_PRIORIDAD
-- Autor: Sistema CUFRE
-- Fecha: 2025-06-20
-- MIGRACIÓN IDEMPOTENTE: Verifica existencia antes de aplicar cambios

DECLARE
    v_count NUMBER;
    v_data_length NUMBER;
    constraint_exists EXCEPTION;
    PRAGMA EXCEPTION_INIT(constraint_exists, -2443);
BEGIN
    -- Eliminar la restricción restrictiva de tipo_variable si existe
    BEGIN
        EXECUTE IMMEDIATE 'ALTER TABLE PARAMETRO_PRIORIDAD DROP CONSTRAINT SYS_C008846';
    EXCEPTION
        WHEN constraint_exists THEN
            NULL; -- La restricción no existe, continuar
        WHEN OTHERS THEN
            IF SQLCODE != -2443 THEN -- ORA-02443: Cannot drop constraint - nonexistent constraint
                RAISE;
            END IF;
    END;
    
    -- Verificar si la columna TIPO_VARIABLE ya tiene el tamaño correcto
    SELECT data_length INTO v_data_length 
    FROM user_tab_columns 
    WHERE table_name = 'PARAMETRO_PRIORIDAD' 
    AND column_name = 'TIPO_VARIABLE';
    
    -- Ampliar el tamaño de la columna TIPO_VARIABLE solo si es necesario
    IF v_data_length < 50 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE PARAMETRO_PRIORIDAD MODIFY TIPO_VARIABLE VARCHAR2(50)';
    END IF;
    
    -- Verificar si los comentarios ya existen (verificando uno como muestra)
    SELECT COUNT(*) INTO v_count 
    FROM user_tab_comments 
    WHERE table_name = 'PARAMETRO_PRIORIDAD' 
    AND comments IS NOT NULL;
    
    -- Agregar comentarios solo si no existen
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON TABLE PARAMETRO_PRIORIDAD IS ''Tabla para almacenar parámetros configurables del sistema de cálculo de prioridad''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.ID IS ''Identificador único del parámetro''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.CLAVE_VARIABLE IS ''Clave única que identifica el parámetro''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.VALOR IS ''Valor numérico del parámetro para el cálculo de prioridad''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.TIPO_VARIABLE IS ''Categoría o tipo del parámetro (PROFESION, DETENCIONES_PREVIAS, etc.)''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.FECHA_CREACION IS ''Fecha y hora de creación del parámetro''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.FECHA_MODIFICACION IS ''Fecha y hora de última modificación del parámetro''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN PARAMETRO_PRIORIDAD.MODIFICADO_POR IS ''Usuario que realizó la última modificación''';
    END IF;
END;
/