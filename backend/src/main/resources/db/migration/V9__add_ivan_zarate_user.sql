-- <PERSON><PERSON><PERSON> usuario <PERSON> con rol SUPERUSUARIO solo si no existe
BEGIN
  DECLARE
    v_count NUMBER;
  BEGIN
    SELECT COUNT(*) INTO v_count FROM USUARIO WHERE EMAIL = '<EMAIL>';
    
    -- Solo insertar si no existe
    IF v_count = 0 THEN
      INSERT INTO USUARIO (ID, ROL, NOMBRE, APELLIDO, CONTRASENA, DEPENDENCIA, EMAIL)
      VALUES (
          SEQ_USUARIO.NEXTVAL, 
          'SUPERUSUARIO', 
          '<PERSON>', 
          'Zarate', 
          '$2a$10$zIZXvwvgL3jHcOyqE0xiWOT0mMf6BpTejP9Y7ZOfRqgiTI9Q<PERSON>raoi', -- Hash para 'Minseg2025-'
          'MINISTERIO DE SEGURIDAD', 
          '<EMAIL>'
      );
      DBMS_OUTPUT.PUT_LINE('<PERSON>ua<PERSON> Ivan <PERSON> creado correctamente');
    ELSE
      DBMS_OUTPUT.PUT_LINE('El usuario Ivan <PERSON>arate ya existe');
    END IF;
  END;
END;
/

COMMIT; 