-- Migración para limpiar la secuencia ANUNCIOS_SEQ que ya no es necesaria
-- Después del cambio a IDENTITY en lugar de SEQUENCE
-- Autor: Sistema CUFRE
-- Fecha: 2025-06-21

-- Eliminar la secuencia ANUNCIOS_SEQ si existe (ya no es necesaria con IDENTITY)
DECLARE
    sequence_exists NUMBER;
BEGIN
    -- Verificar si la secuencia existe
    SELECT COUNT(*) INTO sequence_exists 
    FROM user_sequences 
    WHERE sequence_name = 'ANUNCIOS_SEQ';
    
    IF sequence_exists > 0 THEN
        EXECUTE IMMEDIATE 'DROP SEQUENCE ANUNCIOS_SEQ';
        DBMS_OUTPUT.PUT_LINE('Secuencia ANUNCIOS_SEQ eliminada exitosamente');
    ELSE
        DBMS_OUTPUT.PUT_LINE('La secuencia ANUNCIOS_SEQ no existe, no se requiere eliminación');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error al eliminar la secuencia ANUNCIOS_SEQ: ' || SQLERRM);
        -- No lanzar error para que la migración no falle si hay dependencias
END;
/