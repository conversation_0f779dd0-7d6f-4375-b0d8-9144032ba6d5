-- Migración V1010: Eliminar sistema de avatares y reemplazar con iconos de iniciales
-- Fecha: Enero 2025
-- Descripción: Eliminación completa del sistema de avatares predefinidos y columna avatar_url

-- Eliminar índices de la tabla AVATAR_PREDEFINIDO
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_AVATAR_CATEGORIA';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN -- -942 = table or view does not exist
            NULL; -- Ignorar si el índice no existe
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_AVATAR_ACTIVO';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            NULL;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_AVATAR_ORDEN';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            NULL;
        END IF;
END;
/

-- Eliminar la tabla AVATAR_PREDEFINIDO completamente
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE AVATAR_PREDEFINIDO CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN -- -942 = table or view does not exist
            RAISE;
        END IF;
END;
/

-- Eliminar la columna AVATAR_URL de la tabla USUARIO
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE USUARIO DROP COLUMN AVATAR_URL';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -904 THEN -- -904 = invalid identifier (column doesn't exist)
            RAISE;
        END IF;
END;
/

-- Confirmar cambios
COMMIT;

-- Comentarios para documentación
-- A partir de esta migración, el sistema utilizará iconos de iniciales generados dinámicamente
-- basados en el nombre y apellido del usuario, eliminando la necesidad de avatares predefinidos.