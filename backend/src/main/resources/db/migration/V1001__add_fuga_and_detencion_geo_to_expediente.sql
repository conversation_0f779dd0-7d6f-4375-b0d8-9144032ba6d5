DECLARE
    v_count NUMBER := 0;
BEGIN
    -- FUGA_DESCRIPCION
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'FUGA_DESCRIPCION';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (FUGA_DESCRIPCION VARCHAR2(1000))';
    END IF;

    -- FUGA_LUGAR
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'FUGA_LUGAR';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (FUGA_LUGAR VARCHAR2(255))';
    END IF;

    -- FUGA_LATITUD
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'FUGA_LATITUD';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (FUGA_LATITUD NUMBER(12,8))';
    END IF;

    -- FUGA_LONGITUD
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'FUGA_LONGITUD';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (FUGA_LONGITUD NUMBER(12,8))';
    END IF;

    -- DETENCION_DESCRIPCION
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'DETENCION_DESCRIPCION';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (DETENCION_DESCRIPCION VARCHAR2(1000))';
    END IF;

    -- DETENCION_LUGAR
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'DETENCION_LUGAR';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (DETENCION_LUGAR VARCHAR2(255))';
    END IF;

    -- DETENCION_LATITUD
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'DETENCION_LATITUD';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (DETENCION_LATITUD NUMBER(12,8))';
    END IF;

    -- DETENCION_LONGITUD
    SELECT COUNT(*) INTO v_count FROM user_tab_columns WHERE table_name = 'EXPEDIENTE' AND column_name = 'DETENCION_LONGITUD';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE ADD (DETENCION_LONGITUD NUMBER(12,8))';
    END IF;
END;
/ 