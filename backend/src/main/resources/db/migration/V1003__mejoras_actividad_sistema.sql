-- Migración para mejorar el sistema de actividad del sistema
-- Agregar nuevos campos a la tabla ACTIVIDAD_SISTEMA de forma idempotente

-- Función para verificar si una columna existe
DECLARE
    column_exists NUMBER;
BEGIN
    -- Verificar y agregar IP_CLIENTE
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'IP_CLIENTE';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD IP_CLIENTE VARCHAR2(45 CHAR)';
    END IF;
    
    -- Verificar y agregar USER_AGENT
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'USER_AGENT';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD USER_AGENT VARCHAR2(500 CHAR)';
    END IF;
    
    -- Verificar y agregar SESSION_ID
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'SESSION_ID';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD SESSION_ID VARCHAR2(255 CHAR)';
    END IF;
    
    -- Verificar y agregar ENDPOINT
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'ENDPOINT';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD ENDPOINT VARCHAR2(255 CHAR)';
    END IF;
    
    -- Verificar y agregar METODO_HTTP
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'METODO_HTTP';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD METODO_HTTP VARCHAR2(10 CHAR)';
    END IF;
    
    -- Verificar y agregar MODULO
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'MODULO';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD MODULO VARCHAR2(50 CHAR)';
    END IF;
    
    -- Verificar y agregar CATEGORIA_ACCION
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'CATEGORIA_ACCION';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD CATEGORIA_ACCION VARCHAR2(50 CHAR)';
    END IF;
    
    -- Verificar y agregar DURACION_MS
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'DURACION_MS';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD DURACION_MS NUMBER';
    END IF;
    
    -- Verificar y agregar ESTADO_RESPUESTA
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'ESTADO_RESPUESTA';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE ACTIVIDAD_SISTEMA ADD ESTADO_RESPUESTA VARCHAR2(20 CHAR)';
    END IF;
END;
/

-- Crear tabla ACTIVIDAD_DETALLE si no existe
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'ACTIVIDAD_DETALLE';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE TABLE ACTIVIDAD_DETALLE (
            ID NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            ACTIVIDAD_ID NUMBER NOT NULL,
            TIPO_DETALLE VARCHAR2(50 CHAR) NOT NULL,
            CONTENIDO_JSON CLOB,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT FK_ACTIVIDAD_DETALLE_ACTIVIDAD FOREIGN KEY (ACTIVIDAD_ID) REFERENCES ACTIVIDAD_SISTEMA(ID) ON DELETE CASCADE
        )';
    END IF;
END;
/

-- Crear índices si no existen
DECLARE
    index_exists NUMBER;
BEGIN
    -- Índice para USUARIO
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_USUARIO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_USUARIO ON ACTIVIDAD_SISTEMA(USUARIO)';
    END IF;
    
    -- Índice para FECHA_HORA
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_FECHA';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_FECHA ON ACTIVIDAD_SISTEMA(FECHA_HORA)';
    END IF;
    
    -- Índice para MODULO
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_MODULO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_MODULO ON ACTIVIDAD_SISTEMA(MODULO)';
    END IF;
    
    -- Índice para CATEGORIA_ACCION
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_CATEGORIA';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_CATEGORIA ON ACTIVIDAD_SISTEMA(CATEGORIA_ACCION)';
    END IF;
    
    -- Índice para IP_CLIENTE
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_IP';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_IP ON ACTIVIDAD_SISTEMA(IP_CLIENTE)';
    END IF;
    
    -- Índice para ACTIVIDAD_DETALLE - ACTIVIDAD_ID
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_DETALLE_ACTIVIDAD';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_DETALLE_ACTIVIDAD ON ACTIVIDAD_DETALLE(ACTIVIDAD_ID)';
    END IF;
    
    -- Índice para ACTIVIDAD_DETALLE - TIPO_DETALLE
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_DETALLE_TIPO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_DETALLE_TIPO ON ACTIVIDAD_DETALLE(TIPO_DETALLE)';
    END IF;
END;
/

-- Actualizar registros existentes con valores por defecto solo si las columnas existen y están vacías
DECLARE
    column_exists NUMBER;
BEGIN
    -- Verificar si la columna MODULO existe antes de actualizar
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'MODULO';
    
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE '
        UPDATE ACTIVIDAD_SISTEMA SET 
            MODULO = CASE 
                WHEN TIPO_ACCION LIKE ''%EXPEDIENTE%'' THEN ''EXPEDIENTES''
                WHEN TIPO_ACCION LIKE ''%LOGIN%'' OR TIPO_ACCION LIKE ''%LOGOUT%'' THEN ''SISTEMA''
                WHEN TIPO_ACCION LIKE ''%USUARIO%'' THEN ''USUARIOS''
                ELSE ''SISTEMA''
            END
        WHERE MODULO IS NULL';
    END IF;
    
    -- Verificar si la columna CATEGORIA_ACCION existe antes de actualizar
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'CATEGORIA_ACCION';
    
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE '
        UPDATE ACTIVIDAD_SISTEMA SET 
            CATEGORIA_ACCION = TIPO_ACCION
        WHERE CATEGORIA_ACCION IS NULL';
    END IF;
    
    -- Verificar si la columna ESTADO_RESPUESTA existe antes de actualizar
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'ACTIVIDAD_SISTEMA' AND column_name = 'ESTADO_RESPUESTA';
    
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE '
        UPDATE ACTIVIDAD_SISTEMA SET 
            ESTADO_RESPUESTA = ''SUCCESS''
        WHERE ESTADO_RESPUESTA IS NULL';
    END IF;
END;
/