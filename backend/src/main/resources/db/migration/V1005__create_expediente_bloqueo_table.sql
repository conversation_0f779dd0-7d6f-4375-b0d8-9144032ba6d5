-- Crear tabla para el sistema de bloqueo de expedientes (IDEMPOTENTE)
-- V1005__create_expediente_bloqueo_table.sql

-- Crear secuencia para la tabla expediente_bloqueo (solo si no existe)
DECLARE
    sequence_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO sequence_exists 
    FROM user_sequences 
    WHERE sequence_name = 'EXPEDIENTE_BLOQUEO_SEQ';
    
    IF sequence_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE expediente_bloqueo_seq START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- Crear tabla expediente_bloqueo (solo si no existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'EXPEDIENTE_BLOQUEO';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE expediente_bloqueo (
            id NUMBER(19) NOT NULL,
            expediente_id NUMBER(19) NOT NULL,
            usuario_bloqueando VARCHAR2(100 CHAR) NOT NULL,
            nombre_usuario VARCHAR2(200 CHAR) NOT NULL,
            fecha_bloqueo TIMESTAMP NOT NULL,
            session_id VARCHAR2(100 CHAR),
            CONSTRAINT pk_expediente_bloqueo PRIMARY KEY (id),
            CONSTRAINT uk_expediente_bloqueo_expediente_id UNIQUE (expediente_id),
            CONSTRAINT fk_expediente_bloqueo_expediente FOREIGN KEY (expediente_id) REFERENCES expediente(id) ON DELETE CASCADE
        )';
    END IF;
END;
/

-- Crear índice idx_expediente_bloqueo_usuario (solo si no existe)
-- Nota: No creamos índice en expediente_id porque ya existe uno automático por la constraint UNIQUE
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_EXPEDIENTE_BLOQUEO_USUARIO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expediente_bloqueo_usuario ON expediente_bloqueo(usuario_bloqueando)';
    END IF;
END;
/

-- Crear índice idx_expediente_bloqueo_session (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_EXPEDIENTE_BLOQUEO_SESSION';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expediente_bloqueo_session ON expediente_bloqueo(session_id)';
    END IF;
END;
/

-- Crear índice idx_expediente_bloqueo_fecha (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_EXPEDIENTE_BLOQUEO_FECHA';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX idx_expediente_bloqueo_fecha ON expediente_bloqueo(fecha_bloqueo)';
    END IF;
END;
/

-- Agregar comentarios para documentación (solo si la tabla existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'EXPEDIENTE_BLOQUEO';
    
    IF table_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON TABLE expediente_bloqueo IS ''Tabla para controlar el bloqueo de expedientes durante la edición''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.id IS ''Identificador único del bloqueo''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.expediente_id IS ''ID del expediente bloqueado''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.usuario_bloqueando IS ''Email del usuario que tiene el expediente bloqueado''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.nombre_usuario IS ''Nombre completo del usuario que tiene el expediente bloqueado''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.fecha_bloqueo IS ''Fecha y hora cuando se realizó el bloqueo''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente_bloqueo.session_id IS ''ID de sesión para cleanup automático''';
    END IF;
END;
/
