-- Script de migración para poblar la tabla parametro_prioridad con los valores del sistema de cálculo de prioridad
-- Fecha: 2025-06-20
-- Descripción: Inserta todos los parámetros de prioridad que estaban hardcodeados en PriorityCalculator.java
-- MIGRACIÓN IDEMPOTENTE: Verifica existencia antes de insertar

-- Función para insertar parámetro si no existe
CREATE OR REPLACE PROCEDURE insert_param_if_not_exists(
    p_clave VARCHAR2,
    p_valor NUMBER,
    p_descripcion VARCHAR2,
    p_tipo VARCHAR2
) IS
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM parametro_prioridad WHERE clave_variable = p_clave;
    IF v_count = 0 THEN
        INSERT INTO parametro_prioridad (id, clave_variable, valor, descripcion, tipo_variable, fecha_creacion) 
        VALUES (parametro_prioridad_seq.nextval, p_clave, p_valor, p_descripcion, p_tipo, CURRENT_TIMESTAMP);
    END IF;
END;
/

-- Ejecutar inserts usando la función
BEGIN
    -- PROFESION
    insert_param_if_not_exists('PROFESION_OFICIO', 500, 'Valor para profesión: Oficio', 'PROFESION');
    insert_param_if_not_exists('PROFESION_EMPLEADO', 500, 'Valor para profesión: Empleado', 'PROFESION');
    insert_param_if_not_exists('PROFESION_PROFESIONAL', 800, 'Valor para profesión: Profesional', 'PROFESION');
    insert_param_if_not_exists('PROFESION_FUERZA_SEGURIDAD', 800, 'Valor para profesión: Fuerza de Seguridad', 'PROFESION');
    insert_param_if_not_exists('PROFESION_FUERZA_ARMADA', 900, 'Valor para profesión: Fuerza Armada', 'PROFESION');
    insert_param_if_not_exists('PROFESION_SERVICIO_INTELIGENCIA', 1000, 'Valor para profesión: Servicio de Inteligencia', 'PROFESION');
    insert_param_if_not_exists('PROFESION_DESOCUPADO', 100, 'Valor para profesión: Desocupado', 'PROFESION');
    insert_param_if_not_exists('PROFESION_COMERCIANTE', 800, 'Valor para profesión: Comerciante', 'PROFESION');
    insert_param_if_not_exists('PROFESION_OTRO', 0, 'Valor para profesión: Otro', 'PROFESION');
    insert_param_if_not_exists('PROFESION_DEFAULT', 0, 'Valor por defecto para profesión', 'PROFESION');
    
    -- DETENCIONES_PREVIAS
    insert_param_if_not_exists('DETENCIONES_PREVIAS_NINGUNA', 0, 'Sin detenciones previas', 'DETENCIONES_PREVIAS');
    insert_param_if_not_exists('DETENCIONES_PREVIAS_POCAS', 200, 'Pocas detenciones previas (1-2)', 'DETENCIONES_PREVIAS');
    insert_param_if_not_exists('DETENCIONES_PREVIAS_MODERADAS', 750, 'Detenciones moderadas (3-5)', 'DETENCIONES_PREVIAS');
    insert_param_if_not_exists('DETENCIONES_PREVIAS_MUCHAS', 1000, 'Muchas detenciones previas (6+)', 'DETENCIONES_PREVIAS');
    
    -- NUMERO_COMPLICES
    insert_param_if_not_exists('NUMERO_COMPLICES_NINGUNO', 0, 'Sin cómplices', 'NUMERO_COMPLICES');
    insert_param_if_not_exists('NUMERO_COMPLICES_POCOS', 100, 'Pocos cómplices (1-2)', 'NUMERO_COMPLICES');
    insert_param_if_not_exists('NUMERO_COMPLICES_MODERADOS', 250, 'Cómplices moderados (3-5)', 'NUMERO_COMPLICES');
    insert_param_if_not_exists('NUMERO_COMPLICES_MUCHOS', 1000, 'Muchos cómplices (6+)', 'NUMERO_COMPLICES');
    
    -- TIPO_CAPTURA
    insert_param_if_not_exists('TIPO_CAPTURA_NACIONAL', 500, 'Captura nacional', 'TIPO_CAPTURA');
    insert_param_if_not_exists('TIPO_CAPTURA_INTERNACIONAL', 1000, 'Captura internacional', 'TIPO_CAPTURA');
    insert_param_if_not_exists('TIPO_CAPTURA_NACIONAL E INTERNACIONAL', 1500, 'Captura nacional e internacional', 'TIPO_CAPTURA');
    insert_param_if_not_exists('TIPO_CAPTURA_SIN_DATO', 0, 'Sin dato de tipo de captura', 'TIPO_CAPTURA');
    insert_param_if_not_exists('TIPO_CAPTURA_DEFAULT', 0, 'Valor por defecto para tipo de captura', 'TIPO_CAPTURA');
    
    -- TIPO_VICTIMA
    insert_param_if_not_exists('TIPO_VICTIMA_MENOR', 800, 'Víctima menor de edad', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_MUJER', 250, 'Víctima mujer', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_ANCIANO_JUBILADO', 500, 'Víctima anciano/jubilado', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_POLITICO', 800, 'Víctima político', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_JUEZ', 1000, 'Víctima juez', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_FISCAL', 1000, 'Víctima fiscal', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_OTROS', 250, 'Otros tipos de víctima', 'TIPO_VICTIMA');
    insert_param_if_not_exists('TIPO_VICTIMA_DEFAULT', 0, 'Valor por defecto para tipo de víctima', 'TIPO_VICTIMA');
    
    -- FLAGS BOOLEANOS
    insert_param_if_not_exists('CASO_MEDIATICO_SI', 500, 'Caso mediático', 'CASO_MEDIATICO');
    insert_param_if_not_exists('PROFUGO_REINCIDENTE_SI', 800, 'Prófugo reincidente', 'PROFUGO_REINCIDENTE');
    insert_param_if_not_exists('PROFUGO_REITERANTE_SI', 500, 'Prófugo reiterante', 'PROFUGO_REITERANTE');
    insert_param_if_not_exists('INVOLUCRA_BANDA_SI', 500, 'Involucra banda criminal', 'INVOLUCRA_BANDA');
    insert_param_if_not_exists('INVOLUCRA_TERRORISMO_SI', 1000, 'Involucra terrorismo', 'INVOLUCRA_TERRORISMO');
    
    -- NIVEL_ORGANIZACION
    insert_param_if_not_exists('NIVEL_ORGANIZACION_SIMPLE', 250, 'Organización simple', 'NIVEL_ORGANIZACION');
    insert_param_if_not_exists('NIVEL_ORGANIZACION_COMPLEJA', 800, 'Organización compleja', 'NIVEL_ORGANIZACION');
    insert_param_if_not_exists('NIVEL_ORGANIZACION_DEFAULT', 0, 'Valor por defecto para nivel de organización', 'NIVEL_ORGANIZACION');
    
    -- AMBITO_BANDA
    insert_param_if_not_exists('AMBITO_BANDA_NACIONAL', 750, 'Banda de ámbito nacional', 'AMBITO_BANDA');
    insert_param_if_not_exists('AMBITO_BANDA_PROVINCIAL', 500, 'Banda de ámbito provincial', 'AMBITO_BANDA');
    insert_param_if_not_exists('AMBITO_BANDA_BARRIAL', 250, 'Banda de ámbito barrial', 'AMBITO_BANDA');
    insert_param_if_not_exists('AMBITO_BANDA_INTERNACIONAL', 1000, 'Banda de ámbito internacional', 'AMBITO_BANDA');
    insert_param_if_not_exists('AMBITO_BANDA_DEFAULT', 0, 'Valor por defecto para ámbito de banda', 'AMBITO_BANDA');
    
    -- CAPACIDAD_OPERATIVA
    insert_param_if_not_exists('CAPACIDAD_OPERATIVA_ALTA', 1000, 'Capacidad operativa alta', 'CAPACIDAD_OPERATIVA');
    insert_param_if_not_exists('CAPACIDAD_OPERATIVA_BAJA', 500, 'Capacidad operativa baja', 'CAPACIDAD_OPERATIVA');
    insert_param_if_not_exists('CAPACIDAD_OPERATIVA_DEFAULT', 0, 'Valor por defecto para capacidad operativa', 'CAPACIDAD_OPERATIVA');
    
    -- PLANIFICACION
    insert_param_if_not_exists('PLANIFICACION_SI', 500, 'Hubo planificación del delito', 'PLANIFICACION');
    insert_param_if_not_exists('PLANIFICACION_NO', 100, 'No hubo planificación del delito', 'PLANIFICACION');
    
    -- CONEXIONES_OTRAS_ACTIVIDADES
    insert_param_if_not_exists('CONEXIONES_OTRAS_ACTIVIDADES_SI', 500, 'Conexiones con otras actividades delictivas', 'CONEXIONES_OTRAS_ACTIVIDADES');
    
    -- IMPACTO_SOCIAL
    insert_param_if_not_exists('IMPACTO_SOCIAL_ALTO', 500, 'Impacto social alto', 'IMPACTO_SOCIAL');
    insert_param_if_not_exists('IMPACTO_SOCIAL_BAJO', 250, 'Impacto social bajo', 'IMPACTO_SOCIAL');
    insert_param_if_not_exists('IMPACTO_SOCIAL_DEFAULT', 0, 'Valor por defecto para impacto social', 'IMPACTO_SOCIAL');
    
    -- TIPO_DANO
    insert_param_if_not_exists('TIPO_DANO_FISICO', 250, 'Daño físico', 'TIPO_DANO');
    insert_param_if_not_exists('TIPO_DANO_PSICOLOGICO', 150, 'Daño psicológico', 'TIPO_DANO');
    insert_param_if_not_exists('TIPO_DANO_MATERIAL', 50, 'Daño material', 'TIPO_DANO');
    insert_param_if_not_exists('TIPO_DANO_DEFAULT', 0, 'Valor por defecto para tipo de daño', 'TIPO_DANO');
    
    -- USO_ARMAS
    insert_param_if_not_exists('USO_ARMAS_FUEGO_SI', 500, 'Uso de armas de fuego', 'USO_ARMAS_FUEGO');
    insert_param_if_not_exists('USO_ARMAS_BLANCAS_SI', 250, 'Uso de armas blancas', 'USO_ARMAS_BLANCAS');
    
    -- NIVEL_INCIDENCIA_ZONA
    insert_param_if_not_exists('NIVEL_INCIDENCIA_ZONA_ALTA', 500, 'Nivel de incidencia delictual alta en la zona', 'NIVEL_INCIDENCIA_ZONA');
    insert_param_if_not_exists('NIVEL_INCIDENCIA_ZONA_MEDIA', 250, 'Nivel de incidencia delictual media en la zona', 'NIVEL_INCIDENCIA_ZONA');
    insert_param_if_not_exists('NIVEL_INCIDENCIA_ZONA_BAJA', 100, 'Nivel de incidencia delictual baja en la zona', 'NIVEL_INCIDENCIA_ZONA');
    insert_param_if_not_exists('NIVEL_INCIDENCIA_ZONA_DEFAULT', 0, 'Valor por defecto para nivel de incidencia en la zona', 'NIVEL_INCIDENCIA_ZONA');
    
    -- INSTITUCION_SENSIBLE_CERCANA
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_ESCUELA', 600, 'Cercanía a escuela', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_HOSPITAL', 500, 'Cercanía a hospital', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_IGLESIA', 700, 'Cercanía a iglesia', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_SINAGOGA', 1000, 'Cercanía a sinagoga', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_MEZQUITA', 800, 'Cercanía a mezquita', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_OTRO', 100, 'Cercanía a otra institución sensible', 'INSTITUCION_SENSIBLE_CERCANA');
    insert_param_if_not_exists('INSTITUCION_SENSIBLE_CERCANA_DEFAULT', 0, 'Valor por defecto para institución sensible cercana', 'INSTITUCION_SENSIBLE_CERCANA');
    
    -- RECURSOS_LIMITADOS
    insert_param_if_not_exists('RECURSOS_LIMITADOS_SI', 500, 'Recursos limitados del investigado/banda', 'RECURSOS_LIMITADOS');
    insert_param_if_not_exists('RECURSOS_LIMITADOS_NO', 200, 'Recursos no limitados del investigado/banda', 'RECURSOS_LIMITADOS');
    
    -- AREA_FRONTERIZA
    insert_param_if_not_exists('AREA_FRONTERIZA_SI', 500, 'Ocurrió en área fronteriza', 'AREA_FRONTERIZA');
    insert_param_if_not_exists('AREA_FRONTERIZA_NO', 100, 'No ocurrió en área fronteriza', 'AREA_FRONTERIZA');
    
    -- IMPACTO_PERCEPCION
    insert_param_if_not_exists('IMPACTO_PERCEPCION_ALTA', 500, 'Impacto alto en la percepción de seguridad', 'IMPACTO_PERCEPCION');
    insert_param_if_not_exists('IMPACTO_PERCEPCION_MEDIA', 250, 'Impacto medio en la percepción de seguridad', 'IMPACTO_PERCEPCION');
    insert_param_if_not_exists('IMPACTO_PERCEPCION_BAJA', 100, 'Impacto bajo en la percepción de seguridad', 'IMPACTO_PERCEPCION');
    insert_param_if_not_exists('IMPACTO_PERCEPCION_DEFAULT', 0, 'Valor por defecto para impacto en la percepción', 'IMPACTO_PERCEPCION');
    
    -- RECOMPENSA
    insert_param_if_not_exists('RECOMPENSA_SI', 500, 'Tiene recompensa', 'RECOMPENSA');
    
    COMMIT;
END;
/

-- Eliminar la función temporal
DROP PROCEDURE insert_param_if_not_exists;