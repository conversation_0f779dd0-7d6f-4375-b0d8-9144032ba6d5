-- Inserción de datos de ejemplo
-- V3__insert_sample_data.sql

-- Insertar personas de ejemplo
INSERT INTO PERSONA (ID, TIPO_DOCUMENTO, NUMERO_DOCUMENTO, NOMBRE, APELLIDO, ALIAS, FECHA_NACIMIENTO, EDAD, NACIONALIDAD, GENERO, ESTADO_CIVIL)
VALUES (SEQ_PERSONA.NEXTVAL, 'DNI', '28456789', 'JUAN', 'PEREZ', 'EL RÁPIDO', TO_DATE('1980-05-15', 'YYYY-MM-DD'), 42, 'ARGENTINA', 'MASCULINO', 'SOLTERO');

INSERT INTO PERSONA (ID, TIPO_DOCUMENTO, NUMERO_DOCUMENTO, NOMBRE, APELLIDO, ALIAS, FECHA_NACIMIENTO, EDAD, NACIONALIDAD, GENERO, ESTADO_CIVIL)
VALUES (SEQ_PERSONA.NEXTVAL, 'DNI', '32145678', 'CARLOS', 'GOME<PERSON>', 'CARLITOS', TO_<PERSON>AT<PERSON>('1985-10-22', 'YYYY-MM-DD'), 37, 'ARGENTINA', 'MASCULINO', 'CASADO');

INSERT INTO PERSONA (ID, TIPO_DOCUMENTO, NUMERO_DOCUMENTO, NOMBRE, APELLIDO, FECHA_NACIMIENTO, EDAD, NACIONALIDAD, GENERO, ESTADO_CIVIL)
VALUES (SEQ_PERSONA.NEXTVAL, 'DNI', '35789123', 'MARIA', 'RODRIGUEZ', TO_DATE('1990-03-08', 'YYYY-MM-DD'), 32, 'ARGENTINA', 'FEMENINO', 'CASADA');

INSERT INTO PERSONA (ID, TIPO_DOCUMENTO, NUMERO_DOCUMENTO, NOMBRE, APELLIDO, ALIAS, FECHA_NACIMIENTO, EDAD, NACIONALIDAD, GENERO, ESTADO_CIVIL)
VALUES (SEQ_PERSONA.NEXTVAL, 'PASAPORTE', 'AA12345', 'FERNANDO', 'MARTINEZ', 'EL EXTRANJERO', TO_DATE('1975-12-01', 'YYYY-MM-DD'), 47, 'COLOMBIANA', 'MASCULINO', 'DIVORCIADO');

-- Insertar expedientes de ejemplo
INSERT INTO EXPEDIENTE (
    ID, NUMERO, FECHA_INGRESO, ESTADO_SITUACION, FUERZA_ASIGNADA, FECHA_ASIGNACION,
    DESCRIPCION, PRIORIDAD, NUMERO_CAUSA, CARATULA, JUZGADO, SECRETARIA, FISCALIA,
    JURISDICCION, PROVINCIA, TIPO_CAPTURA, PAIS, MOTIVO_CAPTURA,
    PROFUGO_TEZ, PROFUGO_CONTEXTURA_FISICA, PROFUGO_CABELLO, PROFUGO_OJOS, PROFUGO_ESTATURA,
    FECHA_HECHO, LUGAR_HECHO, DESCRIPCION_HECHO, MEDIATICO_FLAG, PELIGROSIDAD_FLAG
)
VALUES (
    SEQ_EXPEDIENTE.NEXTVAL, 'EXP-2023-001', TO_DATE('2023-01-15', 'YYYY-MM-DD'), 'ACTIVO', 'POLICIA FEDERAL', TO_DATE('2023-01-20', 'YYYY-MM-DD'),
    'Expediente por robo a mano armada en banco', 5, 'CAUSA-2023-123', 'PEREZ JUAN S/ ROBO AGRAVADO', 'JUZGADO CRIMINAL Y CORRECCIONAL 5', 'SECRETARIA 9', 'FISCALIA N°7',
    'CABA', 'BUENOS AIRES', 'NACIONAL', 'ARGENTINA', 'ORDEN DE CAPTURA POR ROBO AGRAVADO',
    'TRIGUEÑA', 'ATLETICA', 'NEGRO', 'MARRONES', 1.75,
    TO_DATE('2023-01-10', 'YYYY-MM-DD'), 'AV. CORRIENTES 1234, CABA', 'Robo a mano armada en sucursal bancaria', 1, 1
);

INSERT INTO EXPEDIENTE (
    ID, NUMERO, FECHA_INGRESO, ESTADO_SITUACION, FUERZA_ASIGNADA, FECHA_ASIGNACION,
    DESCRIPCION, PRIORIDAD, NUMERO_CAUSA, CARATULA, JUZGADO, SECRETARIA, FISCALIA,
    JURISDICCION, PROVINCIA, TIPO_CAPTURA,
    FECHA_HECHO, LUGAR_HECHO, DESCRIPCION_HECHO
)
VALUES (
    SEQ_EXPEDIENTE.NEXTVAL, 'EXP-2023-002', TO_DATE('2023-02-05', 'YYYY-MM-DD'), 'CERRADO', 'GENDARMERIA', TO_DATE('2023-02-10', 'YYYY-MM-DD'),
    'Expediente por contrabando de mercaderías', 3, 'CAUSA-2023-456', 'GOMEZ CARLOS S/ CONTRABANDO', 'JUZGADO FEDERAL 3', 'SECRETARIA 2', 'FISCALIA FEDERAL N°4',
    'FEDERAL', 'MISIONES', 'NACIONAL',
    TO_DATE('2023-01-28', 'YYYY-MM-DD'), 'PUERTO DE POSADAS, MISIONES', 'Contrabando de mercaderías en la frontera'
);

INSERT INTO EXPEDIENTE (
    ID, NUMERO, FECHA_INGRESO, ESTADO_SITUACION, FUERZA_ASIGNADA, FECHA_ASIGNACION,
    DESCRIPCION, PRIORIDAD, NUMERO_CAUSA, CARATULA, JUZGADO, SECRETARIA, FISCALIA,
    JURISDICCION, PROVINCIA, TIPO_CAPTURA, PAIS,
    FECHA_HECHO, LUGAR_HECHO, DESCRIPCION_HECHO, MEDIATICO_FLAG, BANDA_FLAG, NOMBRE_BANDA, NIVEL_ORGANIZACION
)
VALUES (
    SEQ_EXPEDIENTE.NEXTVAL, 'EXP-2023-003', TO_DATE('2023-03-10', 'YYYY-MM-DD'), 'ACTIVO', 'POLICIA FEDERAL', TO_DATE('2023-03-15', 'YYYY-MM-DD'),
    'Expediente por tráfico de estupefacientes', 4, 'CAUSA-2023-789', 'MARTINEZ FERNANDO S/ NARCOTRAFICO', 'JUZGADO FEDERAL 1', 'SECRETARIA 3', 'FISCALIA FEDERAL N°2',
    'FEDERAL', 'BUENOS AIRES', 'INTERNACIONAL', 'COLOMBIA',
    TO_DATE('2023-02-15', 'YYYY-MM-DD'), 'AEROPUERTO EZEIZA, BUENOS AIRES', 'Tráfico de drogas en valija diplomática', 1, 1, 'LOS INTERNACIONALES', 'COMPLEJA'
);

-- Relacionar personas con expedientes
INSERT INTO PERSONA_EXPEDIENTE (ID, PERSONA_ID, EXPEDIENTE_ID, TIPO_RELACION, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 1, 1, 'PROFUGO', 'Sospechoso principal');

INSERT INTO PERSONA_EXPEDIENTE (ID, PERSONA_ID, EXPEDIENTE_ID, TIPO_RELACION, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 2, 2, 'PROFUGO', 'Acusado capturado');

INSERT INTO PERSONA_EXPEDIENTE (ID, PERSONA_ID, EXPEDIENTE_ID, TIPO_RELACION, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 3, 1, 'ASOCIADO', 'Posible cómplice');

INSERT INTO PERSONA_EXPEDIENTE (ID, PERSONA_ID, EXPEDIENTE_ID, TIPO_RELACION, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 4, 3, 'PROFUGO', 'Líder de la organización');

-- Agregar domicilios de ejemplo
INSERT INTO DOMICILIO (ID, PERSONA_ID, CALLE, NUMERO, BARRIO, CODIGO_POSTAL, LOCALIDAD, PROVINCIA, ES_PRINCIPAL, DESCRIPCION)
VALUES (SEQ_DOMICILIO.NEXTVAL, 1, 'AV. RIVADAVIA', '1234', 'CABALLITO', '1406', 'CABA', 'BUENOS AIRES', 1, 'Domicilio principal');

INSERT INTO DOMICILIO (ID, PERSONA_ID, CALLE, NUMERO, BARRIO, CODIGO_POSTAL, LOCALIDAD, PROVINCIA, ES_PRINCIPAL, DESCRIPCION)
VALUES (SEQ_DOMICILIO.NEXTVAL, 1, 'PEDRO MORAN', '2345', 'VILLA URQUIZA', '1431', 'CABA', 'BUENOS AIRES', 0, 'Domicilio secundario');

INSERT INTO DOMICILIO (ID, PERSONA_ID, CALLE, NUMERO, BARRIO, CODIGO_POSTAL, LOCALIDAD, PROVINCIA, ES_PRINCIPAL, DESCRIPCION)
VALUES (SEQ_DOMICILIO.NEXTVAL, 2, 'SAN MARTIN', '789', 'CENTRO', '3300', 'POSADAS', 'MISIONES', 1, 'Domicilio único conocido');

INSERT INTO DOMICILIO (ID, PERSONA_ID, CALLE, NUMERO, PISO, DEPARTAMENTO, BARRIO, CODIGO_POSTAL, LOCALIDAD, PROVINCIA, ES_PRINCIPAL, DESCRIPCION)
VALUES (SEQ_DOMICILIO.NEXTVAL, 3, 'AV. CORRIENTES', '3500', '4', 'B', 'ALMAGRO', '1176', 'CABA', 'BUENOS AIRES', 1, 'Departamento');

INSERT INTO DOMICILIO (ID, PERSONA_ID, CALLE, NUMERO, BARRIO, CODIGO_POSTAL, LOCALIDAD, PROVINCIA, PAIS, ES_PRINCIPAL, DESCRIPCION)
VALUES (SEQ_DOMICILIO.NEXTVAL, 4, 'CALLE 82', '45-12', 'CHAPINERO', '10001', 'BOGOTA', 'BOGOTA DC', 'COLOMBIA', 1, 'Domicilio en Colombia');

-- Agregar medios de comunicación
INSERT INTO MEDIOS_DE_COMUNICACION (ID, PERSONA_ID, TIPO, VALOR, OBSERVACIONES)
VALUES (SEQ_MEDIOS_COMUNICACION.NEXTVAL, 1, 'EMAIL', '<EMAIL>', 'Email personal');

INSERT INTO MEDIOS_DE_COMUNICACION (ID, PERSONA_ID, TIPO, VALOR, OBSERVACIONES)
VALUES (SEQ_MEDIOS_COMUNICACION.NEXTVAL, 1, 'TELEFONO', '11-5555-6666', 'Celular personal');

INSERT INTO MEDIOS_DE_COMUNICACION (ID, PERSONA_ID, TIPO, VALOR, OBSERVACIONES)
VALUES (SEQ_MEDIOS_COMUNICACION.NEXTVAL, 2, 'TELEFONO', '3755-123456', 'Teléfono fijo');

INSERT INTO MEDIOS_DE_COMUNICACION (ID, PERSONA_ID, TIPO, VALOR, OBSERVACIONES)
VALUES (SEQ_MEDIOS_COMUNICACION.NEXTVAL, 3, 'EMAIL', '<EMAIL>', 'Email laboral');

INSERT INTO MEDIOS_DE_COMUNICACION (ID, PERSONA_ID, TIPO, VALOR, OBSERVACIONES)
VALUES (SEQ_MEDIOS_COMUNICACION.NEXTVAL, 4, 'TELEFONO', '+57-************', 'Celular Colombia');

-- Relacionar expedientes con delitos
INSERT INTO EXPEDIENTE_DELITO (ID, EXPEDIENTE_ID, DELITO_ID, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 1, 5, 'Robo a mano armada en banco');

INSERT INTO EXPEDIENTE_DELITO (ID, EXPEDIENTE_ID, DELITO_ID, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 2, 10, 'Contrabando de mercancías');

INSERT INTO EXPEDIENTE_DELITO (ID, EXPEDIENTE_ID, DELITO_ID, OBSERVACIONES)
VALUES (SEQ_PERSONA.NEXTVAL, 3, 12, 'Tráfico internacional de estupefacientes');

-- Commit para aplicar los cambios
COMMIT; 