-- Eliminar la restricción si existe
BEGIN
  EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE DROP CONSTRAINT CHK_FUERZA_ASIGNADA';
EXCEPTION
  WHEN OTHERS THEN
    IF SQLCODE != -2443 THEN -- ORA-02443: restricción no existe
      RAISE;
    END IF;
END;
/

-- Actualizar valores existentes a 'OTRO' si no coinciden con los nuevos valores permitidos
UPDATE EXPEDIENTE
SET FUERZA_ASIGNADA = 'OTRO'
WHERE FUERZA_ASIGNADA IS NULL OR
      FUERZA_ASIGNADA NOT IN (
        'OTRO',
        'POLICÍA',
        'GENDARMERÍ<PERSON>',
        'PREFECTURA',
        '<PERSON><PERSON><PERSON>Í<PERSON> FEDERAL',
        'POLICÍA DE SEGURIDAD AEROPORTUARIA',
        'SERVICIO PENITENCIARIO'
      );

-- Crear la restricción nuevamente
ALTER TABLE EXPEDIENTE ADD CONSTRAINT CHK_FUERZA_ASIGNADA CHECK (
  FUERZA_ASIGNADA IN (
    'OTRO',
    'P<PERSON><PERSON><PERSON><PERSON>',
    'GE<PERSON>ARMERÍ<PERSON>',
    'PREFECTURA',
    'POLICÍA FEDERAL',
    'POLICÍA DE SEGURIDAD AEROPORTUARIA',
    'SERVICIO PENITENCIARIO'
  )
);

-- Commit para aplicar los cambios
COMMIT; 