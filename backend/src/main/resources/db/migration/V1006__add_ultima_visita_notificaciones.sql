-- Migración V1006: Agregar campo para tracking de última visita a notificaciones (IDEMPOTENTE)
-- Fecha: Diciembre 2025
-- Propósito: Sistema de notificaciones para administradores y superusuarios

-- Agregar campo para tracking de última visita a notificaciones (solo si no existe)
DECLARE
    column_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'USUARIO' AND column_name = 'ULTIMA_VISITA_NOTIFICACIONES';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE USUARIO ADD (ULTIMA_VISITA_NOTIFICACIONES TIMESTAMP DEFAULT CURRENT_TIMESTAMP)';
    END IF;
END;
/

-- Crear índice para optimizar consultas de notificaciones (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_ACTIVIDAD_SISTEMA_NOTIF';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_ACTIVIDAD_SISTEMA_NOTIF ON ACTIVIDAD_SISTEMA(MODULO, FECHA_HORA, USUARIO)';
    END IF;
END;
/