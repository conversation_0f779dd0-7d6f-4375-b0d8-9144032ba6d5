-- Eliminar la restricción si existe
BEGIN
  EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE DROP CONSTRAINT CHK_PROFUGO_OCUPACION';
EXCEPTION
  WHEN OTHERS THEN
    IF SQLCODE != -2443 THEN -- ORA-02443: restricción no existe
      RAISE;
    END IF;
END;
/

-- Actualizar valores existentes a 'OTRO' si no coinciden con los nuevos valores permitidos
UPDATE EXPEDIENTE
SET PROFUGO_PROFESION_OCUPACION = 'OTRO'
WHERE PROFUGO_PROFESION_OCUPACION IS NULL OR
      PROFUGO_PROFESION_OCUPACION NOT IN (
        'OTRO',
        'OFICIO',
        'EMPLEADO',
        'PROFESIONAL',
        'FUERZA SEGURIDAD',
        'FUERZA ARMADA',
        'SERVICIO DE INTELIGENCIA',
        'DESOCUPADO',
        'COMERCIANTE'
      );

-- Crear la restricción nuevamente
ALTER TABLE EXPEDIENTE ADD CONSTRAINT CHK_PROFUGO_OCUPACION CHECK (
  PROFUGO_PROFESION_OCUPACION IN (
    'OTRO',
    'OFICIO',
    'EMPLEADO',
    'PROFESIONAL',
    'FUERZA SEGURIDAD',
    'FUERZA ARMADA',
    'SERVICIO DE INTELIGENCIA',
    'DESOCUPADO',
    'COMERCIANTE'
  )
);

-- COMMIT; -- (opcional, Flyway normalmente lo maneja)
