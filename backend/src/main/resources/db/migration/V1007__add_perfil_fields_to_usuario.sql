-- Migración V1007: Agregar campos de perfil a tabla USUARIO y crear sistema de avatares (IDEMPOTENTE)
-- Fecha: Enero 2025
-- Descripción: Implementación del sistema de perfiles de usuario con teléfono móvil y avatares predefinidos

-- Agregar campo TELEFONO_MOVIL a tabla USUARIO (solo si no existe)
DECLARE
    column_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'USUARIO' AND column_name = 'TELEFONO_MOVIL';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE USUARIO ADD TELEFONO_MOVIL VARCHAR2(20 CHAR)';
    END IF;
END;
/

-- Agregar campo AVATAR_URL a tabla USUARIO (solo si no existe)
DECLARE
    column_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'USUARIO' AND column_name = 'AVATAR_URL';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE USUARIO ADD AVATAR_URL VARCHAR2(255 CHAR)';
    END IF;
END;
/

-- Crear tabla de avatares predefinidos (solo si no existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'AVATAR_PREDEFINIDO';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE AVATAR_PREDEFINIDO (
            ID NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            NOMBRE VARCHAR2(100 CHAR) NOT NULL,
            URL VARCHAR2(255 CHAR) NOT NULL,
            CATEGORIA VARCHAR2(50 CHAR) NOT NULL,
            ACTIVO NUMBER(1) DEFAULT 1 CHECK (ACTIVO IN (0,1)),
            ORDEN_DISPLAY NUMBER DEFAULT 0,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_CATEGORIA (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_CATEGORIA';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_CATEGORIA ON AVATAR_PREDEFINIDO(CATEGORIA)';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_ACTIVO (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_ACTIVO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ACTIVO ON AVATAR_PREDEFINIDO(ACTIVO)';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_ORDEN (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_ORDEN';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ORDEN ON AVATAR_PREDEFINIDO(ORDEN_DISPLAY)';
    END IF;
END;
/

-- Insertar avatares predefinidos iniciales (solo si la tabla está vacía)
DECLARE
    record_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO record_count FROM AVATAR_PREDEFINIDO;
    
    IF record_count = 0 THEN
        -- Categoría Profesional (8 avatares)
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Ejecutivo Masculino', '/avatares/profesional/ejecutivo-m.svg', 'profesional', 1, 1);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Ejecutiva Femenina', '/avatares/profesional/ejecutiva-f.svg', 'profesional', 1, 2);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Oficial Masculino', '/avatares/profesional/oficial-m.svg', 'profesional', 1, 3);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Oficial Femenina', '/avatares/profesional/oficial-f.svg', 'profesional', 1, 4);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Investigador', '/avatares/profesional/investigador.svg', 'profesional', 1, 5);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Investigadora', '/avatares/profesional/investigadora.svg', 'profesional', 1, 6);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Analista', '/avatares/profesional/analista.svg', 'profesional', 1, 7);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Supervisor', '/avatares/profesional/supervisor.svg', 'profesional', 1, 8);
        
        -- Categoría Casual (8 avatares)
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Amigable', '/avatares/casual/amigable.svg', 'casual', 1, 9);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Sonriente', '/avatares/casual/sonriente.svg', 'casual', 1, 10);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Relajada', '/avatares/casual/relajada.svg', 'casual', 1, 11);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Moderna', '/avatares/casual/moderna.svg', 'casual', 1, 12);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Joven', '/avatares/casual/joven.svg', 'casual', 1, 13);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Creativa', '/avatares/casual/creativa.svg', 'casual', 1, 14);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Deportiva', '/avatares/casual/deportiva.svg', 'casual', 1, 15);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Artística', '/avatares/casual/artistica.svg', 'casual', 1, 16);
        
        -- Categoría Iconos (8 avatares)
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Escudo Policial', '/avatares/iconos/escudo-policial.svg', 'iconos', 1, 17);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Insignia GNA', '/avatares/iconos/insignia-gna.svg', 'iconos', 1, 18);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Estrella Seguridad', '/avatares/iconos/estrella-seguridad.svg', 'iconos', 1, 19);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Badge Investigador', '/avatares/iconos/badge-investigador.svg', 'iconos', 1, 20);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Símbolo Justicia', '/avatares/iconos/simbolo-justicia.svg', 'iconos', 1, 21);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Emblema Orden', '/avatares/iconos/emblema-orden.svg', 'iconos', 1, 22);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Logo Institucional', '/avatares/iconos/logo-institucional.svg', 'iconos', 1, 23);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Símbolo Autoridad', '/avatares/iconos/simbolo-autoridad.svg', 'iconos', 1, 24);
        
        -- Categoría Diversos (6 avatares)
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Neutro 1', '/avatares/diversos/neutro-1.svg', 'diversos', 1, 25);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Neutro 2', '/avatares/diversos/neutro-2.svg', 'diversos', 1, 26);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Inclusivo 1', '/avatares/diversos/inclusivo-1.svg', 'diversos', 1, 27);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Inclusivo 2', '/avatares/diversos/inclusivo-2.svg', 'diversos', 1, 28);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Universal 1', '/avatares/diversos/universal-1.svg', 'diversos', 1, 29);
        INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Universal 2', '/avatares/diversos/universal-2.svg', 'diversos', 1, 30);
        
        COMMIT;
    END IF;
END;
/

-- Agregar comentarios para documentación (solo si las columnas/tablas existen)
DECLARE
    column_exists NUMBER;
    table_exists NUMBER;
BEGIN
    -- Comentarios para columnas de USUARIO
    SELECT COUNT(*) INTO column_exists FROM user_tab_columns WHERE table_name = 'USUARIO' AND column_name = 'TELEFONO_MOVIL';
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN USUARIO.TELEFONO_MOVIL IS ''Teléfono móvil del usuario con formato argentino (+54 9 xx xxxx-xxxx)''';
    END IF;
    
    SELECT COUNT(*) INTO column_exists FROM user_tab_columns WHERE table_name = 'USUARIO' AND column_name = 'AVATAR_URL';
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN USUARIO.AVATAR_URL IS ''URL del avatar personalizado del usuario''';
    END IF;
    
    -- Comentarios para tabla AVATAR_PREDEFINIDO
    SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'AVATAR_PREDEFINIDO';
    IF table_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON TABLE AVATAR_PREDEFINIDO IS ''Galería de avatares predefinidos organizados por categorías''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN AVATAR_PREDEFINIDO.CATEGORIA IS ''Categoría del avatar: profesional, casual, iconos, diversos''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN AVATAR_PREDEFINIDO.ORDEN_DISPLAY IS ''Orden de visualización dentro de la categoría''';
    END IF;
END;
/