-- Migración V1008: Veri<PERSON>r y corregir datos de avatares predefinidos (IDEMPOTENTE)
-- Fecha: Enero 2025
-- Descripción: As<PERSON><PERSON>r que la tabla AVATAR_PREDEFINIDO existe y tiene todos los datos necesarios

-- Verificar si la tabla existe, si no, crearla (solo si no existe)
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'AVATAR_PREDEFINIDO';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE AVATAR_PREDEFINIDO (
            ID NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            NOMBRE VARCHAR2(100 CHAR) NOT NULL,
            URL VARCHAR2(255 CHAR) NOT NULL,
            CATEGORIA VARCHAR2(50 CHAR) NOT NULL,
            ACTIVO NUMBER(1) DEFAULT 1 CHECK (ACTIVO IN (0,1)),
            ORDEN_DISPLAY NUMBER DEFAULT 0,
            FEC<PERSON>_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_CATEGORIA (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_CATEGORIA';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_CATEGORIA ON AVATAR_PREDEFINIDO(CATEGORIA)';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_ACTIVO (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_ACTIVO';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ACTIVO ON AVATAR_PREDEFINIDO(ACTIVO)';
    END IF;
END;
/

-- Crear índice IDX_AVATAR_ORDEN (solo si no existe)
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'IDX_AVATAR_ORDEN';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ORDEN ON AVATAR_PREDEFINIDO(ORDEN_DISPLAY)';
    END IF;
END;
/

-- Verificar si necesitamos actualizar los datos (solo si hay registros sin ACTIVO definido o tabla vacía)
DECLARE
    needs_update NUMBER;
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'AVATAR_PREDEFINIDO';
    
    IF table_exists > 0 THEN
        -- Verificar si la tabla está vacía o tiene registros problemáticos
        SELECT COUNT(*) INTO needs_update FROM AVATAR_PREDEFINIDO;
        
        -- Si la tabla está vacía o tiene menos de 30 registros esperados, repoblar
        IF needs_update < 30 THEN
            -- Limpiar datos existentes para evitar duplicados
            DELETE FROM AVATAR_PREDEFINIDO;
            
            -- Insertar todos los avatares predefinidos con ACTIVO explícito
            -- Categoría Profesional (8 avatares)
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Ejecutivo Masculino', '/avatares/profesional/ejecutivo-m.svg', 'profesional', 1, 1);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Ejecutiva Femenina', '/avatares/profesional/ejecutiva-f.svg', 'profesional', 1, 2);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Oficial Masculino', '/avatares/profesional/oficial-m.svg', 'profesional', 1, 3);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Oficial Femenina', '/avatares/profesional/oficial-f.svg', 'profesional', 1, 4);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Investigador', '/avatares/profesional/investigador.svg', 'profesional', 1, 5);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Investigadora', '/avatares/profesional/investigadora.svg', 'profesional', 1, 6);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Analista', '/avatares/profesional/analista.svg', 'profesional', 1, 7);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Supervisor', '/avatares/profesional/supervisor.svg', 'profesional', 1, 8);
            
            -- Categoría Casual (8 avatares)
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Amigable', '/avatares/casual/amigable.svg', 'casual', 1, 9);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Sonriente', '/avatares/casual/sonriente.svg', 'casual', 1, 10);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Relajada', '/avatares/casual/relajada.svg', 'casual', 1, 11);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Moderna', '/avatares/casual/moderna.svg', 'casual', 1, 12);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Joven', '/avatares/casual/joven.svg', 'casual', 1, 13);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Creativa', '/avatares/casual/creativa.svg', 'casual', 1, 14);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Deportiva', '/avatares/casual/deportiva.svg', 'casual', 1, 15);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Persona Artística', '/avatares/casual/artistica.svg', 'casual', 1, 16);
            
            -- Categoría Iconos (8 avatares)
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Escudo Policial', '/avatares/iconos/escudo-policial.svg', 'iconos', 1, 17);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Insignia GNA', '/avatares/iconos/insignia-gna.svg', 'iconos', 1, 18);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Estrella Seguridad', '/avatares/iconos/estrella-seguridad.svg', 'iconos', 1, 19);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Badge Investigador', '/avatares/iconos/badge-investigador.svg', 'iconos', 1, 20);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Símbolo Justicia', '/avatares/iconos/simbolo-justicia.svg', 'iconos', 1, 21);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Emblema Orden', '/avatares/iconos/emblema-orden.svg', 'iconos', 1, 22);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Logo Institucional', '/avatares/iconos/logo-institucional.svg', 'iconos', 1, 23);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Símbolo Autoridad', '/avatares/iconos/simbolo-autoridad.svg', 'iconos', 1, 24);
            
            -- Categoría Diversos (6 avatares)
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Neutro 1', '/avatares/diversos/neutro-1.svg', 'diversos', 1, 25);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Neutro 2', '/avatares/diversos/neutro-2.svg', 'diversos', 1, 26);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Inclusivo 1', '/avatares/diversos/inclusivo-1.svg', 'diversos', 1, 27);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Inclusivo 2', '/avatares/diversos/inclusivo-2.svg', 'diversos', 1, 28);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Universal 1', '/avatares/diversos/universal-1.svg', 'diversos', 1, 29);
            INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ACTIVO, ORDEN_DISPLAY) VALUES ('Avatar Universal 2', '/avatares/diversos/universal-2.svg', 'diversos', 1, 30);
            
            COMMIT;
        END IF;
    END IF;
END;
/