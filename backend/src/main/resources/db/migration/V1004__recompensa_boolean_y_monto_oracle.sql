-- Migración: Cambia recompensa a boolean y agrega montoRecompensa (versión Oracle) - IDEMPOTENTE

DECLARE
    column_exists NUMBER;
    column_type VARCHAR2(128);
BEGIN
    -- 1. Verificar si monto_recompensa ya existe, si no, crearla
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'EXPEDIENTE' AND column_name = 'MONTO_RECOMPENSA';
    
    IF column_exists = 0 THEN
        -- Verificar si la columna recompensa existe y tiene datos
        SELECT COUNT(*) INTO column_exists 
        FROM user_tab_columns 
        WHERE table_name = 'EXPEDIENTE' AND column_name = 'RECOMPENSA';
        
        IF column_exists > 0 THEN
            -- Agregar monto_recompensa y copiar datos de recompensa
            EXECUTE IMMEDIATE 'ALTER TABLE expediente ADD monto_recompensa VARCHAR2(200)';
            EXECUTE IMMEDIATE 'UPDATE expediente SET monto_recompensa = recompensa WHERE recompensa IS NOT NULL';
        ELSE
            -- Solo agregar la columna sin copiar datos
            EXECUTE IMMEDIATE 'ALTER TABLE expediente ADD monto_recompensa VARCHAR2(200)';
        END IF;
    END IF;
    
    -- 2. Verificar el tipo de la columna recompensa
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'EXPEDIENTE' AND column_name = 'RECOMPENSA';
    
    IF column_exists > 0 THEN
        SELECT data_type INTO column_type 
        FROM user_tab_columns 
        WHERE table_name = 'EXPEDIENTE' AND column_name = 'RECOMPENSA';
        
        -- Si la columna recompensa no es NUMBER(1), necesitamos convertirla
        IF column_type != 'NUMBER' THEN
            -- Verificar si recompensa_bool ya existe
            SELECT COUNT(*) INTO column_exists 
            FROM user_tab_columns 
            WHERE table_name = 'EXPEDIENTE' AND column_name = 'RECOMPENSA_BOOL';
            
            IF column_exists = 0 THEN
                -- Crear columna temporal para el valor booleano
                EXECUTE IMMEDIATE 'ALTER TABLE expediente ADD recompensa_bool NUMBER(1)';
                
                -- Convertir los valores de texto a booleanos (1=true, 0=false)
                EXECUTE IMMEDIATE '
                UPDATE expediente SET recompensa_bool = 
                  CASE 
                    WHEN UPPER(recompensa) = ''SI'' OR UPPER(recompensa) = ''TRUE'' OR recompensa = ''1'' THEN 1
                    ELSE 0
                  END';
                
                -- Eliminar la columna original recompensa
                EXECUTE IMMEDIATE 'ALTER TABLE expediente DROP COLUMN recompensa';
                
                -- Renombrar la columna temporal a recompensa
                EXECUTE IMMEDIATE 'ALTER TABLE expediente RENAME COLUMN recompensa_bool TO recompensa';
            END IF;
        END IF;
    ELSE
        -- Si la columna recompensa no existe, crearla como NUMBER(1)
        EXECUTE IMMEDIATE 'ALTER TABLE expediente ADD recompensa NUMBER(1) DEFAULT 0';
    END IF;
END;
/

-- Agregar comentarios a las columnas para documentar (solo si las columnas existen)
DECLARE
    column_exists NUMBER;
BEGIN
    -- Comentario para recompensa
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'EXPEDIENTE' AND column_name = 'RECOMPENSA';
    
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente.recompensa IS ''Indica si el expediente tiene recompensa (1=SI, 0=NO)''';
    END IF;
    
    -- Comentario para monto_recompensa
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'EXPEDIENTE' AND column_name = 'MONTO_RECOMPENSA';
    
    IF column_exists > 0 THEN
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN expediente.monto_recompensa IS ''Monto de la recompensa (anteriormente campo recompensa)''';
    END IF;
END;
/