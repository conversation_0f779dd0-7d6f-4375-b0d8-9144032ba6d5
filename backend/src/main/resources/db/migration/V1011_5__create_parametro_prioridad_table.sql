-- Creación de tabla parametro_prioridad para el sistema de cálculo de prioridades
-- Fecha: 2025-06-20
-- Descripción: Crea la tabla parametro_prioridad y su secuencia correspondiente
-- MIGRACIÓN IDEMPOTENTE: Verifica existencia antes de crear cualquier objeto

-- Crear secuencia para la tabla parametro_prioridad (idempotente)
DECLARE
    v_count NUMBER;
    v_sql_error EXCEPTION;
    PRAGMA EXCEPTION_INIT(v_sql_error, -955); -- ORA-00955: name is already used by an existing object
BEGIN
    SELECT COUNT(*) INTO v_count FROM user_sequences WHERE sequence_name = 'PARAMETRO_PRIORIDAD_SEQ';
    IF v_count = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE 'CREATE SEQUENCE parametro_prioridad_seq START WITH 1 INCREMENT BY 1 NOCACHE';
            DBMS_OUTPUT.PUT_LINE('Secuencia parametro_prioridad_seq creada exitosamente');
        EXCEPTION
            WHEN v_sql_error THEN
                DBMS_OUTPUT.PUT_LINE('Secuencia parametro_prioridad_seq ya existe');
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error al crear secuencia: ' || SQLERRM);
                RAISE;
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('Secuencia parametro_prioridad_seq ya existe');
    END IF;
END;
/

-- Crear tabla parametro_prioridad (idempotente)
DECLARE
    v_count NUMBER;
    v_sql_error EXCEPTION;
    PRAGMA EXCEPTION_INIT(v_sql_error, -955); -- ORA-00955: name is already used by an existing object
BEGIN
    SELECT COUNT(*) INTO v_count FROM user_tables WHERE table_name = 'PARAMETRO_PRIORIDAD';
    IF v_count = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE '
            CREATE TABLE parametro_prioridad (
                id NUMBER PRIMARY KEY,
                clave_variable VARCHAR2(100) NOT NULL UNIQUE,
                valor NUMBER NOT NULL,
                descripcion VARCHAR2(500),
                tipo_variable VARCHAR2(20) NOT NULL,
                fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                fecha_modificacion TIMESTAMP,
                modificado_por VARCHAR2(100),
                CONSTRAINT chk_parametro_tipo_variable CHECK (tipo_variable IN (
                    ''PROFESION'', ''DETENCIONES_PREVIAS'', ''NUMERO_COMPLICES'', ''TIPO_CAPTURA'',
                    ''TIPO_VICTIMA'', ''CASO_MEDIATICO'', ''PROFUGO_REINCIDENTE'', ''PROFUGO_REITERANTE'',
                    ''INVOLUCRA_BANDA'', ''INVOLUCRA_TERRORISMO'', ''NIVEL_ORGANIZACION'', ''AMBITO_BANDA'',
                    ''CAPACIDAD_OPERATIVA'', ''PLANIFICACION'', ''CONEXIONES_OTRAS_ACTIVIDADES'',
                    ''IMPACTO_SOCIAL'', ''TIPO_DANO'', ''USO_ARMAS_FUEGO'', ''USO_ARMAS_BLANCAS'',
                    ''NIVEL_INCIDENCIA_ZONA'', ''INSTITUCION_SENSIBLE_CERCANA'', ''RECURSOS_LIMITADOS'',
                    ''AREA_FRONTERIZA'', ''IMPACTO_PERCEPCION'', ''RECOMPENSA''
                ))
            )';
            DBMS_OUTPUT.PUT_LINE('Tabla parametro_prioridad creada exitosamente');
        EXCEPTION
            WHEN v_sql_error THEN
                DBMS_OUTPUT.PUT_LINE('Tabla parametro_prioridad ya existe');
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error al crear tabla: ' || SQLERRM);
                RAISE;
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('Tabla parametro_prioridad ya existe');
    END IF;
END;
/

-- Crear índices para optimizar consultas (idempotente)
DECLARE
    v_count NUMBER;
    v_sql_error EXCEPTION;
    PRAGMA EXCEPTION_INIT(v_sql_error, -955); -- ORA-00955: name is already used by an existing object
BEGIN
    -- Índice por clave_variable
    SELECT COUNT(*) INTO v_count FROM user_indexes WHERE index_name = 'IDX_PARAMETRO_PRIORIDAD_CLAVE';
    IF v_count = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE 'CREATE INDEX idx_parametro_prioridad_clave ON parametro_prioridad(clave_variable)';
            DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_clave creado exitosamente');
        EXCEPTION
            WHEN v_sql_error THEN
                DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_clave ya existe');
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error al crear índice clave_variable: ' || SQLERRM);
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_clave ya existe');
    END IF;
    
    -- Índice por tipo_variable
    SELECT COUNT(*) INTO v_count FROM user_indexes WHERE index_name = 'IDX_PARAMETRO_PRIORIDAD_TIPO';
    IF v_count = 0 THEN
        BEGIN
            EXECUTE IMMEDIATE 'CREATE INDEX idx_parametro_prioridad_tipo ON parametro_prioridad(tipo_variable)';
            DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_tipo creado exitosamente');
        EXCEPTION
            WHEN v_sql_error THEN
                DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_tipo ya existe');
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('Error al crear índice tipo_variable: ' || SQLERRM);
        END;
    ELSE
        DBMS_OUTPUT.PUT_LINE('Índice idx_parametro_prioridad_tipo ya existe');
    END IF;
END;
/

-- Agregar comentarios en la tabla y columnas (idempotente)
BEGIN
    EXECUTE IMMEDIATE 'COMMENT ON TABLE parametro_prioridad IS ''Tabla que almacena los parámetros configurables para el cálculo de prioridades de expedientes''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.id IS ''Identificador único del parámetro''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.clave_variable IS ''Clave única que identifica el parámetro en el sistema''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.valor IS ''Valor numérico del parámetro utilizado en los cálculos''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.descripcion IS ''Descripción del parámetro y su uso''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.tipo_variable IS ''Tipo o categoría del parámetro (ej: PROFESION, DETENCIONES_PREVIAS, etc.)''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.fecha_creacion IS ''Fecha y hora de creación del registro''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.fecha_modificacion IS ''Fecha y hora de la última modificación''';
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN parametro_prioridad.modificado_por IS ''Usuario que realizó la última modificación''';
    DBMS_OUTPUT.PUT_LINE('Comentarios agregados exitosamente');
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error al agregar comentarios: ' || SQLERRM);
        -- No fallar la migración por comentarios
END;
/

-- Verificación final
DECLARE
    v_table_count NUMBER;
    v_seq_count NUMBER;
    v_index_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_table_count FROM user_tables WHERE table_name = 'PARAMETRO_PRIORIDAD';
    SELECT COUNT(*) INTO v_seq_count FROM user_sequences WHERE sequence_name = 'PARAMETRO_PRIORIDAD_SEQ';
    SELECT COUNT(*) INTO v_index_count FROM user_indexes WHERE index_name LIKE 'IDX_PARAMETRO_PRIORIDAD_%';
    
    DBMS_OUTPUT.PUT_LINE('=== VERIFICACIÓN FINAL ===');
    DBMS_OUTPUT.PUT_LINE('Tabla parametro_prioridad: ' || CASE WHEN v_table_count > 0 THEN 'EXISTE' ELSE 'NO EXISTE' END);
    DBMS_OUTPUT.PUT_LINE('Secuencia parametro_prioridad_seq: ' || CASE WHEN v_seq_count > 0 THEN 'EXISTE' ELSE 'NO EXISTE' END);
    DBMS_OUTPUT.PUT_LINE('Índices creados: ' || v_index_count);
    DBMS_OUTPUT.PUT_LINE('=== MIGRACIÓN COMPLETADA ===');
END;
/