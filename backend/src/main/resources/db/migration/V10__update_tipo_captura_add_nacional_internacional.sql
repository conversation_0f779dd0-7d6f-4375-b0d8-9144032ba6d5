-- Actualizar la restricción CHECK para TIPO_CAPTURA para incluir 'NACIONAL E INTERNACIONAL'
BEGIN
  -- Eliminar la restricción existente CHK_TIPO_CAPTURA
  FOR c IN (SELECT constraint_name 
            FROM user_constraints 
            WHERE table_name = 'EXPEDIENTE' 
            AND constraint_type = 'C'
            AND constraint_name = 'CHK_TIPO_CAPTURA') LOOP
    BEGIN
      EXECUTE IMMEDIATE 'ALTER TABLE EXPEDIENTE DROP CONSTRAINT ' || c.constraint_name;
      DBMS_OUTPUT.PUT_LINE('Restricción eliminada: ' || c.constraint_name);
    EXCEPTION
      WHEN OTHERS THEN
        -- Ignorar errores si la restricción no existe
        NULL;
    END;
  END LOOP;
END;
/

-- Aumentar la longitud del campo TIPO_CAPTURA para acomodar valores más largos
ALTER TABLE EXPEDIENTE MODIFY TIPO_CAPTURA VARCHAR2(50);

-- Crear nueva restricción que incluye 'NACIONAL E INTERNACIONAL'
ALTER TABLE EXPEDIENTE ADD CONSTRAINT CHK_TIPO_CAPTURA CHECK (TIPO_CAPTURA IN ('NACIONAL','INTERNACIONAL','NACIONAL E INTERNACIONAL','OTRO'));

-- Commit para aplicar los cambios
COMMIT;