-- Usuarios para pruebas automatizadas (contraseñas: 'password' y 'Minseg2025-')
INSERT INTO usuario (id, email, contrasena, nombre, apellido, enabled, rol, dependencia, requiere_cambio_contrasena, requiere_2fa, secret_2fa) VALUES (1, '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'Ana', 'García', true, 'ADMINISTRADOR', 'SISTEMA', false, false, null);
INSERT INTO usuario (id, email, contrasena, nombre, apellido, enabled, rol, dependencia, requiere_cambio_contrasena, requiere_2fa, secret_2fa) VALUES (2, '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'Super', 'Usuario', true, 'SUPERUSUARIO', 'SISTEMA', false, false, null);
INSERT INTO usuario (id, email, contrasena, nombre, apellido, enabled, rol, dependencia, requiere_cambio_contrasena, requiere_2fa, secret_2fa) VALUES (3, '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'Usuario', 'Carga', true, 'USUARIOCARGA', 'AREA CARGA', false, false, null);
INSERT INTO usuario (id, email, contrasena, nombre, apellido, enabled, rol, dependencia, requiere_cambio_contrasena, requiere_2fa, secret_2fa) VALUES (4, '<EMAIL>', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', 'Usuario', 'Consulta', true, 'USUARIOCONSULTA', 'AREA CONSULTA', false, false, null);
INSERT INTO usuario (id, email, contrasena, nombre, apellido, enabled, rol, dependencia, requiere_cambio_contrasena, requiere_2fa, secret_2fa) VALUES (5, '<EMAIL>', '$2a$10$zIZXvwvgL3jHcOyqE0xiWOT0mMf6BpTejP9Y7ZOfRqgiTI9QFraoi', 'Ivan', 'Zarate', true, 'SUPERUSUARIO', 'MINISTERIO DE SEGURIDAD', false, false, null);

-- Delitos
INSERT INTO delito (id, nombre, descripcion) VALUES (1, 'Robo', 'Sustracción de bienes ajenos');
INSERT INTO delito (id, nombre, descripcion) VALUES (2, 'Hurto', 'Apropiación indebida sin violencia');
INSERT INTO delito (id, nombre, descripcion) VALUES (3, 'Estafa', 'Engaño para obtener beneficio');

-- Personas
INSERT INTO persona (id, nombre, apellido, tipo_documento, numero_documento) VALUES (1, 'Carlos', 'Sosa', 'DNI', '30123456');
INSERT INTO persona (id, nombre, apellido, tipo_documento, numero_documento) VALUES (2, 'Lucía', 'Martínez', 'DNI', '28987654');
INSERT INTO persona (id, nombre, apellido, tipo_documento, numero_documento) VALUES (3, 'Miguel', 'Fernández', 'DNI', '31234567');

-- Expedientes
INSERT INTO expediente (id, numero_expediente, fecha_ingreso, descripcion, estado) VALUES (1, 'EXP-2024-001', '2024-01-15', 'Robo en vivienda', 'ABIERTO');
INSERT INTO expediente (id, numero_expediente, fecha_ingreso, descripcion, estado) VALUES (2, 'EXP-2024-002', '2024-02-10', 'Hurto de vehículo', 'INVESTIGACION');
INSERT INTO expediente (id, numero_expediente, fecha_ingreso, descripcion, estado) VALUES (3, 'EXP-2024-003', '2024-03-05', 'Estafa bancaria', 'CERRADO');

-- Relación expediente-delito
INSERT INTO expediente_delito (id, expediente_id, delito_id) VALUES (1, 1, 1);
INSERT INTO expediente_delito (id, expediente_id, delito_id) VALUES (2, 2, 2);
INSERT INTO expediente_delito (id, expediente_id, delito_id) VALUES (3, 3, 3);

-- Relación persona-expediente
INSERT INTO persona_expediente (id, persona_id, expediente_id, rol) VALUES (1, 1, 1, 'Imputado');
INSERT INTO persona_expediente (id, persona_id, expediente_id, rol) VALUES (2, 2, 2, 'Víctima');
INSERT INTO persona_expediente (id, persona_id, expediente_id, rol) VALUES (3, 3, 3, 'Testigo'); 