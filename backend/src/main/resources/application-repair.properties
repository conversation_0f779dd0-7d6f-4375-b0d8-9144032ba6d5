# Configuración para reparar migraciones Flyway fallidas
spring.flyway.repair-on-migrate=true
spring.flyway.out-of-order=true
spring.flyway.ignore-migration-patterns=V1003__*
spring.flyway.validate-on-migrate=false
spring.flyway.clean-on-validation-error=true
spring.flyway.baseline-on-migrate=true

# Incluir todas las propiedades de Oracle
spring.profiles.include=oracle

# Configuración de conexión a Oracle (duplicada para asegurar que se usa)
spring.datasource.url=****************************************************************
spring.datasource.username=CUFRE-TEST
spring.datasource.password=C5fr3T3st!
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
