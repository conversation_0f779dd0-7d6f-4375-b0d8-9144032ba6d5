package com.cufre.expedientes.config;

import com.cufre.expedientes.interceptor.AuditInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;
import org.springframework.web.util.pattern.PathPatternParser;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Configuración para controlar el comportamiento de Spring MVC
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${app.uploads.dir:uploads}")
    private String uploadsDir;
    
    @Autowired
    private AuditInterceptor auditInterceptor;

    /**
     * Configura los manejadores de recursos estáticos
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        Path uploadsPath = Paths.get(uploadsDir).toAbsolutePath().normalize();
        String uploadsDirUri = uploadsPath.toUri().toString();
        
        // Servir archivos estáticos desde la ruta de uploads configurada
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations(uploadsDirUri);
                
        // Configurar webjars
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/")
                .resourceChain(true);
                
        // Configurar recursos estáticos específicos
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .resourceChain(true);
                
        // Configurar recursos para el frontend (index.html, etc.)
        // IMPORTANTE: Excluir explícitamente /api/** para evitar conflictos con controladores
        registry.addResourceHandler("/", "/index.html", "/*.js", "/*.css", "/*.ico", "/*.png", "/*.jpg", "/*.svg")
                .addResourceLocations("classpath:/static/")
                .resourceChain(true);
        
        // Configurar fallback para SPA (Single Page Application) - solo para rutas que NO sean /api/**
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .resourceChain(true)
                .addResolver(new PathResourceResolver() {
                    @Override
                    protected Resource getResource(String resourcePath, Resource location) throws IOException {
                        // Si la ruta empieza con /api/, no la manejes como recurso estático
                        if (resourcePath.startsWith("api/")) {
                            return null;
                        }
                        Resource requestedResource = location.createRelative(resourcePath);
                        return requestedResource.exists() && requestedResource.isReadable()
                            ? requestedResource
                            : location.createRelative("index.html");
                    }
                });
                
        // IMPORTANTE: NO agregamos /api/** como recurso estático para que sea manejado por los controladores
        // Las rutas /api/** deben ser manejadas exclusivamente por los @RestController
        
        // Excluir explícitamente las rutas API del manejo de recursos estáticos
        registry.setOrder(2);
    }
    
    /**
     * Configura el sistema de coincidencia de rutas
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // Este cambio asegura que las rutas se interpreten de manera estricta,
        // evitando que el manejador de recursos estáticos intercepte las rutas de la API.
        configurer.setPatternParser(new PathPatternParser());
        
        // Asegurar que las rutas de API tengan prioridad sobre recursos estáticos
        configurer.setUseTrailingSlashMatch(false);
        configurer.setUseSuffixPatternMatch(false);
    }
    
    /**
     * Configura la negociación de contenido para preferir JSON
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
            .favorParameter(false)
            .ignoreAcceptHeader(false)
            .defaultContentType(MediaType.APPLICATION_JSON)
            .mediaType("json", MediaType.APPLICATION_JSON);
    }
    
    /**
     * Registra los interceptores de la aplicación
     * NOTA: La configuración del AuditInterceptor se maneja en AuditConfig.java
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // El AuditInterceptor se configura en AuditConfig.java para evitar duplicación
        // Aquí se pueden agregar otros interceptores si es necesario
    }
}