package com.cufre.expedientes.config;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;

/**
 * Estrategia de nomenclatura que convierte todos los identificadores de tabla 
 * a mayúsculas para Oracle, ya que Oracle es sensible a mayúsculas/minúsculas
 * y almacena los nombres de tabla en mayúsculas por defecto.
 */
public class UpperCaseNamingStrategy extends PhysicalNamingStrategyStandardImpl {

    private static final long serialVersionUID = 1L;

    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return Identifier.toIdentifier(name.getText().toUpperCase());
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment context) {
        if (name == null) {
            return null;
        }
        return Identifier.toIdentifier(name.getText().toUpperCase());
    }
}
