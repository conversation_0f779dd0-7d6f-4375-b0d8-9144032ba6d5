package com.cufre.expedientes.config;

import com.cufre.expedientes.interceptor.AuditInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AuditConfig implements WebMvcConfigurer {

    @Autowired
    private AuditInterceptor auditInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(auditInterceptor)
                .addPathPatterns("/**") // Auditar todas las rutas (sin /api/ porque ya está en context path)
                .excludePathPatterns(
                    // Excluir endpoints que podrían causar recursión o spam
                    "/auth/refresh-token",       // Evitar spam de refresh tokens
                    "/health/**",                // Excluir health checks
                    "/actuator/**",              // Excluir actuator endpoints
                    "/actividad-sistema"         // Evitar recursión al consultar actividades
                );
    }
}