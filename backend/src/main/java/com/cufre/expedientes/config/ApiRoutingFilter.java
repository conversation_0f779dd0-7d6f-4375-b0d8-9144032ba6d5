package com.cufre.expedientes.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Filtro para asegurar que las rutas /api/** sean manejadas por los controladores
 * y no por el sistema de recursos estáticos de Spring Boot.
 */
@Component
@Order(1)
@Slf4j
public class ApiRoutingFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestURI = httpRequest.getRequestURI();
        
        // Log para debugging
        log.debug("ApiRoutingFilter - Processing request: {} {}", httpRequest.getMethod(), requestURI);
        
        // Si es una ruta de API, asegurar que se procese correctamente
        if (requestURI.startsWith("/api/")) {
            log.debug("ApiRoutingFilter - API route detected: {}", requestURI);
            
            // Agregar headers para asegurar que se trate como API
            httpResponse.setHeader("Content-Type", "application/json");
            
            // Continuar con la cadena de filtros
            chain.doFilter(request, response);
            return;
        }
        
        // Para rutas no-API, continuar normalmente
        chain.doFilter(request, response);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("ApiRoutingFilter initialized - Ensuring /api/** routes are handled by controllers");
    }

    @Override
    public void destroy() {
        log.info("ApiRoutingFilter destroyed");
    }
}