package com.cufre.expedientes.mapper;

import com.cufre.expedientes.dto.AnuncioDTO;
import com.cufre.expedientes.dto.CrearAnuncioDTO;
import com.cufre.expedientes.model.Anuncio;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface AnuncioMapper {
    
    @Mapping(target = "creadoPorId", source = "creadoPor.id")
    @Mapping(target = "creadoPorNombre", source = "creadoPor.nombreCompleto")
    @Mapping(target = "totalVistas", ignore = true) // Se calculará en el servicio si es necesario
    AnuncioDTO toDto(Anuncio anuncio);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "fechaCreacion", ignore = true) // Se establece automáticamente
    @Mapping(target = "creadoPor", ignore = true) // Se establece en el servicio
    @Mapping(target = "anunciosVistos", ignore = true)
    Anuncio toEntity(CrearAnuncioDTO crearAnuncioDTO);
    
    @Mapping(target = "creadoPor", ignore = true)
    @Mapping(target = "anunciosVistos", ignore = true)
    @Mapping(target = "fechaCreacion", ignore = true)
    Anuncio toEntity(AnuncioDTO anuncioDTO);
    
    @Mapping(target = "creadoPor", ignore = true)
    @Mapping(target = "anunciosVistos", ignore = true)
    @Mapping(target = "fechaCreacion", ignore = true)
    @Mapping(target = "id", ignore = true)
    Anuncio updateEntity(AnuncioDTO anuncioDTO, @org.mapstruct.MappingTarget Anuncio anuncio);
}