package com.cufre.expedientes.interceptor;

import com.cufre.expedientes.service.ActividadSistemaService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Component
public class AuditInterceptor implements HandlerInterceptor {

    @Autowired
    private ActividadSistemaService actividadSistemaService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final String AUDIT_ENABLED_ATTRIBUTE = "auditEnabled";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Marcar el tiempo de inicio
        request.setAttribute(START_TIME_ATTRIBUTE, System.currentTimeMillis());
        
        // Determinar si esta request debe ser auditada
        boolean shouldAudit = shouldAuditRequest(request);
        request.setAttribute(AUDIT_ENABLED_ATTRIBUTE, shouldAudit);
        
        // Log para debugging
        System.out.println("AuditInterceptor.preHandle - URI: " + request.getRequestURI() +
                          ", Method: " + request.getMethod() +
                          ", ShouldAudit: " + shouldAudit);
        
        // Log específico para APIs problemáticas
        if (request.getRequestURI().contains("/api/parametros-prioridad") ||
            request.getRequestURI().contains("/api/anuncios")) {
            System.out.println("DEBUG - API problemática detectada: " + request.getRequestURI() +
                             " - Usuario: " + obtenerUsuarioActual() +
                             " - Timestamp: " + LocalDateTime.now());
        }
        
        if (shouldAudit) {
            // Registrar información de la request
            registrarInformacionRequest(request);
        }
        
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // Este método se ejecuta después del controlador pero antes de la vista
        // Aquí podríamos capturar información adicional si fuera necesario
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        Boolean auditEnabled = (Boolean) request.getAttribute(AUDIT_ENABLED_ATTRIBUTE);
        
        // Log para debugging
        System.out.println("AuditInterceptor.afterCompletion - URI: " + request.getRequestURI() +
                          ", AuditEnabled: " + auditEnabled +
                          ", Status: " + response.getStatus());
        
        // Log específico para APIs problemáticas con errores
        if ((request.getRequestURI().contains("/api/parametros-prioridad") ||
             request.getRequestURI().contains("/api/anuncios")) &&
            response.getStatus() >= 500) {
            System.err.println("ERROR 500 - API problemática: " + request.getRequestURI() +
                             " - Status: " + response.getStatus() +
                             " - Exception: " + (ex != null ? ex.getMessage() : "None") +
                             " - Timestamp: " + LocalDateTime.now());
        }
        
        if (auditEnabled != null && auditEnabled) {
            // Calcular duración
            Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
            long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;
            
            // Registrar actividad completa
            registrarActividadCompleta(request, response, duration, ex);
            System.out.println("AuditInterceptor - Actividad registrada para: " + request.getRequestURI());
        }
    }

    private boolean shouldAuditRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // No auditar requests estáticas y endpoints de salud
        if (uri.contains("/static/") ||
            uri.contains("/css/") ||
            uri.contains("/js/") ||
            uri.contains("/images/") ||
            uri.contains("/favicon.ico") ||
            uri.contains("/actuator/")) {
            return false;
        }
        
        // PERMITIR auditoría de /api/actividad-sistema para consultas
        // Las consultas GET SÍ deben auditarse para tener registros completos
        
        // Auditar todas las operaciones de API importantes
        return uri.startsWith("/api/") ||
               method.equals("POST") ||
               method.equals("PUT") ||
               method.equals("DELETE");
    }

    private void registrarInformacionRequest(HttpServletRequest request) {
        try {
            String usuario = obtenerUsuarioActual();
            String endpoint = request.getRequestURI();
            String metodo = request.getMethod();
            String ipCliente = obtenerIpCliente(request);
            String userAgent = request.getHeader("User-Agent");
            String sessionId = request.getSession(false) != null ? request.getSession().getId() : null;
            
            // Crear información de la request para almacenar como detalle
            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("endpoint", endpoint);
            requestInfo.put("metodo", metodo);
            requestInfo.put("parametros", request.getParameterMap());
            requestInfo.put("headers", obtenerHeadersImportantes(request));
            requestInfo.put("timestamp", LocalDateTime.now().toString());
            
            String requestJson = objectMapper.writeValueAsString(requestInfo);
            
            // Almacenar en el request para usar después
            request.setAttribute("auditRequestInfo", requestJson);
            request.setAttribute("auditUsuario", usuario);
            request.setAttribute("auditIpCliente", ipCliente);
            request.setAttribute("auditUserAgent", userAgent);
            request.setAttribute("auditSessionId", sessionId);
            request.setAttribute("auditEndpoint", endpoint);
            request.setAttribute("auditMetodo", metodo);
            
        } catch (Exception e) {
            // Log error pero no fallar la request
            System.err.println("Error al registrar información de request: " + e.getMessage());
        }
    }

    private void registrarActividadCompleta(HttpServletRequest request, HttpServletResponse response, long duration, Exception ex) {
        try {
            String usuario = (String) request.getAttribute("auditUsuario");
            String ipCliente = (String) request.getAttribute("auditIpCliente");
            String userAgent = (String) request.getAttribute("auditUserAgent");
            String sessionId = (String) request.getAttribute("auditSessionId");
            String endpoint = (String) request.getAttribute("auditEndpoint");
            String metodo = (String) request.getAttribute("auditMetodo");
            String requestInfo = (String) request.getAttribute("auditRequestInfo");
            
            // Determinar tipo de acción basado en el endpoint y método
            String tipoAccion = determinarTipoAccion(endpoint, metodo);
            
            // Determinar estado de respuesta
            String estadoRespuesta = determinarEstadoRespuesta(response, ex);
            
            // Crear detalles de la actividad
            String detalles = crearDetallesActividad(endpoint, metodo, response.getStatus());
            
            // Registrar actividad principal
            var actividad = actividadSistemaService.registrarActividadCompleta(
                usuario != null ? usuario : "ANONIMO",
                tipoAccion,
                detalles,
                ipCliente,
                userAgent,
                sessionId,
                endpoint,
                metodo,
                duration,
                estadoRespuesta
            );
            
            // Registrar detalles adicionales
            if (requestInfo != null) {
                actividadSistemaService.registrarDetalle(actividad.getId(), "REQUEST", requestInfo);
            }
            
            // Registrar información de respuesta
            Map<String, Object> responseInfo = new HashMap<>();
            responseInfo.put("status", response.getStatus());
            responseInfo.put("duracionMs", duration);
            responseInfo.put("timestamp", LocalDateTime.now().toString());
            
            if (ex != null) {
                responseInfo.put("error", ex.getMessage());
                responseInfo.put("excepcion", ex.getClass().getSimpleName());
            }
            
            String responseJson = objectMapper.writeValueAsString(responseInfo);
            actividadSistemaService.registrarDetalle(actividad.getId(), "RESPONSE", responseJson);
            
        } catch (Exception e) {
            // Log error pero no fallar la request
            System.err.println("Error al registrar actividad completa: " + e.getMessage());
        }
    }

    private String obtenerUsuarioActual() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() &&
                !"anonymousUser".equals(authentication.getPrincipal())) {
                return authentication.getName();
            }
        } catch (Exception e) {
            // Log error para debugging
            System.err.println("Error al obtener usuario actual: " + e.getMessage());
        }
        return "SISTEMA"; // Valor por defecto en lugar de null
    }

    private String obtenerIpCliente(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    private Map<String, String> obtenerHeadersImportantes(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        
        // Headers importantes para auditoría
        String[] headersImportantes = {
            "User-Agent", "Accept", "Accept-Language", "Content-Type", 
            "Authorization", "X-Forwarded-For", "X-Real-IP"
        };
        
        for (String headerName : headersImportantes) {
            String headerValue = request.getHeader(headerName);
            if (headerValue != null) {
                // Ocultar información sensible
                if ("Authorization".equals(headerName)) {
                    headers.put(headerName, "Bearer ***");
                } else {
                    headers.put(headerName, headerValue);
                }
            }
        }
        
        return headers;
    }

    private String determinarTipoAccion(String endpoint, String metodo) {
        if (endpoint.contains("/auth/login")) return "LOGIN_EXITOSO";
        if (endpoint.contains("/auth/logout")) return "LOGOUT_MANUAL";
        if (endpoint.contains("/expedientes") && "POST".equals(metodo)) return "EXPEDIENTE_CREAR";
        if (endpoint.contains("/expedientes") && "PUT".equals(metodo)) return "EXPEDIENTE_EDITAR";
        if (endpoint.contains("/expedientes") && "DELETE".equals(metodo)) return "EXPEDIENTE_ELIMINAR";
        if (endpoint.contains("/expedientes") && "GET".equals(metodo)) return "EXPEDIENTE_CONSULTAR";
        if (endpoint.contains("/usuarios") && "POST".equals(metodo)) return "USUARIO_CREAR";
        if (endpoint.contains("/usuarios") && "PUT".equals(metodo)) return "USUARIO_EDITAR";
        if (endpoint.contains("/usuarios") && "DELETE".equals(metodo)) return "USUARIO_ELIMINAR";
        if (endpoint.contains("/estadisticas")) return "ESTADISTICA_CONSULTAR";
        if (endpoint.contains("/exportar")) return "REPORTE_EXPORTAR";
        
        return "API_" + metodo + "_" + endpoint.replaceAll("/api/", "").replaceAll("/", "_").toUpperCase();
    }

    private String determinarEstadoRespuesta(HttpServletResponse response, Exception ex) {
        if (ex != null) return "ERROR";
        
        int status = response.getStatus();
        if (status >= 200 && status < 300) return "SUCCESS";
        if (status >= 400 && status < 500) return "ERROR";
        if (status >= 500) return "ERROR";
        
        return "WARNING";
    }

    private String crearDetallesActividad(String endpoint, String metodo, int status) {
        return String.format("%s %s - Status: %d", metodo, endpoint, status);
    }
}