package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.ParametroPrioridad;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ParametroPrioridadRepository extends JpaRepository<ParametroPrioridad, Long> {
    
    Optional<ParametroPrioridad> findByClaveVariable(String claveVariable);
    
    List<ParametroPrioridad> findByTipoVariable(ParametroPrioridad.TipoVariable tipoVariable);
    
    @Query("SELECT p FROM ParametroPrioridad p ORDER BY p.tipoVariable, p.claveVariable")
    List<ParametroPrioridad> findAllOrderedByTypeAndKey();
    
    boolean existsByClaveVariable(String claveVariable);
}