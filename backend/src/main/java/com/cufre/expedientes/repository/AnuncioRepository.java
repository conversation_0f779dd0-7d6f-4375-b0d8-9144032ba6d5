package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.Anuncio;
import com.cufre.expedientes.model.Usuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repositorio para la entidad Anuncio
 */
@Repository
public interface AnuncioRepository extends JpaRepository<Anuncio, Long> {
    
    /**
     * Busca el anuncio activo
     * @return Anuncio activo o vacío si no hay ninguno
     */
    Optional<Anuncio> findByActivoTrue();
    
    /**
     * Busca todos los anuncios ordenados por fecha de creación descendente
     * @return Lista de anuncios ordenados
     */
    List<Anuncio> findAllByOrderByFechaCreacionDesc();
    
    /**
     * Busca anuncios creados por un usuario específico
     * @param creadoPor Usuario que creó los anuncios
     * @return Lista de anuncios creados por el usuario
     */
    List<Anuncio> findByCreadoPorOrderByFechaCreacionDesc(Usuario creadoPor);
    
    /**
     * Desactiva todos los anuncios
     * Usado antes de activar un nuevo anuncio para asegurar que solo uno esté activo
     */
    @Modifying
    @Query("UPDATE Anuncio a SET a.activo = false WHERE a.activo = true")
    void desactivarTodos();
    
    /**
     * Cuenta cuántos anuncios activos hay
     * @return Número de anuncios activos (debería ser 0 o 1)
     */
    @Query("SELECT COUNT(a) FROM Anuncio a WHERE a.activo = true")
    long countByActivoTrue();
    
    /**
     * Verifica si existe un anuncio activo
     * @return true si existe un anuncio activo, false en caso contrario
     */
    boolean existsByActivoTrue();
}