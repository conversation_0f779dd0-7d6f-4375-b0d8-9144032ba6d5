package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.ActividadSistema;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ActividadSistemaRepository extends JpaRepository<ActividadSistema, Long> {
    
    /**
     * Buscar actividades por usuario
     */
    Page<ActividadSistema> findByUsuarioContainingIgnoreCaseOrderByFechaHoraDesc(String usuario, Pageable pageable);
    
    /**
     * Buscar actividades por módulo
     */
    Page<ActividadSistema> findByModuloOrderByFechaHoraDesc(String modulo, Pageable pageable);
    
    /**
     * Buscar actividades por categoría de acción
     */
    Page<ActividadSistema> findByCategoriaAccionOrderByFechaHoraDesc(String categoriaAccion, Pageable pageable);
    
    /**
     * Buscar actividades por rango de fechas
     */
    Page<ActividadSistema> findByFechaHoraBetweenOrderByFechaHoraDesc(LocalDateTime fechaInicio, LocalDateTime fechaFin, Pageable pageable);
    
    /**
     * Buscar actividades por IP cliente
     */
    Page<ActividadSistema> findByIpClienteOrderByFechaHoraDesc(String ipCliente, Pageable pageable);
    
    /**
     * Buscar actividades por estado de respuesta
     */
    Page<ActividadSistema> findByEstadoRespuestaOrderByFechaHoraDesc(String estadoRespuesta, Pageable pageable);
    
    /**
     * Consulta compleja con múltiples filtros
     */
    @Query("SELECT a FROM ActividadSistema a WHERE " +
           "(:usuario IS NULL OR LOWER(a.usuario) LIKE LOWER(CONCAT('%', :usuario, '%'))) AND " +
           "(:modulo IS NULL OR a.modulo = :modulo) AND " +
           "(:categoriaAccion IS NULL OR a.categoriaAccion = :categoriaAccion) AND " +
           "(:fechaInicio IS NULL OR a.fechaHora >= :fechaInicio) AND " +
           "(:fechaFin IS NULL OR a.fechaHora <= :fechaFin) AND " +
           "(:ipCliente IS NULL OR a.ipCliente = :ipCliente) AND " +
           "(:estadoRespuesta IS NULL OR a.estadoRespuesta = :estadoRespuesta) " +
           "ORDER BY a.fechaHora DESC")
    Page<ActividadSistema> findWithFilters(
        @Param("usuario") String usuario,
        @Param("modulo") String modulo,
        @Param("categoriaAccion") String categoriaAccion,
        @Param("fechaInicio") LocalDateTime fechaInicio,
        @Param("fechaFin") LocalDateTime fechaFin,
        @Param("ipCliente") String ipCliente,
        @Param("estadoRespuesta") String estadoRespuesta,
        Pageable pageable
    );
    
    /**
     * Obtener estadísticas de actividad por módulo
     */
    @Query("SELECT a.modulo, COUNT(a) FROM ActividadSistema a GROUP BY a.modulo ORDER BY COUNT(a) DESC")
    List<Object[]> getEstadisticasPorModulo();
    
    /**
     * Obtener estadísticas de actividad por usuario
     */
    @Query("SELECT a.usuario, COUNT(a) FROM ActividadSistema a GROUP BY a.usuario ORDER BY COUNT(a) DESC")
    List<Object[]> getEstadisticasPorUsuario();
    
    /**
     * Obtener actividades recientes (últimas 24 horas)
     */
    @Query("SELECT a FROM ActividadSistema a WHERE a.fechaHora >= :fechaLimite ORDER BY a.fechaHora DESC")
    List<ActividadSistema> getActividadesRecientes(@Param("fechaLimite") LocalDateTime fechaLimite);
    
    /**
     * Contar actividades por estado de respuesta
     */
    @Query("SELECT a.estadoRespuesta, COUNT(a) FROM ActividadSistema a GROUP BY a.estadoRespuesta")
    List<Object[]> getEstadisticasPorEstado();
    
    /**
     * Buscar actividades sospechosas (múltiples intentos fallidos)
     */
    @Query("SELECT a FROM ActividadSistema a WHERE a.estadoRespuesta = 'ERROR' AND a.ipCliente = :ip " +
           "AND a.fechaHora >= :fechaLimite ORDER BY a.fechaHora DESC")
    List<ActividadSistema> getActividadesSospechosas(@Param("ip") String ip, @Param("fechaLimite") LocalDateTime fechaLimite);
    
    /**
     * Busca actividades por módulo y fecha posterior a la especificada para notificaciones
     */
    Page<ActividadSistema> findByModuloAndFechaHoraAfterOrderByFechaHoraDesc(
        String modulo,
        LocalDateTime fechaHora,
        Pageable pageable
    );
    
    /**
     * Cuenta actividades por módulo y fecha posterior para notificaciones
     */
    long countByModuloAndFechaHoraAfter(String modulo, LocalDateTime fechaHora);
    
    /**
     * Eliminar actividades anteriores a una fecha específica
     */
    long deleteByFechaHoraBefore(LocalDateTime fechaLimite);
}