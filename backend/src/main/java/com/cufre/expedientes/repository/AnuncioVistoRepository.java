package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.AnuncioVisto;
import com.cufre.expedientes.model.AnuncioVistoId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repositorio para la entidad AnuncioVisto
 */
@Repository
public interface AnuncioVistoRepository extends JpaRepository<AnuncioVisto, AnuncioVistoId> {
    
    /**
     * Verifica si un usuario ya vio un anuncio específico
     * @param usuarioId ID del usuario
     * @param anuncioId ID del anuncio
     * @return true si el usuario ya vio el anuncio, false en caso contrario
     */
    boolean existsByIdUsuarioIdAndIdAnuncioId(Long usuarioId, Long anuncioId);
    
    /**
     * Busca todos los anuncios vistos por un usuario específico
     * @param usuarioId ID del usuario
     * @return Lista de anuncios vistos por el usuario
     */
    List<AnuncioVisto> findByIdUsuarioId(Long usuarioId);
    
    /**
     * Busca todos los usuarios que vieron un anuncio específico
     * @param anuncioId ID del anuncio
     * @return Lista de registros de usuarios que vieron el anuncio
     */
    List<AnuncioVisto> findByIdAnuncioId(Long anuncioId);
    
    /**
     * Cuenta cuántos usuarios vieron un anuncio específico
     * @param anuncioId ID del anuncio
     * @return Número de usuarios que vieron el anuncio
     */
    @Query("SELECT COUNT(av) FROM AnuncioVisto av WHERE av.id.anuncioId = :anuncioId")
    long countByAnuncioId(@Param("anuncioId") Long anuncioId);
    
    /**
     * Cuenta cuántos anuncios ha visto un usuario específico
     * @param usuarioId ID del usuario
     * @return Número de anuncios vistos por el usuario
     */
    @Query("SELECT COUNT(av) FROM AnuncioVisto av WHERE av.id.usuarioId = :usuarioId")
    long countByUsuarioId(@Param("usuarioId") Long usuarioId);
}