package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.ExpedienteDelito;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExpedienteDelitoRepository extends JpaRepository<ExpedienteDelito, Long> {
    
    List<ExpedienteDelito> findByExpedienteId(Long expedienteId);
    
    List<ExpedienteDelito> findByDelitoId(Long delitoId);
    
    /**
     * Verifica si ya existe una relación entre un expediente y un delito
     * @param expedienteId ID del expediente
     * @param delitoId ID del delito
     * @return true si la relación ya existe, false en caso contrario
     */
    @Query("SELECT CASE WHEN COUNT(ed) > 0 THEN true ELSE false END FROM ExpedienteDelito ed WHERE ed.expediente.id = :expedienteId AND ed.delito.id = :delitoId")
    boolean existsByExpedienteIdAndDelitoId(@Param("expedienteId") Long expedienteId, @Param("delitoId") Long delitoId);
    
    // Estadísticas
    @Query("SELECT ed.delito.nombre, COUNT(ed) FROM ExpedienteDelito ed GROUP BY ed.delito.nombre ORDER BY COUNT(ed) DESC")
    List<Object[]> countByDelito();
    
    @Query("SELECT COUNT(DISTINCT ed.expediente.id) FROM ExpedienteDelito ed WHERE ed.delito.id = :delitoId")
    Long countExpedientesByDelito(@Param("delitoId") Long delitoId);
    
    /**
     * Ranking de delitos con filtros opcionales
     */
    @Query("SELECT ed.delito.nombre, COUNT(ed) FROM ExpedienteDelito ed " +
           "WHERE (:estado IS NULL OR ed.expediente.estadoSituacion = :estado) " +
           "AND (:fuerza IS NULL OR ed.expediente.fuerzaAsignada = :fuerza) " +
           "AND (:tipoCaptura IS NULL OR ed.expediente.tipoCaptura = :tipoCaptura) " +
           "GROUP BY ed.delito.nombre ORDER BY COUNT(ed) DESC")
    List<Object[]> countByDelitoWithFilters(@Param("estado") String estado,
                                           @Param("fuerza") String fuerza,
                                           @Param("tipoCaptura") String tipoCaptura);
}