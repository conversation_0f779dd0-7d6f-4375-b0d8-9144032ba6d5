package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.Expediente;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExpedienteRepository extends JpaRepository<Expediente, Long>, JpaSpecificationExecutor<Expediente> {
    
    Optional<Expediente> findByNumero(String numero);
    
    List<Expediente> findByFechaIngresoBetween(LocalDate fechaInicio, LocalDate fechaFin);
    
    List<Expediente> findByEstadoSituacion(String estadoSituacion);
    
    List<Expediente> findByFuerzaAsignada(String fuerzaAsignada);
    
    List<Expediente> findByPrioridadGreaterThanEqual(Integer prioridadMinima);
    
    @Query("SELECT e FROM Expediente e JOIN e.personaExpedientes pe WHERE pe.persona.id = :personaId AND pe.tipoRelacion = :tipoRelacion")
    List<Expediente> findByPersonaAndTipoRelacion(@Param("personaId") Long personaId, @Param("tipoRelacion") String tipoRelacion);
    
    @Query("SELECT e FROM Expediente e JOIN e.expedienteDelitos ed WHERE ed.delito.id = :delitoId")
    List<Expediente> findByDelito(@Param("delitoId") Long delitoId);
    
    @Query("SELECT e FROM Expediente e WHERE e.provincia = :provincia")
    List<Expediente> findByProvincia(@Param("provincia") String provincia);
    
    // Consultas para estadísticas
    @Query("SELECT COUNT(e) FROM Expediente e WHERE e.fechaIngreso BETWEEN :fechaInicio AND :fechaFin")
    Long countByPeriodo(@Param("fechaInicio") LocalDate fechaInicio, @Param("fechaFin") LocalDate fechaFin);
    
    @Query("SELECT UPPER(TRIM(e.provincia)), COUNT(e) FROM Expediente e GROUP BY UPPER(TRIM(e.provincia)) ORDER BY COUNT(e) DESC")
    List<Object[]> countByProvincia();

    @Query("SELECT e.estadoSituacion, COUNT(e) FROM Expediente e GROUP BY e.estadoSituacion ORDER BY COUNT(e) DESC")
    List<Object[]> countByEstadoSituacion();

    @Query("SELECT e.tipoCaptura, COUNT(e) FROM Expediente e GROUP BY e.tipoCaptura ORDER BY COUNT(e) DESC")
    List<Object[]> countByTipoCaptura();

    @Query("SELECT e.fuerzaAsignada, COUNT(e) FROM Expediente e GROUP BY e.fuerzaAsignada ORDER BY COUNT(e) DESC")
    List<Object[]> countByFuerzaAsignada();

    @Query("SELECT e.estadoSituacion, COUNT(e) FROM Expediente e WHERE e.fuerzaAsignada = :fuerza GROUP BY e.estadoSituacion ORDER BY COUNT(e) DESC")
    List<Object[]> countByEstadoSituacionAndFuerza(@Param("fuerza") String fuerza);

    @Query("SELECT e.fuerzaAsignada, COUNT(e) FROM Expediente e WHERE e.estadoSituacion = :estado GROUP BY e.fuerzaAsignada ORDER BY COUNT(e) DESC")
    List<Object[]> countByFuerzaAsignadaAndEstado(@Param("estado") String estado);
    
    @Query("SELECT FUNCTION('YEAR', e.fechaIngreso) as anio, COUNT(e) FROM Expediente e GROUP BY anio ORDER BY anio")
    List<Object[]> countByAnio();
    
    @Query("SELECT FUNCTION('MONTH', e.fechaIngreso) as mes, COUNT(e) FROM Expediente e WHERE FUNCTION('YEAR', e.fechaIngreso) = :anio GROUP BY mes ORDER BY mes")
    List<Object[]> countByMesEnAnio(@Param("anio") Integer anio);
    
    @Query("SELECT COUNT(e) FROM Expediente e WHERE e.fechaDetencion IS NOT NULL")
    Long countCapturados();
    
    @Query("SELECT COUNT(e) FROM Expediente e WHERE e.fechaDetencion IS NULL")
    Long countNoCapturados();

    /**
     * Encuentra expedientes que contienen un número de expediente (búsqueda parcial e insensible a mayúsculas/minúsculas)
     */
    List<Expediente> findByNumeroContainingIgnoreCase(String numeroExpediente);

    /**
     * Encuentra expedientes asociados a una persona
     */
    @Query("SELECT e FROM Expediente e JOIN e.personaExpedientes pe WHERE pe.persona.id = :personaId")
    List<Expediente> findByPersonaId(@Param("personaId") Long personaId);

    /**
     * Encuentra expedientes asociados a un delito
     */
    @Query("SELECT e FROM Expediente e JOIN e.expedienteDelitos ed WHERE ed.delito.id = :delitoId")
    List<Expediente> findByDelitoId(@Param("delitoId") Long delitoId);

    /**
     * Cuenta expedientes entre dos fechas
     */
    long countByFechaIngresoBetween(LocalDate fechaInicio, LocalDate fechaFin);
    
    /**
     * Encuentra un expediente por ID sin cargar sus relaciones
     * Esto evita el error MultipleBagFetchException
     */
    @Query("SELECT e FROM Expediente e WHERE e.id = :id")
    Optional<Expediente> findByIdWithRelations(@Param("id") Long id);
    
    /**
     * Carga las fotografías de un expediente
     */
    @Query("SELECT f FROM Expediente e JOIN e.fotografias f WHERE e.id = :expedienteId")
    List<com.cufre.expedientes.model.Fotografia> findFotografiasByExpedienteId(@Param("expedienteId") Long expedienteId);
    
    /**
     * Carga los documentos de un expediente
     */
    @Query("SELECT d FROM Expediente e JOIN e.documentos d WHERE e.id = :expedienteId")
    List<com.cufre.expedientes.model.Documento> findDocumentosByExpedienteId(@Param("expedienteId") Long expedienteId);
    
    /**
     * Carga las personas asociadas a un expediente
     */
    @Query("SELECT pe FROM Expediente e JOIN e.personaExpedientes pe WHERE e.id = :expedienteId")
    List<com.cufre.expedientes.model.PersonaExpediente> findPersonaExpedientesByExpedienteId(@Param("expedienteId") Long expedienteId);
    
    /**
     * Carga los delitos asociados a un expediente
     */
    @Query("SELECT ed FROM Expediente e JOIN e.expedienteDelitos ed WHERE e.id = :expedienteId")
    List<com.cufre.expedientes.model.ExpedienteDelito> findExpedienteDelitosByExpedienteId(@Param("expedienteId") Long expedienteId);
    
    /**
     * Cuenta los expedientes por un estado de situación específico
     */
    @Query("SELECT COUNT(e) FROM Expediente e WHERE e.estadoSituacion = :estado")
    Long countByEstadoSituacionEquals(@Param("estado") String estado);

    List<Expediente> findAllByOrderByPrioridadAsc(Pageable pageable);

    List<Expediente> findAllByEstadoSituacionOrderByPrioridadDesc(String estadoSituacion, Pageable pageable);

    @Query("SELECT TO_CHAR(e.fechaIngreso, 'YYYY-MM') as mes, COUNT(e) FROM Expediente e GROUP BY TO_CHAR(e.fechaIngreso, 'YYYY-MM') ORDER BY mes ASC")
    List<Object[]> countByMes();

    @Query("SELECT UPPER(COALESCE(e.fuerzaAsignada, 'SIN DATO')) as fuerza, COUNT(e) FROM Expediente e WHERE UPPER(e.estadoSituacion) = 'DETENIDO' GROUP BY UPPER(COALESCE(e.fuerzaAsignada, 'SIN DATO')) ORDER BY fuerza")
    List<Object[]> countDetenidosPorFuerza();
    
    /**
     * Encuentra el expediente más nuevo con estado CAPTURA VIGENTE
     */
    @Query("SELECT e FROM Expediente e WHERE e.estadoSituacion = 'CAPTURA VIGENTE' ORDER BY e.fechaIngreso DESC")
    List<Expediente> findNewestCapturaVigente(Pageable pageable);
    
    /**
     * Encuentra el expediente más antiguo con estado CAPTURA VIGENTE
     */
    @Query("SELECT e FROM Expediente e WHERE e.estadoSituacion = 'CAPTURA VIGENTE' ORDER BY e.fechaIngreso ASC")
    List<Expediente> findOldestCapturaVigente(Pageable pageable);
    
    /**
     * Encuentra los expedientes con mayor prioridad con estado CAPTURA VIGENTE
     */
    @Query("SELECT e FROM Expediente e WHERE e.estadoSituacion = 'CAPTURA VIGENTE' ORDER BY e.prioridad DESC")
    List<Expediente> findTopPriorityCapturaVigente(Pageable pageable);
    
    /**
     * Obtiene todas las fuerzas de seguridad únicas
     */
    @Query("SELECT DISTINCT e.fuerzaAsignada FROM Expediente e WHERE e.fuerzaAsignada IS NOT NULL ORDER BY e.fuerzaAsignada")
    List<String> findDistinctFuerzasAsignadas();
    
    // Consultas con filtros para estadísticas
    @Query("SELECT e.estadoSituacion, COUNT(e) FROM Expediente e WHERE (:fuerza IS NULL OR e.fuerzaAsignada = :fuerza) AND (:tipoCaptura IS NULL OR e.tipoCaptura = :tipoCaptura) GROUP BY e.estadoSituacion ORDER BY COUNT(e) DESC")
    List<Object[]> countByEstadoSituacionWithFilters(@Param("fuerza") String fuerza, @Param("tipoCaptura") String tipoCaptura);
    
    @Query("SELECT e.fuerzaAsignada, COUNT(e) FROM Expediente e WHERE (:estado IS NULL OR e.estadoSituacion = :estado) AND (:tipoCaptura IS NULL OR e.tipoCaptura = :tipoCaptura) GROUP BY e.fuerzaAsignada ORDER BY COUNT(e) DESC")
    List<Object[]> countByFuerzaAsignadaWithFilters(@Param("estado") String estado, @Param("tipoCaptura") String tipoCaptura);
    
    @Query("SELECT e.tipoCaptura, COUNT(e) FROM Expediente e WHERE (:estado IS NULL OR e.estadoSituacion = :estado) AND (:fuerza IS NULL OR e.fuerzaAsignada = :fuerza) GROUP BY e.tipoCaptura ORDER BY COUNT(e) DESC")
    List<Object[]> countByTipoCapturaWithFilters(@Param("estado") String estado, @Param("fuerza") String fuerza);
    
    @Query("SELECT TO_CHAR(e.fechaIngreso, 'YYYY-MM') as mes, COUNT(e) FROM Expediente e WHERE (:estado IS NULL OR e.estadoSituacion = :estado) AND (:fuerza IS NULL OR e.fuerzaAsignada = :fuerza) AND (:tipoCaptura IS NULL OR e.tipoCaptura = :tipoCaptura) GROUP BY TO_CHAR(e.fechaIngreso, 'YYYY-MM') ORDER BY mes ASC")
    List<Object[]> countByMesWithFilters(@Param("estado") String estado, @Param("fuerza") String fuerza, @Param("tipoCaptura") String tipoCaptura);
}