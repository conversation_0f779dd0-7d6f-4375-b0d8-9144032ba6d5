package com.cufre.expedientes.repository;

import com.cufre.expedientes.model.ActividadDetalle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActividadDetalleRepository extends JpaRepository<ActividadDetalle, Long> {
    
    /**
     * Buscar detalles por ID de actividad
     */
    List<ActividadDetalle> findByActividadIdOrderByFechaCreacionAsc(Long actividadId);
    
    /**
     * Buscar detalles por tipo
     */
    List<ActividadDetalle> findByTipoDetalleOrderByFechaCreacionDesc(String tipoDetalle);
    
    /**
     * Buscar detalles por actividad y tipo
     */
    List<ActividadDetalle> findByActividadIdAndTipoDetalle(Long actividadId, String tipoDetalle);
    
    /**
     * Eliminar detalles por ID de actividad
     */
    void deleteByActividadId(Long actividadId);
    
    /**
     * Contar detalles por actividad
     */
    @Query("SELECT COUNT(ad) FROM ActividadDetalle ad WHERE ad.actividadId = :actividadId")
    Long countByActividadId(@Param("actividadId") Long actividadId);
}