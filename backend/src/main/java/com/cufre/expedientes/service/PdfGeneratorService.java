package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.ExpedienteDTO;
import com.cufre.expedientes.dto.FotografiaDTO;
import com.cufre.expedientes.dto.DocumentoDTO;
import com.cufre.expedientes.dto.PersonaExpedienteDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class PdfGeneratorService {

    @Value("${app.upload.path:uploads}")
    private String uploadPath;

    private static final float MARGIN = 50;
    private static final float PAGE_WIDTH = PDRectangle.A4.getWidth();
    private static final float PAGE_HEIGHT = PDRectangle.A4.getHeight();
    private static final float CONTENT_WIDTH = PAGE_WIDTH - 2 * MARGIN;

    public byte[] generarPdfExpediente(ExpedienteDTO expediente, List<FotografiaDTO> fotografias, 
                                     List<DocumentoDTO> documentos, List<PersonaExpedienteDTO> personas) throws IOException {
        
        log.info("Generando PDF para expediente ID: {}", expediente.getId());
        
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                float yPosition = PAGE_HEIGHT - MARGIN;
                
                // Agregar marca de agua de confidencialidad
                agregarMarcaAgua(contentStream, document);
                
                // Encabezado del documento
                yPosition = agregarEncabezado(contentStream, yPosition);
                
                // Información del expediente
                yPosition = agregarInformacionExpediente(contentStream, expediente, yPosition);
                
                // Información de las personas asociadas
                if (personas != null && !personas.isEmpty()) {
                    yPosition = agregarInformacionPersonas(contentStream, personas, yPosition);
                }
                
                // Foto principal si existe
                PersonaExpedienteDTO personaPrincipal = obtenerPersonaPrincipal(personas);
                if (personaPrincipal != null && fotografias != null && !fotografias.isEmpty()) {
                    FotografiaDTO fotoPrincipal = obtenerFotoPrincipal(fotografias);
                    if (fotoPrincipal != null) {
                        yPosition = agregarFotoPrincipal(contentStream, document, fotoPrincipal, yPosition);
                    }
                }
                
                // Verificar si necesitamos una nueva página
                if (yPosition < 200) {
                    page = new PDPage(PDRectangle.A4);
                    document.addPage(page);
                    contentStream.close();
                    
                    try (PDPageContentStream newContentStream = new PDPageContentStream(document, page)) {
                        yPosition = PAGE_HEIGHT - MARGIN;
                        agregarMarcaAgua(newContentStream, document);
                        
                        // Galería de fotos
                        if (fotografias != null && fotografias.size() > 1) {
                            yPosition = agregarGaleriaFotos(newContentStream, document, fotografias, yPosition);
                        }
                        
                        // Pie de página
                        agregarPiePagina(newContentStream, 1);
                    }
                } else {
                    // Galería de fotos en la misma página
                    if (fotografias != null && fotografias.size() > 1) {
                        yPosition = agregarGaleriaFotos(contentStream, document, fotografias, yPosition);
                    }
                    
                    // Pie de página
                    agregarPiePagina(contentStream, 1);
                }
            }
            
            // Adjuntar documentos como archivos adjuntos
            if (documentos != null && !documentos.isEmpty()) {
                adjuntarDocumentos(document, documentos);
            }
            
            // Convertir a bytes
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            return baos.toByteArray();
        }
    }

    private void agregarMarcaAgua(PDPageContentStream contentStream, PDDocument document) throws IOException {
        // Guardar estado gráfico actual
        contentStream.saveGraphicsState();
        
        // Crear estado gráfico con transparencia
        PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
        graphicsState.setNonStrokingAlphaConstant(0.3f);
        contentStream.setGraphicsStateParameters(graphicsState);
        
        // Configurar fuente y color
        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 60);
        contentStream.setNonStrokingColor(Color.LIGHT_GRAY);
        
        // Rotar y posicionar el texto
        contentStream.beginText();
        contentStream.setTextMatrix(
            (float) Math.cos(Math.toRadians(45)), 
            (float) Math.sin(Math.toRadians(45)),
            (float) -Math.sin(Math.toRadians(45)), 
            (float) Math.cos(Math.toRadians(45)),
            PAGE_WIDTH / 2 - 100, 
            PAGE_HEIGHT / 2
        );
        contentStream.showText("CONFIDENCIAL");
        contentStream.endText();
        
        // Restaurar estado gráfico
        contentStream.restoreGraphicsState();
    }

    private float agregarEncabezado(PDPageContentStream contentStream, float yPosition) throws IOException {
        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 18);
        contentStream.setNonStrokingColor(Color.BLACK);
        
        contentStream.beginText();
        contentStream.newLineAtOffset(MARGIN, yPosition);
        contentStream.showText("INFORME DE EXPEDIENTE - CUFRE");
        contentStream.endText();
        
        yPosition -= 30;
        
        // Línea separadora
        contentStream.moveTo(MARGIN, yPosition);
        contentStream.lineTo(PAGE_WIDTH - MARGIN, yPosition);
        contentStream.stroke();
        
        return yPosition - 20;
    }

    private float agregarInformacionExpediente(PDPageContentStream contentStream, ExpedienteDTO expediente, float yPosition) throws IOException {
        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
        
        yPosition = escribirTexto(contentStream, "INFORMACIÓN DEL EXPEDIENTE", MARGIN, yPosition);
        yPosition -= 10;
        
        contentStream.setFont(PDType1Font.HELVETICA, 12);
        
        if (expediente.getNumero() != null) {
            yPosition = escribirTexto(contentStream, "Número de Expediente: " + expediente.getNumero(), MARGIN, yPosition);
        }
        
        if (expediente.getFechaIngreso() != null) {
            yPosition = escribirTexto(contentStream, "Fecha de Ingreso: " + expediente.getFechaIngreso().toString(), MARGIN, yPosition);
        }
        
        if (expediente.getEstadoSituacion() != null) {
            yPosition = escribirTexto(contentStream, "Estado: " + expediente.getEstadoSituacion(), MARGIN, yPosition);
        }
        
        if (expediente.getFuerzaAsignada() != null) {
            yPosition = escribirTexto(contentStream, "Fuerza Asignada: " + expediente.getFuerzaAsignada(), MARGIN, yPosition);
        }
        
        if (expediente.getMontoRecompensa() != null) {
            yPosition = escribirTexto(contentStream, "Recompensa: $" + expediente.getMontoRecompensa(), MARGIN, yPosition);
        }
        
        if (expediente.getDescripcion() != null && !expediente.getDescripcion().trim().isEmpty()) {
            yPosition = escribirTextoMultilinea(contentStream, "Descripción: " + expediente.getDescripcion(), MARGIN, yPosition);
        }
        
        return yPosition - 20;
    }

    private float agregarInformacionPersonas(PDPageContentStream contentStream, List<PersonaExpedienteDTO> personas, float yPosition) throws IOException {
        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
        yPosition = escribirTexto(contentStream, "PERSONAS ASOCIADAS", MARGIN, yPosition);
        yPosition -= 10;
        
        contentStream.setFont(PDType1Font.HELVETICA, 12);
        
        for (PersonaExpedienteDTO personaExp : personas) {
            if (personaExp.getPersona() != null) {
                String nombreCompleto = (personaExp.getPersona().getNombre() != null ? personaExp.getPersona().getNombre() : "") + 
                                      " " + (personaExp.getPersona().getApellido() != null ? personaExp.getPersona().getApellido() : "");
                yPosition = escribirTexto(contentStream, "• " + nombreCompleto.trim(), MARGIN, yPosition);
                
                if (personaExp.getPersona().getNumeroDocumento() != null) {
                    yPosition = escribirTexto(contentStream, "  DNI: " + personaExp.getPersona().getNumeroDocumento(), MARGIN + 20, yPosition);
                }
                
                if (personaExp.getTipoRelacion() != null) {
                    yPosition = escribirTexto(contentStream, "  Rol: " + personaExp.getTipoRelacion(), MARGIN + 20, yPosition);
                }
                
                yPosition -= 5;
            }
        }
        
        return yPosition - 10;
    }

    private float agregarFotoPrincipal(PDPageContentStream contentStream, PDDocument document, FotografiaDTO foto, float yPosition) throws IOException {
        try {
            String rutaCompleta = uploadPath + "/fotografias/" + foto.getRutaArchivo();
            File archivoFoto = new File(rutaCompleta);
            
            if (archivoFoto.exists()) {
                PDImageXObject imagen = PDImageXObject.createFromFile(rutaCompleta, document);
                
                // Calcular dimensiones manteniendo proporción
                float maxWidth = 200;
                float maxHeight = 250;
                float width = imagen.getWidth();
                float height = imagen.getHeight();
                
                float ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
                
                // Centrar la imagen
                float xPosition = MARGIN + (CONTENT_WIDTH - width) / 2;
                
                contentStream.drawImage(imagen, xPosition, yPosition - height, width, height);
                
                // Agregar descripción si existe
                if (foto.getDescripcion() != null && !foto.getDescripcion().trim().isEmpty()) {
                    contentStream.setFont(PDType1Font.HELVETICA, 10);
                    contentStream.beginText();
                    contentStream.newLineAtOffset(xPosition, yPosition - height - 15);
                    contentStream.showText("Foto Principal: " + foto.getDescripcion());
                    contentStream.endText();
                    return yPosition - height - 30;
                }
                
                return yPosition - height - 10;
            }
        } catch (Exception e) {
            log.warn("No se pudo cargar la foto principal: {}", e.getMessage());
        }
        
        return yPosition;
    }

    private float agregarGaleriaFotos(PDPageContentStream contentStream, PDDocument document, List<FotografiaDTO> fotografias, float yPosition) throws IOException {
        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
        yPosition = escribirTexto(contentStream, "GALERÍA DE FOTOGRAFÍAS", MARGIN, yPosition);
        yPosition -= 20;
        
        int fotosEnFila = 3;
        float anchoFoto = (CONTENT_WIDTH - 20) / fotosEnFila;
        float altoFoto = 80;
        
        int contador = 0;
        for (FotografiaDTO foto : fotografias) {
            if (contador == 0) continue; // Saltar la foto principal
            
            try {
                String rutaCompleta = uploadPath + "/fotografias/" + foto.getRutaArchivo();
                File archivoFoto = new File(rutaCompleta);
                
                if (archivoFoto.exists()) {
                    PDImageXObject imagen = PDImageXObject.createFromFile(rutaCompleta, document);
                    
                    int posicionEnFila = (contador - 1) % fotosEnFila;
                    float xPosition = MARGIN + posicionEnFila * (anchoFoto + 10);
                    
                    contentStream.drawImage(imagen, xPosition, yPosition - altoFoto, anchoFoto, altoFoto);
                    
                    if (posicionEnFila == fotosEnFila - 1) {
                        yPosition -= altoFoto + 10;
                    }
                }
            } catch (Exception e) {
                log.warn("No se pudo cargar la fotografía: {}", e.getMessage());
            }
            
            contador++;
        }
        
        return yPosition - 20;
    }

    private void agregarPiePagina(PDPageContentStream contentStream, int numeroPagina) throws IOException {
        contentStream.setFont(PDType1Font.HELVETICA, 10);
        contentStream.setNonStrokingColor(Color.GRAY);
        
        String fechaGeneracion = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
        String textoPie = "Generado el " + fechaGeneracion + " - Página " + numeroPagina;
        
        contentStream.beginText();
        contentStream.newLineAtOffset(MARGIN, 30);
        contentStream.showText(textoPie);
        contentStream.endText();
        
        contentStream.beginText();
        contentStream.newLineAtOffset(PAGE_WIDTH - MARGIN - 100, 30);
        contentStream.showText("CONFIDENCIAL");
        contentStream.endText();
    }

    private void adjuntarDocumentos(PDDocument document, List<DocumentoDTO> documentos) {
        // TODO: Implementar adjuntos de documentos usando PDFBox
        // Esta funcionalidad requiere una implementación más compleja
        log.info("Se encontraron {} documentos para adjuntar (funcionalidad pendiente)", documentos.size());
    }

    private PersonaExpedienteDTO obtenerPersonaPrincipal(List<PersonaExpedienteDTO> personas) {
        if (personas == null || personas.isEmpty()) return null;
        
        // Buscar la persona con rol "PROFUGO" o la primera si no hay ninguna
        return personas.stream()
                .filter(p -> "PROFUGO".equalsIgnoreCase(p.getTipoRelacion()))
                .findFirst()
                .orElse(personas.get(0));
    }

    private FotografiaDTO obtenerFotoPrincipal(List<FotografiaDTO> fotografias) {
        if (fotografias == null || fotografias.isEmpty()) return null;
        
        // Simplemente devolver la primera foto ya que no hay campo esPrincipal
        return fotografias.get(0);
    }

    private float escribirTexto(PDPageContentStream contentStream, String texto, float x, float y) throws IOException {
        contentStream.beginText();
        contentStream.newLineAtOffset(x, y);
        contentStream.showText(texto);
        contentStream.endText();
        return y - 15;
    }

    private float escribirTextoMultilinea(PDPageContentStream contentStream, String texto, float x, float y) throws IOException {
        String[] lineas = texto.split("\n");
        for (String linea : lineas) {
            y = escribirTexto(contentStream, linea, x, y);
        }
        return y;
    }
}