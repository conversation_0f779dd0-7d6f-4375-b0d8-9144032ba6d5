package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.PerfilUsuarioDTO;
import com.cufre.expedientes.dto.UsuarioDTO;
import com.cufre.expedientes.dto.UpdateRoleResponseDTO;
import com.cufre.expedientes.dto.ForceLogoutResponseDTO;
import com.cufre.expedientes.exception.ResourceNotFoundException;
import com.cufre.expedientes.mapper.UsuarioMapper;
import com.cufre.expedientes.model.Usuario;
import com.cufre.expedientes.model.enums.Rol;
import com.cufre.expedientes.repository.UsuarioRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.AccessDeniedException;
import jakarta.mail.MessagingException;
import com.cufre.expedientes.service.ActividadSistemaService;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * Servicio para la gestión de usuarios
 */
@Service
@Slf4j
public class UsuarioService extends AbstractBaseService<Usuario, UsuarioDTO, Long, UsuarioRepository, UsuarioMapper> {

    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final ActividadSistemaService actividadSistemaService;

    public UsuarioService(UsuarioRepository repository, UsuarioMapper mapper, PasswordEncoder passwordEncoder, EmailService emailService, ActividadSistemaService actividadSistemaService) {
        super(repository, mapper);
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
        this.actividadSistemaService = actividadSistemaService;
    }

    @Override
    protected UsuarioDTO toDto(Usuario entity) {
        return mapper.toDto(entity);
    }

    @Override
    protected Usuario toEntity(UsuarioDTO dto) {
        return mapper.toEntity(dto);
    }

    @Override
    protected Usuario updateEntity(UsuarioDTO dto, Usuario entity) {
        return mapper.updateEntity(dto, entity);
    }

    @Override
    protected String getEntityName() {
        return "Usuario";
    }

    /**
     * Crea un nuevo usuario
     * @param usuarioDTO Datos del usuario
     * @param password Contraseña en texto plano
     * @return Usuario creado
     */
    @Transactional
    public UsuarioDTO create(UsuarioDTO usuarioDTO, String password) {
        Usuario usuario = toEntity(usuarioDTO);
        usuario.setContrasena(passwordEncoder.encode(password));
        // Asegurar que los valores por defecto sean correctos
        usuario.setRequiereCambioContrasena(true); // Requerir cambio de contraseña al primer inicio
        usuario.setRequiere2FA(true); // Requerir configuración de 2FA
        usuario = repository.save(usuario);
        log.info("Usuario creado: {}", usuario.getNombre());
        // Registrar actividad de creación de usuario con el usuario autenticado
        // La actividad se registra automáticamente por el AuditInterceptor

        // Intentar enviar email de bienvenida, pero no interrumpir el flujo si falla
        try {
            emailService.enviarBienvenida(usuario.getNombre(), usuario.getApellido(), usuario.getEmail());
            log.info("Email de bienvenida enviado a {}", usuario.getEmail());
        } catch (Exception e) {
            // Capturar cualquier excepción, no solo MessagingException
            log.error("No se pudo enviar el email de bienvenida a {}: {}", usuario.getEmail(), e.getMessage());
            // No re-lanzar la excepción, permitiendo que la creación del usuario continúe
        }

        return toDto(usuario);
    }

    /**
     * Cambia la contraseña de un usuario
     * @param id ID del usuario
     * @param newPassword Nueva contraseña
     */
    @Transactional
    public void changePassword(Long id, String newPassword) {
        repository.findById(id)
                .map(usuario -> {
                    usuario.setContrasena(passwordEncoder.encode(newPassword));
                    return repository.save(usuario);
                })
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + id));
        log.info("Contraseña cambiada para usuario con ID: {}", id);
    }

    /**
     * Busca usuarios por rol
     * @param rol Rol de usuario
     * @return Lista de usuarios con el rol especificado
     */
    @Transactional(readOnly = true)
    public List<UsuarioDTO> findByRol(Rol rol) {
        return repository.findByRol(rol).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Busca un usuario por nombre de usuario
     * @param nombre Nombre del usuario
     * @return Usuario encontrado o vacío
     */
    @Transactional(readOnly = true)
    public Optional<UsuarioDTO> findByNombre(String nombre) {
        return repository.findByNombre(nombre)
                .map(this::toDto);
    }

    /**
     * Busca un usuario por email
     * @param email Email del usuario
     * @return Usuario encontrado o vacío
     */
    @Transactional(readOnly = true)
    public Optional<UsuarioDTO> findByEmail(String email) {
        return repository.findByEmail(email)
                .map(this::toDto);
    }

    /**
     * Busca un usuario por email y retorna la entidad Usuario
     * @param email Email del usuario
     * @return Usuario encontrado o excepción
     */
    @Transactional(readOnly = true)
    public Usuario findByEmailEntity(String email) {
        return repository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con email: " + email));
    }

    /**
     * Cambia la contraseña de un usuario por email
     * @param email Email del usuario
     * @param newPassword Nueva contraseña
     */
    @Transactional
    public void changePasswordByEmail(String email, String newPassword) {
        Usuario usuario = findByEmailEntity(email);
        usuario.setContrasena(passwordEncoder.encode(newPassword));
        usuario.setRequiereCambioContrasena(false);
        // Si el usuario no tiene 2FA configurado, forzar que lo requiera
        if (usuario.getSecret2FA() == null || usuario.getSecret2FA().isEmpty()) {
            usuario.setRequiere2FA(true);
        }
        log.info("Contraseña cambiada para usuario con email: {}", email);
    }

    /**
     * Resetea la contraseña y el 2FA de un usuario (para uso administrativo)
     * @param id ID del usuario
     */
    @Transactional
    public void resetPasswordAnd2FA(Long id) {
        Usuario usuario = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + id));
        usuario.setContrasena(passwordEncoder.encode("Minseg2025-"));
        usuario.setRequiereCambioContrasena(true);
        usuario.setRequiere2FA(true);
        usuario.setSecret2FA(null);
        repository.save(usuario);
        log.info("Contraseña y 2FA reseteados para usuario con ID: {}", id);
    }

    /**
     * Cambia el rol de un usuario con validación de contraseña
     * @param userId ID del usuario objetivo
     * @param newRole Nuevo rol a asignar
     * @param password Contraseña del usuario actual para validación
     * @return Respuesta con el resultado de la operación
     */
    @Transactional
    public UpdateRoleResponseDTO updateRoleWithPassword(Long userId, Rol newRole, String password) {
        try {
            // Obtener usuario actual
            String currentUserEmail = obtenerUsuarioActual();
            Usuario currentUser = findByEmailEntity(currentUserEmail);
            
            // Validar que el usuario actual sea SUPERUSUARIO
            if (currentUser.getRol() != Rol.SUPERUSUARIO) {
                throw new AccessDeniedException("Solo los SUPERUSUARIOS pueden cambiar roles de otros usuarios");
            }
            
            // Validar contraseña del usuario actual
            if (!passwordEncoder.matches(password, currentUser.getContrasena())) {
                throw new AccessDeniedException("Contraseña incorrecta");
            }
            
            // Obtener usuario objetivo
            Usuario targetUser = repository.findById(userId)
                    .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + userId));
            
            // Validar que no se esté intentando cambiar el rol de otro SUPERUSUARIO
            if (targetUser.getRol() == Rol.SUPERUSUARIO && !currentUser.getId().equals(targetUser.getId())) {
                throw new AccessDeniedException("No se puede cambiar el rol de otro SUPERUSUARIO");
            }
            
            // Guardar rol anterior para logging
            Rol previousRole = targetUser.getRol();
            
            // Actualizar rol
            targetUser.setRol(newRole);
            repository.save(targetUser);
            
            // Registrar actividad
            String mensaje = String.format("Rol cambiado de %s a %s para usuario: %s %s",
                    previousRole, newRole, targetUser.getNombre(), targetUser.getApellido());
            // La actividad se registra automáticamente por el AuditInterceptor

            log.info("Rol actualizado para usuario ID {}: {} -> {}", userId, previousRole, newRole);
            
            return UpdateRoleResponseDTO.builder()
                    .success(true)
                    .message("Rol actualizado correctamente")
                    .updatedUser(toDto(targetUser))
                    .build();
                    
        } catch (AccessDeniedException | ResourceNotFoundException e) {
            log.warn("Error al cambiar rol para usuario ID {}: {}", userId, e.getMessage());
            return UpdateRoleResponseDTO.builder()
                    .success(false)
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("Error inesperado al cambiar rol para usuario ID {}: {}", userId, e.getMessage(), e);
            return UpdateRoleResponseDTO.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
        }
    }
    
    /**
     * Fuerza el cierre de sesión de un usuario
     * @param userId ID del usuario objetivo
     * @return Respuesta con el resultado de la operación
     */
    @Transactional
    public ForceLogoutResponseDTO forceLogout(Long userId) {
        try {
            // Obtener usuario actual
            String currentUserEmail = obtenerUsuarioActual();
            Usuario currentUser = findByEmailEntity(currentUserEmail);
            
            // Validar que el usuario actual sea SUPERUSUARIO
            if (currentUser.getRol() != Rol.SUPERUSUARIO) {
                throw new AccessDeniedException("Solo los SUPERUSUARIOS pueden forzar el cierre de sesión");
            }
            
            // Obtener usuario objetivo
            Usuario targetUser = repository.findById(userId)
                    .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con id: " + userId));
            
            // Validar que no se esté intentando forzar su propio logout
            if (currentUser.getId().equals(targetUser.getId())) {
                throw new AccessDeniedException("No puedes forzar tu propio cierre de sesión");
            }
            
            // Para invalidar sesiones, podemos usar una estrategia simple:
            // Cambiar un campo que fuerce la re-autenticación (como requerir cambio de contraseña)
            // En una implementación más avanzada, aquí se invalidarían los tokens JWT
            targetUser.setRequiereCambioContrasena(true);
            repository.save(targetUser);
            
            // Registrar actividad
            String mensaje = String.format("Cierre de sesión forzado para usuario: %s %s",
                    targetUser.getNombre(), targetUser.getApellido());
            // La actividad se registra automáticamente por el AuditInterceptor

            log.info("Cierre de sesión forzado para usuario ID {}: {} {}",
                    userId, targetUser.getNombre(), targetUser.getApellido());
            
            return ForceLogoutResponseDTO.builder()
                    .success(true)
                    .message("Cierre de sesión forzado correctamente")
                    .sessionsInvalidated(1) // En implementación simple, asumimos 1 sesión
                    .build();
                    
        } catch (AccessDeniedException | ResourceNotFoundException e) {
            log.warn("Error al forzar logout para usuario ID {}: {}", userId, e.getMessage());
            return ForceLogoutResponseDTO.builder()
                    .success(false)
                    .message(e.getMessage())
                    .sessionsInvalidated(0)
                    .build();
        } catch (Exception e) {
            log.error("Error inesperado al forzar logout para usuario ID {}: {}", userId, e.getMessage(), e);
            return ForceLogoutResponseDTO.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .sessionsInvalidated(0)
                    .build();
        }
    }
    
    /**
     * Valida la contraseña del usuario actual
     * @param password Contraseña a validar
     * @return true si la contraseña es correcta
     */
    private boolean validatePasswordForCurrentUser(String password) {
        try {
            String currentUserEmail = obtenerUsuarioActual();
            Usuario currentUser = findByEmailEntity(currentUserEmail);
            return passwordEncoder.matches(password, currentUser.getContrasena());
        } catch (Exception e) {
            log.error("Error al validar contraseña del usuario actual: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Obtiene el email del usuario autenticado actual
     */
    private String obtenerUsuarioActual() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()
            && !authentication.getName().equals("anonymousUser")) {
            return authentication.getName();
        }
        return "SISTEMA";
    }

    // ==================== MÉTODOS DE PERFIL ====================

    /**
     * Obtiene el perfil completo de un usuario por email
     */
    @Transactional(readOnly = true)
    public PerfilUsuarioDTO obtenerPerfilPorEmail(String email) {
        log.debug("Obteniendo perfil para usuario con email: {}", email);
        
        Usuario usuario = findByEmailEntity(email);
        return convertirAPerfilDTO(usuario);
    }

    /**
     * Obtiene el perfil completo de un usuario por ID
     */
    @Transactional(readOnly = true)
    public PerfilUsuarioDTO obtenerPerfilPorId(Long id) {
        log.debug("Obteniendo perfil para usuario con ID: {}", id);
        
        Usuario usuario = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con ID: " + id));
        return convertirAPerfilDTO(usuario);
    }

    /**
     * Actualiza el perfil de un usuario por email (usuario actual)
     */
    @Transactional
    public PerfilUsuarioDTO actualizarPerfilPorEmail(String email, PerfilUsuarioDTO perfilDTO) {
        log.info("Actualizando perfil para usuario con email: {}", email);
        
        Usuario usuario = findByEmailEntity(email);
        
        // Validar que el usuario solo pueda editar ciertos campos
        actualizarCamposPerfil(usuario, perfilDTO, false);
        
        Usuario usuarioActualizado = repository.save(usuario);
        
        log.info("Perfil actualizado exitosamente para usuario: {}", email);
        return convertirAPerfilDTO(usuarioActualizado);
    }

    /**
     * Actualiza el perfil de un usuario por ID (solo SUPERUSUARIO)
     */
    @Transactional
    public PerfilUsuarioDTO actualizarPerfilPorId(Long id, PerfilUsuarioDTO perfilDTO) {
        log.info("Actualizando perfil para usuario con ID: {}", id);
        
        Usuario usuario = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado con ID: " + id));
        
        // SUPERUSUARIO puede editar todos los campos
        actualizarCamposPerfil(usuario, perfilDTO, true);
        
        Usuario usuarioActualizado = repository.save(usuario);
        
        log.info("Perfil actualizado exitosamente para usuario ID: {}", id);
        return convertirAPerfilDTO(usuarioActualizado);
    }

    /**
     * Actualiza los campos del perfil según los permisos
     */
    private void actualizarCamposPerfil(Usuario usuario, PerfilUsuarioDTO perfilDTO, boolean esSuperusuario) {
        // Campos que siempre se pueden actualizar
        if (perfilDTO.getTelefonoMovil() != null) {
            usuario.setTelefonoMovil(perfilDTO.getTelefonoMovil());
        }
        
        // Campos que solo SUPERUSUARIO puede actualizar
        if (esSuperusuario) {
            if (perfilDTO.getNombre() != null) {
                usuario.setNombre(perfilDTO.getNombre());
            }
            
            if (perfilDTO.getApellido() != null) {
                usuario.setApellido(perfilDTO.getApellido());
            }
            
            if (perfilDTO.getEmail() != null) {
                usuario.setEmail(perfilDTO.getEmail());
            }
            
            if (perfilDTO.getDependencia() != null) {
                usuario.setDependencia(perfilDTO.getDependencia());
            }
        }
    }

    /**
     * Convierte una entidad Usuario a PerfilUsuarioDTO
     */
    private PerfilUsuarioDTO convertirAPerfilDTO(Usuario usuario) {
        return PerfilUsuarioDTO.builder()
                .id(usuario.getId())
                .nombre(usuario.getNombre())
                .apellido(usuario.getApellido())
                .email(usuario.getEmail())
                .dependencia(usuario.getDependencia())
                .telefonoMovil(usuario.getTelefonoMovil())
                .rol(usuario.getRol())
                .build();
    }
}