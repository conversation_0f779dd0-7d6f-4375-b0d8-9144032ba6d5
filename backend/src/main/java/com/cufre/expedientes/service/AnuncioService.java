package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.AnuncioDTO;
import com.cufre.expedientes.dto.CrearAnuncioDTO;
import com.cufre.expedientes.exception.ResourceNotFoundException;
import com.cufre.expedientes.mapper.AnuncioMapper;
import com.cufre.expedientes.model.Anuncio;
import com.cufre.expedientes.model.AnuncioVisto;
import com.cufre.expedientes.model.Usuario;
import com.cufre.expedientes.repository.AnuncioRepository;
import com.cufre.expedientes.repository.AnuncioVistoRepository;
import com.cufre.expedientes.repository.UsuarioRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Servicio para la gestión de anuncios globales
 */
@Service
@Slf4j
public class AnuncioService {
    
    private final AnuncioRepository anuncioRepository;
    private final AnuncioVistoRepository anuncioVistoRepository;
    private final UsuarioRepository usuarioRepository;
    private final AnuncioMapper anuncioMapper;
    
    public AnuncioService(AnuncioRepository anuncioRepository, 
                         AnuncioVistoRepository anuncioVistoRepository,
                         UsuarioRepository usuarioRepository,
                         AnuncioMapper anuncioMapper) {
        this.anuncioRepository = anuncioRepository;
        this.anuncioVistoRepository = anuncioVistoRepository;
        this.usuarioRepository = usuarioRepository;
        this.anuncioMapper = anuncioMapper;
    }
    
    /**
     * Obtiene todos los anuncios ordenados por fecha de creación
     */
    public List<AnuncioDTO> obtenerTodos() {
        log.debug("Obteniendo todos los anuncios");
        log.debug("DEBUG - Consultando anuncios en base de datos");
        List<Anuncio> anuncios = anuncioRepository.findAllByOrderByFechaCreacionDesc();
        log.debug("DEBUG - Encontrados {} anuncios en base de datos", anuncios.size());
        
        log.debug("DEBUG - Convirtiendo anuncios a DTOs");
        List<AnuncioDTO> resultado = anuncios.stream()
                .map(anuncioMapper::toDto)
                .collect(Collectors.toList());
        log.debug("DEBUG - Conversión exitosa, devolviendo {} anuncios", resultado.size());
        
        return resultado;
    }
    
    /**
     * Obtiene un anuncio por su ID
     */
    public AnuncioDTO obtenerPorId(Long id) {
        log.debug("Obteniendo anuncio con ID: {}", id);
        Anuncio anuncio = anuncioRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Anuncio no encontrado con ID: " + id));
        return anuncioMapper.toDto(anuncio);
    }
    
    /**
     * Obtiene el anuncio activo para un usuario específico
     * Retorna null si no hay anuncio activo o si el usuario ya lo vio
     */
    public AnuncioDTO obtenerAnuncioActivoParaUsuario(Long usuarioId) {
        log.debug("Obteniendo anuncio activo para usuario ID: {}", usuarioId);
        
        // Validar que el usuarioId no sea null
        if (usuarioId == null) {
            log.error("ERROR - usuarioId es null en obtenerAnuncioActivoParaUsuario");
            throw new IllegalArgumentException("Usuario ID no puede ser null");
        }
        
        // Verificar la integridad de anuncios activos
        log.debug("DEBUG - Verificando integridad de anuncios activos");
        long cantidadAnunciosActivos = anuncioRepository.countByActivoTrue();
        log.debug("DEBUG - Cantidad de anuncios activos en BD: {}", cantidadAnunciosActivos);
        
        if (cantidadAnunciosActivos > 1) {
            log.warn("ADVERTENCIA - Se encontraron {} anuncios activos, debería haber máximo 1. Corrigiendo automáticamente.",
                    cantidadAnunciosActivos);
            // Desactivar todos y no devolver ninguno hasta que se corrija manualmente
            anuncioRepository.desactivarTodos();
            log.info("Se desactivaron todos los anuncios debido a múltiples anuncios activos");
            return null;
        }
        
        if (cantidadAnunciosActivos == 0) {
            log.debug("DEBUG - No hay anuncios activos en base de datos");
            return null;
        }
        
        log.debug("DEBUG - Buscando anuncio activo en base de datos");
        Optional<Anuncio> anuncioActivo = anuncioRepository.findByActivoTrue();
        
        if (anuncioActivo.isEmpty()) {
            log.error("ERROR - Inconsistencia: count indica 1 anuncio activo pero findByActivoTrue devuelve vacío");
            return null;
        }
        
        Anuncio anuncio = anuncioActivo.get();
        log.debug("DEBUG - Anuncio activo encontrado: ID={}, Titulo={}, Activo={}",
                 anuncio.getId(), anuncio.getTitulo(), anuncio.isActivo());
        
        // Verificar si el usuario ya vio este anuncio
        log.debug("DEBUG - Verificando si usuario {} ya vio anuncio {}", usuarioId, anuncio.getId());
        boolean yaVisto = anuncioVistoRepository.existsByIdUsuarioIdAndIdAnuncioId(usuarioId, anuncio.getId());
        log.debug("DEBUG - Resultado verificación yaVisto: {}", yaVisto);
        
        if (yaVisto) {
            log.debug("Usuario {} ya vio el anuncio {}", usuarioId, anuncio.getId());
            return null;
        }
        
        log.debug("DEBUG - Convirtiendo anuncio a DTO usando mapper");
        AnuncioDTO resultado = anuncioMapper.toDto(anuncio);
        log.debug("DEBUG - Conversión exitosa, devolviendo anuncio activo {} para usuario {}",
                 anuncio.getId(), usuarioId);
        
        return resultado;
    }
    
    /**
     * Crea un nuevo anuncio
     */
    @Transactional
    public AnuncioDTO crear(CrearAnuncioDTO crearAnuncioDTO) {
        log.info("Creando nuevo anuncio: {}", crearAnuncioDTO.getTitulo());
        
        // Obtener el usuario actual
        Usuario usuarioActual = obtenerUsuarioActual();
        
        // Convertir DTO a entidad
        Anuncio anuncio = anuncioMapper.toEntity(crearAnuncioDTO);
        anuncio.setCreadoPor(usuarioActual);
        anuncio.setFechaCreacion(LocalDateTime.now());
        
        // Si se marca como activo, desactivar todos los demás
        if (crearAnuncioDTO.isActivo()) {
            log.info("Desactivando anuncios anteriores antes de activar el nuevo");
            anuncioRepository.desactivarTodos();
        }
        
        // Guardar el anuncio
        Anuncio anuncioGuardado = anuncioRepository.save(anuncio);
        
        log.info("Anuncio creado exitosamente con ID: {}", anuncioGuardado.getId());
        return anuncioMapper.toDto(anuncioGuardado);
    }
    
    /**
     * Actualiza un anuncio existente
     */
    @Transactional
    public AnuncioDTO actualizar(Long id, CrearAnuncioDTO anuncioDTO) {
        log.info("Actualizando anuncio con ID: {}", id);
        
        // Buscar el anuncio existente
        Anuncio anuncioExistente = anuncioRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Anuncio no encontrado con ID: " + id));
        
        // Actualizar los campos del anuncio
        anuncioExistente.setTitulo(anuncioDTO.getTitulo());
        anuncioExistente.setContenido(anuncioDTO.getContenido());
        
        // Si se marca como activo, desactivar todos los demás
        if (anuncioDTO.isActivo() && !anuncioExistente.isActivo()) {
            log.info("Desactivando anuncios anteriores antes de activar el anuncio actualizado");
            anuncioRepository.desactivarTodos();
        }
        
        anuncioExistente.setActivo(anuncioDTO.isActivo());
        
        // Guardar los cambios
        Anuncio anuncioActualizado = anuncioRepository.save(anuncioExistente);
        
        log.info("Anuncio {} actualizado exitosamente", id);
        return anuncioMapper.toDto(anuncioActualizado);
    }
    
    /**
     * Activa un anuncio específico (desactiva todos los demás)
     */
    @Transactional
    public AnuncioDTO activar(Long id) {
        log.info("Activando anuncio con ID: {}", id);
        
        Anuncio anuncio = anuncioRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Anuncio no encontrado con ID: " + id));
        
        // Desactivar todos los anuncios
        anuncioRepository.desactivarTodos();
        
        // Activar el anuncio específico
        anuncio.setActivo(true);
        Anuncio anuncioActivado = anuncioRepository.save(anuncio);
        
        log.info("Anuncio {} activado exitosamente", id);
        return anuncioMapper.toDto(anuncioActivado);
    }
    
    /**
     * Desactiva un anuncio específico
     */
    @Transactional
    public AnuncioDTO desactivar(Long id) {
        log.info("Desactivando anuncio con ID: {}", id);
        
        Anuncio anuncio = anuncioRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Anuncio no encontrado con ID: " + id));
        
        anuncio.setActivo(false);
        Anuncio anuncioDesactivado = anuncioRepository.save(anuncio);
        
        log.info("Anuncio {} desactivado exitosamente", id);
        return anuncioMapper.toDto(anuncioDesactivado);
    }
    
    /**
     * Marca un anuncio como visto por un usuario
     */
    @Transactional
    public void marcarComoVisto(Long anuncioId, Long usuarioId) {
        log.debug("Marcando anuncio {} como visto por usuario {}", anuncioId, usuarioId);
        
        // Verificar que el anuncio existe
        if (!anuncioRepository.existsById(anuncioId)) {
            throw new ResourceNotFoundException("Anuncio no encontrado con ID: " + anuncioId);
        }
        
        // Verificar que el usuario existe
        if (!usuarioRepository.existsById(usuarioId)) {
            throw new ResourceNotFoundException("Usuario no encontrado con ID: " + usuarioId);
        }
        
        // Verificar si ya fue marcado como visto
        if (anuncioVistoRepository.existsByIdUsuarioIdAndIdAnuncioId(usuarioId, anuncioId)) {
            log.debug("El anuncio {} ya fue marcado como visto por el usuario {}", anuncioId, usuarioId);
            return;
        }
        
        // Crear el registro de anuncio visto
        AnuncioVisto anuncioVisto = new AnuncioVisto(usuarioId, anuncioId);
        anuncioVistoRepository.save(anuncioVisto);
        
        log.info("Anuncio {} marcado como visto por usuario {}", anuncioId, usuarioId);
    }
    
    /**
     * Elimina un anuncio
     */
    @Transactional
    public void eliminar(Long id) {
        log.info("Eliminando anuncio con ID: {}", id);
        
        if (!anuncioRepository.existsById(id)) {
            throw new ResourceNotFoundException("Anuncio no encontrado con ID: " + id);
        }
        
        anuncioRepository.deleteById(id);
        log.info("Anuncio {} eliminado exitosamente", id);
    }
    
    /**
     * Obtiene el usuario actual desde el contexto de seguridad
     */
    private Usuario obtenerUsuarioActual() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        
        return usuarioRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado: " + email));
    }
}