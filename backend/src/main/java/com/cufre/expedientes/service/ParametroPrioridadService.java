package com.cufre.expedientes.service;

import com.cufre.expedientes.exception.ResourceNotFoundException;
import com.cufre.expedientes.model.ParametroPrioridad;
import com.cufre.expedientes.repository.ParametroPrioridadRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Servicio para la gestión de parámetros de prioridad con caché en memoria
 */
@Service
@Slf4j
public class ParametroPrioridadService {

    private final ParametroPrioridadRepository parametroPrioridadRepository;
    
    // Caché en memoria para los parámetros de prioridad
    private final Map<String, Integer> cacheParametros = new ConcurrentHashMap<>();
    private volatile boolean cacheInicializado = false;

    @Autowired
    public ParametroPrioridadService(ParametroPrioridadRepository parametroPrioridadRepository) {
        this.parametroPrioridadRepository = parametroPrioridadRepository;
    }

    /**
     * Inicializa la caché de parámetros de prioridad al arrancar la aplicación
     */
    @PostConstruct
    public void inicializarCache() {
        log.info("Iniciando carga proactiva de la caché de parámetros de prioridad...");
        cargarCache();
    }

    /**
     * Obtiene el valor de un parámetro de prioridad desde la caché
     * Si la caché no está inicializada, la carga desde la base de datos
     * Si no encuentra el parámetro en caché, intenta buscarlo en la BD
     */
    public Integer getValorParametro(String claveVariable) {
        if (!cacheInicializado) {
            synchronized (this) {
                if (!cacheInicializado) {
                    cargarCache();
                }
            }
        }

        Integer valor = cacheParametros.get(claveVariable);
        if (valor == null) {
            // Nueva lógica: intentar buscar en BD si no está en caché
            log.warn("Parámetro '{}' no encontrado en caché. Intentando buscar en BD.", claveVariable);
            Optional<ParametroPrioridad> parametroDB = parametroPrioridadRepository.findByClaveVariable(claveVariable);
            if (parametroDB.isPresent()) {
                valor = parametroDB.get().getValor();
                cacheParametros.put(claveVariable, valor); // Actualizar caché
                log.info("Parámetro '{}' encontrado en BD y agregado a caché: {}", claveVariable, valor);
                return valor;
            }
            log.error("Parámetro '{}' no encontrado ni en caché ni en BD. Se devolverá 0.", claveVariable);
            return 0; // Valor por defecto si no se encuentra en ningún lado
        }

        return valor;
    }

    /**
     * Obtiene el valor de un parámetro con un valor por defecto específico
     */
    public Integer getValorParametro(String claveVariable, Integer valorPorDefecto) {
        if (!cacheInicializado) {
            synchronized (this) {
                if (!cacheInicializado) {
                    cargarCache();
                }
            }
        }
        
        return cacheParametros.getOrDefault(claveVariable, valorPorDefecto);
    }

    /**
     * Obtiene todos los parámetros de prioridad
     */
    @Transactional(readOnly = true)
    public List<ParametroPrioridad> obtenerTodosLosParametros() {
        return parametroPrioridadRepository.findAllOrderedByTypeAndKey();
    }

    /**
     * Obtiene un parámetro por su clave
     */
    @Transactional(readOnly = true)
    public Optional<ParametroPrioridad> obtenerParametroPorClave(String claveVariable) {
        return parametroPrioridadRepository.findByClaveVariable(claveVariable);
    }

    /**
     * Actualiza un parámetro de prioridad
     */
    @Transactional
    public ParametroPrioridad actualizarParametro(Long id, Integer nuevoValor, String modificadoPor) {
        ParametroPrioridad parametro = parametroPrioridadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Parámetro no encontrado con ID: " + id));
        
        parametro.setValor(nuevoValor);
        parametro.setModificadoPor(modificadoPor);
        
        ParametroPrioridad parametroActualizado = parametroPrioridadRepository.save(parametro);
        
        // Actualizar la caché
        cacheParametros.put(parametro.getClaveVariable(), nuevoValor);
        
        log.info("Parámetro de prioridad actualizado: {} = {} por {}", 
                parametro.getClaveVariable(), nuevoValor, modificadoPor);
        
        return parametroActualizado;
    }

    /**
     * Actualiza múltiples parámetros de prioridad
     */
    @Transactional
    public List<ParametroPrioridad> actualizarParametros(Map<Long, Integer> parametrosActualizados, String modificadoPor) {
        List<ParametroPrioridad> parametrosModificados = new ArrayList<>();
        
        for (Map.Entry<Long, Integer> entry : parametrosActualizados.entrySet()) {
            Long id = entry.getKey();
            Integer nuevoValor = entry.getValue();
            
            ParametroPrioridad parametro = parametroPrioridadRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("Parámetro no encontrado con ID: " + id));
            
            parametro.setValor(nuevoValor);
            parametro.setModificadoPor(modificadoPor);
            
            ParametroPrioridad parametroActualizado = parametroPrioridadRepository.save(parametro);
            parametrosModificados.add(parametroActualizado);
            
            // Actualizar la caché
            cacheParametros.put(parametro.getClaveVariable(), nuevoValor);
        }
        
        log.info("Actualizados {} parámetros de prioridad por {}", 
                parametrosActualizados.size(), modificadoPor);
        
        return parametrosModificados;
    }

    /**
     * Crea un nuevo parámetro de prioridad
     */
    @Transactional
    public ParametroPrioridad crearParametro(ParametroPrioridad parametro) {
        if (parametroPrioridadRepository.existsByClaveVariable(parametro.getClaveVariable())) {
            throw new DataIntegrityViolationException("Ya existe un parámetro con la clave: " + parametro.getClaveVariable());
        }
        
        ParametroPrioridad parametroGuardado = parametroPrioridadRepository.save(parametro);
        
        // Actualizar la caché
        cacheParametros.put(parametro.getClaveVariable(), parametro.getValor());
        
        log.info("Nuevo parámetro de prioridad creado: {} = {}", 
                parametro.getClaveVariable(), parametro.getValor());
        
        return parametroGuardado;
    }

    /**
     * Invalida y recarga la caché desde la base de datos
     */
    public void recargarCache() {
        synchronized (this) {
            cacheParametros.clear();
            cacheInicializado = false;
            cargarCache();
        }
        log.info("Caché de parámetros de prioridad recargada");
    }

    /**
     * Carga todos los parámetros desde la base de datos a la caché
     */
    private void cargarCache() {
        try {
            List<ParametroPrioridad> parametros = parametroPrioridadRepository.findAll();

            // Verificar si la consulta devolvió parámetros
            if (parametros.isEmpty()) {
                log.warn("¡ATENCIÓN! La caché de parámetros de prioridad se ha cargado vacía. No se encontraron parámetros en la base de datos.");
            }

            Map<String, Integer> nuevaCache = new HashMap<>();
            for (ParametroPrioridad parametro : parametros) {
                nuevaCache.put(parametro.getClaveVariable(), parametro.getValor());
                log.debug("Cargando parámetro en caché: {} = {}", parametro.getClaveVariable(), parametro.getValor());
            }

            cacheParametros.putAll(nuevaCache);
            cacheInicializado = true;

            log.info("Caché de parámetros de prioridad inicializada con {} parámetros", parametros.size());

        } catch (Exception e) {
            log.error("Error al cargar la caché de parámetros de prioridad: {}", e.getMessage(), e);
            // Re-lanzar la excepción original para que el GlobalExceptionHandler la maneje
            throw e;
        }
    }

    /**
     * Obtiene el estado de la caché
     */
    public Map<String, Object> getEstadoCache() {
        Map<String, Object> estado = new HashMap<>();
        estado.put("inicializada", cacheInicializado);
        estado.put("cantidadParametros", cacheParametros.size());
        estado.put("parametros", new HashMap<>(cacheParametros));
        return estado;
    }
}