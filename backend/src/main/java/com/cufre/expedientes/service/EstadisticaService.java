package com.cufre.expedientes.service;

import com.cufre.expedientes.repository.ExpedienteRepository;
import com.cufre.expedientes.repository.UsuarioRepository;
import com.cufre.expedientes.repository.DelitoRepository;
import com.cufre.expedientes.repository.PersonaExpedienteRepository;
import com.cufre.expedientes.repository.ExpedienteDelitoRepository;
import com.cufre.expedientes.dto.DelitoRankingDTO;
import com.cufre.expedientes.model.Expediente;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EstadisticaService {
    private final ExpedienteRepository expedienteRepository;
    private final UsuarioRepository usuarioRepository;
    private final DelitoRepository delitoRepository;
    private final PersonaExpedienteRepository personaExpedienteRepository;
    private final ExpedienteDelitoRepository expedienteDelitoRepository;
    
    /**
     * Obtiene la cantidad de expedientes por provincia
     */
    public Map<String, Long> countByProvincia() {
        List<Object[]> results = expedienteRepository.countByProvincia();
        Map<String, Long> estadisticas = new HashMap<>();
        
        for (Object[] result : results) {
            String provincia = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(provincia != null ? provincia : "Sin datos", count);
        }
        
        return estadisticas;
    }
    
    /**
     * Obtiene la cantidad de expedientes por estado de situación
     */
    public Map<String, Long> countByEstadoSituacion() {
        List<Object[]> results = expedienteRepository.countByEstadoSituacion();
        Map<String, Long> estadisticas = new HashMap<>();
        
        for (Object[] result : results) {
            String estado = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(estado != null ? estado : "Sin datos", count);
        }
        
        return estadisticas;
    }
    
    /**
     * Obtiene la cantidad de expedientes por tipo de captura
     */
    public Map<String, Long> countByTipoCaptura() {
        List<Object[]> results = expedienteRepository.countByTipoCaptura();
        Map<String, Long> estadisticas = new HashMap<>();

        for (Object[] result : results) {
            String tipoCaptura = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(tipoCaptura != null ? tipoCaptura : "Sin datos", count);
        }

        return estadisticas;
    }

    /**
     * Obtiene la cantidad de expedientes por fuerza asignada
     */
    public Map<String, Long> countByFuerzaAsignada() {
        List<Object[]> results = expedienteRepository.countByFuerzaAsignada();
        Map<String, Long> estadisticas = new HashMap<>();

        for (Object[] result : results) {
            String fuerza = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(fuerza != null ? fuerza : "Sin datos", count);
        }

        return estadisticas;
    }

    /**
     * Obtiene la cantidad de expedientes por estado para una fuerza específica
     */
    public Map<String, Long> countByEstadoSituacionAndFuerza(String fuerza) {
        List<Object[]> results = expedienteRepository.countByEstadoSituacionAndFuerza(fuerza);
        Map<String, Long> estadisticas = new HashMap<>();

        for (Object[] result : results) {
            String estado = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(estado != null ? estado : "Sin datos", count);
        }

        return estadisticas;
    }

    /**
     * Obtiene la cantidad de expedientes por fuerza para un estado específico
     */
    public Map<String, Long> countByFuerzaAsignadaAndEstado(String estado) {
        List<Object[]> results = expedienteRepository.countByFuerzaAsignadaAndEstado(estado);
        Map<String, Long> estadisticas = new HashMap<>();

        for (Object[] result : results) {
            String fuerza = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(fuerza != null ? fuerza : "Sin datos", count);
        }

        return estadisticas;
    }
    
    /**
     * Obtiene la cantidad de expedientes por rango de fechas
     */
    public Map<String, Long> countByPeriodo(LocalDate inicio, LocalDate fin) {
        Map<String, Long> estadisticas = new HashMap<>();
        estadisticas.put("total", expedienteRepository.countByPeriodo(inicio, fin));
        return estadisticas;
    }
    
    /**
     * Obtiene un resumen general de todas las estadísticas
     */
    public Map<String, Object> getEstadisticasGenerales() {
        Map<String, Object> estadisticas = new HashMap<>();
        
        estadisticas.put("totalExpedientes", expedienteRepository.count());
        estadisticas.put("porProvincia", countByProvincia());
        estadisticas.put("porEstado", countByEstadoSituacion());
        estadisticas.put("porTipoCaptura", countByTipoCaptura());
        
        return estadisticas;
    }
    
    /**
     * Obtiene datos consolidados para el dashboard
     * @return Map con estadísticas para el dashboard
     */
    public Map<String, Object> getDashboardData() {
        Map<String, Object> dashboardData = new HashMap<>();
        
        // Total de expedientes
        dashboardData.put("totalExpedientes", expedienteRepository.count());
        
        // Total de usuarios
        dashboardData.put("totalUsuarios", usuarioRepository.count());
        
        // Total de personas vinculadas a expedientes
        dashboardData.put("totalPersonas", personaExpedienteRepository.countDistinctPersonasVinculadas());
        
        // Total de delitos
        dashboardData.put("totalDelitos", delitoRepository.count());
        
        // Expedientes por provincia
        dashboardData.put("expedientesPorProvincia", countByProvincia());
        
        // Expedientes por estado
        dashboardData.put("expedientesPorEstado", countByEstadoSituacion());
        
        // Expedientes por tipo de captura
        dashboardData.put("expedientesPorTipoCaptura", countByTipoCaptura());
        
        // Expedientes recientes (último mes)
        LocalDate hoy = LocalDate.now();
        LocalDate mesAnterior = hoy.minusMonths(1);
        dashboardData.put("expedientesRecientes", expedienteRepository.countByPeriodo(mesAnterior, hoy));
        
        // Lista de datos formateados para las tarjetas del dashboard
        List<Map<String, Object>> stats = new java.util.ArrayList<>();
        
        Map<String, Object> totalStats = new HashMap<>();
        totalStats.put("title", "Total Expedientes");
        totalStats.put("value", expedienteRepository.count());
        totalStats.put("icon", "folder");
        totalStats.put("color", "primary");
        stats.add(totalStats);
        
        Map<String, Object> recentStats = new HashMap<>();
        recentStats.put("title", "Expedientes Recientes");
        recentStats.put("value", expedienteRepository.countByPeriodo(mesAnterior, hoy));
        recentStats.put("icon", "recent");
        recentStats.put("color", "success");
        stats.add(recentStats);
        
        Map<String, Object> pendingStats = new HashMap<>();
        pendingStats.put("title", "En Proceso");
        Long pendientes = expedienteRepository.countByEstadoSituacionEquals("EN PROCESO");
        pendingStats.put("value", pendientes != null ? pendientes : 0);
        pendingStats.put("icon", "pending");
        pendingStats.put("color", "warning");
        stats.add(pendingStats);
        
        Map<String, Object> completedStats = new HashMap<>();
        completedStats.put("title", "Completados");
        Long completados = expedienteRepository.countByEstadoSituacionEquals("COMPLETADO");
        completedStats.put("value", completados != null ? completados : 0);
        completedStats.put("icon", "done");
        completedStats.put("color", "info");
        stats.add(completedStats);
        
        dashboardData.put("stats", stats);
        
        return dashboardData;
    }

    /**
     * Ranking de delitos por cantidad de expedientes asociados
     */
    public List<DelitoRankingDTO> rankingDelitos() {
        return expedienteDelitoRepository.countByDelito().stream()
            .map(obj -> new DelitoRankingDTO((String) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }
    
    /**
     * Ranking de delitos con filtros
     */
    public List<DelitoRankingDTO> rankingDelitosWithFilters(String estado, String fuerza, String tipoCaptura) {
        return expedienteDelitoRepository.countByDelitoWithFilters(estado, fuerza, tipoCaptura).stream()
            .map(obj -> new DelitoRankingDTO((String) obj[0], (Long) obj[1]))
            .collect(Collectors.toList());
    }

    /**
     * Devuelve la cantidad de expedientes agrupados por año-mes de ingreso
     */
    public List<Map<String, Object>> countExpedientesPorMes() {
        List<Object[]> results = expedienteRepository.countByMes();
        List<Map<String, Object>> data = new ArrayList<>();
        for (Object[] row : results) {
            Map<String, Object> item = new HashMap<>();
            item.put("month", row[0]);
            item.put("value", row[1]);
            data.add(item);
        }
        return data;
    }

    /**
     * Devuelve la cantidad de expedientes en estado DETENIDO agrupados por fuerza asignada
     */
    public List<Map<String, Object>> countDetenidosPorFuerza() {
        List<Object[]> results = expedienteRepository.countDetenidosPorFuerza();
        List<Map<String, Object>> data = new ArrayList<>();
        for (Object[] row : results) {
            Map<String, Object> item = new HashMap<>();
            item.put("fuerza", row[0]);
            item.put("value", row[1]);
            data.add(item);
        }
        return data;
    }
    
    /**
     * Obtiene el expediente más nuevo con estado CAPTURA VIGENTE
     */
    public Expediente getNewestCapturaVigente() {
        Pageable pageable = PageRequest.of(0, 1);
        List<Expediente> expedientes = expedienteRepository.findNewestCapturaVigente(pageable);
        return expedientes.isEmpty() ? null : expedientes.get(0);
    }
    
    /**
     * Obtiene el expediente más antiguo con estado CAPTURA VIGENTE
     */
    public Expediente getOldestCapturaVigente() {
        Pageable pageable = PageRequest.of(0, 1);
        List<Expediente> expedientes = expedienteRepository.findOldestCapturaVigente(pageable);
        return expedientes.isEmpty() ? null : expedientes.get(0);
    }
    
    /**
     * Obtiene los 3 expedientes con mayor prioridad con estado CAPTURA VIGENTE
     */
    public List<Expediente> getTopPriorityCapturaVigente() {
        Pageable pageable = PageRequest.of(0, 3);
        return expedienteRepository.findTopPriorityCapturaVigente(pageable);
    }
    
    /**
     * Obtiene el total de fuerzas de seguridad únicas
     */
    public Long getTotalFuerzasSeguridad() {
        List<String> fuerzas = expedienteRepository.findDistinctFuerzasAsignadas();
        return (long) fuerzas.size();
    }
    
    /**
     * Obtiene las métricas clave para el centro de comando
     */
    public Map<String, Object> getMetricasClave() {
        Map<String, Object> metricas = new HashMap<>();
        
        // Total de expedientes
        metricas.put("totalExpedientes", expedienteRepository.count());
        
        // Total de usuarios activos (todos los registrados)
        metricas.put("totalUsuarios", usuarioRepository.count());
        
        // Total de personas vinculadas
        metricas.put("totalPersonasVinculadas", personaExpedienteRepository.countDistinctPersonasVinculadas());
        
        // Total de delitos
        metricas.put("totalDelitos", delitoRepository.count());
        
        // Total de fuerzas de seguridad
        metricas.put("totalFuerzasSeguridad", getTotalFuerzasSeguridad());
        
        // Caso más nuevo
        Expediente casoMasNuevo = getNewestCapturaVigente();
        if (casoMasNuevo != null) {
            Map<String, Object> casoNuevo = new HashMap<>();
            casoNuevo.put("id", casoMasNuevo.getId());
            casoNuevo.put("numero", casoMasNuevo.getNumero());
            casoNuevo.put("fechaIngreso", casoMasNuevo.getFechaIngreso());
            casoNuevo.put("descripcion", casoMasNuevo.getDescripcion());
            metricas.put("casoMasNuevo", casoNuevo);
        }
        
        // Caso más antiguo
        Expediente casoMasAntiguo = getOldestCapturaVigente();
        if (casoMasAntiguo != null) {
            Map<String, Object> casoAntiguo = new HashMap<>();
            casoAntiguo.put("id", casoMasAntiguo.getId());
            casoAntiguo.put("numero", casoMasAntiguo.getNumero());
            casoAntiguo.put("fechaIngreso", casoMasAntiguo.getFechaIngreso());
            casoAntiguo.put("descripcion", casoMasAntiguo.getDescripcion());
            metricas.put("casoMasAntiguo", casoAntiguo);
        }
        
        // Top 3 casos con mayor prioridad
        List<Expediente> topPrioridad = getTopPriorityCapturaVigente();
        List<Map<String, Object>> casosPrioridad = new ArrayList<>();
        for (Expediente exp : topPrioridad) {
            Map<String, Object> caso = new HashMap<>();
            caso.put("id", exp.getId());
            caso.put("numero", exp.getNumero());
            caso.put("prioridad", exp.getPrioridad());
            caso.put("descripcion", exp.getDescripcion());
            caso.put("fechaIngreso", exp.getFechaIngreso());
            casosPrioridad.add(caso);
        }
        metricas.put("top3Prioridad", casosPrioridad);
        
        return metricas;
    }
    
    // Métodos con filtros para estadísticas
    
    /**
     * Obtiene la cantidad de expedientes por estado de situación con filtros
     */
    public Map<String, Long> countByEstadoSituacionWithFilters(String fuerza, String tipoCaptura) {
        List<Object[]> results = expedienteRepository.countByEstadoSituacionWithFilters(fuerza, tipoCaptura);
        Map<String, Long> estadisticas = new HashMap<>();
        
        for (Object[] result : results) {
            String estado = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(estado != null ? estado : "Sin datos", count);
        }
        
        return estadisticas;
    }
    
    /**
     * Obtiene la cantidad de expedientes por fuerza asignada con filtros
     */
    public Map<String, Long> countByFuerzaAsignadaWithFilters(String estado, String tipoCaptura) {
        List<Object[]> results = expedienteRepository.countByFuerzaAsignadaWithFilters(estado, tipoCaptura);
        Map<String, Long> estadisticas = new HashMap<>();
        
        for (Object[] result : results) {
            String fuerza = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(fuerza != null ? fuerza : "Sin datos", count);
        }
        
        return estadisticas;
    }
    
    /**
     * Obtiene la cantidad de expedientes por tipo de captura con filtros
     */
    public Map<String, Long> countByTipoCapturaWithFilters(String estado, String fuerza) {
        List<Object[]> results = expedienteRepository.countByTipoCapturaWithFilters(estado, fuerza);
        Map<String, Long> estadisticas = new HashMap<>();
        
        for (Object[] result : results) {
            String tipoCaptura = (String) result[0];
            Long count = (Long) result[1];
            estadisticas.put(tipoCaptura != null ? tipoCaptura : "Sin datos", count);
        }
        
        return estadisticas;
    }
    
    /**
     * Devuelve la cantidad de expedientes agrupados por año-mes con filtros
     */
    public List<Map<String, Object>> countExpedientesPorMesWithFilters(String estado, String fuerza, String tipoCaptura) {
        List<Object[]> results = expedienteRepository.countByMesWithFilters(estado, fuerza, tipoCaptura);
        List<Map<String, Object>> data = new ArrayList<>();
        for (Object[] row : results) {
            Map<String, Object> item = new HashMap<>();
            item.put("month", row[0]);
            item.put("value", row[1]);
            data.add(item);
        }
        return data;
    }
}