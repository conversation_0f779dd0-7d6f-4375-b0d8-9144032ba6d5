package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.DelitoDTO;
import com.cufre.expedientes.dto.ExpedienteDTO;
import com.cufre.expedientes.dto.ExpedienteDelitoDTO;
import com.cufre.expedientes.dto.RecompensaUpdateDTO;
import com.cufre.expedientes.exception.ResourceNotFoundException;
import com.cufre.expedientes.mapper.ExpedienteMapper;
import com.cufre.expedientes.model.Delito;
import com.cufre.expedientes.model.Expediente;
import com.cufre.expedientes.model.ExpedienteDelito;
import com.cufre.expedientes.model.Usuario;
import com.cufre.expedientes.model.enums.Rol;
import com.cufre.expedientes.repository.ExpedienteRepository;
import com.cufre.expedientes.repository.PersonaExpedienteRepository;
import com.cufre.expedientes.repository.UsuarioRepository;
import com.cufre.expedientes.mapper.PersonaExpedienteMapper;
import com.cufre.expedientes.util.PriorityCalculator;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.*;
import jakarta.persistence.criteria.Subquery;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Servicio para la gestión de expedientes
 */
@Service
@Slf4j
public class ExpedienteService extends AbstractBaseService<Expediente, ExpedienteDTO, Long, ExpedienteRepository, ExpedienteMapper> {

    private final DelitoService delitoService;
    private final ExpedienteDelitoService expedienteDelitoService;
    private final PersonaExpedienteRepository personaExpedienteRepository;
    private final PersonaExpedienteMapper personaExpedienteMapper;
    private final ActividadSistemaService actividadSistemaService;
    private final UsuarioRepository usuarioRepository;
    private final PriorityCalculator priorityCalculator;

    public ExpedienteService(
            ExpedienteRepository repository,
            ExpedienteMapper mapper,
            DelitoService delitoService,
            ExpedienteDelitoService expedienteDelitoService,
            PersonaExpedienteRepository personaExpedienteRepository,
            PersonaExpedienteMapper personaExpedienteMapper,
            ActividadSistemaService actividadSistemaService,
            UsuarioRepository usuarioRepository,
            PriorityCalculator priorityCalculator) {
        super(repository, mapper);
        this.delitoService = delitoService;
        this.expedienteDelitoService = expedienteDelitoService;
        this.personaExpedienteRepository = personaExpedienteRepository;
        this.personaExpedienteMapper = personaExpedienteMapper;
        this.actividadSistemaService = actividadSistemaService;
        this.usuarioRepository = usuarioRepository;
        this.priorityCalculator = priorityCalculator;
    }

    @Override
    protected ExpedienteDTO toDto(Expediente entity) {
        ExpedienteDTO dto = mapper.toDto(entity); // Usa ExpedienteMapper para el mapeo base

        // Poblar manualmente los delitos asociados
        if (entity.getId() != null) { // Asegurarse de que la entidad tenga ID para buscar relaciones
            // Obtenemos las relaciones ExpedienteDelito para preservar las asociaciones múltiples
            List<ExpedienteDelitoDTO> expedienteDelitos = expedienteDelitoService.findByExpedienteId(entity.getId());
            
            // Extraemos los delitos de las relaciones preservando duplicados
            List<DelitoDTO> delitos = new ArrayList<>();
            for (ExpedienteDelitoDTO relacion : expedienteDelitos) {
                if (relacion.getDelito() != null) {
                    // Añadimos el delito completo desde la relación
                    delitos.add(relacion.getDelito());
                }
            }
            
            dto.setDelitos(delitos);
        } else {
            dto.setDelitos(new ArrayList<>());
        }

        // Poblar manualmente los nombres de los prófugos/imputados
        if (entity.getPersonaExpedientes() != null && !entity.getPersonaExpedientes().isEmpty()) {
            List<String> profugosNombres = entity.getPersonaExpedientes().stream()
                .filter(pe -> pe.getPersona() != null && pe.getTipoRelacion() != null &&
                               ("Imputado".equalsIgnoreCase(pe.getTipoRelacion().trim()) ||
                                "Profugo".equalsIgnoreCase(pe.getTipoRelacion().trim())))
                .map(pe -> {
                    // Construir nombre completo a partir de la persona asociada
                    String nombre = pe.getPersona().getNombre() != null ? pe.getPersona().getNombre().trim() : "";
                    String apellido = pe.getPersona().getApellido() != null ? pe.getPersona().getApellido().trim() : "";
                    String nombreCompleto = (nombre + " " + apellido).trim();
                    return nombreCompleto.isEmpty() ? null : nombreCompleto;
                })
                .filter(nombreCompleto -> nombreCompleto != null)
                .distinct() // Evitar duplicados si una persona tiene múltiples roles relevantes
                .collect(Collectors.toList());
            dto.setProfugos(profugosNombres);
        } else {
            // Si no hay personas asociadas o la colección es null, inicializar como lista vacía
            dto.setProfugos(new ArrayList<>());
        }
        
        // Ejemplo de cómo podrías poblar otras listas si fuera necesario y si el mapper no las maneja:
        // if (entity.getFotografias() != null && fotografiaMapper != null) { // Suponiendo que tienes un fotografiaMapper inyectado o disponible
        //    dto.setFotografias(entity.getFotografias().stream().map(fotografiaMapper::toDto).collect(Collectors.toList()));
        // } else {
        //    dto.setFotografias(new ArrayList<>());
        // }
        // Similar para documentos si tienes un documentoMapper

        return dto;
    }

    @Override
    protected Expediente toEntity(ExpedienteDTO dto) {
        return mapper.toEntity(dto);
    }

    @Override
    protected Expediente updateEntity(ExpedienteDTO dto, Expediente entity) {
        return mapper.updateEntity(dto, entity);
    }

    @Override
    protected String getEntityName() {
        return "Expediente";
    }
    
    /**
     * Sobrescribe el método findById del servicio base para cargar manualmente
     * las relaciones muchos-a-muchos de personas y expedientes.
     */
    @Override
    @Transactional(readOnly = true)
    public ExpedienteDTO findById(Long id) {
        Expediente entity = ((ExpedienteRepository) repository).findByIdWithRelations(id)
                .orElseThrow(() -> new ResourceNotFoundException("Expediente no encontrado con ID: " + id));

        // Inicializar colecciones explícitamente
        Hibernate.initialize(entity.getFotografias());
        Hibernate.initialize(entity.getDocumentos());

        return toDto(entity);
    }

    /**
     * Busca expedientes por rango de fechas de ingreso
     * @param fechaInicio Fecha de inicio
     * @param fechaFin Fecha de fin
     * @return Lista de expedientes en el rango de fechas
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findByFechaIngresoBetween(LocalDate fechaInicio, LocalDate fechaFin) {
        return repository.findByFechaIngresoBetween(fechaInicio, fechaFin).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Busca expedientes por número de expediente
     * @param numeroExpediente Número de expediente
     * @return Lista de expedientes con el número indicado
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findByNumeroExpediente(String numeroExpediente) {
        return repository.findByNumeroContainingIgnoreCase(numeroExpediente).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Busca expedientes con personaId
     * @param personaId Id de la persona
     * @return Lista de expedientes relacionados con la persona
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findByPersonaId(Long personaId) {
        return repository.findByPersonaId(personaId).stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Busca expedientes por delitoId
     * @param delitoId Id del delito
     * @return Lista de expedientes relacionados con el delito
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findByDelitoId(Long delitoId) {
        List<Expediente> expedientes = ((ExpedienteRepository) repository).findByDelitoId(delitoId);
        return expedientes.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Obtiene estadísticas de expedientes por provincia
     * @return Mapa con provincia y cantidad de expedientes
     */
    @Transactional(readOnly = true)
    public Map<String, Long> getEstadisticasPorProvincia() {
        return repository.countByProvincia().stream()
                .collect(Collectors.toMap(
                        item -> (String) item[0],
                        item -> (Long) item[1]
                ));
    }
    
    /**
     * Obtiene estadísticas de expedientes por período
     * @param fechaInicio Fecha de inicio
     * @param fechaFin Fecha de fin
     * @return Cantidad de expedientes en el período
     */
    @Transactional(readOnly = true)
    public Long getEstadisticasPorPeriodo(LocalDate fechaInicio, LocalDate fechaFin) {
        return repository.countByFechaIngresoBetween(fechaInicio, fechaFin);
    }

    /**
     * Crea un nuevo expediente calculando la prioridad antes de guardar
     */
    @Transactional
    public ExpedienteDTO create(ExpedienteDTO dto) {
        Expediente entity = toEntity(dto);
        
        // Procesar los delitos del DTO y crear las entidades ExpedienteDelito
        if (dto.getDelitos() != null && !dto.getDelitos().isEmpty()) {
            // Asegurar que la colección expedienteDelitos esté inicializada
            if (entity.getExpedienteDelitos() == null) {
                entity.setExpedienteDelitos(new ArrayList<>());
            }
            
            // Crear una nueva entidad ExpedienteDelito por cada DelitoDTO
            for (DelitoDTO delitoDTO : dto.getDelitos()) {
                if (delitoDTO != null && delitoDTO.getId() != null) {
                    ExpedienteDelito expedienteDelito = new ExpedienteDelito();
                    expedienteDelito.setExpediente(entity);
                    
                    // Crear una entidad Delito con el ID para la referencia
                    Delito delito = new Delito();
                    delito.setId(delitoDTO.getId());
                    expedienteDelito.setDelito(delito);
                    
                    // Agregar a la colección del expediente
                    entity.getExpedienteDelitos().add(expedienteDelito);
                }
            }
        }
        
        // Calcular prioridad antes de guardar
        entity.setPrioridad(priorityCalculator.calcularPrioridad(entity));
        entity = repository.save(entity);
        ExpedienteDTO result = toDto(entity);
        // Registrar actividad de creación de expediente
        // La actividad se registra automáticamente por el AuditInterceptor
        return result;
    }

    /**
     * Sobrescribe el método update del servicio base para mantener las relaciones
     * existentes (fotografías, documentos, personas) al actualizar el expediente
     * y sincronizar correctamente las relaciones con delitos permitiendo duplicados intencionales
     */
    @Override
    @Transactional
    public ExpedienteDTO update(Long id, ExpedienteDTO dto) {
        Expediente existingEntity = ((ExpedienteRepository) repository).findByIdWithRelations(id)
                .orElseThrow(() -> new ResourceNotFoundException(getEntityName() + " no encontrado con id: " + id));
        
        // Validar permisos para editar el número de expediente
        validarPermisosCambioNumero(existingEntity, dto);
        
        // Inicializamos las colecciones manualmente para evitar MultipleBagFetchException
        try {
            if (existingEntity.getFotografias() != null) existingEntity.getFotografias().size();
            if (existingEntity.getDocumentos() != null) existingEntity.getDocumentos().size();
            if (existingEntity.getPersonaExpedientes() != null) existingEntity.getPersonaExpedientes().size();
            if (existingEntity.getExpedienteDelitos() != null) existingEntity.getExpedienteDelitos().size();
        } catch (Exception e) {
            log.warn("Error al inicializar colecciones del expediente: {}", e.getMessage());
        }
        
        // Sincronizar los delitos del DTO con la colección expedienteDelitos de la entidad
        if (dto.getDelitos() != null) {
            // Asegurar que la colección expedienteDelitos esté inicializada
            if (existingEntity.getExpedienteDelitos() == null) {
                existingEntity.setExpedienteDelitos(new ArrayList<>());
            }
            
            // Limpiar la colección existente para reemplazarla completamente
            existingEntity.getExpedienteDelitos().clear();
            
            // Crear nuevas entidades ExpedienteDelito basadas en los DelitoDTOs del DTO entrante
            for (DelitoDTO delitoDTO : dto.getDelitos()) {
                if (delitoDTO != null && delitoDTO.getId() != null) {
                    ExpedienteDelito expedienteDelito = new ExpedienteDelito();
                    expedienteDelito.setExpediente(existingEntity);
                    
                    // Crear una entidad Delito con el ID para la referencia
                    Delito delito = new Delito();
                    delito.setId(delitoDTO.getId());
                    expedienteDelito.setDelito(delito);
                    
                    // Agregar a la colección del expediente
                    existingEntity.getExpedienteDelitos().add(expedienteDelito);
                }
            }
        }
        
        Expediente updatedEntity = updateEntity(dto, existingEntity);
        
        // Recalcular prioridad antes de guardar
        updatedEntity.setPrioridad(priorityCalculator.calcularPrioridad(updatedEntity));
        
        updatedEntity = repository.save(updatedEntity);
        ExpedienteDTO result = toDto(updatedEntity);
        
        // Registrar actividad de edición de expediente
        String usuario = obtenerUsuarioActual();
        String mensaje = "Expediente editado: " + result.getNumero();
        
        // Si se cambió el número, registrar actividad específica
        if (!existingEntity.getNumero().equals(dto.getNumero())) {
            mensaje += " (número cambiado de " + existingEntity.getNumero() + " a " + dto.getNumero() + ")";
        }
        
        // La actividad se registra automáticamente por el AuditInterceptor
        return result;
    }

    /**
     * Busca un expediente con toda su información completa, incluyendo relaciones.
     * 
     * @param id El ID del expediente
     * @return DTO completo con la información del expediente y todas sus relaciones cargadas
     */
    @Transactional(readOnly = true)
    public ExpedienteDTO findByIdComplete(Long id) {
        log.debug("Buscando expediente completo con ID: {}", id);
        
        Expediente entity = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Expediente no encontrado con ID: " + id));
        
        // Inicializar manualmente todas las colecciones
        Hibernate.initialize(entity.getPersonaExpedientes());
        Hibernate.initialize(entity.getFotografias());
        Hibernate.initialize(entity.getDocumentos());
        
        return toDto(entity);
    }

    /**
     * Encuentra todos los delitos asociados a un expediente
     * @param expedienteId ID del expediente
     * @return Lista de DelitoDTO
     */
    @Transactional(readOnly = true)
    public List<DelitoDTO> findDelitosByExpedienteId(Long expedienteId) {
        return delitoService.findByExpedienteId(expedienteId);
    }
    
    /**
     * Encuentra todas las fotografías asociadas a un expediente
     * @param expedienteId ID del expediente
     * @return Lista de FotografiaDTO
     */
    @Transactional(readOnly = true)
    public List<com.cufre.expedientes.dto.FotografiaDTO> findFotografiasByExpedienteId(Long expedienteId) {
        log.info("Buscando fotografías para expediente con ID: {}", expedienteId);
        Expediente expediente = repository.findById(expedienteId)
                .orElseThrow(() -> new com.cufre.expedientes.exception.ResourceNotFoundException("Expediente no encontrado con ID: " + expedienteId));
        
        // Inicializar colección de fotografías
        Hibernate.initialize(expediente.getFotografias());
        
        return expediente.getFotografias().stream()
                .map(fotografia -> {
                    com.cufre.expedientes.dto.FotografiaDTO dto = new com.cufre.expedientes.dto.FotografiaDTO();
                    dto.setId(fotografia.getId());
                    dto.setExpedienteId(expedienteId);
                    dto.setRutaArchivo(fotografia.getRutaArchivo());
                    dto.setDescripcion(fotografia.getDescripcion());
                    dto.setNombreArchivo(fotografia.getNombreArchivo());
                    dto.setTipoArchivo(fotografia.getTipoArchivo());
                    dto.setFecha(fotografia.getFecha());
                    return dto;
                })
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Encuentra todos los documentos asociados a un expediente
     * @param expedienteId ID del expediente
     * @return Lista de DocumentoDTO
     */
    @Transactional(readOnly = true)
    public List<com.cufre.expedientes.dto.DocumentoDTO> findDocumentosByExpedienteId(Long expedienteId) {
        log.info("Buscando documentos para expediente con ID: {}", expedienteId);
        Expediente expediente = repository.findById(expedienteId)
                .orElseThrow(() -> new com.cufre.expedientes.exception.ResourceNotFoundException("Expediente no encontrado con ID: " + expedienteId));
        
        // Inicializar colección de documentos
        Hibernate.initialize(expediente.getDocumentos());
        
        return expediente.getDocumentos().stream()
                .map(documento -> {
                    com.cufre.expedientes.dto.DocumentoDTO dto = new com.cufre.expedientes.dto.DocumentoDTO();
                    dto.setId(documento.getId());
                    dto.setExpedienteId(expedienteId);
                    dto.setRutaArchivo(documento.getRutaArchivo());
                    dto.setTipo(documento.getTipo());
                    dto.setDescripcion(documento.getDescripcion());
                    dto.setNombreArchivo(documento.getNombreArchivo());
                    dto.setTipoArchivo(documento.getTipoArchivo());
                    dto.setFecha(documento.getFecha());
                    dto.setTamanio(documento.getTamanio());
                    return dto;
                })
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Encuentra todas las personas asociadas a un expediente
     * @param expedienteId ID del expediente
     * @return Lista de PersonaExpedienteDTO
     */
    @Transactional(readOnly = true)
    public List<com.cufre.expedientes.dto.PersonaExpedienteDTO> findPersonasByExpedienteId(Long expedienteId) {
        log.info("Buscando personas para expediente con ID: {} (usando EntityGraph)", expedienteId);
        return personaExpedienteRepository.findByExpedienteId(expedienteId)
            .stream()
            .map(personaExpedienteMapper::toDto)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Asocia un delito a un expediente
     * @param expedienteDelitoDTO Datos de la relación
     * @return DTO con los datos de la relación creada
     */
    @Transactional
    public ExpedienteDelitoDTO addDelito(ExpedienteDelitoDTO expedienteDelitoDTO) {
        log.info("Asociando delito: expedienteId={}, delitoId={}", expedienteDelitoDTO.getExpedienteId(), expedienteDelitoDTO.getDelitoId());
        return expedienteDelitoService.save(expedienteDelitoDTO);
    }
    
    /**
     * Elimina la relación entre un expediente y un delito
     * @param expedienteId ID del expediente
     * @param delitoId ID del delito
     */
    @Transactional
    public void removeDelito(Long expedienteId, Long delitoId) {
        // Buscar la relación por expedienteId y delitoId
        List<ExpedienteDelitoDTO> relaciones = expedienteDelitoService.findByExpedienteId(expedienteId);
        
        for (ExpedienteDelitoDTO relacion : relaciones) {
            if (relacion.getDelitoId().equals(delitoId)) {
                expedienteDelitoService.delete(relacion.getId());
                break;
            }
        }
    }

    @Transactional(readOnly = true)
    public ExpedienteDTO getExpedienteById(Long id) {
        log.info("Buscando expediente con ID: {}", id);
        Expediente expediente = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Expediente no encontrado con ID: " + id));
        
        // Cargar explícitamente las colecciones para asegurar que estén inicializadas
        Hibernate.initialize(expediente.getFotografias());
        Hibernate.initialize(expediente.getDocumentos());
        Hibernate.initialize(expediente.getPersonaExpedientes());
        
        // Mapear a DTO incluyendo toda la información relacionada
        return toDto(expediente);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findAll() {
        List<Expediente> expedientes = repository.findAll();
        List<ExpedienteDTO> dtos = expedientes.stream().map(this::toDto).collect(java.util.stream.Collectors.toList());
        // Poblar el campo 'profugos' en cada DTO
        for (int i = 0; i < expedientes.size(); i++) {
            Expediente expediente = expedientes.get(i);
            ExpedienteDTO dto = dtos.get(i);
            if (expediente.getPersonaExpedientes() != null) {
                List<String> profugos = expediente.getPersonaExpedientes().stream()
                    .filter(pe -> pe.getTipoRelacion() != null && pe.getTipoRelacion().trim().equalsIgnoreCase("Imputado"))
                    .map(pe -> {
                        if (pe.getPersona() != null) {
                            String nombre = pe.getPersona().getNombre() != null ? pe.getPersona().getNombre() : "";
                            String apellido = pe.getPersona().getApellido() != null ? pe.getPersona().getApellido() : "";
                            return (nombre + " " + apellido).trim();
                        }
                        return null;
                    })
                    .filter(n -> n != null && !n.isEmpty())
                    .collect(java.util.stream.Collectors.toList());
                dto.setProfugos(profugos);
            }
        }
        return dtos;
    }

    /**
     * Actualiza la foto principal de un expediente
     */
    @Transactional
    public void setFotoPrincipal(Long expedienteId, Long fotoId) {
        Expediente expediente = repository.findById(expedienteId)
            .orElseThrow(() -> new com.cufre.expedientes.exception.ResourceNotFoundException("Expediente no encontrado con ID: " + expedienteId));
        expediente.setFotoPrincipalId(fotoId);
        repository.save(expediente);
    }

    /**
     * Devuelve los expedientes más buscados (mayor prioridad primero) solo con estado CAPTURA VIGENTE
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> findMasBuscados(int limit) {
        List<Expediente> expedientes = repository.findAllByEstadoSituacionOrderByPrioridadDesc("CAPTURA VIGENTE", PageRequest.of(0, limit));
        return expedientes.stream().map(this::toDto).collect(Collectors.toList());
    }

    /**
     * Búsqueda avanzada de expedientes y personas
     */
    @Transactional(readOnly = true)
    public List<ExpedienteDTO> busquedaAvanzada(String nombre, String apellido, String numeroExpediente, String tipoBusqueda, String numeroIdentificacion) {
        List<Expediente> expedientesPorNumero = new ArrayList<>();
        List<Expediente> expedientesPorPersona = new ArrayList<>();
        boolean buscarPorExpediente = tipoBusqueda == null || tipoBusqueda.equalsIgnoreCase("expediente") || tipoBusqueda.equalsIgnoreCase("ambos");
        boolean buscarPorPersona = tipoBusqueda == null || tipoBusqueda.equalsIgnoreCase("persona") || tipoBusqueda.equalsIgnoreCase("ambos");

        // Buscar por número de expediente
        if (buscarPorExpediente && numeroExpediente != null && !numeroExpediente.isEmpty()) {
            expedientesPorNumero = repository.findByNumeroContainingIgnoreCase(numeroExpediente);
            // Si no encuentra nada, intenta buscar quitando ceros a la izquierda
            if (expedientesPorNumero.isEmpty()) {
                String sinCeros = numeroExpediente.replaceFirst("^0+(?!$)", "");
                if (!sinCeros.equals(numeroExpediente)) {
                    expedientesPorNumero = repository.findByNumeroContainingIgnoreCase(sinCeros);
                }
            }
        }

        // Buscar por persona asociada
        if (buscarPorPersona && ( (nombre != null && !nombre.isEmpty()) || (apellido != null && !apellido.isEmpty()) || (numeroIdentificacion != null && !numeroIdentificacion.isEmpty()) )) {
            expedientesPorPersona = personaExpedienteRepository.findExpedientesByPersonaDatos(nombre, apellido, numeroIdentificacion);
        }

        // Unir resultados sin duplicados
        List<Expediente> resultado = new ArrayList<>();
        if (!expedientesPorNumero.isEmpty()) resultado.addAll(expedientesPorNumero);
        for (Expediente e : expedientesPorPersona) {
            if (resultado.stream().noneMatch(x -> x.getId().equals(e.getId()))) {
                resultado.add(e);
            }
        }
        // Si no hay filtros, retorna vacío
        if (resultado.isEmpty() && ( (numeroExpediente == null || numeroExpediente.isEmpty()) && (nombre == null || nombre.isEmpty()) && (apellido == null || apellido.isEmpty()) && (numeroIdentificacion == null || numeroIdentificacion.isEmpty()) )) {
            return new ArrayList<>();
        }
        // Mapear a DTO
        return resultado.stream().map(this::toDto).collect(Collectors.toList());
    }

    /**
     * Obtiene el email del usuario autenticado actual
     */
    private String obtenerUsuarioActual() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String nombre = authentication.getName();
            if (nombre != null && !nombre.equalsIgnoreCase("anonymousUser")) {
                return nombre;
            }
        }
        return "SISTEMA";
    }

    @Override
    @Transactional
    public ExpedienteDTO save(ExpedienteDTO dto) {
        return this.create(dto);
    }

    @Transactional(readOnly = true)
    public org.springframework.data.domain.Page<ExpedienteDTO> findPaginated(int page, int size) {
        // Crear un PageRequest con ordenación por ID en orden descendente
        // Esto hará que los expedientes con IDs más altos (generalmente los más recientes) aparezcan primero
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "id"));
        
        org.springframework.data.domain.Page<Expediente> pageResult = repository.findAll(pageRequest);
        return pageResult.map(expediente -> {
            ExpedienteDTO dto = toDto(expediente);
            if (expediente.getPersonaExpedientes() != null) {
                List<String> profugos = expediente.getPersonaExpedientes().stream()
                    .filter(pe -> pe.getTipoRelacion() != null && pe.getTipoRelacion().trim().equalsIgnoreCase("Imputado"))
                    .map(pe -> {
                        if (pe.getPersona() != null) {
                            String nombre = pe.getPersona().getNombre() != null ? pe.getPersona().getNombre() : "";
                            String apellido = pe.getPersona().getApellido() != null ? pe.getPersona().getApellido() : "";
                            return (nombre + " " + apellido).trim();
                        }
                        return null;
                    })
                    .filter(n -> n != null && !n.isEmpty())
                    .collect(java.util.stream.Collectors.toList());
                dto.setProfugos(profugos);
            }
            return dto;
        });
    }

    @Transactional(readOnly = true)
    public org.springframework.data.domain.Page<ExpedienteDTO> findPaginatedWithFilters(
            int page, int size, String fuerzaAsignada, String estadoSituacion,
            String numero, String profugo, String fechaDesde, String fechaHasta,
            String delitoNombre, String sort, String direction) {
        
        Specification<Expediente> spec = Specification.where(null);
        if (fuerzaAsignada != null && !fuerzaAsignada.isEmpty()) {
            if ("S/D".equalsIgnoreCase(fuerzaAsignada.trim())) {
                // Para 'S/D', buscamos registros donde fuerzaAsignada es null o vacío
                spec = spec.and((root, query, cb) -> cb.or(
                    cb.isNull(root.get("fuerzaAsignada")),
                    cb.equal(cb.trim(root.get("fuerzaAsignada")), ""),
                    cb.equal(cb.upper(cb.trim(root.get("fuerzaAsignada"))), "S/D")
                ));
            } else {
                // Para otras fuerzas, usamos el filtro normal
                spec = spec.and((root, query, cb) -> cb.equal(cb.upper(cb.trim(root.get("fuerzaAsignada"))), fuerzaAsignada.trim().toUpperCase()));
            }
        }
        if (estadoSituacion != null && !estadoSituacion.isEmpty()) {
            if ("SIN DATO".equalsIgnoreCase(estadoSituacion.trim())) {
                // Para 'SIN DATO', buscamos registros donde estadoSituacion es null o vacío
                spec = spec.and((root, query, cb) -> cb.or(
                    cb.isNull(root.get("estadoSituacion")),
                    cb.equal(cb.trim(root.get("estadoSituacion")), ""),
                    cb.equal(cb.upper(cb.trim(root.get("estadoSituacion"))), "SIN DATO")
                ));
            } else {
                // Para otros estados, usamos el filtro normal
                spec = spec.and((root, query, cb) -> cb.equal(cb.upper(cb.trim(root.get("estadoSituacion"))), estadoSituacion.trim().toUpperCase()));
            }
        }
        if (numero != null && !numero.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("numero")), "%" + numero.toLowerCase() + "%"));
        }
        if (fechaDesde != null && !fechaDesde.isEmpty()) {
            LocalDate desde = LocalDate.parse(fechaDesde, DateTimeFormatter.ISO_DATE);
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("fechaIngreso"), desde));
        }
        if (fechaHasta != null && !fechaHasta.isEmpty()) {
            LocalDate hasta = LocalDate.parse(fechaHasta, DateTimeFormatter.ISO_DATE);
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("fechaIngreso"), hasta));
        }
        if (profugo != null && !profugo.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                // Join a personaExpedientes y persona
                var joinPE = root.join("personaExpedientes", JoinType.LEFT);
                var joinPersona = joinPE.join("persona", JoinType.LEFT);
                // Solo tipoRelacion = 'Imputado'
                var tipoRelacionPredicate = cb.equal(cb.upper(cb.trim(joinPE.get("tipoRelacion"))), "IMPUTADO");
                // Buscar por nombre o apellido
                var nombrePredicate = cb.like(cb.lower(joinPersona.get("nombre")), "%" + profugo.toLowerCase() + "%");
                var apellidoPredicate = cb.like(cb.lower(joinPersona.get("apellido")), "%" + profugo.toLowerCase() + "%");
                return cb.and(tipoRelacionPredicate, cb.or(nombrePredicate, apellidoPredicate));
            });
        }
        if (delitoNombre != null && !delitoNombre.isEmpty()) {
            if ("SIN_DATO".equalsIgnoreCase(delitoNombre.trim())) {
                // Para 'SIN_DATO', buscamos expedientes que no tienen delitos asociados
                spec = spec.and((root, query, cb) -> {
                    // Subconsulta para verificar que no existen ExpedienteDelito para este expediente
                    Subquery<Long> subquery = query.subquery(Long.class);
                    Root<ExpedienteDelito> subRoot = subquery.from(ExpedienteDelito.class);
                    subquery.select(subRoot.get("expediente").get("id"))
                           .where(cb.equal(subRoot.get("expediente").get("id"), root.get("id")));
                    return cb.not(cb.exists(subquery));
                });
            } else {
                // Para delitos específicos, buscar por nombre del delito
                spec = spec.and((root, query, cb) -> {
                    // Join a expedienteDelitos y delito
                    var joinED = root.join("expedienteDelitos", JoinType.LEFT);
                    var joinDelito = joinED.join("delito", JoinType.LEFT);
                    // Buscar por nombre del delito (coincidencia parcial, case insensitive)
                    return cb.like(cb.lower(joinDelito.get("nombre")), "%" + delitoNombre.toLowerCase() + "%");
                });
            }
        }
        
        // Determinar la dirección de ordenación
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        
        // Crear un PageRequest con la ordenación especificada
        // Por defecto, ordenar por ID en orden descendente
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sort != null && !sort.isEmpty() ? sort : "id"));
        
        Page<Expediente> pageResult = ((ExpedienteRepository) repository).findAll(spec, pageRequest);
        return pageResult.map(this::toDto);
    }

    /**
     * Valida los permisos para cambiar el número de expediente
     * @param existingEntity Expediente existente
     * @param dto DTO con los nuevos datos
     * @throws AccessDeniedException Si el usuario no tiene permisos para cambiar el número
     * @throws IllegalArgumentException Si el nuevo número ya existe
     */
    private void validarPermisosCambioNumero(Expediente existingEntity, ExpedienteDTO dto) {
        // Si el número no cambió, no hay nada que validar
        if (existingEntity.getNumero().equals(dto.getNumero())) {
            return;
        }

        // Obtener el usuario actual
        Usuario usuarioActual = obtenerUsuarioActualCompleto();
        
        // Verificar si el usuario tiene permisos para cambiar el número
        boolean tienePermisos = usuarioActual != null &&
                               (usuarioActual.getRol() == Rol.SUPERUSUARIO ||
                                usuarioActual.getRol() == Rol.ADMINISTRADOR);
        
        if (!tienePermisos) {
            log.warn("Usuario {} intentó cambiar número de expediente sin permisos",
                    usuarioActual != null ? usuarioActual.getEmail() : "DESCONOCIDO");
            throw new AccessDeniedException("No tiene permisos para modificar el número de expediente");
        }

        // Validar que el nuevo número sea único
        Optional<Expediente> expedienteConMismoNumero = repository.findByNumero(dto.getNumero());
        if (expedienteConMismoNumero.isPresent() &&
            !expedienteConMismoNumero.get().getId().equals(existingEntity.getId())) {
            log.warn("Intento de cambiar número de expediente a uno ya existente: {}", dto.getNumero());
            throw new IllegalArgumentException("El número de expediente " + dto.getNumero() + " ya existe");
        }

        log.info("Usuario {} cambió número de expediente de {} a {}",
                usuarioActual.getEmail(), existingEntity.getNumero(), dto.getNumero());
    }

    /**
     * Obtiene el usuario completo autenticado actual
     * @return Usuario completo o null si no está autenticado
     */
    private Usuario obtenerUsuarioActualCompleto() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String email = authentication.getName();
            if (email != null && !email.equalsIgnoreCase("anonymousUser")) {
                return usuarioRepository.findByEmail(email).orElse(null);
            }
        }
        return null;
    }

    /**
     * Obtiene expedientes para P.N Recompensas con filtros y paginación
     */
    @Transactional(readOnly = true)
    public Page<ExpedienteDTO> findPNRecompensas(
            org.springframework.data.domain.Pageable pageable,
            String profugo,
            String numero,
            String fuerzaAsignada,
            String estadoSituacion,
            LocalDate fechaDesde,
            LocalDate fechaHasta,
            Long delitoId
    ) {
        // Reutilizar la lógica de filtros existente
        Specification<Expediente> spec = Specification.where(null);
        
        // Aplicar filtros (reutilizando lógica del método findAllPaginado)
        if (fuerzaAsignada != null && !fuerzaAsignada.isEmpty()) {
            if (!fuerzaAsignada.equals("S/D")) {
                spec = spec.and((root, query, cb) -> cb.equal(cb.upper(cb.trim(root.get("fuerzaAsignada"))), fuerzaAsignada.trim().toUpperCase()));
            }
        }
        
        if (estadoSituacion != null && !estadoSituacion.isEmpty()) {
            if (!estadoSituacion.equals("SIN DATO")) {
                spec = spec.and((root, query, cb) -> cb.equal(cb.upper(cb.trim(root.get("estadoSituacion"))), estadoSituacion.trim().toUpperCase()));
            }
        }
        
        if (numero != null && !numero.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("numero")), "%" + numero.toLowerCase() + "%"));
        }
        
        if (fechaDesde != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("fechaIngreso"), fechaDesde));
        }
        
        if (fechaHasta != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("fechaIngreso"), fechaHasta));
        }
        
        if (profugo != null && !profugo.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<com.cufre.expedientes.model.PersonaExpediente> peRoot = subquery.from(com.cufre.expedientes.model.PersonaExpediente.class);
                subquery.select(peRoot.get("expediente").get("id"));
                subquery.where(cb.or(
                    cb.like(cb.lower(peRoot.get("persona").get("nombre")), "%" + profugo.toLowerCase() + "%"),
                    cb.like(cb.lower(peRoot.get("persona").get("apellido")), "%" + profugo.toLowerCase() + "%")
                ));
                return cb.in(root.get("id")).value(subquery);
            });
        }
        
        if (delitoId != null) {
            spec = spec.and((root, query, cb) -> {
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<ExpedienteDelito> edRoot = subquery.from(ExpedienteDelito.class);
                subquery.select(edRoot.get("expediente").get("id"));
                subquery.where(cb.equal(edRoot.get("delito").get("id"), delitoId));
                return cb.in(root.get("id")).value(subquery);
            });
        }
        
        Page<Expediente> pageResult = repository.findAll(spec, pageable);
        return pageResult.map(this::toDto);
    }

    /**
     * Actualiza la recompensa de un expediente
     */
    @Transactional
    public ExpedienteDTO actualizarRecompensa(Long id, RecompensaUpdateDTO recompensaDto) {
        Expediente expediente = repository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Expediente no encontrado con ID: " + id));
        
        // Actualizar campos de recompensa
        expediente.setRecompensa(recompensaDto.getRecompensa());
        expediente.setMontoRecompensa(recompensaDto.getMontoRecompensa());
        
        // Recalcular prioridad usando PriorityCalculator
        Integer nuevaPrioridad = priorityCalculator.calcularPrioridad(expediente);
        expediente.setPrioridad(nuevaPrioridad);
        
        // Guardar cambios
        Expediente expedienteActualizado = repository.save(expediente);
        
        // Registrar actividad del sistema (opcional - si existe el servicio)
        try {
            String usuarioActual = obtenerUsuarioActual();
            log.info("Usuario {} actualizó recompensa del expediente {}: recompensa={}, monto={}",
                usuarioActual, id, recompensaDto.getRecompensa(), recompensaDto.getMontoRecompensa());
        } catch (Exception e) {
            log.warn("No se pudo registrar la actividad del sistema: {}", e.getMessage());
        }
        
        return toDto(expedienteActualizado);
    }
}