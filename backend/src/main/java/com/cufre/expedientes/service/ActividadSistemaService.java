package com.cufre.expedientes.service;

import com.cufre.expedientes.dto.ActividadDetalleDTO;
import com.cufre.expedientes.dto.ActividadSistemaDTO;
import com.cufre.expedientes.dto.FiltroActividadDTO;
import com.cufre.expedientes.model.ActividadDetalle;
import com.cufre.expedientes.model.ActividadSistema;
import com.cufre.expedientes.repository.ActividadDetalleRepository;
import com.cufre.expedientes.repository.ActividadSistemaRepository;
import com.cufre.expedientes.repository.UsuarioRepository;
import com.cufre.expedientes.model.Usuario;
import com.cufre.expedientes.exception.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ActividadSistemaService {
    
    @Autowired
    private ActividadSistemaRepository actividadSistemaRepository;
    
    @Autowired
    private ActividadDetalleRepository actividadDetalleRepository;
    
    @Autowired
    private UsuarioRepository usuarioRepository;

    /**
     * Registra una nueva actividad en el sistema (método original mantenido para compatibilidad).
     * @param usuario Usuario que realiza la acción (email o nombre)
     * @param tipoAccion Tipo de acción (LOGIN, CREAR_EXPEDIENTE, etc.)
     * @param detalles Detalles adicionales (opcional)
     */
    public void registrarActividad(String usuario, String tipoAccion, String detalles) {
        ActividadSistema actividad = new ActividadSistema();
        actividad.setUsuario(usuario);
        actividad.setTipoAccion(tipoAccion);
        actividad.setFechaHora(LocalDateTime.now());
        actividad.setDetalles(detalles);
        
        // Determinar módulo y categoría basado en el tipo de acción
        determinarModuloYCategoria(actividad, tipoAccion);
        actividad.setEstadoRespuesta(ActividadSistema.EstadoRespuesta.SUCCESS);
        
        actividadSistemaRepository.save(actividad);
    }

    /**
     * Registra una actividad completa con información detallada.
     */
    public ActividadSistema registrarActividadCompleta(String usuario, String tipoAccion, String detalles,
                                                      String ipCliente, String userAgent, String sessionId,
                                                      String endpoint, String metodoHttp, Long duracionMs,
                                                      String estadoRespuesta) {
        ActividadSistema actividad = new ActividadSistema();
        actividad.setUsuario(usuario);
        actividad.setTipoAccion(tipoAccion);
        actividad.setFechaHora(LocalDateTime.now());
        actividad.setDetalles(detalles);
        actividad.setIpCliente(ipCliente);
        actividad.setUserAgent(userAgent);
        actividad.setSessionId(sessionId);
        actividad.setEndpoint(endpoint);
        actividad.setMetodoHttp(metodoHttp);
        actividad.setDuracionMs(duracionMs);
        actividad.setEstadoRespuesta(estadoRespuesta != null ? estadoRespuesta : ActividadSistema.EstadoRespuesta.SUCCESS);
        
        // Determinar módulo y categoría
        determinarModuloYCategoria(actividad, tipoAccion);
        
        return actividadSistemaRepository.save(actividad);
    }

    /**
     * Registra detalles adicionales para una actividad.
     */
    public void registrarDetalle(Long actividadId, String tipoDetalle, String contenidoJson) {
        ActividadDetalle detalle = new ActividadDetalle(actividadId, tipoDetalle, contenidoJson);
        actividadDetalleRepository.save(detalle);
    }

    /**
     * Obtiene todas las actividades con paginación y filtros.
     */
    public Page<ActividadSistemaDTO> obtenerActividadesConFiltros(FiltroActividadDTO filtro) {
        Pageable pageable = crearPageable(filtro);
        
        Page<ActividadSistema> actividades = actividadSistemaRepository.findWithFilters(
            filtro.getUsuario(),
            filtro.getModulo(),
            filtro.getCategoriaAccion(),
            filtro.getFechaInicio(),
            filtro.getFechaFin(),
            filtro.getIpCliente(),
            filtro.getEstadoRespuesta(),
            pageable
        );
        
        return actividades.map(this::convertirADTO);
    }

    /**
     * Obtiene una actividad específica por ID con sus detalles.
     */
    public Optional<ActividadSistemaDTO> obtenerActividadPorId(Long id) {
        Optional<ActividadSistema> actividad = actividadSistemaRepository.findById(id);
        
        if (actividad.isPresent()) {
            ActividadSistemaDTO dto = convertirADTO(actividad.get());
            
            // Cargar detalles adicionales
            List<ActividadDetalle> detalles = actividadDetalleRepository.findByActividadIdOrderByFechaCreacionAsc(id);
            List<ActividadDetalleDTO> detallesDTO = detalles.stream()
                .map(this::convertirDetalleADTO)
                .collect(Collectors.toList());
            
            dto.setActividadDetalles(detallesDTO);
            return Optional.of(dto);
        }
        
        return Optional.empty();
    }

    /**
     * Obtiene todas las actividades (método original mantenido para compatibilidad).
     */
    public List<ActividadSistema> obtenerTodas() {
        return actividadSistemaRepository.findAll();
    }

    /**
     * Obtiene estadísticas de actividad por módulo.
     */
    public List<Object[]> obtenerEstadisticasPorModulo() {
        return actividadSistemaRepository.getEstadisticasPorModulo();
    }

    /**
     * Obtiene estadísticas de actividad por usuario.
     */
    public List<Object[]> obtenerEstadisticasPorUsuario() {
        return actividadSistemaRepository.getEstadisticasPorUsuario();
    }

    /**
     * Obtiene actividades recientes (últimas 24 horas).
     */
    public List<ActividadSistema> obtenerActividadesRecientes() {
        LocalDateTime fechaLimite = LocalDateTime.now().minusHours(24);
        return actividadSistemaRepository.getActividadesRecientes(fechaLimite);
    }

    /**
     * Obtiene actividades sospechosas para una IP.
     */
    public List<ActividadSistema> obtenerActividadesSospechosas(String ip) {
        LocalDateTime fechaLimite = LocalDateTime.now().minusHours(1);
        return actividadSistemaRepository.getActividadesSospechosas(ip, fechaLimite);
    }

    /**
     * Obtiene notificaciones no vistas para un usuario específico
     */
    public List<ActividadSistemaDTO> obtenerNotificacionesNoVistas(String emailUsuario, int limite) {
        Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
            .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
        
        LocalDateTime ultimaVisita = usuario.getUltimaVisitaNotificaciones();
        
        Page<ActividadSistema> actividades = actividadSistemaRepository
            .findByModuloAndFechaHoraAfterOrderByFechaHoraDesc(
                ActividadSistema.Modulo.EXPEDIENTES,
                ultimaVisita,
                PageRequest.of(0, limite)
            );
        
        return actividades.getContent().stream()
            .map(this::convertirADTO)
            .collect(Collectors.toList());
    }

    /**
     * Cuenta notificaciones no vistas
     */
    public long contarNotificacionesNoVistas(String emailUsuario) {
        Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
            .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
        
        return actividadSistemaRepository
            .countByModuloAndFechaHoraAfter(
                ActividadSistema.Modulo.EXPEDIENTES,
                usuario.getUltimaVisitaNotificaciones()
            );
    }

    /**
     * Marca todas las notificaciones como vistas
     */
    @Transactional
    public void marcarNotificacionesComoVistas(String emailUsuario) {
        Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
            .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
        
        usuario.setUltimaVisitaNotificaciones(LocalDateTime.now());
        usuarioRepository.save(usuario);
    }

    /**
     * Elimina registros de actividad anteriores a un número específico de días
     */
    @Transactional
    public long eliminarRegistrosAntiguos(int dias) {
        LocalDateTime fechaLimite = LocalDateTime.now().minusDays(dias);
        return actividadSistemaRepository.deleteByFechaHoraBefore(fechaLimite);
    }

    // Métodos privados de utilidad

    private void determinarModuloYCategoria(ActividadSistema actividad, String tipoAccion) {
        if (tipoAccion.contains("EXPEDIENTE")) {
            actividad.setModulo(ActividadSistema.Modulo.EXPEDIENTES);
        } else if (tipoAccion.contains("USUARIO") || tipoAccion.contains("ROL")) {
            actividad.setModulo(ActividadSistema.Modulo.USUARIOS);
        } else if (tipoAccion.contains("LOGIN") || tipoAccion.contains("LOGOUT") || tipoAccion.contains("SESION")) {
            actividad.setModulo(ActividadSistema.Modulo.SISTEMA);
        } else if (tipoAccion.contains("REPORTE") || tipoAccion.contains("ESTADISTICA") || tipoAccion.contains("EXPORTAR")) {
            actividad.setModulo(ActividadSistema.Modulo.REPORTES);
        } else {
            actividad.setModulo(ActividadSistema.Modulo.SISTEMA);
        }
        
        actividad.setCategoriaAccion(tipoAccion);
    }

    private Pageable crearPageable(FiltroActividadDTO filtro) {
        Sort.Direction direction = "asc".equalsIgnoreCase(filtro.getSortDirection())
            ? Sort.Direction.ASC
            : Sort.Direction.DESC;
        
        Sort sort = Sort.by(direction, filtro.getSortBy());
        return PageRequest.of(filtro.getPage(), filtro.getSize(), sort);
    }

    private ActividadSistemaDTO convertirADTO(ActividadSistema actividad) {
        return new ActividadSistemaDTO(
            actividad.getId(),
            actividad.getUsuario(),
            actividad.getTipoAccion(),
            actividad.getFechaHora(),
            actividad.getDetalles(),
            actividad.getIpCliente(),
            actividad.getUserAgent(),
            actividad.getSessionId(),
            actividad.getEndpoint(),
            actividad.getMetodoHttp(),
            actividad.getModulo(),
            actividad.getCategoriaAccion(),
            actividad.getDuracionMs(),
            actividad.getEstadoRespuesta()
        );
    }

    private ActividadDetalleDTO convertirDetalleADTO(ActividadDetalle detalle) {
        return new ActividadDetalleDTO(
            detalle.getId(),
            detalle.getActividadId(),
            detalle.getTipoDetalle(),
            detalle.getContenidoJson(),
            detalle.getFechaCreacion()
        );
    }
}