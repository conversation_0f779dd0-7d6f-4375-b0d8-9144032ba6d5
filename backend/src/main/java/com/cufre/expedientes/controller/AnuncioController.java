package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.AnuncioDTO;
import com.cufre.expedientes.dto.CrearAnuncioDTO;
import com.cufre.expedientes.exception.ResourceNotFoundException;
import com.cufre.expedientes.service.AnuncioService;
import com.cufre.expedientes.service.UsuarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Controlador para operaciones relacionadas con anuncios globales.
 */
@RestController
@RequestMapping("/anuncios")
@Slf4j
public class AnuncioController {

    private final AnuncioService anuncioService;
    private final UsuarioService usuarioService;

    public AnuncioController(AnuncioService anuncioService, UsuarioService usuarioService) {
        this.anuncioService = anuncioService;
        this.usuarioService = usuarioService;
    }

    /**
     * Obtiene todos los anuncios (solo para administradores)
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<List<AnuncioDTO>> obtenerTodos() {
        log.debug("Obteniendo todos los anuncios");
        List<AnuncioDTO> anuncios = anuncioService.obtenerTodos();
        return ResponseEntity.ok(anuncios);
    }

    /**
     * Obtiene un anuncio por ID (solo para administradores)
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<AnuncioDTO> obtenerPorId(@PathVariable Long id) {
        log.debug("Obteniendo anuncio con ID: {}", id);
        AnuncioDTO anuncio = anuncioService.obtenerPorId(id);
        return ResponseEntity.ok(anuncio);
    }

    /**
     * Obtiene el anuncio activo para el usuario actual
     * Este endpoint es usado por el frontend para mostrar el modal
     */
    @GetMapping("/activo")
    public ResponseEntity<AnuncioDTO> obtenerAnuncioActivo() {
        log.debug("Iniciando obtención de anuncio activo");
        // Obtener el ID del usuario actual
        Long usuarioId = obtenerUsuarioActualId();
        
        log.debug("Obteniendo anuncio activo para usuario ID: {}", usuarioId);
        AnuncioDTO anuncio = anuncioService.obtenerAnuncioActivoParaUsuario(usuarioId);
        log.debug("Anuncio obtenido: {}", anuncio != null ? "encontrado" : "no encontrado");
        
        if (anuncio == null) {
            return ResponseEntity.noContent().build();
        }
        
        return ResponseEntity.ok(anuncio);
    }

    /**
     * Crea un nuevo anuncio (solo para administradores)
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<AnuncioDTO> crear(@Valid @RequestBody CrearAnuncioDTO crearAnuncioDTO) {
        log.debug("Request para crear anuncio recibido: {}", crearAnuncioDTO);
        log.info("Creando nuevo anuncio: {}", crearAnuncioDTO.getTitulo());
        AnuncioDTO anuncioCreado = anuncioService.crear(crearAnuncioDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(anuncioCreado);
    }

    /**
     * Actualiza un anuncio existente (solo para administradores)
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<AnuncioDTO> actualizar(@PathVariable Long id, @Valid @RequestBody CrearAnuncioDTO anuncioDTO) {
        log.info("Actualizando anuncio con ID: {}", id);
        AnuncioDTO anuncioActualizado = anuncioService.actualizar(id, anuncioDTO);
        return ResponseEntity.ok(anuncioActualizado);
    }

    /**
     * Activa un anuncio específico (solo para administradores)
     */
    @PutMapping("/{id}/activar")
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<AnuncioDTO> activar(@PathVariable Long id) {
        log.info("Activando anuncio con ID: {}", id);
        AnuncioDTO anuncioActivado = anuncioService.activar(id);
        return ResponseEntity.ok(anuncioActivado);
    }

    /**
     * Desactiva un anuncio específico (solo para administradores)
     */
    @PutMapping("/{id}/desactivar")
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<AnuncioDTO> desactivar(@PathVariable Long id) {
        log.info("Desactivando anuncio con ID: {}", id);
        AnuncioDTO anuncioDesactivado = anuncioService.desactivar(id);
        return ResponseEntity.ok(anuncioDesactivado);
    }

    /**
     * Marca un anuncio como visto por el usuario actual
     */
    @PostMapping("/visto")
    public ResponseEntity<Map<String, String>> marcarComoVisto(@RequestBody Map<String, Long> request) {
        log.debug("Iniciando marcado de anuncio como visto. Request: {}", request);
        Long anuncioId = request.get("anuncioId");
        if (anuncioId == null) {
            log.warn("anuncioId es null en la request");
            return ResponseEntity.badRequest().body(Map.of("error", "anuncioId es requerido"));
        }

        log.debug("Obteniendo usuario actual para marcar anuncio {} como visto", anuncioId);
        Long usuarioId = obtenerUsuarioActualId();
        
        log.debug("Marcando anuncio {} como visto por usuario {}", anuncioId, usuarioId);
        anuncioService.marcarComoVisto(anuncioId, usuarioId);
        log.debug("Anuncio {} marcado exitosamente como visto por usuario {}", anuncioId, usuarioId);
        
        return ResponseEntity.ok(Map.of("mensaje", "Anuncio marcado como visto"));
    }

    /**
     * Elimina un anuncio (solo para administradores)
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    public ResponseEntity<Map<String, String>> eliminar(@PathVariable Long id) {
        log.info("Eliminando anuncio con ID: {}", id);
        anuncioService.eliminar(id);
        return ResponseEntity.ok(Map.of("mensaje", "Anuncio eliminado exitosamente"));
    }

    /**
     * Obtiene el ID del usuario actual desde el contexto de seguridad
     */
    private Long obtenerUsuarioActualId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            log.error("No hay autenticación válida en el contexto de seguridad");
            throw new ResourceNotFoundException("Usuario no autenticado");
        }
        
        String email = authentication.getName();
        log.debug("Obteniendo usuario por email: {}", email);
        
        if (email == null || email.trim().isEmpty() || "anonymousUser".equals(email)) {
            log.error("Email de usuario inválido: {}", email);
            throw new ResourceNotFoundException("Usuario no válido");
        }
        
        try {
            return usuarioService.findByEmail(email)
                    .orElseThrow(() -> {
                        log.error("Usuario no encontrado en base de datos por email: {}", email);
                        return new ResourceNotFoundException("Usuario no encontrado: " + email);
                    })
                    .getId();
        } catch (ResourceNotFoundException e) {
            // Re-lanzar ResourceNotFoundException tal como está
            throw e;
        } catch (Exception e) {
            log.error("Error inesperado al obtener usuario actual por email: {}", email, e);
            throw new ResourceNotFoundException("Error al obtener usuario: " + email);
        }
    }
}