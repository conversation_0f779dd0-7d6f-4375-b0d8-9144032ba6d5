package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.ActividadSistemaDTO;
import com.cufre.expedientes.dto.FiltroActividadDTO;
import com.cufre.expedientes.model.ActividadSistema;
import com.cufre.expedientes.service.ActividadSistemaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.security.core.Authentication;
import org.springframework.security.access.prepost.PreAuthorize;

@RestController
@RequestMapping("/actividad-sistema")
public class ActividadSistemaController {
    
    @Autowired
    private ActividadSistemaService actividadSistemaService;

    /**
     * Obtiene actividades con filtros y paginación.
     * Solo accesible para ADMINISTRADOR y SUPERUSUARIO.
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping
    public ResponseEntity<Page<ActividadSistemaDTO>> obtenerActividades(
            @RequestParam(required = false) String usuario,
            @RequestParam(required = false) String modulo,
            @RequestParam(required = false) String categoriaAccion,
            @RequestParam(required = false) String estadoRespuesta,
            @RequestParam(required = false) String ipCliente,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fechaFin,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "fechaHora") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        FiltroActividadDTO filtro = new FiltroActividadDTO();
        filtro.setUsuario(usuario);
        filtro.setModulo(modulo);
        filtro.setCategoriaAccion(categoriaAccion);
        filtro.setEstadoRespuesta(estadoRespuesta);
        filtro.setIpCliente(ipCliente);
        filtro.setFechaInicio(fechaInicio);
        filtro.setFechaFin(fechaFin);
        filtro.setPage(page);
        filtro.setSize(size);
        filtro.setSortBy(sortBy);
        filtro.setSortDirection(sortDirection);
        
        Page<ActividadSistemaDTO> actividades = actividadSistemaService.obtenerActividadesConFiltros(filtro);
        return ResponseEntity.ok(actividades);
    }

    /**
     * Obtiene una actividad específica por ID con todos sus detalles.
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/{id}")
    public ResponseEntity<ActividadSistemaDTO> obtenerActividadPorId(@PathVariable Long id) {
        Optional<ActividadSistemaDTO> actividad = actividadSistemaService.obtenerActividadPorId(id);
        
        if (actividad.isPresent()) {
            return ResponseEntity.ok(actividad.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Obtiene estadísticas generales de actividad.
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/estadisticas")
    public ResponseEntity<Map<String, Object>> obtenerEstadisticas() {
        Map<String, Object> estadisticas = new HashMap<>();
        
        estadisticas.put("porModulo", actividadSistemaService.obtenerEstadisticasPorModulo());
        estadisticas.put("porUsuario", actividadSistemaService.obtenerEstadisticasPorUsuario());
        estadisticas.put("actividadesRecientes", actividadSistemaService.obtenerActividadesRecientes());
        
        return ResponseEntity.ok(estadisticas);
    }

    /**
     * Obtiene actividades recientes (últimas 24 horas).
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/recientes")
    public ResponseEntity<List<ActividadSistema>> obtenerActividadesRecientes() {
        List<ActividadSistema> actividades = actividadSistemaService.obtenerActividadesRecientes();
        return ResponseEntity.ok(actividades);
    }

    /**
     * Obtiene actividades sospechosas para una IP específica.
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/sospechosas")
    public ResponseEntity<List<ActividadSistema>> obtenerActividadesSospechosas(
            @RequestParam String ip) {
        List<ActividadSistema> actividades = actividadSistemaService.obtenerActividadesSospechosas(ip);
        return ResponseEntity.ok(actividades);
    }

    /**
     * Endpoint para exportar actividades (placeholder para futura implementación).
     */
    // @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @PostMapping("/exportar")
    public ResponseEntity<Map<String, String>> exportarActividades(@RequestBody FiltroActividadDTO filtro) {
        // TODO: Implementar exportación a CSV/Excel
        Map<String, String> response = new HashMap<>();
        response.put("mensaje", "Funcionalidad de exportación en desarrollo");
        response.put("estado", "pendiente");
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).body(response);
    }

    /**
     * Obtiene notificaciones no vistas para el usuario actual
     */
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/notificaciones")
    public ResponseEntity<List<ActividadSistemaDTO>> obtenerNotificaciones(
            Authentication authentication,
            @RequestParam(defaultValue = "10") int limite) {
        
        String emailUsuario = authentication.getName();
        List<ActividadSistemaDTO> notificaciones =
            actividadSistemaService.obtenerNotificacionesNoVistas(emailUsuario, limite);
        
        return ResponseEntity.ok(notificaciones);
    }

    /**
     * Obtiene el contador de notificaciones no vistas
     */
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @GetMapping("/notificaciones/count")
    public ResponseEntity<Map<String, Long>> contarNotificaciones(Authentication authentication) {
        String emailUsuario = authentication.getName();
        long count = actividadSistemaService.contarNotificacionesNoVistas(emailUsuario);
        
        Map<String, Long> response = new HashMap<>();
        response.put("count", count);
        
        return ResponseEntity.ok(response);
    }

    /**
     * Marca todas las notificaciones como vistas
     */
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @PostMapping("/notificaciones/marcar-vistas")
    public ResponseEntity<Map<String, String>> marcarNotificacionesVistas(Authentication authentication) {
        String emailUsuario = authentication.getName();
        actividadSistemaService.marcarNotificacionesComoVistas(emailUsuario);
        
        Map<String, String> response = new HashMap<>();
        response.put("mensaje", "Notificaciones marcadas como vistas");
        
        return ResponseEntity.ok(response);
    }

    /**
     * Endpoint legacy para compatibilidad (devuelve la lista simple).
     * Temporalmente permitido sin autenticación para pruebas.
     */
    @GetMapping("/legacy")
    public ResponseEntity<List<ActividadSistema>> obtenerActividadLegacy() {
        List<ActividadSistema> actividades = actividadSistemaService.obtenerTodas();
        return ResponseEntity.ok(actividades);
    }

    /**
     * Elimina registros de actividad anteriores a un número específico de días
     */
    @PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
    @DeleteMapping("/eliminar")
    public ResponseEntity<Map<String, Object>> eliminarActividades(@RequestParam int dias) {
        long eliminados = actividadSistemaService.eliminarRegistrosAntiguos(dias);
        
        Map<String, Object> response = new HashMap<>();
        response.put("eliminados", eliminados);
        response.put("mensaje", "Se eliminaron " + eliminados + " registros de actividad anteriores a " + dias + " días");
        
        return ResponseEntity.ok(response);
    }
}