package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.ExpedienteDTO;
import com.cufre.expedientes.service.ExpedienteService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controlador específico para manejar endpoints de paginación de expedientes,
 * separado del controlador principal para evitar conflictos de rutas.
 */
@RestController
@RequiredArgsConstructor
public class ExpedientePaginadoController {

    private final ExpedienteService expedienteService;
    
    /**
     * Endpoint para obtener expedientes paginados
     * @param page número de página (comenzando desde 0)
     * @param size tamaño de la página
     * @param fuerzaAsignada filtro de fuerza asignada
     * @param estadoSituacion filtro de estado de situación
     * @param numero filtro de número
     * @param profugo filtro de profugo
     * @param fechaDesde filtro de fecha desde
     * @param fechaHasta filtro de fecha hasta
     * @return página de expedientes
     */
    @GetMapping(value = {"/expedientes-paginados", "/expedientes/paginated"}, produces = "application/json")
    public ResponseEntity<?> getExpedientesPaginados(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String fuerzaAsignada,
            @RequestParam(required = false) String estadoSituacion,
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String profugo,
            @RequestParam(required = false) String fechaDesde,
            @RequestParam(required = false) String fechaHasta,
            @RequestParam(required = false) String delitoNombre,
            @RequestParam(required = false, defaultValue = "id") String sort,
            @RequestParam(required = false, defaultValue = "desc") String direction
    ) {
        Page<ExpedienteDTO> expedientes = expedienteService.findPaginatedWithFilters(
            page, size, fuerzaAsignada, estadoSituacion, numero, profugo,
            fechaDesde, fechaHasta, delitoNombre, sort, direction);
        return ResponseEntity.ok(expedientes);
    }
}
