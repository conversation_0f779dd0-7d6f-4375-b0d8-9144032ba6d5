package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.ActualizacionParametrosDTO;
import com.cufre.expedientes.dto.ParametroPrioridadDTO;
import com.cufre.expedientes.model.ParametroPrioridad;
import com.cufre.expedientes.service.ParametroPrioridadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controlador para la gestión de parámetros de prioridad
 */
@RestController
@RequestMapping("/parametros-prioridad")
@Slf4j
public class ParametroPrioridadController {

    private final ParametroPrioridadService parametroPrioridadService;

    @Autowired
    public ParametroPrioridadController(ParametroPrioridadService parametroPrioridadService) {
        this.parametroPrioridadService = parametroPrioridadService;
    }

    /**
     * Obtiene todos los parámetros de prioridad
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<List<ParametroPrioridadDTO>> obtenerTodosLosParametros() {
        List<ParametroPrioridad> parametros = parametroPrioridadService.obtenerTodosLosParametros();
        
        List<ParametroPrioridadDTO> parametrosDTO = parametros.stream()
                .map(this::convertirADTO)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(parametrosDTO);
    }

    /**
     * Obtiene un parámetro específico por su ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<ParametroPrioridadDTO> obtenerParametroPorId(@PathVariable Long id) {
        // Buscar en la lista de todos los parámetros
        List<ParametroPrioridad> parametros = parametroPrioridadService.obtenerTodosLosParametros();
        ParametroPrioridad parametro = parametros.stream()
                .filter(p -> p.getId().equals(id))
                .findFirst()
                .orElse(null);
        
        if (parametro == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(convertirADTO(parametro));
    }

    /**
     * Actualiza múltiples parámetros de prioridad
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<List<ParametroPrioridadDTO>> actualizarParametros(
            @RequestBody ActualizacionParametrosDTO actualizacionDTO,
            Authentication authentication) {
        String usuario = authentication.getName();
        
        List<ParametroPrioridad> parametrosActualizados = parametroPrioridadService
                .actualizarParametros(actualizacionDTO.getParametros(), usuario);
        
        List<ParametroPrioridadDTO> parametrosDTO = parametrosActualizados.stream()
                .map(this::convertirADTO)
                .collect(Collectors.toList());
        
        log.info("Parámetros de prioridad actualizados por usuario: {}", usuario);
        
        return ResponseEntity.ok(parametrosDTO);
    }

    /**
     * Actualiza un parámetro específico
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<ParametroPrioridadDTO> actualizarParametro(
            @PathVariable Long id,
            @RequestParam Integer valor,
            Authentication authentication) {
        String usuario = authentication.getName();
        
        ParametroPrioridad parametroActualizado = parametroPrioridadService
                .actualizarParametro(id, valor, usuario);
        
        log.info("Parámetro de prioridad {} actualizado a {} por usuario: {}",
                parametroActualizado.getClaveVariable(), valor, usuario);
        
        return ResponseEntity.ok(convertirADTO(parametroActualizado));
    }

    /**
     * Recarga la caché de parámetros
     */
    @PostMapping("/recargar-cache")
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<Map<String, String>> recargarCache(Authentication authentication) {
        parametroPrioridadService.recargarCache();
        
        String usuario = authentication.getName();
        log.info("Caché de parámetros de prioridad recargada por usuario: {}", usuario);
        
        return ResponseEntity.ok(Map.of("mensaje", "Caché recargada exitosamente"));
    }

    /**
     * Obtiene el estado actual de la caché
     */
    @GetMapping("/estado-cache")
    @PreAuthorize("hasRole('ADMINISTRADOR') or hasRole('SUPERUSUARIO')")
    public ResponseEntity<Map<String, Object>> obtenerEstadoCache() {
        Map<String, Object> estado = parametroPrioridadService.getEstadoCache();
        return ResponseEntity.ok(estado);
    }

    /**
     * Convierte una entidad ParametroPrioridad a DTO
     */
    private ParametroPrioridadDTO convertirADTO(ParametroPrioridad parametro) {
        return ParametroPrioridadDTO.builder()
                .id(parametro.getId())
                .claveVariable(parametro.getClaveVariable())
                .valor(parametro.getValor())
                .descripcion(parametro.getDescripcion())
                .tipoVariable(parametro.getTipoVariable())
                .fechaCreacion(parametro.getFechaCreacion())
                .fechaModificacion(parametro.getFechaModificacion())
                .modificadoPor(parametro.getModificadoPor())
                .build();
    }
}