package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.CambioPasswordDTO;
import com.cufre.expedientes.dto.PerfilUsuarioDTO;
import com.cufre.expedientes.dto.UsuarioDTO;
import com.cufre.expedientes.dto.UpdateRoleRequestDTO;
import com.cufre.expedientes.dto.UpdateRoleResponseDTO;
import com.cufre.expedientes.dto.ForceLogoutResponseDTO;
import com.cufre.expedientes.model.Usuario;
import com.cufre.expedientes.model.enums.Rol;
import com.cufre.expedientes.service.UsuarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import java.util.List;
import java.util.Optional;

import jakarta.validation.Valid;

/**
 * Controlador para operaciones relacionadas con usuarios.
 */
@RestController
@RequestMapping("/usuarios")
@Slf4j
public class UsuarioController extends AbstractBaseController<Usuario, UsuarioDTO, Long> {

    private final UsuarioService usuarioService;

    public UsuarioController(UsuarioService service) {
        super(service);
        this.usuarioService = service;
    }

    /**
     * Sobreescribe el método create del controlador base para asegurar que
     * se establezca una contraseña por defecto al crear un usuario.
     */
    @Override
    @PostMapping
    public ResponseEntity<UsuarioDTO> create(@Valid @RequestBody UsuarioDTO dto) {
        try {
            // Usa el método create específico que establece una contraseña por defecto "Minseg2025-"
            UsuarioDTO created = usuarioService.create(dto, "Minseg2025-");
            return ResponseEntity.status(HttpStatus.CREATED).body(created);
        } catch (Exception e) {
            // Log del error detallado para diagnóstico en el servidor
            log.error("Error al crear usuario: {}", e.getMessage(), e);
            // Re-lanzar la excepción para que sea manejada por el GlobalExceptionHandler
            throw e;
        }
    }

    @PatchMapping("/{id}/password")
    public ResponseEntity<Void> changePassword(
            @PathVariable Long id,
            @Valid @RequestBody CambioPasswordDTO cambioDTO) {
        usuarioService.changePassword(id, cambioDTO.getNewPassword());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/search/rol")
    public ResponseEntity<List<UsuarioDTO>> findByRol(@RequestParam Rol rol) {
        return ResponseEntity.ok(usuarioService.findByRol(rol));
    }

    @GetMapping("/search/nombre")
    public ResponseEntity<UsuarioDTO> findByNombre(@RequestParam String nombre) {
        return usuarioService.findByNombre(nombre)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search/email")
    public ResponseEntity<UsuarioDTO> findByEmail(@RequestParam String email) {
        return usuarioService.findByEmail(email)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/{id}/reset-password")
    public ResponseEntity<Void> resetPasswordAnd2FA(@PathVariable Long id) {
        usuarioService.resetPasswordAnd2FA(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/me")
    public ResponseEntity<UsuarioDTO> getUsuarioActual() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String email = authentication.getName();
            Optional<UsuarioDTO> usuarioOpt = usuarioService.findByEmail(email);
            if (usuarioOpt.isPresent()) {
                return ResponseEntity.ok(usuarioOpt.get());
            }
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    /**
     * Cambia el rol de un usuario con validación de contraseña
     * Solo SUPERUSUARIOS pueden usar este endpoint
     */
    @PutMapping("/{id}/role")
    @PreAuthorize("hasRole('SUPERUSUARIO')")
    public ResponseEntity<UpdateRoleResponseDTO> updateRole(
            @PathVariable Long id,
            @Valid @RequestBody UpdateRoleRequestDTO request) {
        try {
            log.info("Solicitud de cambio de rol para usuario ID: {} a rol: {}", id, request.getNewRole());
            
            UpdateRoleResponseDTO response = usuarioService.updateRoleWithPassword(
                    id, request.getNewRole(), request.getPassword());
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        } catch (Exception e) {
            log.error("Error inesperado al cambiar rol para usuario ID {}: {}", id, e.getMessage(), e);
            UpdateRoleResponseDTO errorResponse = UpdateRoleResponseDTO.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Fuerza el cierre de sesión de un usuario
     * Solo SUPERUSUARIOS pueden usar este endpoint
     */
    @PostMapping("/{id}/force-logout")
    @PreAuthorize("hasRole('SUPERUSUARIO')")
    public ResponseEntity<ForceLogoutResponseDTO> forceLogout(@PathVariable Long id) {
        try {
            log.info("Solicitud de forzar logout para usuario ID: {}", id);
            
            ForceLogoutResponseDTO response = usuarioService.forceLogout(id);
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
        } catch (Exception e) {
            log.error("Error inesperado al forzar logout para usuario ID {}: {}", id, e.getMessage(), e);
            ForceLogoutResponseDTO errorResponse = ForceLogoutResponseDTO.builder()
                    .success(false)
                    .message("Error interno del servidor")
                    .sessionsInvalidated(0)
                    .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Obtiene el perfil completo del usuario actual
     */
    @GetMapping("/me/perfil")
    public ResponseEntity<PerfilUsuarioDTO> obtenerPerfilActual() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String email = authentication.getName();
                log.info("Solicitud de perfil para usuario: {}", email);
                
                PerfilUsuarioDTO perfil = usuarioService.obtenerPerfilPorEmail(email);
                return ResponseEntity.ok(perfil);
            }
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        } catch (Exception e) {
            log.error("Error al obtener perfil del usuario actual: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Actualiza el perfil del usuario actual
     */
    @PutMapping("/me/perfil")
    public ResponseEntity<PerfilUsuarioDTO> actualizarPerfilActual(@Valid @RequestBody PerfilUsuarioDTO perfilDTO) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String email = authentication.getName();
                log.info("Actualizando perfil para usuario: {}", email);
                
                PerfilUsuarioDTO perfilActualizado = usuarioService.actualizarPerfilPorEmail(email, perfilDTO);
                return ResponseEntity.ok(perfilActualizado);
            }
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        } catch (Exception e) {
            log.error("Error al actualizar perfil del usuario actual: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Actualiza el perfil de otro usuario (solo SUPERUSUARIO)
     */
    @PutMapping("/{id}/perfil")
    @PreAuthorize("hasRole('SUPERUSUARIO')")
    public ResponseEntity<PerfilUsuarioDTO> actualizarPerfilOtroUsuario(
            @PathVariable Long id,
            @Valid @RequestBody PerfilUsuarioDTO perfilDTO) {
        try {
            log.info("SUPERUSUARIO actualizando perfil del usuario ID: {}", id);
            
            PerfilUsuarioDTO perfilActualizado = usuarioService.actualizarPerfilPorId(id, perfilDTO);
            return ResponseEntity.ok(perfilActualizado);
        } catch (RuntimeException e) {
            log.error("Error al actualizar perfil del usuario ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error inesperado al actualizar perfil del usuario ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Obtiene el perfil de otro usuario (solo SUPERUSUARIO)
     */
    @GetMapping("/{id}/perfil")
    @PreAuthorize("hasRole('SUPERUSUARIO')")
    public ResponseEntity<PerfilUsuarioDTO> obtenerPerfilOtroUsuario(@PathVariable Long id) {
        try {
            log.info("SUPERUSUARIO obteniendo perfil del usuario ID: {}", id);
            
            PerfilUsuarioDTO perfil = usuarioService.obtenerPerfilPorId(id);
            return ResponseEntity.ok(perfil);
        } catch (RuntimeException e) {
            log.error("Error al obtener perfil del usuario ID {}: {}", id, e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error inesperado al obtener perfil del usuario ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}