package com.cufre.expedientes.controller;

import com.cufre.expedientes.service.EstadisticaService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.cufre.expedientes.dto.DelitoRankingDTO;

@RestController
@RequestMapping("/estadisticas")
@RequiredArgsConstructor
public class EstadisticaController {
    private final EstadisticaService estadisticaService;
    
    @GetMapping("/provincia")
    public ResponseEntity<Map<String, Long>> countByProvincia() {
        return ResponseEntity.ok(estadisticaService.countByProvincia());
    }
    
    @GetMapping("/estado")
    public ResponseEntity<Map<String, Long>> countByEstadoSituacion() {
        return ResponseEntity.ok(estadisticaService.countByEstadoSituacion());
    }

    @GetMapping("/expedientes-por-estado")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorEstado() {
        Map<String, Long> datos = estadisticaService.countByEstadoSituacion();
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());

        return ResponseEntity.ok(resultado);
    }

    @GetMapping("/expedientes-por-fuerza")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorFuerza() {
        Map<String, Long> datos = estadisticaService.countByFuerzaAsignada();
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());

        return ResponseEntity.ok(resultado);
    }

    @GetMapping("/expedientes-por-estado-y-fuerza/{fuerza}")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorEstadoYFuerza(@PathVariable String fuerza) {
        Map<String, Long> datos = estadisticaService.countByEstadoSituacionAndFuerza(fuerza);
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());

        return ResponseEntity.ok(resultado);
    }

    @GetMapping("/expedientes-por-fuerza-y-estado/{estado}")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorFuerzaYEstado(@PathVariable String estado) {
        Map<String, Long> datos = estadisticaService.countByFuerzaAsignadaAndEstado(estado);
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());

        return ResponseEntity.ok(resultado);
    }

    @GetMapping("/tipo-captura")
    public ResponseEntity<Map<String, Long>> countByTipoCaptura() {
        return ResponseEntity.ok(estadisticaService.countByTipoCaptura());
    }

    @GetMapping("/expedientes-por-tipo-captura")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorTipoCaptura() {
        Map<String, Long> datos = estadisticaService.countByTipoCaptura();
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());

        return ResponseEntity.ok(resultado);
    }
    
    @GetMapping("/periodo")
    public ResponseEntity<Map<String, Long>> countByPeriodo(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate inicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin) {
        return ResponseEntity.ok(estadisticaService.countByPeriodo(inicio, fin));
    }
    
    @GetMapping("/generales")
    public ResponseEntity<Map<String, Object>> getEstadisticasGenerales() {
        return ResponseEntity.ok(estadisticaService.getEstadisticasGenerales());
    }
    
    /**
     * Endpoint para obtener todas las estadísticas necesarias para el dashboard
     * @return Datos consolidados del dashboard
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardData() {
        return ResponseEntity.ok(estadisticaService.getDashboardData());
    }

    /**
     * Endpoint para ranking de delitos por expedientes asociados
     */
    @GetMapping("/delitos-por-tipo")
    public ResponseEntity<List<DelitoRankingDTO>> rankingDelitos() {
        return ResponseEntity.ok(estadisticaService.rankingDelitos());
    }
    
    /**
     * Endpoint para ranking de delitos con filtros
     */
    @GetMapping("/delitos-por-tipo-filtrado")
    public ResponseEntity<List<DelitoRankingDTO>> rankingDelitosWithFilters(
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String fuerza,
            @RequestParam(required = false) String tipoCaptura) {
        return ResponseEntity.ok(estadisticaService.rankingDelitosWithFilters(estado, fuerza, tipoCaptura));
    }

    /**
     * Endpoint para evolución temporal de expedientes por mes
     */
    @GetMapping("/expedientes-por-mes")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorMes() {
        return ResponseEntity.ok(estadisticaService.countExpedientesPorMes());
    }

    /**
     * Endpoint para detenidos por fuerza
     */
    @GetMapping("/detenidos-por-fuerza")
    public ResponseEntity<List<Map<String, Object>>> getDetenidosPorFuerza() {
        return ResponseEntity.ok(estadisticaService.countDetenidosPorFuerza());
    }
    
    /**
     * Endpoint para obtener las métricas clave del centro de comando
     */
    @GetMapping("/metricas-clave")
    public ResponseEntity<Map<String, Object>> getMetricasClave() {
        return ResponseEntity.ok(estadisticaService.getMetricasClave());
    }
    
    /**
     * Endpoint para obtener expedientes por estado con filtros
     */
    @GetMapping("/expedientes-por-estado-filtrado")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorEstadoFiltrado(
            @RequestParam(required = false) String fuerza,
            @RequestParam(required = false) String tipoCaptura) {
        Map<String, Long> datos = estadisticaService.countByEstadoSituacionWithFilters(fuerza, tipoCaptura);
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(resultado);
    }
    
    /**
     * Endpoint para obtener expedientes por fuerza con filtros
     */
    @GetMapping("/expedientes-por-fuerza-filtrado")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorFuerzaFiltrado(
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String tipoCaptura) {
        Map<String, Long> datos = estadisticaService.countByFuerzaAsignadaWithFilters(estado, tipoCaptura);
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(resultado);
    }
    
    /**
     * Endpoint para obtener expedientes por tipo de captura con filtros
     */
    @GetMapping("/expedientes-por-tipo-captura-filtrado")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorTipoCapturaFiltrado(
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String fuerza) {
        Map<String, Long> datos = estadisticaService.countByTipoCapturaWithFilters(estado, fuerza);
        List<Map<String, Object>> resultado = datos.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", entry.getKey());
                    item.put("value", entry.getValue());
                    return item;
                })
                .collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(resultado);
    }
    
    /**
     * Endpoint para evolución temporal de expedientes por mes con filtros
     */
    @GetMapping("/expedientes-por-mes-filtrado")
    public ResponseEntity<List<Map<String, Object>>> getExpedientesPorMesFiltrado(
            @RequestParam(required = false) String estado,
            @RequestParam(required = false) String fuerza,
            @RequestParam(required = false) String tipoCaptura) {
        return ResponseEntity.ok(estadisticaService.countExpedientesPorMesWithFilters(estado, fuerza, tipoCaptura));
    }
}