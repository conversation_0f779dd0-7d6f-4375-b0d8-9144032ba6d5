package com.cufre.expedientes.controller;

import com.cufre.expedientes.dto.ExpedienteDTO;
import com.cufre.expedientes.dto.RecompensaUpdateDTO;
import com.cufre.expedientes.dto.FotografiaDTO;
import com.cufre.expedientes.dto.DocumentoDTO;
import com.cufre.expedientes.model.Expediente;
import com.cufre.expedientes.service.ExpedienteService;
import com.cufre.expedientes.service.FotografiaService;
import com.cufre.expedientes.service.PdfGeneratorService;
import com.cufre.expedientes.service.ActividadSistemaService;
import com.cufre.expedientes.dto.PersonaExpedienteDTO;
import com.cufre.expedientes.service.PersonaExpedienteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ContentDisposition;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Controlador para operaciones relacionadas con expedientes.
 */
@RestController
@RequestMapping("/expedientes")
@Slf4j
public class ExpedienteController extends AbstractBaseController<Expediente, ExpedienteDTO, Long> {
    
    private final ExpedienteService expedienteService;
    private final FotografiaService fotografiaService;
    private final PersonaExpedienteService personaExpedienteService;
    private final PdfGeneratorService pdfGeneratorService;
    private final ActividadSistemaService actividadSistemaService;
    
    public ExpedienteController(ExpedienteService service, FotografiaService fotografiaService,
                               PersonaExpedienteService personaExpedienteService,
                               PdfGeneratorService pdfGeneratorService,
                               ActividadSistemaService actividadSistemaService) {
        super(service);
        this.expedienteService = service;
        this.fotografiaService = fotografiaService;
        this.personaExpedienteService = personaExpedienteService;
        this.pdfGeneratorService = pdfGeneratorService;
        this.actividadSistemaService = actividadSistemaService;
    }
    
    @GetMapping("/search/fechas")
    public ResponseEntity<List<ExpedienteDTO>> findByFechasIngreso(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {
        return ResponseEntity.ok(expedienteService.findByFechaIngresoBetween(fechaInicio, fechaFin));
    }
    
    @GetMapping("/search/numero")
    public ResponseEntity<List<ExpedienteDTO>> findByNumeroExpediente(@RequestParam String numero) {
        return ResponseEntity.ok(expedienteService.findByNumeroExpediente(numero));
    }
    
    @GetMapping("/search/persona/{personaId}")
    public ResponseEntity<List<ExpedienteDTO>> findByPersonaId(@PathVariable Long personaId) {
        return ResponseEntity.ok(expedienteService.findByPersonaId(personaId));
    }
    
    @GetMapping("/search/delito/{delitoId}")
    public ResponseEntity<List<ExpedienteDTO>> findByDelitoId(@PathVariable Long delitoId) {
        return ResponseEntity.ok(expedienteService.findByDelitoId(delitoId));
    }
    
    @GetMapping("/estadisticas/provincia")
    public ResponseEntity<Map<String, Long>> getEstadisticasPorProvincia() {
        return ResponseEntity.ok(expedienteService.getEstadisticasPorProvincia());
    }
    
    @GetMapping("/estadisticas/periodo")
    public ResponseEntity<Long> getEstadisticasPorPeriodo(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {
        return ResponseEntity.ok(expedienteService.getEstadisticasPorPeriodo(fechaInicio, fechaFin));
    }

    @Override
    @GetMapping(value = "/{id:[\\d]+}", produces = "application/json")
    public ResponseEntity<ExpedienteDTO> getById(@PathVariable Long id) {
        return ResponseEntity.ok(expedienteService.findByIdComplete(id));
    }
    
    /**
     * Obtiene todos los delitos asociados a un expediente
     * @param id ID del expediente
     * @return Lista de delitos asociados
     */
    @GetMapping("/{id}/delitos")
    public ResponseEntity<List<com.cufre.expedientes.dto.DelitoDTO>> getDelitos(@PathVariable Long id) {
        return ResponseEntity.ok(expedienteService.findDelitosByExpedienteId(id));
    }
    
    /**
     * Obtiene todas las fotografías asociadas a un expediente
     * @param id ID del expediente
     * @return Lista de fotografías asociadas
     */
    @GetMapping("/{id}/fotografias")
    public ResponseEntity<List<com.cufre.expedientes.dto.FotografiaDTO>> getFotografias(@PathVariable Long id) {
        log.info("Obteniendo fotografías para expediente ID: {}", id);
        return ResponseEntity.ok(expedienteService.findFotografiasByExpedienteId(id));
    }
    
    /**
     * Obtiene todos los documentos asociados a un expediente
     * @param id ID del expediente
     * @return Lista de documentos asociados
     */
    @GetMapping("/{id}/documentos")
    public ResponseEntity<List<com.cufre.expedientes.dto.DocumentoDTO>> getDocumentos(@PathVariable Long id) {
        log.info("Obteniendo documentos para expediente ID: {}", id);
        return ResponseEntity.ok(expedienteService.findDocumentosByExpedienteId(id));
    }
    
    /**
     * Obtiene todas las personas asociadas a un expediente
     * @param id ID del expediente
     * @return Lista de relaciones persona-expediente
     */
    @GetMapping("/{id}/personas")
    public ResponseEntity<List<com.cufre.expedientes.dto.PersonaExpedienteDTO>> getPersonas(@PathVariable Long id) {
        log.info("Obteniendo personas para expediente ID: {}", id);
        return ResponseEntity.ok(expedienteService.findPersonasByExpedienteId(id));
    }
    
    /**
     * Asocia un delito a un expediente
     * @param id ID del expediente
     * @param expedienteDelitoDTO Información de la relación
     * @return Relación creada
     */
    @PostMapping("/{id}/delitos")
    public ResponseEntity<com.cufre.expedientes.dto.ExpedienteDelitoDTO> addDelito(
            @PathVariable Long id, 
            @RequestBody com.cufre.expedientes.dto.ExpedienteDelitoDTO expedienteDelitoDTO) {
        // Asegurar que el ID del expediente en el DTO coincida con el de la URL
        expedienteDelitoDTO.setExpedienteId(id);
        return ResponseEntity.ok(expedienteService.addDelito(expedienteDelitoDTO));
    }
    
    /**
     * Elimina un delito de un expediente
     * @param id ID del expediente
     * @param delitoId ID del delito
     * @return Respuesta vacía con status OK
     */
    @DeleteMapping("/{id}/delitos/{delitoId}")
    public ResponseEntity<Void> removeDelito(@PathVariable Long id, @PathVariable Long delitoId) {
        expedienteService.removeDelito(id, delitoId);
        return ResponseEntity.ok().build();
    }
    
    /**
     * Elimina una fotografía de un expediente
     * @param id ID del expediente
     * @param fotografiaId ID de la fotografía
     * @return Respuesta vacía con status OK
     */
    @DeleteMapping("/{id}/fotografias/{fotografiaId}")
    public ResponseEntity<Void> eliminarFotografia(@PathVariable Long id, @PathVariable Long fotografiaId) {
        fotografiaService.eliminarFotografiaDeExpediente(id, fotografiaId);
        return ResponseEntity.ok().build();
    }
    
    /**
     * Asocia una persona a un expediente
     * @param id ID del expediente
     * @param dto Información de la relación persona-expediente
     * @return Relación creada
     */
    @PostMapping("/{id}/personas")
    public ResponseEntity<PersonaExpedienteDTO> addPersona(@PathVariable Long id, @RequestBody PersonaExpedienteDTO dto) {
        dto.setExpedienteId(id);
        PersonaExpedienteDTO saved = personaExpedienteService.savePersonaExpediente(dto);
        return ResponseEntity.ok(saved);
    }
    
    /**
     * Elimina un vínculo (PersonaExpediente) por su ID.
     * @param vinculoId ID del vínculo (PersonaExpediente) a eliminar.
     * @return Respuesta vacía con status OK si la eliminación fue exitosa.
     */
    @DeleteMapping("/vinculos/{vinculoId}")
    public ResponseEntity<Void> deleteVinculo(@PathVariable Long vinculoId) {
        log.info("Solicitud para eliminar vínculo con ID: {}", vinculoId);
        personaExpedienteService.deleteVinculo(vinculoId);
        return ResponseEntity.ok().build();
    }
    
    /**
     * Actualiza la foto principal de un expediente
     */
    @PutMapping("/{id}/foto-principal/{fotoId}")
    public ResponseEntity<Void> setFotoPrincipal(@PathVariable Long id, @PathVariable Long fotoId) {
        expedienteService.setFotoPrincipal(id, fotoId);
        return ResponseEntity.ok().build();
    }

    // --- NUEVO ENDPOINT ---
    @GetMapping("/mas-buscados")
    public ResponseEntity<List<ExpedienteDTO>> getMasBuscados(@RequestParam(defaultValue = "10") int limit) {
        return ResponseEntity.ok(expedienteService.findMasBuscados(limit));
    }

    /**
     * Búsqueda avanzada de expedientes y personas
     * @param nombre Nombre de la persona (opcional)
     * @param apellido Apellido de la persona (opcional)
     * @param numeroExpediente Número de expediente (opcional)
     * @param tipoBusqueda "expediente", "persona" o "ambos" (opcional)
     * @param numeroIdentificacion Número de identificación de la persona (opcional)
     */
    @GetMapping("/busqueda-avanzada")
    public ResponseEntity<List<ExpedienteDTO>> busquedaAvanzada(
            @RequestParam(required = false) String nombre,
            @RequestParam(required = false) String apellido,
            @RequestParam(required = false) String numeroExpediente,
            @RequestParam(required = false) String tipoBusqueda, // "expediente", "persona", "ambos"
            @RequestParam(required = false) String numeroIdentificacion
    ) {
        // TODO: Implementar lógica de búsqueda avanzada en el servicio
        List<ExpedienteDTO> resultados = expedienteService.busquedaAvanzada(nombre, apellido, numeroExpediente, tipoBusqueda, numeroIdentificacion);
        return ResponseEntity.ok(resultados);
    }

    // Los endpoints de bloqueo han sido eliminados

    // Endpoint paginado movido a ExpedientePaginadoController
    // para evitar conflictos de rutas

    /**
     * Endpoint para P.N Recompensas - Lista paginada de expedientes con filtros
     */
    @GetMapping("/pn-recompensas")
    public ResponseEntity<Page<ExpedienteDTO>> getPNRecompensas(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String profugo,
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String fuerzaAsignada,
            @RequestParam(required = false) String estadoSituacion,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaDesde,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaHasta,
            @RequestParam(required = false) Long delitoId
    ) {
        // Crear Sort basado en sortBy y sortDir
        Sort.Direction direction = sortDir.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = Sort.by(direction, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<ExpedienteDTO> result = expedienteService.findPNRecompensas(
            pageable, profugo, numero, fuerzaAsignada, estadoSituacion,
            fechaDesde, fechaHasta, delitoId
        );
        
        return ResponseEntity.ok(result);
    }

    /**
     * Endpoint para actualizar la recompensa de un expediente
     */
    @PutMapping("/{id}/recompensa")
    public ResponseEntity<ExpedienteDTO> actualizarRecompensa(
            @PathVariable Long id,
            @RequestBody RecompensaUpdateDTO recompensaDto
    ) {
        ExpedienteDTO expedienteActualizado = expedienteService.actualizarRecompensa(id, recompensaDto);
        return ResponseEntity.ok(expedienteActualizado);
    }

    /**
     * Endpoint para exportar un expediente a PDF
     */
    @GetMapping("/{id}/pdf")
    public ResponseEntity<byte[]> exportarExpedienteAPDF(@PathVariable Long id) {
        try {
            log.info("Generando PDF para expediente ID: {}", id);
            
            // Obtener datos completos del expediente
            ExpedienteDTO expediente = expedienteService.findByIdComplete(id);
            List<FotografiaDTO> fotografias = expedienteService.findFotografiasByExpedienteId(id);
            List<DocumentoDTO> documentos = expedienteService.findDocumentosByExpedienteId(id);
            List<PersonaExpedienteDTO> personas = expedienteService.findPersonasByExpedienteId(id);
            
            // Generar PDF
            byte[] pdfBytes = pdfGeneratorService.generarPdfExpediente(expediente, fotografias, documentos, personas);
            
            // Registrar actividad en el sistema
            actividadSistemaService.registrarActividad(
                getCurrentUserEmail(),
                "EXPORTAR_PDF_EXPEDIENTE",
                "Expediente ID: " + id + " - " + expediente.getNumero()
            );
            
            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDisposition(ContentDisposition.attachment()
                .filename("expediente_" + id + ".pdf")
                .build());
            headers.setContentLength(pdfBytes.length);
            
            return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
                
        } catch (Exception e) {
            log.error("Error al generar PDF para expediente ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    private String getCurrentUserEmail() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null ? authentication.getName() : "sistema";
    }
}