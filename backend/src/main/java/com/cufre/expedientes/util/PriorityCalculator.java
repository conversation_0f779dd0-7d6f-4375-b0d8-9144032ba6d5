package com.cufre.expedientes.util;

import com.cufre.expedientes.model.Expediente;
import com.cufre.expedientes.model.ExpedienteDelito;
import com.cufre.expedientes.service.ParametroPrioridadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PriorityCalculator {

    private final ParametroPrioridadService parametroPrioridadService;

    @Autowired
    public PriorityCalculator(ParametroPrioridadService parametroPrioridadService) {
        this.parametroPrioridadService = parametroPrioridadService;
    }

    // --- FUNCIÓN PRINCIPAL ---
    public int calcularPrioridad(Expediente expediente) {
        int prioridad = 0;

        // 1. Puntos base por delitos
        if (expediente.getExpedienteDelitos() != null) {
            for (ExpedienteDelito ed : expediente.getExpedienteDelitos()) {
                if (ed.getDelito() != null && ed.getDelito().getValoracion() != null) {
                    prioridad += ed.getDelito().getValoracion();
                }
            }
        }

        // 2. Factores de la persona (prófugo)
        // Nivel de estudios (todos 0 según tu tabla)
        // Profesión
        String profesion = safeUpper(expediente.getProfugoProfesionOcupacion());
        prioridad += getParametroValor("PROFESION", profesion);

        // Detenciones previas
        Integer detenciones = expediente.getProfugoNumeroDetencionesPrevias();
        if (detenciones == null || detenciones == 0) {
            prioridad += getParametroValor("DETENCIONES_PREVIAS", "NINGUNA");
        } else if (detenciones <= 2) {
            prioridad += getParametroValor("DETENCIONES_PREVIAS", "POCAS");
        } else if (detenciones <= 5) {
            prioridad += getParametroValor("DETENCIONES_PREVIAS", "MODERADAS");
        } else {
            prioridad += getParametroValor("DETENCIONES_PREVIAS", "MUCHAS");
        }

        // 3. Factores del expediente
        // Número de cómplices
        Integer complices = expediente.getNumeroComplices();
        if (complices == null || complices == 0) {
            prioridad += getParametroValor("NUMERO_COMPLICES", "NINGUNO");
        } else if (complices <= 2) {
            prioridad += getParametroValor("NUMERO_COMPLICES", "POCOS");
        } else if (complices <= 5) {
            prioridad += getParametroValor("NUMERO_COMPLICES", "MODERADOS");
        } else {
            prioridad += getParametroValor("NUMERO_COMPLICES", "MUCHOS");
        }

        // Tipo de captura
        String tipoCaptura = safeUpper(expediente.getTipoCaptura());
        prioridad += getParametroValor("TIPO_CAPTURA", tipoCaptura);

        // Tipo de víctima
        String tipoVictima = safeUpper(expediente.getTipoVictima());
        prioridad += getParametroValor("TIPO_VICTIMA", tipoVictima);

        // Caso mediático
        if (Boolean.TRUE.equals(expediente.getMediaticoFlag())) {
            prioridad += getParametroValor("CASO_MEDIATICO", "SI");
        }

        // Prófugo reincidente
        if (Boolean.TRUE.equals(expediente.getReincicenteFlag())) {
            prioridad += getParametroValor("PROFUGO_REINCIDENTE", "SI");
        }

        // Prófugo reiterante
        if (Boolean.TRUE.equals(expediente.getReiteranteFlag())) {
            prioridad += getParametroValor("PROFUGO_REITERANTE", "SI");
        }

        // Involucra banda
        if (Boolean.TRUE.equals(expediente.getBandaFlag())) {
            prioridad += getParametroValor("INVOLUCRA_BANDA", "SI");
        }

        // Involucra terrorismo
        if (Boolean.TRUE.equals(expediente.getTerrorismoFlag())) {
            prioridad += getParametroValor("INVOLUCRA_TERRORISMO", "SI");
        }

        // Nivel de organización de la banda
        String nivelOrganizacion = safeUpper(expediente.getNivelOrganizacion());
        prioridad += getParametroValor("NIVEL_ORGANIZACION", nivelOrganizacion);

        // Ámbito de la banda
        String ambitoBanda = safeUpper(expediente.getAmbitoBanda());
        prioridad += getParametroValor("AMBITO_BANDA", ambitoBanda);

        // Capacidad operativa de la banda
        String capacidadOperativa = safeUpper(expediente.getCapacidadOperativa());
        prioridad += getParametroValor("CAPACIDAD_OPERATIVA", capacidadOperativa);

        // Hubo planificación
        if (expediente.getPlanificacionFlag() != null) {
            if (expediente.getPlanificacionFlag()) {
                prioridad += getParametroValor("PLANIFICACION", "SI");
            } else {
                prioridad += getParametroValor("PLANIFICACION", "NO");
            }
        }

        // Conexiones con otras actividades delictivas
        if (Boolean.TRUE.equals(expediente.getConexionesOtrasActividadesFlag())) {
            prioridad += getParametroValor("CONEXIONES_OTRAS_ACTIVIDADES", "SI");
        }

        // Impacto social
        String impactoSocial = safeUpper(expediente.getImpactoSocial());
        prioridad += getParametroValor("IMPACTO_SOCIAL", impactoSocial);

        // Tipo de daño
        String tipoDano = safeUpper(expediente.getTipoDano());
        prioridad += getParametroValor("TIPO_DANO", tipoDano);

        // Uso de armas de fuego
        if (Boolean.TRUE.equals(expediente.getUsoArmasFuegoFlag())) {
            prioridad += getParametroValor("USO_ARMAS_FUEGO", "SI");
        }

        // Uso de armas blancas
        if (Boolean.TRUE.equals(expediente.getUsoArmasBlancasFlag())) {
            prioridad += getParametroValor("USO_ARMAS_BLANCAS", "SI");
        }

        // Nivel de incidencia delictual en la zona
        String nivelIncidenciaZona = safeUpper(expediente.getNivelIncidenciaZona());
        prioridad += getParametroValor("NIVEL_INCIDENCIA_ZONA", nivelIncidenciaZona);

        // Cercanía a institución sensible
        String institucionSensible = safeUpper(expediente.getInstitucionSensibleCercana());
        prioridad += getParametroValor("INSTITUCION_SENSIBLE_CERCANA", institucionSensible);

        // Recursos limitados del investigado/banda
        String recursosLimitados = safeUpper(expediente.getRecursosLimitados());
        if ("SI".equals(recursosLimitados)) {
            prioridad += getParametroValor("RECURSOS_LIMITADOS", "SI");
        } else if ("NO".equals(recursosLimitados)) {
            prioridad += getParametroValor("RECURSOS_LIMITADOS", "NO");
        }

        // Ocurrió en área fronteriza
        String areaFronteriza = safeUpper(expediente.getAreaFronteriza());
        if ("SI".equals(areaFronteriza)) {
            prioridad += getParametroValor("AREA_FRONTERIZA", "SI");
        } else if ("NO".equals(areaFronteriza)) {
            prioridad += getParametroValor("AREA_FRONTERIZA", "NO");
        }

        // Impacto en la percepción de seguridad
        String impactoPercepcion = safeUpper(expediente.getImpactoPercepcion());
        prioridad += getParametroValor("IMPACTO_PERCEPCION", impactoPercepcion);

        // Recompensa (SI/NO)
        if (Boolean.TRUE.equals(expediente.getRecompensa())) {
            prioridad += getParametroValor("RECOMPENSA", "SI");
        }

        return prioridad;
    }

    /**
     * Obtiene el valor de un parámetro de prioridad desde el servicio.
     * Si no encuentra el parámetro específico, busca el valor DEFAULT para ese tipo.
     * Si tampoco encuentra DEFAULT, retorna 0.
     */
    private int getParametroValor(String tipoVariable, String claveVariable) {
        try {
            // Primero intenta obtener el valor específico
            Integer valor = parametroPrioridadService.getValorParametro(tipoVariable + "_" + claveVariable);
            if (valor != null) {
                return valor;
            }
            
            // Si no encuentra el valor específico, busca el DEFAULT para ese tipo
            valor = parametroPrioridadService.getValorParametro(tipoVariable + "_DEFAULT");
            if (valor != null) {
                return valor;
            }
            
            // Si no encuentra ni el específico ni el DEFAULT, retorna 0
            return 0;
        } catch (Exception e) {
            // En caso de error, retorna 0 para no interrumpir el cálculo
            return 0;
        }
    }

    private static String safeUpper(String value) {
        return value != null ? value.trim().toUpperCase() : "DEFAULT";
    }
}