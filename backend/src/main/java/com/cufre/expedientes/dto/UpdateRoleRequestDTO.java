package com.cufre.expedientes.dto;

import com.cufre.expedientes.model.enums.Rol;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO para solicitud de cambio de rol de usuario
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleRequestDTO {
    
    @NotNull(message = "El nuevo rol es obligatorio")
    private Rol newRole;
    
    @NotBlank(message = "La contraseña es obligatoria")
    private String password;
}