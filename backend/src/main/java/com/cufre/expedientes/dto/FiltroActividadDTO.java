package com.cufre.expedientes.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

public class FiltroActividadDTO {
    private String usuario;
    private String modulo;
    private String categoriaAccion;
    private String estadoRespuesta;
    private String ipCliente;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fechaInicio;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fechaFin;
    
    // Parámetros de paginación
    private int page = 0;
    private int size = 20;
    private String sortBy = "fechaHora";
    private String sortDirection = "desc";

    // Constructor por defecto
    public FiltroActividadDTO() {}

    // Constructor completo
    public FiltroActividadDTO(String usuario, String modulo, String categoriaAccion, 
                             String estadoRespuesta, String ipCliente, 
                             LocalDateTime fechaInicio, LocalDateTime fechaFin,
                             int page, int size, String sortBy, String sortDirection) {
        this.usuario = usuario;
        this.modulo = modulo;
        this.categoriaAccion = categoriaAccion;
        this.estadoRespuesta = estadoRespuesta;
        this.ipCliente = ipCliente;
        this.fechaInicio = fechaInicio;
        this.fechaFin = fechaFin;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
        this.sortDirection = sortDirection;
    }

    // Getters y setters
    public String getUsuario() { return usuario; }
    public void setUsuario(String usuario) { this.usuario = usuario; }

    public String getModulo() { return modulo; }
    public void setModulo(String modulo) { this.modulo = modulo; }

    public String getCategoriaAccion() { return categoriaAccion; }
    public void setCategoriaAccion(String categoriaAccion) { this.categoriaAccion = categoriaAccion; }

    public String getEstadoRespuesta() { return estadoRespuesta; }
    public void setEstadoRespuesta(String estadoRespuesta) { this.estadoRespuesta = estadoRespuesta; }

    public String getIpCliente() { return ipCliente; }
    public void setIpCliente(String ipCliente) { this.ipCliente = ipCliente; }

    public LocalDateTime getFechaInicio() { return fechaInicio; }
    public void setFechaInicio(LocalDateTime fechaInicio) { this.fechaInicio = fechaInicio; }

    public LocalDateTime getFechaFin() { return fechaFin; }
    public void setFechaFin(LocalDateTime fechaFin) { this.fechaFin = fechaFin; }

    public int getPage() { return page; }
    public void setPage(int page) { this.page = page; }

    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }

    public String getSortBy() { return sortBy; }
    public void setSortBy(String sortBy) { this.sortBy = sortBy; }

    public String getSortDirection() { return sortDirection; }
    public void setSortDirection(String sortDirection) { this.sortDirection = sortDirection; }

    // Métodos de utilidad
    public boolean hasUsuarioFilter() {
        return usuario != null && !usuario.trim().isEmpty();
    }

    public boolean hasModuloFilter() {
        return modulo != null && !modulo.trim().isEmpty();
    }

    public boolean hasCategoriaAccionFilter() {
        return categoriaAccion != null && !categoriaAccion.trim().isEmpty();
    }

    public boolean hasEstadoRespuestaFilter() {
        return estadoRespuesta != null && !estadoRespuesta.trim().isEmpty();
    }

    public boolean hasIpClienteFilter() {
        return ipCliente != null && !ipCliente.trim().isEmpty();
    }

    public boolean hasFechaInicioFilter() {
        return fechaInicio != null;
    }

    public boolean hasFechaFinFilter() {
        return fechaFin != null;
    }

    public boolean hasDateRangeFilter() {
        return hasFechaInicioFilter() || hasFechaFinFilter();
    }
}