package com.cufre.expedientes.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;

public class ActividadSistemaDTO {
    private Long id;
    private String usuario;
    private String tipoAccion;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fechaHora;
    
    private String detalles;
    private String ipCliente;
    private String userAgent;
    private String sessionId;
    private String endpoint;
    private String metodoHttp;
    private String modulo;
    private String categoriaAccion;
    private Long duracionMs;
    private String estadoRespuesta;
    
    // Lista de detalles adicionales (opcional, solo para vista detallada)
    private List<ActividadDetalleDTO> actividadDetalles;

    // Constructor por defecto
    public ActividadSistemaDTO() {}

    // Constructor completo
    public ActividadSistemaDTO(Long id, String usuario, String tipoAccion, LocalDateTime fechaHora,
                              String detalles, String ipCliente, String userAgent, String sessionId,
                              String endpoint, String metodoHttp, String modulo, String categoriaAccion,
                              Long duracionMs, String estadoRespuesta) {
        this.id = id;
        this.usuario = usuario;
        this.tipoAccion = tipoAccion;
        this.fechaHora = fechaHora;
        this.detalles = detalles;
        this.ipCliente = ipCliente;
        this.userAgent = userAgent;
        this.sessionId = sessionId;
        this.endpoint = endpoint;
        this.metodoHttp = metodoHttp;
        this.modulo = modulo;
        this.categoriaAccion = categoriaAccion;
        this.duracionMs = duracionMs;
        this.estadoRespuesta = estadoRespuesta;
    }

    // Getters y setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsuario() { return usuario; }
    public void setUsuario(String usuario) { this.usuario = usuario; }

    public String getTipoAccion() { return tipoAccion; }
    public void setTipoAccion(String tipoAccion) { this.tipoAccion = tipoAccion; }

    public LocalDateTime getFechaHora() { return fechaHora; }
    public void setFechaHora(LocalDateTime fechaHora) { this.fechaHora = fechaHora; }

    public String getDetalles() { return detalles; }
    public void setDetalles(String detalles) { this.detalles = detalles; }

    public String getIpCliente() { return ipCliente; }
    public void setIpCliente(String ipCliente) { this.ipCliente = ipCliente; }

    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }

    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }

    public String getMetodoHttp() { return metodoHttp; }
    public void setMetodoHttp(String metodoHttp) { this.metodoHttp = metodoHttp; }

    public String getModulo() { return modulo; }
    public void setModulo(String modulo) { this.modulo = modulo; }

    public String getCategoriaAccion() { return categoriaAccion; }
    public void setCategoriaAccion(String categoriaAccion) { this.categoriaAccion = categoriaAccion; }

    public Long getDuracionMs() { return duracionMs; }
    public void setDuracionMs(Long duracionMs) { this.duracionMs = duracionMs; }

    public String getEstadoRespuesta() { return estadoRespuesta; }
    public void setEstadoRespuesta(String estadoRespuesta) { this.estadoRespuesta = estadoRespuesta; }

    public List<ActividadDetalleDTO> getActividadDetalles() { return actividadDetalles; }
    public void setActividadDetalles(List<ActividadDetalleDTO> actividadDetalles) { this.actividadDetalles = actividadDetalles; }
}