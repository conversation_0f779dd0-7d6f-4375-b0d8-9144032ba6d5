package com.cufre.expedientes.dto;

import com.cufre.expedientes.model.ParametroPrioridad;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParametroPrioridadDTO {
    
    private Long id;
    private String claveVariable;
    private Integer valor;
    private String descripcion;
    private ParametroPrioridad.TipoVariable tipoVariable;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaModificacion;
    private String modificadoPor;
}