package com.cufre.expedientes.dto;

import com.cufre.expedientes.model.enums.Rol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PerfilUsuarioDTO {
    
    private Long id;
    
    @NotBlank(message = "El nombre es obligatorio")
    @Size(max = 100, message = "El nombre no puede exceder 100 caracteres")
    private String nombre;
    
    @NotBlank(message = "El apellido es obligatorio")
    @Size(max = 100, message = "El apellido no puede exceder 100 caracteres")
    private String apellido;
    
    @NotBlank(message = "El email es obligatorio")
    @Email(message = "El formato del email no es válido")
    @Size(max = 150, message = "El email no puede exceder 150 caracteres")
    private String email;
    
    @Size(max = 100, message = "La dependencia no puede exceder 100 caracteres")
    private String dependencia;
    
    @Pattern(
        regexp = "^(\\+54\\s9\\s\\d{2}\\s\\d{4}-\\d{4})?$",
        message = "El teléfono debe tener el formato argentino: +54 9 xx xxxx-xxxx"
    )
    private String telefonoMovil;
    
    private Rol rol;
    
    // Método de conveniencia
    public String getNombreCompleto() {
        return nombre + " " + apellido;
    }
}