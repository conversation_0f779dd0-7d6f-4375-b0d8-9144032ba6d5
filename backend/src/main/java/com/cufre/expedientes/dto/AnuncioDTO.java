package com.cufre.expedientes.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnuncioDTO {
    
    private Long id;
    private String titulo;
    private String contenido;
    private boolean activo;
    private LocalDateTime fechaCreacion;
    private Long creadoPorId;
    private String creadoPorNombre;
    
    // Campos adicionales para estadísticas (opcional)
    private Long totalVistas;
    
    // Método de conveniencia
    public boolean esActivo() {
        return activo;
    }
}