package com.cufre.expedientes.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

public class ActividadDetalleDTO {
    private Long id;
    private Long actividadId;
    private String tipoDetalle;
    private String contenidoJson;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fechaCreacion;

    // Constructor por defecto
    public ActividadDetalleDTO() {}

    // Constructor completo
    public ActividadDetalleDTO(Long id, Long actividadId, String tipoDetalle, 
                              String contenidoJson, LocalDateTime fechaCreacion) {
        this.id = id;
        this.actividadId = actividadId;
        this.tipoDetalle = tipoDetalle;
        this.contenidoJson = contenidoJson;
        this.fechaCreacion = fechaCreacion;
    }

    // Getters y setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getActividadId() { return actividadId; }
    public void setActividadId(Long actividadId) { this.actividadId = actividadId; }

    public String getTipoDetalle() { return tipoDetalle; }
    public void setTipoDetalle(String tipoDetalle) { this.tipoDetalle = tipoDetalle; }

    public String getContenidoJson() { return contenidoJson; }
    public void setContenidoJson(String contenidoJson) { this.contenidoJson = contenidoJson; }

    public LocalDateTime getFechaCreacion() { return fechaCreacion; }
    public void setFechaCreacion(LocalDateTime fechaCreacion) { this.fechaCreacion = fechaCreacion; }
}