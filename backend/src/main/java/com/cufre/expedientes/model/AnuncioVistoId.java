package com.cufre.expedientes.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.persistence.Embeddable;
import jakarta.persistence.Column;
import java.io.Serializable;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnuncioVistoId implements Serializable {
    
    @Column(name = "USUARIO_ID")
    private Long usuarioId;
    
    @Column(name = "ANUNCIO_ID")
    private Long anuncioId;
}