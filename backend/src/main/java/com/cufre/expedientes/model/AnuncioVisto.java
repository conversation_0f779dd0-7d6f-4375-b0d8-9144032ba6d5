package com.cufre.expedientes.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "ANUNCIOS_VISTOS")
@Data
@EqualsAndHashCode(of = "id")
@ToString(exclude = {"usuario", "anuncio"})
@NoArgsConstructor
@AllArgsConstructor
public class AnuncioVisto {
    
    @EmbeddedId
    private AnuncioVistoId id;
    
    @Column(name = "FECHA_VISTO", nullable = false)
    private LocalDateTime fechaVisto = LocalDateTime.now();
    
    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("usuarioId")
    @JoinColumn(name = "USUARIO_ID")
    private Usuario usuario;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("anuncioId")
    @JoinColumn(name = "ANUNCIO_ID")
    private Anuncio anuncio;
    
    // Constructor de conveniencia
    public AnuncioVisto(Long usuarioId, Long anuncioId) {
        this.id = new AnuncioVistoId(usuarioId, anuncioId);
        this.fechaVisto = LocalDateTime.now();
    }
    
    // Constructor de conveniencia con entidades
    public AnuncioVisto(Usuario usuario, Anuncio anuncio) {
        this.id = new AnuncioVistoId(usuario.getId(), anuncio.getId());
        this.usuario = usuario;
        this.anuncio = anuncio;
        this.fechaVisto = LocalDateTime.now();
    }
}