package com.cufre.expedientes.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "ACTIVIDAD_DETALLE")
public class ActividadDetalle {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "actividad_detalle_seq")
    @SequenceGenerator(name = "actividad_detalle_seq", sequenceName = "actividad_detalle_seq", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "ACTIVIDAD_ID", nullable = false)
    private Long actividadId;

    @Column(name = "TIPO_DETALLE", nullable = false, length = 50)
    private String tipoDetalle; // REQUEST, RESPONSE, CAMBIO_ANTERIOR, CAMBIO_POSTERIOR

    @Lob
    @Column(name = "CONTENIDO_JSON")
    private String contenidoJson;

    @Column(name = "FECHA_CREACION")
    private LocalDateTime fechaCreacion;

    // Relación con ActividadSistema
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ACTIVIDAD_ID", insertable = false, updatable = false)
    private ActividadSistema actividadSistema;

    // Constructor por defecto
    public ActividadDetalle() {
        this.fechaCreacion = LocalDateTime.now();
    }

    // Constructor con parámetros
    public ActividadDetalle(Long actividadId, String tipoDetalle, String contenidoJson) {
        this.actividadId = actividadId;
        this.tipoDetalle = tipoDetalle;
        this.contenidoJson = contenidoJson;
        this.fechaCreacion = LocalDateTime.now();
    }

    // Getters y setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActividadId() {
        return actividadId;
    }

    public void setActividadId(Long actividadId) {
        this.actividadId = actividadId;
    }

    public String getTipoDetalle() {
        return tipoDetalle;
    }

    public void setTipoDetalle(String tipoDetalle) {
        this.tipoDetalle = tipoDetalle;
    }

    public String getContenidoJson() {
        return contenidoJson;
    }

    public void setContenidoJson(String contenidoJson) {
        this.contenidoJson = contenidoJson;
    }

    public LocalDateTime getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(LocalDateTime fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public ActividadSistema getActividadSistema() {
        return actividadSistema;
    }

    public void setActividadSistema(ActividadSistema actividadSistema) {
        this.actividadSistema = actividadSistema;
    }

    // Enums para tipos de detalle
    public static class TipoDetalle {
        public static final String REQUEST = "REQUEST";
        public static final String RESPONSE = "RESPONSE";
        public static final String CAMBIO_ANTERIOR = "CAMBIO_ANTERIOR";
        public static final String CAMBIO_POSTERIOR = "CAMBIO_POSTERIOR";
        public static final String METADATA = "METADATA";
        public static final String ERROR = "ERROR";
    }
}