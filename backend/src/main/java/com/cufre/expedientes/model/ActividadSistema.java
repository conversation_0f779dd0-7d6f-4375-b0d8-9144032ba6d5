package com.cufre.expedientes.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "ACTIVIDAD_SISTEMA")
public class ActividadSistema {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "actividad_sistema_seq")
    @SequenceGenerator(name = "actividad_sistema_seq", sequenceName = "actividad_sistema_seq", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "USUARIO", nullable = false)
    private String usuario; // email o nombre del usuario que realizó la acción

    @Column(name = "TIPO_ACCION", nullable = false)
    private String tipoAccion; // Ej: LOGIN, CREAR_EXPEDIENTE, EDITAR_EXPEDIENTE, etc.

    @Column(name = "FECHA_HORA", nullable = false)
    private LocalDateTime fechaHora;

    @Column(name = "DETALLES", length = 1000)
    private String detalles; // Detalles adicionales de la acción (ej: número de expediente, cambios, etc.)

    // Nuevos campos para auditoría mejorada
    @Column(name = "IP_CLIENTE", length = 45)
    private String ipCliente;

    @Column(name = "USER_AGENT", length = 500)
    private String userAgent;

    @Column(name = "SESSION_ID")
    private String sessionId;

    @Column(name = "ENDPOINT")
    private String endpoint;

    @Column(name = "METODO_HTTP", length = 10)
    private String metodoHttp;

    @Column(name = "MODULO", length = 50)
    private String modulo; // EXPEDIENTES, USUARIOS, SISTEMA, REPORTES

    @Column(name = "CATEGORIA_ACCION", length = 100)
    private String categoriaAccion; // Categoría específica de la acción

    @Column(name = "DURACION_MS")
    private Long duracionMs;

    @Column(name = "ESTADO_RESPUESTA", length = 20)
    private String estadoRespuesta; // SUCCESS, ERROR, WARNING

    // Relación con detalles adicionales
    @OneToMany(mappedBy = "actividadSistema", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ActividadDetalle> actividadDetalles;

    // Constructor por defecto
    public ActividadSistema() {
        this.fechaHora = LocalDateTime.now();
    }

    // Getters y setters originales
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsuario() { return usuario; }
    public void setUsuario(String usuario) { this.usuario = usuario; }

    public String getTipoAccion() { return tipoAccion; }
    public void setTipoAccion(String tipoAccion) { this.tipoAccion = tipoAccion; }

    public LocalDateTime getFechaHora() { return fechaHora; }
    public void setFechaHora(LocalDateTime fechaHora) { this.fechaHora = fechaHora; }

    public String getDetalles() { return detalles; }
    public void setDetalles(String detalles) { this.detalles = detalles; }

    // Getters y setters para nuevos campos
    public String getIpCliente() { return ipCliente; }
    public void setIpCliente(String ipCliente) { this.ipCliente = ipCliente; }

    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }

    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }

    public String getMetodoHttp() { return metodoHttp; }
    public void setMetodoHttp(String metodoHttp) { this.metodoHttp = metodoHttp; }

    public String getModulo() { return modulo; }
    public void setModulo(String modulo) { this.modulo = modulo; }

    public String getCategoriaAccion() { return categoriaAccion; }
    public void setCategoriaAccion(String categoriaAccion) { this.categoriaAccion = categoriaAccion; }

    public Long getDuracionMs() { return duracionMs; }
    public void setDuracionMs(Long duracionMs) { this.duracionMs = duracionMs; }

    public String getEstadoRespuesta() { return estadoRespuesta; }
    public void setEstadoRespuesta(String estadoRespuesta) { this.estadoRespuesta = estadoRespuesta; }

    public List<ActividadDetalle> getActividadDetalles() { return actividadDetalles; }
    public void setActividadDetalles(List<ActividadDetalle> actividadDetalles) { this.actividadDetalles = actividadDetalles; }

    // Enums para constantes
    public static class Modulo {
        public static final String EXPEDIENTES = "EXPEDIENTES";
        public static final String USUARIOS = "USUARIOS";
        public static final String SISTEMA = "SISTEMA";
        public static final String REPORTES = "REPORTES";
    }

    public static class EstadoRespuesta {
        public static final String SUCCESS = "SUCCESS";
        public static final String ERROR = "ERROR";
        public static final String WARNING = "WARNING";
    }
}
