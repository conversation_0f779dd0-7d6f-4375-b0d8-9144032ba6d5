package com.cufre.expedientes.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "ANUNCIOS")
@Data
@EqualsAndHashCode(of = "id")
@ToString(exclude = {"creadoPor", "anunciosVistos"})
public class Anuncio {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "TITULO", nullable = false, length = 255)
    private String titulo;
    
    @Lob
    @Column(name = "CONTENIDO", nullable = false)
    private String contenido;
    
    @Column(name = "ACTIVO", nullable = false)
    private boolean activo = false;
    
    @Column(name = "FECHA_CREACION", nullable = false)
    private LocalDateTime fechaCreacion = LocalDateTime.now();
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CREADO_POR_ID", nullable = false)
    private Usuario creadoPor;
    
    @OneToMany(mappedBy = "anuncio", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AnuncioVisto> anunciosVistos;
    
    // Métodos de conveniencia
    public boolean esActivo() {
        return activo;
    }
    
    public void activar() {
        this.activo = true;
    }
    
    public void desactivar() {
        this.activo = false;
    }
}