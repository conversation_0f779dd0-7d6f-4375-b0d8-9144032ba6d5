package com.cufre.expedientes.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "PARAMETRO_PRIORIDAD")
public class ParametroPrioridad {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "parametro_prioridad_seq")
    @SequenceGenerator(name = "parametro_prioridad_seq", sequenceName = "PARAMETRO_PRIORIDAD_SEQ", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CLAVE_VARIABLE", nullable = false, unique = true, length = 100)
    private String claveVariable;

    @Column(name = "VALOR", nullable = false)
    private Integer valor;

    @Column(name = "DESCRIPCION", length = 500)
    private String descripcion;

    @Enumerated(EnumType.STRING)
    @Column(name = "TIPO_VARIABLE", nullable = false, length = 20)
    private TipoVariable tipoVariable;

    @Column(name = "FECHA_CREACION", nullable = false)
    private LocalDateTime fechaCreacion;

    @Column(name = "FECHA_MODIFICACION")
    private LocalDateTime fechaModificacion;

    @Column(name = "MODIFICADO_POR", length = 100)
    private String modificadoPor;

    // Constructores
    public ParametroPrioridad() {
        this.fechaCreacion = LocalDateTime.now();
    }

    public ParametroPrioridad(String claveVariable, Integer valor, String descripcion, TipoVariable tipoVariable) {
        this();
        this.claveVariable = claveVariable;
        this.valor = valor;
        this.descripcion = descripcion;
        this.tipoVariable = tipoVariable;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClaveVariable() {
        return claveVariable;
    }

    public void setClaveVariable(String claveVariable) {
        this.claveVariable = claveVariable;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public TipoVariable getTipoVariable() {
        return tipoVariable;
    }

    public void setTipoVariable(TipoVariable tipoVariable) {
        this.tipoVariable = tipoVariable;
    }

    public LocalDateTime getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(LocalDateTime fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public LocalDateTime getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(LocalDateTime fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(String modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    // Método de callback JPA para actualizar fechaModificacion antes de persistir
    @PreUpdate
    private void preUpdate() {
        this.fechaModificacion = LocalDateTime.now();
    }

    // Enum para tipos de variable
    public enum TipoVariable {
        PROFESION,
        DETENCIONES_PREVIAS,
        NUMERO_COMPLICES,
        TIPO_CAPTURA,
        TIPO_VICTIMA,
        CASO_MEDIATICO,
        PROFUGO_REINCIDENTE,
        PROFUGO_REITERANTE,
        INVOLUCRA_BANDA,
        INVOLUCRA_TERRORISMO,
        NIVEL_ORGANIZACION,
        AMBITO_BANDA,
        CAPACIDAD_OPERATIVA,
        PLANIFICACION,
        CONEXIONES_OTRAS_ACTIVIDADES,
        IMPACTO_SOCIAL,
        TIPO_DANO,
        USO_ARMAS_FUEGO,
        USO_ARMAS_BLANCAS,
        NIVEL_INCIDENCIA_ZONA,
        INSTITUCION_SENSIBLE_CERCANA,
        RECURSOS_LIMITADOS,
        AREA_FRONTERIZA,
        IMPACTO_PERCEPCION,
        RECOMPENSA,
        // Mantener los originales por si se usan en otro lado
        CATEGORICA,
        BOOLEANA,
        RANGO
    }

    @Override
    public String toString() {
        return "ParametroPrioridad{" +
                "id=" + id +
                ", claveVariable='" + claveVariable + '\'' +
                ", valor=" + valor +
                ", descripcion='" + descripcion + '\'' +
                ", tipoVariable=" + tipoVariable +
                ", fechaCreacion=" + fechaCreacion +
                ", fechaModificacion=" + fechaModificacion +
                ", modificadoPor='" + modificadoPor + '\'' +
                '}';
    }
}