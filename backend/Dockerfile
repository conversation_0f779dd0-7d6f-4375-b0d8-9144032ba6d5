FROM openjdk:17-jdk-slim

# Instalar dependencias necesarias para Oracle y curl para health checks
RUN apt-get update && apt-get install -y libaio1 curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copiar el JAR de la aplicación
COPY target/*.jar app.jar

EXPOSE 8080

# Iniciar la aplicación con el perfil Oracle
ENTRYPOINT ["java", "-jar", "/app/app.jar", "--spring.profiles.active=oracle", "--spring.flyway.validate-on-migrate=false", "--spring.flyway.ignore-migration-patterns=*:*"]
