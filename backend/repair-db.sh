#!/bin/bash

echo "Iniciando proceso de reparación de migración Flyway..."

# Instalar cliente Oracle
echo "Instalando dependencias de Oracle..."
apt-get update
apt-get install -y libaio1 wget unzip

# Descargar Oracle Instant Client
echo "Descargando Oracle Instant Client..."
mkdir -p /opt/oracle
cd /opt/oracle
wget -q https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip
unzip -q instantclient-basiclite-linuxx64.zip
rm instantclient-basiclite-linuxx64.zip
cd instantclient*
export LD_LIBRARY_PATH=`pwd`:$LD_LIBRARY_PATH

# Descargar SQLcl (cliente SQL de Oracle)
echo "Descargando SQLcl..."
cd /opt/oracle
wget -q https://download.oracle.com/otn_software/java/sqldeveloper/sqlcl-latest.zip
unzip -q sqlcl-latest.zip
rm sqlcl-latest.zip

# Ejecutar el script SQL para eliminar la entrada fallida de V1003
echo "Eliminando entrada fallida de V1003 del historial de Flyway..."
/opt/oracle/sqlcl/bin/sql -S CUFRE-TEST/C5fr3T3st!@test-oracle-db.minseg.gob.ar:1521/testoracledb @/app/repair-flyway.sql

# Descargar Flyway CLI para ejecutar la reparación
echo "Descargando Flyway CLI..."
cd /app
wget -q https://repo1.maven.org/maven2/org/flywaydb/flyway-commandline/9.22.3/flyway-commandline-9.22.3-linux-x64.tar.gz
tar -xzf flyway-commandline-9.22.3-linux-x64.tar.gz
rm flyway-commandline-9.22.3-linux-x64.tar.gz
mv flyway-9.22.3 flyway

# Configurar Flyway
echo "Configurando Flyway..."
cat > flyway/conf/flyway.conf << EOF
flyway.url=****************************************************************
flyway.user=CUFRE-TEST
flyway.password=C5fr3T3st!
flyway.locations=filesystem:/app/migrations
flyway.baselineOnMigrate=true
flyway.outOfOrder=true
flyway.ignoreMigrationPatterns=V1003__*
flyway.cleanOnValidationError=true
flyway.validateOnMigrate=false
EOF

# Ejecutar comando de reparación
echo "Ejecutando reparación de Flyway..."
./flyway/flyway repair

# Ejecutar migración
echo "Ejecutando migración..."
./flyway/flyway migrate

# Limpiar
echo "Limpiando archivos temporales..."
rm -rf flyway

# Iniciar la aplicación con perfil oracle
echo "Iniciando la aplicación con perfil oracle..."
java -jar /app/app.jar --spring.profiles.active=oracle,repair
