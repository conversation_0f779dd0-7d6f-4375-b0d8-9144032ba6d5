#!/bin/bash

# Descargar Flyway CLI
echo "Descargando Flyway CLI..."
wget -q https://repo1.maven.org/maven2/org/flywaydb/flyway-commandline/9.22.3/flyway-commandline-9.22.3-linux-x64.tar.gz
tar -xzf flyway-commandline-9.22.3-linux-x64.tar.gz
rm flyway-commandline-9.22.3-linux-x64.tar.gz
mv flyway-9.22.3 flyway

# Configurar Flyway
echo "Configurando Flyway..."
cat > flyway/conf/flyway.conf << EOF
flyway.url=****************************************************************
flyway.user=CUFRE-TEST
flyway.password=C5fr3T3st!
flyway.locations=filesystem:/app/classes/db/migration
flyway.baselineOnMigrate=true
flyway.outOfOrder=true
flyway.ignoreMigrationPatterns=V1003__*
EOF

# Ejecutar comando de reparación
echo "Ejecutando comando de reparación de Flyway..."
./flyway/flyway repair

# Limpiar
echo "Limpiando..."
rm -rf flyway

# Iniciar la aplicación
echo "Iniciando la aplicación..."
java -jar /app/app.jar
