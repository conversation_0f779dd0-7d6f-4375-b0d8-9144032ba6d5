#!/bin/bash

echo "=== REPARACIÓN DE FLYWAY PARA DESARROLLO LOCAL ==="
echo "Este script reparará la migración fallida V1003 en tu base de datos Oracle local"

# Configuración de la base de datos local
DB_URL="***********************************"
DB_USER="C##CUFRE_USER"
DB_PASSWORD="Cufre-2025"

echo "1. Descargando Flyway CLI..."
cd /tmp
curl -L -o flyway-commandline-9.22.3-macosx-x64.tar.gz https://repo1.maven.org/maven2/org/flywaydb/flyway-commandline/9.22.3/flyway-commandline-9.22.3-macosx-x64.tar.gz
tar -xzf flyway-commandline-9.22.3-macosx-x64.tar.gz
rm flyway-commandline-9.22.3-macosx-x64.tar.gz

echo "2. Configurando Flyway para tu base de datos local..."
cat > flyway-9.22.3/conf/flyway.conf << EOF
flyway.url=${DB_URL}
flyway.user=${DB_USER}
flyway.password=${DB_PASSWORD}
flyway.locations=filesystem:$(pwd)/../backend/src/main/resources/db/migration
flyway.baselineOnMigrate=true
flyway.outOfOrder=true
flyway.validateOnMigrate=false
flyway.cleanOnValidationError=true
EOF

echo "3. Ejecutando reparación de Flyway..."
./flyway-9.22.3/flyway repair

echo "4. Verificando estado de las migraciones..."
./flyway-9.22.3/flyway info

echo "5. Limpiando archivos temporales..."
rm -rf flyway-9.22.3

echo "=== REPARACIÓN COMPLETADA ==="
echo "Ahora puedes intentar ejecutar tu aplicación Spring Boot nuevamente."
echo "Comando: cd backend && JAVA_HOME=/opt/homebrew/Cellar/openjdk@17/17.0.15/libexec/openjdk.jdk/Contents/Home mvn spring-boot:run -Dspring-boot.run.profiles=dev"