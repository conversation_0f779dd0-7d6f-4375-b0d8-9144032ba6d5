#!/bin/bash

echo "=== [CUFRE] Solucionando problema del backend ==="
echo ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==> Paso 1: Deteniendo contenedores existentes...${NC}"
docker compose down

echo ""
echo -e "${BLUE}==> Paso 2: Recompilando JAR del backend...${NC}"
echo "Limpiando target anterior..."
rm -rf backend/target/
echo "Compilando con Maven..."
cd backend
mvn clean package -DskipTests
cd ..

echo ""
echo -e "${BLUE}==> Paso 3: Limpiando imágenes Docker antiguas...${NC}"
docker compose build --no-cache backend

echo ""
echo -e "${BLUE}==> Paso 4: Verificando migraciones de Flyway...${NC}"
echo "Archivos de migración V8:"
ls -la backend/src/main/resources/db/migration/V8__* 2>/dev/null || echo "No hay conflictos V8"
echo ""
echo "Archivo V10 (renombrado):"
ls -la backend/src/main/resources/db/migration/V10__* 2>/dev/null || echo "Archivo V10 no encontrado"

echo ""
echo -e "${BLUE}==> Paso 5: Levantando servicios con health checks...${NC}"
docker compose up -d

echo ""
echo -e "${BLUE}==> Paso 6: Esperando que los servicios estén listos...${NC}"
echo "Esperando 30 segundos para que el backend inicie..."
sleep 30

echo ""
echo -e "${BLUE}==> Paso 7: Verificando estado de los contenedores...${NC}"
docker compose ps

echo ""
echo -e "${BLUE}==> Paso 8: Verificando logs del backend...${NC}"
echo "Últimas 20 líneas de logs del backend:"
docker compose logs --tail=20 backend

echo ""
echo -e "${BLUE}==> Paso 9: Verificando conectividad...${NC}"
echo "Probando endpoint de salud del backend..."
if curl -f http://localhost/api/actuator/health 2>/dev/null; then
    echo -e "${GREEN}✅ Backend responde correctamente${NC}"
else
    echo -e "${YELLOW}⚠️  Backend aún no responde, puede necesitar más tiempo${NC}"
fi

echo ""
echo -e "${BLUE}==> Paso 10: Verificando frontend...${NC}"
if curl -f http://localhost/ 2>/dev/null >/dev/null; then
    echo -e "${GREEN}✅ Frontend responde correctamente${NC}"
else
    echo -e "${RED}❌ Frontend no responde${NC}"
fi

echo ""
echo "=== [CUFRE] Resumen del despliegue ==="
echo -e "${GREEN}✅ Conflicto de migraciones Flyway resuelto${NC}"
echo -e "${GREEN}✅ Docker-compose mejorado con health checks${NC}"
echo -e "${GREEN}✅ Dockerfile actualizado con curl${NC}"
echo ""
echo "Accede a la aplicación en:"
echo -e "${BLUE}🌐 Frontend: http://localhost${NC}"
echo -e "${BLUE}🔧 API: http://localhost/api/${NC}"
echo -e "${BLUE}💚 Health Check: http://localhost/api/actuator/health${NC}"
echo ""
echo "Para ver logs en tiempo real:"
echo "docker compose logs -f backend"
echo ""