# Sistema de Gestión de Expedientes CUFRE

Este proyecto está organizado con una arquitectura de microservicios, separando el backend (Java/Spring Boot) y el frontend (React).

## Estructura del Proyecto

```
CUFRE_V04/
├── backend/                 # Código del backend (Java/Spring Boot)
│   ├── src/                 # Código fuente
│   ├── pom.xml              # Configuración de Maven
│   ├── Dockerfile           # Configuración para construir imagen Docker
│   └── ...
├── frontend/                # Código del frontend (React)
│   ├── src/                 # Código fuente
│   ├── public/              # Archivos estáticos
│   ├── package.json         # Dependencias de NPM
│   ├── Dockerfile           # Configuración para construir imagen Docker
│   └── ...
└── docker-compose.yml       # Configuración para ejecutar todos los servicios
```

## Requisitos

- Java 17
- Node.js (versión 16 o superior)
- <PERSON><PERSON>
- Docker (opcional, para contenedores)

## Ejecutar la Aplicación

### Modo Desarrollo

**Backend:**
```bash
cd backend
mvn spring-boot:run
```
El backend estará disponible en http://localhost:8080

**Frontend:**
```bash
cd frontend
npm install
npm start
```
El frontend estará disponible en http://localhost:3000

### Usando Docker (recomendado para producción)

Para ejecutar toda la aplicación usando Docker:

```bash
docker-compose up -d
```

El frontend estará disponible en http://localhost y el backend en http://localhost:8080

## Compilación

**Backend:**
```bash
cd backend
mvn clean install
```

**Frontend:**
```bash
cd frontend
npm run build
```

# Manual de Despliegue Dockerizado

## Requisitos previos
- Docker y Docker Compose instalados.
- Acceso a una base de datos Oracle ya configurada con el esquema necesario.
- El archivo JAR del backend debe estar generado en `backend/target/` (ejecuta `mvn package` en el backend si no está).

## Configuración

La conexión a la base de datos Oracle está definida en el archivo:
- `backend/src/main/resources/application-oracle.properties`

Por defecto, el perfil activo es `oracle`. Si necesitas cambiar usuario, contraseña o URL de la base de datos, edita ese archivo:

```properties
spring.datasource.url=jdbc:oracle:thin:@<host>:<puerto>/<servicio>
spring.datasource.username=<usuario>
spring.datasource.password=<contraseña>
```

## Despliegue

Desde la raíz del proyecto, ejecuta:

```bash
docker-compose up --build
```

Esto levantará:
- Backend (Java, puerto 8080)
- Frontend (Nginx, puerto 80)

El frontend redirige automáticamente las llamadas `/api` al backend.

## Verificación

- Accede a la aplicación en: [http://localhost](http://localhost)
- Para ver los logs de los servicios:
  ```bash
  docker-compose logs -f
  ```

## Notas
- Si cambias la configuración de la base de datos, vuelve a construir el backend y reinicia los contenedores.
- Si necesitas cambiar el perfil de Spring Boot, edita la variable `SPRING_PROFILES_ACTIVE` en `docker-compose.yml`. 