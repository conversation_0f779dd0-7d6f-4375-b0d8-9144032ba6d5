-- Migración V1007: Agregar campos de perfil a tabla USUARIO y crear sistema de avatares
-- Fecha: Enero 2025
-- Descripción: Implementación del sistema de perfiles de usuario con teléfono móvil y avatares predefinidos

-- Agregar campos de perfil a tabla USUARIO
ALTER TABLE USUARIO ADD TELEFONO_MOVIL VARCHAR2(20 CHAR);
ALTER TABLE USUARIO ADD AVATAR_URL VARCHAR2(255 CHAR);

-- Crear tabla de avatares predefinidos
CREATE TABLE AVATAR_PREDEFINIDO (
    ID NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
    NOMBRE VARCHAR2(100 CHAR) NOT NULL,
    URL VARCHAR2(255 CHAR) NOT NULL,
    CATEGORIA VARCHAR2(50 CHAR) NOT NULL,
    ACTIVO NUMBER(1) DEFAULT 1 CHECK (ACTIVO IN (0,1)),
    ORDEN_DISPLAY NUMBER DEFAULT 0,
    FEC<PERSON>_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Crear índices para optimizar consultas
CREATE INDEX IDX_AVATAR_CATEGORIA ON AVATAR_PREDEFINIDO(CATEGORIA);
CREATE INDEX IDX_AVATAR_ACTIVO ON AVATAR_PREDEFINIDO(ACTIVO);
CREATE INDEX IDX_AVATAR_ORDEN ON AVATAR_PREDEFINIDO(ORDEN_DISPLAY);

-- Insertar avatares predefinidos iniciales
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
-- Categoría Profesional (8 avatares)
('Ejecutivo Masculino', '/avatares/profesional/ejecutivo-m.svg', 'profesional', 1),
('Ejecutiva Femenina', '/avatares/profesional/ejecutiva-f.svg', 'profesional', 2),
('Oficial Masculino', '/avatares/profesional/oficial-m.svg', 'profesional', 3),
('Oficial Femenina', '/avatares/profesional/oficial-f.svg', 'profesional', 4),
('Investigador', '/avatares/profesional/investigador.svg', 'profesional', 5),
('Investigadora', '/avatares/profesional/investigadora.svg', 'profesional', 6),
('Analista', '/avatares/profesional/analista.svg', 'profesional', 7),
('Supervisor', '/avatares/profesional/supervisor.svg', 'profesional', 8),

-- Categoría Casual (8 avatares)
('Persona Amigable', '/avatares/casual/amigable.svg', 'casual', 9),
('Persona Sonriente', '/avatares/casual/sonriente.svg', 'casual', 10),
('Persona Relajada', '/avatares/casual/relajada.svg', 'casual', 11),
('Persona Moderna', '/avatares/casual/moderna.svg', 'casual', 12),
('Persona Joven', '/avatares/casual/joven.svg', 'casual', 13),
('Persona Creativa', '/avatares/casual/creativa.svg', 'casual', 14),
('Persona Deportiva', '/avatares/casual/deportiva.svg', 'casual', 15),
('Persona Artística', '/avatares/casual/artistica.svg', 'casual', 16),

-- Categoría Iconos (8 avatares)
('Escudo Policial', '/avatares/iconos/escudo-policial.svg', 'iconos', 17),
('Insignia GNA', '/avatares/iconos/insignia-gna.svg', 'iconos', 18),
('Estrella Seguridad', '/avatares/iconos/estrella-seguridad.svg', 'iconos', 19),
('Badge Investigador', '/avatares/iconos/badge-investigador.svg', 'iconos', 20),
('Símbolo Justicia', '/avatares/iconos/simbolo-justicia.svg', 'iconos', 21),
('Emblema Orden', '/avatares/iconos/emblema-orden.svg', 'iconos', 22),
('Logo Institucional', '/avatares/iconos/logo-institucional.svg', 'iconos', 23),
('Símbolo Autoridad', '/avatares/iconos/simbolo-autoridad.svg', 'iconos', 24),

-- Categoría Diversos (6 avatares)
('Avatar Neutro 1', '/avatares/diversos/neutro-1.svg', 'diversos', 25),
('Avatar Neutro 2', '/avatares/diversos/neutro-2.svg', 'diversos', 26),
('Avatar Inclusivo 1', '/avatares/diversos/inclusivo-1.svg', 'diversos', 27),
('Avatar Inclusivo 2', '/avatares/diversos/inclusivo-2.svg', 'diversos', 28),
('Avatar Universal 1', '/avatares/diversos/universal-1.svg', 'diversos', 29),
('Avatar Universal 2', '/avatares/diversos/universal-2.svg', 'diversos', 30);

-- Comentarios para documentación
COMMENT ON COLUMN USUARIO.TELEFONO_MOVIL IS 'Teléfono móvil del usuario con formato argentino (+54 9 xx xxxx-xxxx)';
COMMENT ON COLUMN USUARIO.AVATAR_URL IS 'URL del avatar personalizado del usuario';
COMMENT ON TABLE AVATAR_PREDEFINIDO IS 'Galería de avatares predefinidos organizados por categorías';
COMMENT ON COLUMN AVATAR_PREDEFINIDO.CATEGORIA IS 'Categoría del avatar: profesional, casual, iconos, diversos';
COMMENT ON COLUMN AVATAR_PREDEFINIDO.ORDEN_DISPLAY IS 'Orden de visualización dentro de la categoría';