-- Migración para crear las tablas del sistema de anuncios globales
-- Autor: Sistema CUFRE
-- Fecha: 2025-01-20

-- Crear secuencia para la tabla ANUNCIOS
CREATE SEQUENCE ANUNCIOS_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Tabla para almacenar los anuncios globales
CREATE TABLE ANUNCIOS (
    ID NUMBER(19,0) DEFAULT ANUNCIOS_SEQ.NEXTVAL PRIMARY KEY,
    TITULO VARCHAR2(255) NOT NULL,
    CONTENIDO CLOB NOT NULL, -- CLOB para soportar HTML largo
    ACTIVO NUMBER(1,0) DEFAULT 0 NOT NULL,
    FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREADO_POR_ID NUMBER(19,0) NOT NULL,
    CONSTRAINT FK_ANUNCIOS_USUARIO FOREIGN KEY (CREADO_POR_ID) REFERENCES USUARIO(ID)
);

-- Tabla para rastrear qué usuarios han visto qué anuncios
CREATE TABLE ANUNCIOS_VISTOS (
    USUARIO_ID NUMBER(19,0) NOT NULL,
    ANUNCIO_ID NUMBER(19,0) NOT NULL,
    FECHA_VISTO TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (USUARIO_ID, ANUNCIO_ID),
    CONSTRAINT FK_VISTOS_USUARIO FOREIGN KEY (USUARIO_ID) REFERENCES USUARIO(ID) ON DELETE CASCADE,
    CONSTRAINT FK_VISTOS_ANUNCIO FOREIGN KEY (ANUNCIO_ID) REFERENCES ANUNCIOS(ID) ON DELETE CASCADE
);

-- Índices para mejorar el rendimiento
CREATE INDEX IDX_ANUNCIOS_ACTIVO ON ANUNCIOS(ACTIVO);
CREATE INDEX IDX_ANUNCIOS_FECHA_CREACION ON ANUNCIOS(FECHA_CREACION);
CREATE INDEX IDX_ANUNCIOS_VISTOS_USUARIO ON ANUNCIOS_VISTOS(USUARIO_ID);
CREATE INDEX IDX_ANUNCIOS_VISTOS_ANUNCIO ON ANUNCIOS_VISTOS(ANUNCIO_ID);

-- Comentarios para documentar las tablas
COMMENT ON TABLE ANUNCIOS IS 'Tabla para almacenar anuncios globales del sistema';
COMMENT ON COLUMN ANUNCIOS.ID IS 'Identificador único del anuncio';
COMMENT ON COLUMN ANUNCIOS.TITULO IS 'Título del anuncio que se mostrará en el modal';
COMMENT ON COLUMN ANUNCIOS.CONTENIDO IS 'Contenido HTML del anuncio';
COMMENT ON COLUMN ANUNCIOS.ACTIVO IS 'Indica si este es el anuncio activo (solo uno puede estar activo a la vez)';
COMMENT ON COLUMN ANUNCIOS.FECHA_CREACION IS 'Fecha y hora de creación del anuncio';
COMMENT ON COLUMN ANUNCIOS.CREADO_POR_ID IS 'ID del usuario administrador que creó el anuncio';

COMMENT ON TABLE ANUNCIOS_VISTOS IS 'Tabla para rastrear qué usuarios han visto qué anuncios';
COMMENT ON COLUMN ANUNCIOS_VISTOS.USUARIO_ID IS 'ID del usuario que vio el anuncio';
COMMENT ON COLUMN ANUNCIOS_VISTOS.ANUNCIO_ID IS 'ID del anuncio que fue visto';
COMMENT ON COLUMN ANUNCIOS_VISTOS.FECHA_VISTO IS 'Fecha y hora en que el usuario vio el anuncio';