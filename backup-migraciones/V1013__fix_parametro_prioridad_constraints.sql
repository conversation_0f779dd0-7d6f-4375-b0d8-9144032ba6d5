-- Migración para corregir las restricciones de la tabla PARAMETRO_PRIORIDAD
-- Autor: Sistema CUFRE
-- Fecha: 2025-06-20

-- Eliminar la restricción restrictiva de tipo_variable si existe
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE PARAMETRO_PRIORIDAD DROP CONSTRAINT SYS_C008846';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -2443 THEN -- ORA-02443: Cannot drop constraint - nonexistent constraint
            RAISE;
        END IF;
END;
/

-- Ampliar el tamaño de la columna TIPO_VARIABLE para permitir valores más largos
ALTER TABLE PARAMETRO_PRIORIDAD MODIFY TIPO_VARIABLE VARCHAR2(50);

-- Ag<PERSON>gar comentarios para documentar la tabla
COMMENT ON TABLE PARAMETRO_PRIORIDAD IS 'Tabla para almacenar parámetros configurables del sistema de cálculo de prioridad';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.ID IS 'Identificador único del parámetro';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.CLAVE_VARIABLE IS 'Clave única que identifica el parámetro';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.VALOR IS 'Valor numérico del parámetro para el cálculo de prioridad';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.DESCRIPCION IS 'Descripción del parámetro';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.TIPO_VARIABLE IS 'Categoría o tipo del parámetro (PROFESION, DETENCIONES_PREVIAS, etc.)';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.FECHA_CREACION IS 'Fecha y hora de creación del parámetro';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.FECHA_MODIFICACION IS 'Fecha y hora de última modificación del parámetro';
COMMENT ON COLUMN PARAMETRO_PRIORIDAD.MODIFICADO_POR IS 'Usuario que realizó la última modificación';