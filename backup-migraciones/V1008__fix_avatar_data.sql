-- Migración V1008: Verificar y corregir datos de avatares predefinidos
-- Fecha: Enero 2025
-- Descripción: As<PERSON><PERSON>r que la tabla AVATAR_PREDEFINIDO existe y tiene todos los datos necesarios

-- Verificar si la tabla existe, si no, crearla
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE AVATAR_PREDEFINIDO (
        ID NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
        NOMBRE VARCHAR2(100 CHAR) NOT NULL,
        URL VARCHAR2(255 CHAR) NOT NULL,
        CATEGORIA VARCHAR2(50 CHAR) NOT NULL,
        ACTIVO NUMBER(1) DEFAULT 1 CHECK (ACTIVO IN (0,1)),
        ORDEN_DISPLAY NUMBER DEFAULT 0,
        FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN -- -955 = table already exists
            RAISE;
        END IF;
END;
/

-- Crear índices si no existen
BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_CATEGORIA ON AVATAR_PREDEFINIDO(CATEGORIA)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ACTIVO ON AVATAR_PREDEFINIDO(ACTIVO)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_AVATAR_ORDEN ON AVATAR_PREDEFINIDO(ORDEN_DISPLAY)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -955 THEN
            RAISE;
        END IF;
END;
/

-- Limpiar datos existentes para evitar duplicados
DELETE FROM AVATAR_PREDEFINIDO;

-- Insertar todos los avatares predefinidos
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
-- Categoría Profesional (8 avatares)
('Ejecutivo Masculino', '/avatares/profesional/ejecutivo-m.svg', 'profesional', 1);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Ejecutiva Femenina', '/avatares/profesional/ejecutiva-f.svg', 'profesional', 2);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Oficial Masculino', '/avatares/profesional/oficial-m.svg', 'profesional', 3);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Oficial Femenina', '/avatares/profesional/oficial-f.svg', 'profesional', 4);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Investigador', '/avatares/profesional/investigador.svg', 'profesional', 5);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Investigadora', '/avatares/profesional/investigadora.svg', 'profesional', 6);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Analista', '/avatares/profesional/analista.svg', 'profesional', 7);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Supervisor', '/avatares/profesional/supervisor.svg', 'profesional', 8);

-- Categoría Casual (8 avatares)
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Amigable', '/avatares/casual/amigable.svg', 'casual', 9);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Sonriente', '/avatares/casual/sonriente.svg', 'casual', 10);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Relajada', '/avatares/casual/relajada.svg', 'casual', 11);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Moderna', '/avatares/casual/moderna.svg', 'casual', 12);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Joven', '/avatares/casual/joven.svg', 'casual', 13);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Creativa', '/avatares/casual/creativa.svg', 'casual', 14);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Deportiva', '/avatares/casual/deportiva.svg', 'casual', 15);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Persona Artística', '/avatares/casual/artistica.svg', 'casual', 16);

-- Categoría Iconos (8 avatares)
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Escudo Policial', '/avatares/iconos/escudo-policial.svg', 'iconos', 17);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Insignia GNA', '/avatares/iconos/insignia-gna.svg', 'iconos', 18);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Estrella Seguridad', '/avatares/iconos/estrella-seguridad.svg', 'iconos', 19);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Badge Investigador', '/avatares/iconos/badge-investigador.svg', 'iconos', 20);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Símbolo Justicia', '/avatares/iconos/simbolo-justicia.svg', 'iconos', 21);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Emblema Orden', '/avatares/iconos/emblema-orden.svg', 'iconos', 22);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Logo Institucional', '/avatares/iconos/logo-institucional.svg', 'iconos', 23);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Símbolo Autoridad', '/avatares/iconos/simbolo-autoridad.svg', 'iconos', 24);

-- Categoría Diversos (6 avatares)
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Neutro 1', '/avatares/diversos/neutro-1.svg', 'diversos', 25);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Neutro 2', '/avatares/diversos/neutro-2.svg', 'diversos', 26);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Inclusivo 1', '/avatares/diversos/inclusivo-1.svg', 'diversos', 27);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Inclusivo 2', '/avatares/diversos/inclusivo-2.svg', 'diversos', 28);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Universal 1', '/avatares/diversos/universal-1.svg', 'diversos', 29);
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Universal 2', '/avatares/diversos/universal-2.svg', 'diversos', 30);

-- Confirmar inserción
COMMIT;