-- Crear tabla para el sistema de bloqueo de expedientes
-- V1005__create_expediente_bloqueo_table.sql

-- Crear secuencia para la tabla expediente_bloqueo
CREATE SEQUENCE expediente_bloqueo_seq START WITH 1 INCREMENT BY 1;

-- <PERSON><PERSON>r tabla expediente_bloqueo
CREATE TABLE expediente_bloqueo (
    id NUMBER(19) NOT NULL,
    expediente_id NUMBER(19) NOT NULL,
    usuario_bloqueando VARCHAR2(100 CHAR) NOT NULL,
    nombre_usuario VARCHAR2(200 CHAR) NOT NULL,
    fecha_bloqueo TIMESTAMP NOT NULL,
    session_id VARCHAR2(100 CHAR),
    CONSTRAINT pk_expediente_bloqueo PRIMARY KEY (id),
    CONSTRAINT uk_expediente_bloqueo_expediente_id UNIQUE (expediente_id),
    CONSTRAINT fk_expediente_bloqueo_expediente FOREIGN KEY (expediente_id) REFERENCES expediente(id) ON DELETE CASCADE
);

-- Índices para mejorar el rendimiento
CREATE INDEX idx_expediente_bloqueo_expediente_id ON expediente_bloqueo(expediente_id);
CREATE INDEX idx_expediente_bloqueo_usuario ON expediente_bloqueo(usuario_bloqueando);
CREATE INDEX idx_expediente_bloqueo_session ON expediente_bloqueo(session_id);
CREATE INDEX idx_expediente_bloqueo_fecha ON expediente_bloqueo(fecha_bloqueo);

-- Comentarios para documentación
COMMENT ON TABLE expediente_bloqueo IS 'Tabla para controlar el bloqueo de expedientes durante la edición';
COMMENT ON COLUMN expediente_bloqueo.id IS 'Identificador único del bloqueo';
COMMENT ON COLUMN expediente_bloqueo.expediente_id IS 'ID del expediente bloqueado';
COMMENT ON COLUMN expediente_bloqueo.usuario_bloqueando IS 'Email del usuario que tiene el expediente bloqueado';
COMMENT ON COLUMN expediente_bloqueo.nombre_usuario IS 'Nombre completo del usuario que tiene el expediente bloqueado';
COMMENT ON COLUMN expediente_bloqueo.fecha_bloqueo IS 'Fecha y hora cuando se realizó el bloqueo';
COMMENT ON COLUMN expediente_bloqueo.session_id IS 'ID de sesión para cleanup automático';
