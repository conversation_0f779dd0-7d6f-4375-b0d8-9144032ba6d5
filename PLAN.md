# Plan para Solucionar Error 403 en Fotografías

## 1. Diagnóstico del Problema

El error `403 Forbidden` al intentar cargar las imágenes de los expedientes se debe a que el frontend intenta acceder a ellas directamente a través de una URL (`/api/uploads/fotografias/...`) que está protegida por el backend. El backend requiere que todas las peticiones a `/api/**` estén autenticadas, pero el navegador no envía las cabeceras de autenticación (token JWT) al solicitar el recurso de una etiqueta `<img>`.

**Confirmado por:**
-   **Backend:** El archivo `WebSecurityConfig.java` en la línea 55 (`.anyRequest().authenticated()`) confirma que todas las rutas no exentas requieren autenticación.
-   **Frontend:** El componente `FotografiasTab.tsx` (línea 336 y 86) muestra que se asigna la URL de la API directamente al atributo `src` de la etiqueta `<img>`.

## 2. Plan de Solución

La solución consiste en modificar el componente de React que renderiza la imagen para que la solicite de forma autenticada.

### Paso 1: Modificar el componente `ImageWithFallback`

Se modificará el componente `ImageWithFallback` dentro de `frontend/src/components/expedientes/FotografiasTab.tsx`.

La nueva lógica será la siguiente:

1.  **Utilizar `useEffect`:** Se usará un hook `useEffect` que se active cuando la URL de la imagen (`src`) cambie.
2.  **Petición Autenticada:** Dentro del `useEffect`, se realizará una petición `fetch` a la URL de la imagen. A esta petición se le añadirán las cabeceras de autenticación necesarias, obteniendo el token JWT del `localStorage` o del estado de la aplicación.
3.  **Crear Blob URL:** La respuesta exitosa de la petición (los datos binarios de la imagen) se convertirá en un `blob`. A partir de este `blob`, se generará una URL local y temporal usando `URL.createObjectURL()`.
4.  **Renderizar Imagen:** La etiqueta `<img>` usará esta nueva "blob URL" en su atributo `src`.
5.  **Manejo de Errores y Carga:** Se mantendrá la lógica para mostrar un indicador de carga y un mensaje de error si la petición falla.
6.  **Limpieza de Memoria:** Se implementará la función de limpieza del `useEffect` para revocar la "blob URL" con `URL.revokeObjectURL()` cuando el componente se desmonte, evitando fugas de memoria.

### 3. Diagrama del Flujo de la Solución

```mermaid
graph TD
    A[Componente ImageWithFallback recibe la prop 'src' con la URL de la API] --> B{useEffect se dispara};
    B --> C[Obtener token de autenticación];
    C --> D[Llamar a fetch(src, { headers: { Authorization: 'Bearer ...' } })];
    D --> E{¿Respuesta OK?};
    E -- Sí --> F[Convertir respuesta a blob];
    F --> G[Crear URL de objeto: URL.createObjectURL(blob)];
    G --> H[Guardar la 'blob URL' en el estado];
    E -- No --> I[Marcar estado de error];
    H --> J[Renderizar <img src={blobUrl}>];
    I --> K[Renderizar mensaje de error];
    B --> L[Registrar función de limpieza: URL.revokeObjectURL(blobUrl)];
```

## 4. Siguientes Pasos

Una vez aprobado este plan, se procederá a cambiar al modo `code` para aplicar las modificaciones en el archivo `frontend/src/components/expedientes/FotografiasTab.tsx`.