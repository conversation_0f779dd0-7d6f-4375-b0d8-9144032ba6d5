# Plan de Refactorización: EstadisticasPage en Componentes Modulares

## Objetivo Principal

Refactorizar la `EstadisticasPage.tsx` (1032 líneas) dividiéndola en **componentes reutilizables y modulares** manteniendo el **CinematicLayout** (como todas las páginas de estadísticas), conservando **toda la funcionalidad actual** intacta y mejorando la organización del código mediante componentes especializados.

## Análisis de la Situación Actual

### Problemas Identificados
- **Archivo monolítico**: 1032 líneas en un solo componente
- **Múltiples responsabilidades**: gestión de estado, renderizado, lógica de negocio
- **Código duplicado**: lógica de gráficos repetida en el mismo archivo
- **Difícil mantenimiento**: cambios requieren modificar un archivo muy grande
- **Falta de reutilización**: componentes que podrían usarse en otras páginas de estadísticas

### Funcionalidades a Mantener
- ✅ Filtrado interactivo entre gráficos de fuerza y estado
- ✅ Tooltips personalizados con información detallada
- ✅ Normalización automática de datos del backend
- ✅ Manejo de estados de carga y error
- ✅ Datos de ejemplo cuando no hay información
- ✅ Navegación a creación de expedientes
- ✅ Actualización manual de datos
- ✅ Responsive design

## Arquitectura de Componentes Propuesta

```mermaid
graph TD
    A[EstadisticasPage.tsx] --> B[EstadisticasHeader]
    A --> C[EstadisticasMetricas]
    A --> D[EstadisticasFilters]
    A --> E[EstadisticasChartsGrid]
    A --> F[EstadisticasEmptyState]
    
    B --> B1[PanelEstadisticas base]
    
    C --> C1[MetricCard - Total]
    C --> C2[MetricCard - Capturas]
    C --> C3[MetricCard - Detenidos]
    C --> C4[MetricCard - Sin Efecto]
    
    D --> D1[ActiveFilterChip]
    D --> D2[ClearFiltersButton]
    
    E --> E1[ExpedientesPorFuerzaChart]
    E --> E2[ExpedientesPorEstadoChart]
    
    E1 --> G[PanelEstadisticas base]
    E2 --> G
    
    G --> H[CustomTooltip]
    G --> I[DonutChart]
    G --> J[ChartEmptyState]
    
    F --> F1[EmptyStateMessage]
    F --> F2[CreateExpedienteButton]
```

## Componentes a Crear

### 1. **EstadisticasHeader**
```typescript
// frontend/src/components/estadisticas/EstadisticasHeader.tsx
interface EstadisticasHeaderProps {
  onRefresh: () => void;
  isRefreshing: boolean;
  lastUpdate?: Date | null;
}
```
**Responsabilidad**: Header con título, subtítulo y controles de actualización
**Estilo**: Usar clases del Centro de Comando (`centro-comando-header`, `centro-comando-title`)

### 2. **EstadisticasMetricas**
```typescript
// frontend/src/components/estadisticas/EstadisticasMetricas.tsx
interface EstadisticasMetricasProps {
  estadisticasGenerales: any;
  expedientesPorEstado: ExpedienteData[];
  loading: boolean;
}
```
**Responsabilidad**: Grid de métricas principales usando el patrón del Centro de Comando
**Estilo**: Usar `centro-comando-metrics` con cards individuales

### 3. **EstadisticasFilters**
```typescript
// frontend/src/components/estadisticas/EstadisticasFilters.tsx
interface EstadisticasFiltersProps {
  activeFilter: { type: 'fuerza' | 'estado' | null, value: string | null };
  onClearFilter: () => void;
}
```
**Responsabilidad**: Mostrar filtros activos con chips y botón de limpiar
**Estilo**: Usar el patrón de chips del Centro de Comando

### 4. **ExpedientesPorFuerzaChart**
```typescript
// frontend/src/components/estadisticas/ExpedientesPorFuerzaChart.tsx
interface ExpedientesPorFuerzaChartProps {
  data: ExpedienteData[];
  loading: boolean;
  onSegmentClick: (data: ExpedienteData, index: number) => void;
  activeIndex: number | null;
  isFiltered: boolean;
}
```
**Responsabilidad**: Gráfico de dona para expedientes por fuerza
**Estilo**: Usar `PanelEstadisticas` como base, siguiendo el patrón de `PanelGraficoDona`

### 5. **ExpedientesPorEstadoChart**
```typescript
// frontend/src/components/estadisticas/ExpedientesPorEstadoChart.tsx
interface ExpedientesPorEstadoChartProps {
  data: ExpedienteData[];
  loading: boolean;
  onSegmentClick: (data: ExpedienteData, index: number) => void;
  activeIndex: number | null;
  isFiltered: boolean;
}
```
**Responsabilidad**: Gráfico de dona para expedientes por estado
**Estilo**: Usar `PanelEstadisticas` como base, siguiendo el patrón de `PanelGraficoDona`

### 6. **EstadisticasChartsGrid**
```typescript
// frontend/src/components/estadisticas/EstadisticasChartsGrid.tsx
interface EstadisticasChartsGridProps {
  expedientesPorFuerza: ExpedienteData[];
  expedientesPorEstado: ExpedienteData[];
  filteredExpedientesPorFuerza: ExpedienteData[];
  filteredExpedientesPorEstado: ExpedienteData[];
  loading: boolean;
  activeFuerzaIndex: number | null;
  activeEstadoIndex: number | null;
  onPieClick: (data: ExpedienteData, type: 'fuerza' | 'estado', index: number) => void;
}
```
**Responsabilidad**: Grid contenedor para los dos gráficos principales
**Estilo**: Usar `centro-comando-grid` para layout 2x1

### 7. **EstadisticasEmptyState**
```typescript
// frontend/src/components/estadisticas/EstadisticasEmptyState.tsx
interface EstadisticasEmptyStateProps {
  onCreateExpediente: () => void;
}
```
**Responsabilidad**: Estado vacío cuando no hay expedientes
**Estilo**: Usar `PanelEstadisticas` con mensaje centrado

### 8. **hooks/useEstadisticasData**
```typescript
// frontend/src/hooks/useEstadisticasData.ts
interface UseEstadisticasDataReturn {
  // Estados
  loading: boolean;
  error: string | null;
  expedientesPorFuerza: ExpedienteData[];
  expedientesPorEstado: ExpedienteData[];
  filteredExpedientesPorFuerza: ExpedienteData[];
  filteredExpedientesPorEstado: ExpedienteData[];
  estadisticasGenerales: any;
  activeFuerzaIndex: number | null;
  activeEstadoIndex: number | null;
  activeFilter: { type: 'fuerza' | 'estado' | null, value: string | null };
  
  // Funciones
  fetchData: () => Promise<void>;
  handlePieClick: (data: ExpedienteData, type: 'fuerza' | 'estado', index: number) => Promise<void>;
  clearFilters: () => void;
}
```
**Responsabilidad**: Lógica de estado y datos centralizada

### 9. **utils/estadisticasUtils**
```typescript
// frontend/src/utils/estadisticasUtils.ts
export const normalizarDatos = (datos: any[], tipoMapa: 'fuerza' | 'estado'): ExpedienteData[];
export const COLORS_FUERZA = { /* colores existentes */ };
export const COLORS_ESTADO = { /* colores existentes */ };
export const DEFAULT_COLORS = [ /* colores por defecto */ ];
```
**Responsabilidad**: Utilidades y constantes compartidas

## Mantenimiento del Estilo Cinematográfico

### Layout Principal - MANTENER CinematicLayout
```typescript
// MANTENER: CinematicLayout (como todas las páginas de estadísticas)
<CinematicLayout
  title="Estadísticas Generales"
  subtitle="Distribución de expedientes por estado y fuerza asignada"
  icon={<PieChartIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
>
  <Box className="cinematic-fade-in">
    <EstadisticasControls />
    <EstadisticasFilters />
    <EstadisticasMetricas />
    <EstadisticasChartsGrid />
    <EstadisticasEmptyState />
  </Box>
</CinematicLayout>
```

### Componentes con Estilo Cinematográfico

#### 1. **Controles Superiores**
```typescript
// Mantener el patrón actual de controles
<Box sx={{
  mb: 3,
  display: 'flex',
  justifyContent: 'flex-end',
  alignItems: 'center'
}}>
  <Tooltip title="Actualizar datos">
    <IconButton className="cinematic-button">
      <RefreshIcon />
    </IconButton>
  </Tooltip>
</Box>
```

#### 2. **Métricas**
```typescript
// Usar cinematic-main-panel para el contenedor de métricas
<Box className="cinematic-main-panel" sx={{ mb: 4 }}>
  <Typography className="cinematic-title">
    <DonutLargeIcon sx={{ mr: 2, color: 'var(--cc-cufre)' }} />
    Resumen de Expedientes
  </Typography>
  <Box sx={{
    display: 'grid',
    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', lg: '1fr 1fr 1fr 1fr' },
    gap: 3
  }}>
    {/* Métricas individuales */}
  </Box>
</Box>
```

#### 3. **Gráficos**
```typescript
// Usar cinematic-main-panel para cada gráfico
<Box className="cinematic-main-panel">
  <Typography className="cinematic-title">
    <PieChartIcon sx={{ mr: 2, color: 'var(--cc-cufre)' }} />
    Expedientes por Fuerza
  </Typography>
  <Typography className="cinematic-subtitle">
    Distribución por fuerza asignada
  </Typography>
  <Box className="cinematic-chart-container">
    {/* Contenido del gráfico */}
  </Box>
</Box>
```

#### 4. **Filtros**
```typescript
// Mantener el patrón de alertas cinematográficas
{activeFilter.type && (
  <Alert
    severity="info"
    className="cinematic-alert info"
    sx={{
      mb: 3,
      backgroundColor: 'rgba(255, 214, 0, 0.1)',
      border: '1px solid var(--cc-cufre)',
      color: 'var(--cc-text-primary)'
    }}
  >
    {/* Contenido del filtro */}
  </Alert>
)}
```

### CSS Classes a Utilizar

#### Cinematográficas (MANTENER):
- `cinematic-fade-in`
- `cinematic-main-panel`
- `cinematic-chart-container`
- `cinematic-title`
- `cinematic-subtitle`
- `cinematic-button`
- `cinematic-alert`
- `cinematic-loading`
- `cinematic-tooltip`

#### Variables CSS (MANTENER):
- `var(--cc-cufre)` - Color principal
- `var(--cc-text-primary)` - Texto principal
- `var(--cc-text-secondary)` - Texto secundario
- `var(--cc-bg-primary)` - Fondo principal
- `var(--cc-bg-panel)` - Fondo de paneles
- `var(--cc-border)` - Bordes
- `var(--cc-glow)` - Efectos de brillo

## Flujo de Datos

```mermaid
sequenceDiagram
    participant EP as EstadisticasPage
    participant UED as useEstadisticasData
    participant ES as estadisticaService
    participant EFC as ExpedientesPorFuerzaChart
    participant EEC as ExpedientesPorEstadoChart
    
    EP->>UED: useEstadisticasData()
    UED->>ES: fetchData()
    ES-->>UED: datos normalizados
    UED-->>EP: estado y funciones
    
    EP->>EFC: data, onSegmentClick
    EP->>EEC: data, onSegmentClick
    
    EFC->>EP: onSegmentClick(data, index)
    EP->>UED: handlePieClick(data, 'fuerza', index)
    UED->>ES: getExpedientesPorEstadoYFuerza()
    ES-->>UED: datos filtrados
    UED-->>EP: estado actualizado
    EP->>EEC: nuevos datos filtrados
```

## Estructura de Archivos Final

```
frontend/src/
├── components/estadisticas/
│   ├── EstadisticasHeader.tsx          # Header con controles
│   ├── EstadisticasMetricas.tsx        # Panel de métricas
│   ├── EstadisticasFilters.tsx         # Filtros activos
│   ├── EstadisticasChartsGrid.tsx      # Grid de gráficos
│   ├── ExpedientesPorFuerzaChart.tsx   # Gráfico por fuerza
│   ├── ExpedientesPorEstadoChart.tsx   # Gráfico por estado
│   ├── EstadisticasEmptyState.tsx      # Estado vacío
│   ├── PanelEstadisticas.tsx           # ✅ Ya existe
│   ├── PanelGraficoDona.tsx           # ✅ Ya existe (referencia)
│   └── PanelMetricas.tsx              # ✅ Ya existe
├── hooks/
│   └── useEstadisticasData.ts          # Hook de datos
├── utils/
│   └── estadisticasUtils.ts            # Utilidades y constantes
├── styles/
│   ├── CentroComando.css              # ✅ Ya existe
│   └── CinematicPages.css             # ✅ Ya existe (no usar)
└── pages/estadisticas/
    └── EstadisticasPage.tsx            # 🔄 Refactorizada (< 200 líneas)
```

## Pasos de Implementación

### Fase 1: Preparación
1. **Crear utilidades** (`estadisticasUtils.ts`)
2. **Crear hook personalizado** (`useEstadisticasData.ts`)
3. **Extraer constantes** de colores y configuración

### Fase 2: Componentes Base
4. **Crear EstadisticasHeader** con estilo del Centro de Comando
5. **Crear EstadisticasMetricas** usando `PanelMetricas`
6. **Crear EstadisticasFilters** con chips del Centro de Comando

### Fase 3: Componentes de Gráficos
7. **Crear ExpedientesPorFuerzaChart** usando `PanelEstadisticas`
8. **Crear ExpedientesPorEstadoChart** usando `PanelEstadisticas`
9. **Crear EstadisticasChartsGrid** con layout del Centro de Comando

### Fase 4: Componentes Auxiliares
10. **Crear EstadisticasEmptyState**
11. **Adaptar CustomTooltip** si es necesario

### Fase 5: Integración
12. **Refactorizar EstadisticasPage** principal
13. **Verificar funcionalidad** completa
14. **Ajustar estilos** para consistencia total

## Beneficios Esperados

### Técnicos
- ✅ **Mantenibilidad**: Componentes de ~50-100 líneas cada uno
- ✅ **Reutilización**: Componentes usables en otras páginas
- ✅ **Testabilidad**: Componentes aislados más fáciles de testear
- ✅ **Legibilidad**: Código organizado por responsabilidades
- ✅ **Consistencia**: Estilo unificado con Centro de Comando

### Funcionales
- ✅ **Misma funcionalidad**: Cero pérdida de características
- ✅ **Mejor UX**: Interfaz consistente con otras páginas
- ✅ **Performance**: Posible mejora por componentes optimizados
- ✅ **Escalabilidad**: Fácil agregar nuevas funcionalidades

## Criterios de Éxito

1. **Funcionalidad**: Todos los filtros e interacciones funcionan igual
2. **Estilo**: Interfaz visualmente consistente con Centro de Comando
3. **Performance**: Tiempo de carga igual o mejor
4. **Código**: Archivo principal < 200 líneas
5. **Reutilización**: Componentes usables en otras páginas de estadísticas

## Notas de Implementación

- **Mantener** todas las funciones existentes: `normalizarDatos`, `handlePieClick`, `fetchData`
- **Conservar** todos los colores y constantes actuales
- **Usar** el mismo servicio `estadisticaService`
- **Mantener** la misma estructura de datos `ExpedienteData`
- **Preservar** todos los tooltips y animaciones
- **Adaptar** solo el estilo visual, no la lógica de negocio

Este plan garantiza una refactorización exitosa manteniendo toda la funcionalidad actual mientras mejora significativamente la organización del código y la consistencia visual con el resto de la aplicación.