# Plan de Unificación de Interfaz Visual de Estadísticas

## Objetivo Principal

Adaptar todas las páginas de estadísticas (excepto Mapa) para que usen la misma interfaz visual cinematográfica del Centro de Comando, implementando un sistema híbrido con navegación clickeable desde las tarjetas del Centro de Comando hacia páginas individuales fullscreen.

## Análisis de la Situación Actual

### Centro de Comando Actual
- **Archivo**: [`frontend/src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx)
- **Estilos**: [`frontend/src/styles/CentroComando.css`](frontend/src/styles/CentroComando.css)
- **Características**:
  - Layout fullscreen cinematográfico
  - Tema oscuro con efectos visuales
  - Grid de paneles interactivos (2x2)
  - Auto-refresh cada 30 segundos
  - Filtros dinámicos compartidos
  - Variables CSS cinematográficas

### Páginas de Estadísticas Actuales
- **DetenidosPorFuerzaPage.tsx**: Layout tradicional con fondo claro y Paper components
- **RankingDelitosPage.tsx**: Layout tradicional con gradientes claros
- **EvolucionExpedientesPage.tsx**: Patrón similar tradicional
- **EstadisticasPage.tsx**: Página principal de gráficos con layout estándar

### Barra Lateral Actual
- **Archivo**: [`frontend/src/components/layout/Sidebar.tsx`](frontend/src/components/layout/Sidebar.tsx)
- **Líneas 293-370**: Sección expandible "Estadísticas" con 6 opciones
- **Problema**: Demasiadas opciones directas en la barra lateral

## Arquitectura de la Solución

```mermaid
graph TD
    A[Centro de Comando] --> B[Click en Tarjeta]
    B --> C[Página Individual Fullscreen]
    C --> D[Botón Volver al Centro]
    D --> A
    
    E[Barra Lateral] --> F[Estadísticas]
    F --> G[Centro de Estadísticas]
    F --> H[Mapa]
    
    I[Páginas Actuales] --> J[Convertir a Layout Cinematográfico]
    J --> K[Aplicar CentroComando.css]
    K --> L[Agregar Botón Volver]
    L --> M[Implementar Fullscreen]
```

## Plan de Implementación

### Fase 1: Modificación de la Barra Lateral

**Archivo**: [`frontend/src/components/layout/Sidebar.tsx`](frontend/src/components/layout/Sidebar.tsx)

**Ubicación**: Líneas 293-370

**Cambio Requerido**:
```typescript
// ANTES: Sección expandible con múltiples opciones
{canSee('estadisticas') && (
  <ListItem disablePadding>
    <ListItemButton onClick={handleEstadisticasClick}>
      <ListItemIcon><BarChartRoundedIcon /></ListItemIcon>
      <ListItemText primary="Estadísticas" />
      {estadisticasOpen ? <ExpandLess /> : <ExpandMore />}
    </ListItemButton>
  </ListItem>
)}
{canSee('estadisticas') && (
  <Collapse in={estadisticasOpen} timeout="auto" unmountOnExit>
    <List component="div" disablePadding>
      <ListItemButton>Centro de Estadísticas</ListItemButton>
      <ListItemButton>Gráficos</ListItemButton>
      <ListItemButton>Mapa</ListItemButton>
      <ListItemButton>Detenidos por Fuerza</ListItemButton>
      <ListItemButton>Evolución de Expedientes</ListItemButton>
      <ListItemButton>Ranking de Delitos</ListItemButton>
    </List>
  </Collapse>
)}

// DESPUÉS: Estructura simplificada fija
{canSee('estadisticas') && (
  <>
    <ListItem disablePadding>
      <ListItemButton>
        <ListItemIcon><BarChartRoundedIcon /></ListItemIcon>
        <ListItemText primary="Estadísticas" />
      </ListItemButton>
    </ListItem>
    <ListItem disablePadding>
      <ListItemButton 
        sx={{ pl: 5 }}
        onClick={() => handleNavigation('/estadisticas/centro-comando')}
      >
        <ListItemIcon><DashboardCustomizeIcon /></ListItemIcon>
        <ListItemText primary="Centro de Estadísticas" />
      </ListItemButton>
    </ListItem>
    <ListItem disablePadding>
      <ListItemButton 
        sx={{ pl: 5 }}
        onClick={() => handleNavigation('/estadisticas/mapa')}
      >
        <ListItemIcon><PlaceRoundedIcon /></ListItemIcon>
        <ListItemText primary="Mapa" />
      </ListItemButton>
    </ListItem>
  </>
)}
```

### Fase 2: Creación de Layout Base Cinematográfico

**Nuevo archivo**: [`frontend/src/components/layout/CinematicLayout.tsx`](frontend/src/components/layout/CinematicLayout.tsx)

```typescript
import React, { useEffect, useState } from 'react';
import { Box, Typography, IconButton, Tooltip } from '@mui/material';
import { ArrowBack as ArrowBackIcon, Menu as MenuIcon } from '@mui/icons-material';
import { useNavigate, useOutletContext } from 'react-router-dom';
import '../../styles/CentroComando.css';

interface CinematicLayoutProps {
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  onBack?: () => void;
  showBackButton?: boolean;
  backRoute?: string;
}

interface OutletContextType {
  onFullscreenChange?: (fullscreen: boolean) => void;
}

const CinematicLayout: React.FC<CinematicLayoutProps> = ({
  title,
  subtitle,
  icon,
  children,
  onBack,
  showBackButton = true,
  backRoute = '/estadisticas/centro-comando'
}) => {
  const navigate = useNavigate();
  const { onFullscreenChange } = useOutletContext<OutletContextType>() || {};
  const [isFullscreen, setIsFullscreen] = useState(true);

  // Auto-activar fullscreen al montar
  useEffect(() => {
    if (onFullscreenChange) {
      onFullscreenChange(true);
    }
    
    return () => {
      if (onFullscreenChange) {
        onFullscreenChange(false);
      }
    };
  }, [onFullscreenChange]);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(backRoute);
    }
  };

  const exitFullscreen = () => {
    setIsFullscreen(false);
    if (onFullscreenChange) {
      onFullscreenChange(false);
    }
  };

  return (
    <Box className={`centro-comando-container ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Botón de salida de fullscreen */}
      {isFullscreen && (
        <Tooltip title="Mostrar menú principal" arrow placement="right">
          <IconButton
            className="exit-fullscreen-btn"
            onClick={exitFullscreen}
            size="large"
          >
            <MenuIcon />
          </IconButton>
        </Tooltip>
      )}

      {/* Header cinematográfico */}
      <Box className="centro-comando-header">
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {showBackButton && (
            <Tooltip title="Volver al Centro de Comando" arrow>
              <IconButton
                onClick={handleBack}
                sx={{
                  color: 'var(--cc-cufre)',
                  border: '2px solid var(--cc-cufre)',
                  borderRadius: 2,
                  p: 1,
                  '&:hover': {
                    backgroundColor: 'var(--cc-cufre)',
                    color: 'var(--cc-bg-primary)'
                  }
                }}
              >
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
          )}
          
          <Box>
            <Typography className="centro-comando-title">
              {icon}
              {title}
            </Typography>
            {subtitle && (
              <Typography className="centro-comando-subtitle">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Contenido principal */}
      <Box sx={{ 
        flex: 1, 
        position: 'relative', 
        zIndex: 5,
        padding: '2rem 3rem',
        overflow: 'auto'
      }}>
        {children}
      </Box>
    </Box>
  );
};

export default CinematicLayout;
```

### Fase 3: Conversión de Páginas Individuales

#### 3.1 DetenidosPorFuerzaPage.tsx

**Flujo de Conversión**:
```mermaid
graph LR
    A[Layout Actual] --> B[CinematicLayout]
    B --> C[Fondo Cinematográfico]
    C --> D[Panel Único Centrado]
    D --> E[Gráfico con Estilos CC]
    E --> F[Botón Volver]
```

**Cambios Principales**:
```typescript
// ANTES
return (
  <Box sx={{
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #e3e9f7 0%, #f5f7fa 100%)',
    py: { xs: 2, md: 6 },
    px: { xs: 0, md: 4 }
  }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, mt: 2 }}>
      <BarChartIcon sx={{ color: '#1976d2', fontSize: 40, mr: 1 }} />
      <Typography variant="h3">Detenidos por Fuerza</Typography>
    </Box>
    <Paper elevation={0} sx={{ p: 4, borderRadius: 4 }}>
      {/* Contenido */}
    </Paper>
  </Box>
);

// DESPUÉS
return (
  <CinematicLayout
    title="Detenidos por Fuerza"
    subtitle="Cantidad de expedientes en estado DETENIDO agrupados por fuerza asignada"
    icon={<BarChartIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
  >
    <Box className="panel-estadisticas" sx={{ maxWidth: '1200px', mx: 'auto' }}>
      <Box className="panel-content">
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress sx={{ color: 'var(--cc-cufre)' }} />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ backgroundColor: 'var(--cc-bg-accent)' }}>{error}</Alert>
        ) : (
          <Box className="chart-container" sx={{ height: 500 }}>
            <ResponsiveContainer width="100%" height="100%">
              {/* Gráfico con tooltips cinematográficos */}
            </ResponsiveContainer>
          </Box>
        )}
      </Box>
    </Box>
  </CinematicLayout>
);
```

#### 3.2 RankingDelitosPage.tsx

**Cambios Principales**:
- Aplicar [`CinematicLayout`](frontend/src/components/layout/CinematicLayout.tsx)
- Mantener funcionalidad "Ver Todos" / "Top 10"
- Convertir tooltips a estilo cinematográfico
- Aplicar variables CSS del Centro de Comando

#### 3.3 EvolucionExpedientesPage.tsx

**Cambios Principales**:
- Convertir a layout cinematográfico
- Mantener gráfico de línea temporal
- Aplicar estilos coherentes con Centro de Comando
- Integrar con sistema de filtros

#### 3.4 EstadisticasPage.tsx

**Cambios Principales**:
- Reestructurar como página cinematográfica principal
- Mantener múltiples gráficos pero con estilo unificado
- Posible integración con filtros globales del Centro de Comando

### Fase 4: Mejoras en Centro de Comando

**Archivo**: [`frontend/src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx)

**Mejoras en navegación (líneas 278-280)**:
```typescript
const handlePanelClick = (route: string) => {
  // Pasar filtros activos y ruta de retorno
  navigate(route, { 
    state: { 
      filters: filters,
      returnTo: '/estadisticas/centro-comando',
      timestamp: Date.now()
    } 
  });
};
```

**Actualizar onClick handlers de paneles**:
```typescript
// Panel de detenidos por fuerza (líneas 440-457)
<PanelGraficoBarras
  title="Detenidos por Fuerza"
  data={detenidosPorFuerza}
  onClick={() => handlePanelClick('/estadisticas/detenidos-por-fuerza')}
  // ... otros props
/>

// Panel de evolución temporal (líneas 460-468)
<PanelLineaTemporal
  title="Evolución Temporal"
  data={evolucionTemporal}
  onClick={() => handlePanelClick('/estadisticas/evolucion-expedientes')}
  // ... otros props
/>

// Panel de ranking (líneas 485-495)
<PanelRanking
  title="Ranking de Delitos"
  data={rankingDelitos}
  onClick={() => handlePanelClick('/estadisticas/ranking-delitos')}
  // ... otros props
/>
```

### Fase 5: Gestión de Estado y Filtros

**Archivo**: [`frontend/src/context/EstadisticasFilterContext.tsx`](frontend/src/context/EstadisticasFilterContext.tsx)

**Extensión del contexto**:
```typescript
interface EstadisticasContextType {
  filters: FilterState;
  setFilter: (key: string, value: any) => void;
  clearFilters: () => void;
  // Nuevas propiedades para navegación
  returnRoute?: string;
  setReturnRoute: (route: string) => void;
  navigationState?: any;
  setNavigationState: (state: any) => void;
}

// Implementar persistencia de filtros entre páginas
const EstadisticasFilterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [filters, setFilters] = useState<FilterState>({});
  const [returnRoute, setReturnRoute] = useState<string>('/estadisticas/centro-comando');
  const [navigationState, setNavigationState] = useState<any>(null);
  
  // Recuperar filtros desde localStorage o navigation state
  useEffect(() => {
    const savedFilters = localStorage.getItem('estadisticas-filters');
    if (savedFilters) {
      setFilters(JSON.parse(savedFilters));
    }
  }, []);
  
  // Guardar filtros en localStorage
  useEffect(() => {
    localStorage.setItem('estadisticas-filters', JSON.stringify(filters));
  }, [filters]);
  
  // ... resto de la implementación
};
```

### Fase 6: Estilos Cinematográficos Extendidos

**Nuevo archivo**: [`frontend/src/styles/CinematicPages.css`](frontend/src/styles/CinematicPages.css)

```css
/* Extensiones específicas para páginas individuales */
.cinematic-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--cc-bg-primary) 0%, #0d1117 50%, var(--cc-bg-primary) 100%);
  position: relative;
  overflow-y: auto;
}

.cinematic-main-panel {
  background: linear-gradient(135deg, var(--cc-bg-panel) 0%, var(--cc-bg-accent) 100%);
  border: 1px solid var(--cc-border);
  border-radius: 16px;
  padding: 3rem;
  margin: 2rem auto;
  max-width: 1400px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.cinematic-chart-container {
  background: rgba(26, 26, 46, 0.3);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--cc-border);
  backdrop-filter: blur(10px);
}

/* Tooltips cinematográficos para gráficos */
.cinematic-tooltip {
  background: rgba(26, 26, 46, 0.95) !important;
  border: 2px solid var(--cc-cufre) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  color: var(--cc-text-primary) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(10px) !important;
}

/* Animaciones de entrada */
.cinematic-fade-in {
  animation: cinematicFadeIn 0.6s ease-out;
}

@keyframes cinematicFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## Flujo de Usuario Final

```mermaid
sequenceDiagram
    participant U as Usuario
    participant S as Sidebar
    participant CC as Centro Comando
    participant PI as Página Individual
    
    U->>S: Click "Centro de Estadísticas"
    S->>CC: Navegar a Centro Comando
    CC->>CC: Mostrar dashboard fullscreen
    Note over CC: Grid 2x2 con paneles interactivos
    
    U->>CC: Click en tarjeta "Detenidos por Fuerza"
    CC->>PI: Navegar con filtros activos
    Note over PI: Página fullscreen cinematográfica
    
    PI->>PI: Mostrar gráfico con estilo Centro Comando
    Note over PI: Botón "Volver al Centro" visible
    
    U->>PI: Click "Volver al Centro"
    PI->>CC: Regresar al Centro Comando
    Note over CC: Mantener filtros aplicados
```

## Estructura de Archivos Resultante

```
frontend/src/
├── components/
│   ├── layout/
│   │   ├── CinematicLayout.tsx (NUEVO)
│   │   ├── MainLayout.tsx (EXISTENTE)
│   │   └── Sidebar.tsx (MODIFICADO - líneas 293-370)
│   └── estadisticas/
│       ├── PanelMetricas.tsx (EXISTENTE)
│       ├── PanelGraficoDona.tsx (EXISTENTE)
│       ├── PanelGraficoBarras.tsx (EXISTENTE)
│       ├── PanelLineaTemporal.tsx (EXISTENTE)
│       ├── PanelRanking.tsx (EXISTENTE)
│       └── PanelCasosDestacados.tsx (EXISTENTE)
├── pages/estadisticas/
│   ├── CentroComandoPage.tsx (MEJORADO - navegación)
│   ├── DetenidosPorFuerzaPage.tsx (CONVERTIDO)
│   ├── RankingDelitosPage.tsx (CONVERTIDO)
│   ├── EvolucionExpedientesPage.tsx (CONVERTIDO)
│   ├── EstadisticasPage.tsx (CONVERTIDO)
│   └── MapaGeneralPage.tsx (SIN CAMBIOS)
├── styles/
│   ├── CentroComando.css (EXTENDIDO)
│   └── CinematicPages.css (NUEVO)
├── context/
│   └── EstadisticasFilterContext.tsx (EXTENDIDO)
└── routes/
    └── AppRoutes.tsx (SIN CAMBIOS - rutas mantienen)
```

## Checklist de Implementación

### Fase 1: Barra Lateral
- [ ] Modificar [`Sidebar.tsx`](frontend/src/components/layout/Sidebar.tsx) líneas 293-370
- [ ] Eliminar estado `estadisticasOpen`
- [ ] Simplificar a estructura fija: "Centro de Estadísticas" y "Mapa"
- [ ] Probar navegación desde barra lateral

### Fase 2: Layout Cinematográfico
- [ ] Crear [`CinematicLayout.tsx`](frontend/src/components/layout/CinematicLayout.tsx)
- [ ] Implementar fullscreen automático
- [ ] Agregar botón "Volver al Centro"
- [ ] Integrar con contexto de fullscreen

### Fase 3: Conversión de Páginas
- [ ] Convertir [`DetenidosPorFuerzaPage.tsx`](frontend/src/pages/estadisticas/DetenidosPorFuerzaPage.tsx)
- [ ] Convertir [`RankingDelitosPage.tsx`](frontend/src/pages/estadisticas/RankingDelitosPage.tsx)
- [ ] Convertir [`EvolucionExpedientesPage.tsx`](frontend/src/pages/estadisticas/EvolucionExpedientesPage.tsx)
- [ ] Convertir [`EstadisticasPage.tsx`](frontend/src/pages/estadisticas/EstadisticasPage.tsx)

### Fase 4: Centro de Comando
- [ ] Mejorar navegación en [`CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx)
- [ ] Implementar paso de filtros entre páginas
- [ ] Probar clicks en tarjetas

### Fase 5: Estado y Filtros
- [ ] Extender [`EstadisticasFilterContext.tsx`](frontend/src/context/EstadisticasFilterContext.tsx)
- [ ] Implementar persistencia de filtros
- [ ] Probar navegación con filtros activos

### Fase 6: Estilos
- [ ] Crear [`CinematicPages.css`](frontend/src/styles/CinematicPages.css)
- [ ] Extender [`CentroComando.css`](frontend/src/styles/CentroComando.css) si es necesario
- [ ] Probar responsive design

### Pruebas Finales
- [ ] Navegación completa: Sidebar → Centro → Página Individual → Volver
- [ ] Filtros persistentes entre páginas
- [ ] Fullscreen funcionando correctamente
- [ ] Responsive en diferentes tamaños de pantalla
- [ ] Performance de animaciones y transiciones

## Beneficios Esperados

1. **Consistencia Visual**: Todas las estadísticas tendrán la misma interfaz cinematográfica
2. **Navegación Intuitiva**: Flujo natural desde Centro de Comando a páginas específicas
3. **Experiencia Fullscreen**: Sin distracciones de barras laterales en estadísticas
4. **Mantenimiento de Funcionalidad**: Cada página mantiene sus características específicas
5. **Filtros Compartidos**: Estado consistente entre Centro de Comando y páginas individuales
6. **Mejor UX**: Interfaz más moderna y profesional
7. **Simplicidad en Navegación**: Menos opciones en barra lateral, más enfoque en Centro de Comando

## Consideraciones Técnicas

- **Performance**: Las animaciones CSS están optimizadas con `transform` y `opacity`
- **Responsive**: El layout se adapta a diferentes tamaños de pantalla
- **Accesibilidad**: Botones con tooltips y navegación por teclado
- **Mantenibilidad**: Componente reutilizable [`CinematicLayout`](frontend/src/components/layout/CinematicLayout.tsx)
- **Compatibilidad**: Uso de variables CSS para fácil mantenimiento de temas