# Plan de Implementación: Sistema de Notificaciones para Administradores y Superusuarios

## Resumen Ejecutivo

Este documento detalla la implementación de un sistema de notificaciones en tiempo real para usuarios con roles de **ADMINISTRADOR** y **SUPERUSUARIO**, basado en las actividades del módulo EXPEDIENTES registradas en el sistema de auditoría existente.

### Objetivos
- Mostrar una campanita en el navbar con contador de notificaciones no vistas
- Dropdown con las últimas 10 actividades del módulo EXPEDIENTES
- Navegación directa a detalles de actividades específicas
- Botón "Ver todos" que lleve al menú de Actividad del Sistema
- Actualización automática cada 30 segundos

## Análisis del Sistema Actual

### Backend
- **Framework**: Spring Boot con JPA/Hibernate
- **Base de datos**: Oracle con Flyway para migraciones
- **Auditoría**: Sistema [`ActividadSistema`](backend/src/main/java/com/cufre/expedientes/model/ActividadSistema.java) ya implementado
- **Seguridad**: JWT con roles definidos en [`Rol.java`](backend/src/main/java/com/cufre/expedientes/model/enums/Rol.java)

### Frontend
- **Framework**: React 18 con TypeScript
- **UI Library**: Material-UI (MUI)
- **Estado**: Context API para autenticación
- **Estructura**: Modular con componentes reutilizables

### Roles Objetivo
- `ADMINISTRADOR`: Acceso completo al sistema
- `SUPERUSUARIO`: Máximo nivel de privilegios

## Arquitectura del Sistema de Notificaciones

```mermaid
graph TB
    A[Usuario Admin/Super] --> B[Header con Campanita]
    B --> C[Contador de Notificaciones]
    B --> D[Dropdown de Notificaciones]
    
    D --> E[Lista de 10 Últimas Actividades]
    D --> F[Botón Ver Todos]
    
    E --> G[Click en Actividad Específica]
    F --> H[Página Actividad del Sistema]
    G --> I[Detalle de Actividad]
    
    J[Timer 30s] --> K[Actualización Automática]
    K --> L[API Notificaciones]
    L --> M[Filtro por Módulo EXPEDIENTES]
    M --> N[Filtro por Fecha Última Visita]
    
    H --> O[Marcar como Vistas]
    O --> P[Actualizar Timestamp Usuario]
```

## Modelo de Datos

### Modificación de la Tabla USUARIO

```sql
-- Migración V1006__add_ultima_visita_notificaciones.sql
ALTER TABLE USUARIO ADD COLUMN ULTIMA_VISITA_NOTIFICACIONES TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

### Relación de Entidades

```mermaid
erDiagram
    USUARIO {
        Long id
        String email
        Rol rol
        LocalDateTime ultima_visita_notificaciones
    }
    
    ACTIVIDAD_SISTEMA {
        Long id
        String usuario
        String modulo
        LocalDateTime fecha_hora
        String tipo_accion
        String detalles
        String categoria_accion
        String estado_respuesta
    }
    
    USUARIO ||--o{ ACTIVIDAD_SISTEMA : "genera"
```

## Plan de Implementación Detallado

### Fase 1: Backend - Extensión del Sistema de Auditoría

#### 1.1 Migración de Base de Datos
**Archivo**: `backend/src/main/resources/db/migration/V1006__add_ultima_visita_notificaciones.sql`

```sql
-- Agregar campo para tracking de última visita a notificaciones
ALTER TABLE USUARIO ADD COLUMN ULTIMA_VISITA_NOTIFICACIONES TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Crear índice para optimizar consultas de notificaciones
CREATE INDEX IDX_ACTIVIDAD_SISTEMA_NOTIF ON ACTIVIDAD_SISTEMA(MODULO, FECHA_HORA, USUARIO);
```

#### 1.2 Modificación del Modelo Usuario
**Archivo**: [`backend/src/main/java/com/cufre/expedientes/model/Usuario.java`](backend/src/main/java/com/cufre/expedientes/model/Usuario.java)

```java
@Column(name = "ULTIMA_VISITA_NOTIFICACIONES")
private LocalDateTime ultimaVisitaNotificaciones = LocalDateTime.now();

// Getter y setter
public LocalDateTime getUltimaVisitaNotificaciones() { 
    return ultimaVisitaNotificaciones; 
}

public void setUltimaVisitaNotificaciones(LocalDateTime ultimaVisitaNotificaciones) { 
    this.ultimaVisitaNotificaciones = ultimaVisitaNotificaciones; 
}
```

#### 1.3 Extensión del Servicio ActividadSistema
**Archivo**: [`backend/src/main/java/com/cufre/expedientes/service/ActividadSistemaService.java`](backend/src/main/java/com/cufre/expedientes/service/ActividadSistemaService.java)

```java
/**
 * Obtiene notificaciones no vistas para un usuario específico
 */
public List<ActividadSistemaDTO> obtenerNotificacionesNoVistas(String emailUsuario, int limite) {
    Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
        .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
    
    LocalDateTime ultimaVisita = usuario.getUltimaVisitaNotificaciones();
    
    return actividadSistemaRepository
        .findByModuloAndFechaHoraAfterOrderByFechaHoraDesc(
            ActividadSistema.Modulo.EXPEDIENTES, 
            ultimaVisita,
            PageRequest.of(0, limite)
        )
        .stream()
        .map(this::convertirADTO)
        .collect(Collectors.toList());
}

/**
 * Cuenta notificaciones no vistas
 */
public long contarNotificacionesNoVistas(String emailUsuario) {
    Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
        .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
    
    return actividadSistemaRepository
        .countByModuloAndFechaHoraAfter(
            ActividadSistema.Modulo.EXPEDIENTES,
            usuario.getUltimaVisitaNotificaciones()
        );
}

/**
 * Marca todas las notificaciones como vistas
 */
@Transactional
public void marcarNotificacionesComoVistas(String emailUsuario) {
    Usuario usuario = usuarioRepository.findByEmail(emailUsuario)
        .orElseThrow(() -> new ResourceNotFoundException("Usuario no encontrado"));
    
    usuario.setUltimaVisitaNotificaciones(LocalDateTime.now());
    usuarioRepository.save(usuario);
}
```

#### 1.4 Nuevos Métodos en Repository
**Archivo**: [`backend/src/main/java/com/cufre/expedientes/repository/ActividadSistemaRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/ActividadSistemaRepository.java)

```java
/**
 * Busca actividades por módulo y fecha posterior a la especificada
 */
Page<ActividadSistema> findByModuloAndFechaHoraAfterOrderByFechaHoraDesc(
    String modulo, 
    LocalDateTime fechaHora, 
    Pageable pageable
);

/**
 * Cuenta actividades por módulo y fecha posterior
 */
long countByModuloAndFechaHoraAfter(String modulo, LocalDateTime fechaHora);
```

#### 1.5 Nuevos Endpoints en Controller
**Archivo**: [`backend/src/main/java/com/cufre/expedientes/controller/ActividadSistemaController.java`](backend/src/main/java/com/cufre/expedientes/controller/ActividadSistemaController.java)

```java
/**
 * Obtiene notificaciones no vistas para el usuario actual
 */
@PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
@GetMapping("/notificaciones")
public ResponseEntity<List<ActividadSistemaDTO>> obtenerNotificaciones(
        Authentication authentication,
        @RequestParam(defaultValue = "10") int limite) {
    
    String emailUsuario = authentication.getName();
    List<ActividadSistemaDTO> notificaciones = 
        actividadSistemaService.obtenerNotificacionesNoVistas(emailUsuario, limite);
    
    return ResponseEntity.ok(notificaciones);
}

/**
 * Obtiene el contador de notificaciones no vistas
 */
@PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
@GetMapping("/notificaciones/count")
public ResponseEntity<Map<String, Long>> contarNotificaciones(Authentication authentication) {
    String emailUsuario = authentication.getName();
    long count = actividadSistemaService.contarNotificacionesNoVistas(emailUsuario);
    
    Map<String, Long> response = new HashMap<>();
    response.put("count", count);
    
    return ResponseEntity.ok(response);
}

/**
 * Marca todas las notificaciones como vistas
 */
@PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
@PostMapping("/notificaciones/marcar-vistas")
public ResponseEntity<Map<String, String>> marcarNotificacionesVistas(Authentication authentication) {
    String emailUsuario = authentication.getName();
    actividadSistemaService.marcarNotificacionesComoVistas(emailUsuario);
    
    Map<String, String> response = new HashMap<>();
    response.put("mensaje", "Notificaciones marcadas como vistas");
    
    return ResponseEntity.ok(response);
}
```

### Fase 2: Frontend - Componentes de Notificaciones

#### 2.1 Extensión del Servicio API
**Archivo**: [`frontend/src/api/actividadSistemaService.ts`](frontend/src/api/actividadSistemaService.ts)

```typescript
// Nuevos métodos para notificaciones
export const notificacionesAPI = {
  obtenerNotificaciones: async (limite: number = 10): Promise<ActividadSistemaDTO[]> => {
    const response = await apiClient.get(`/actividad-sistema/notificaciones?limite=${limite}`);
    return response.data;
  },

  contarNotificaciones: async (): Promise<number> => {
    const response = await apiClient.get('/actividad-sistema/notificaciones/count');
    return response.data.count;
  },

  marcarComoVistas: async (): Promise<void> => {
    await apiClient.post('/actividad-sistema/notificaciones/marcar-vistas');
  }
};
```

#### 2.2 Hook Personalizado para Notificaciones
**Archivo**: `frontend/src/hooks/useNotifications.ts`

```typescript
import { useState, useEffect, useCallback } from 'react';
import { notificacionesAPI } from '../api/actividadSistemaService';
import { ActividadSistemaDTO } from '../types/actividad.types';

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<ActividadSistemaDTO[]>([]);
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      const [notificationsData, countData] = await Promise.all([
        notificacionesAPI.obtenerNotificaciones(10),
        notificacionesAPI.contarNotificaciones()
      ]);
      
      setNotifications(notificationsData);
      setCount(countData);
      setError(null);
    } catch (err) {
      setError('Error al cargar notificaciones');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsViewed = useCallback(async () => {
    try {
      await notificacionesAPI.marcarComoVistas();
      setCount(0);
      await fetchNotifications();
    } catch (err) {
      setError('Error al marcar notificaciones como vistas');
      console.error('Error marking notifications as viewed:', err);
    }
  }, [fetchNotifications]);

  // Auto-actualización cada 30 segundos
  useEffect(() => {
    fetchNotifications();
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  return {
    notifications,
    count,
    loading,
    error,
    fetchNotifications,
    markAsViewed
  };
};
```

#### 2.3 Componente NotificationBell
**Archivo**: `frontend/src/components/notifications/NotificationBell.tsx`

```typescript
import React, { useState } from 'react';
import {
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  CircularProgress
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { useNotifications } from '../../hooks/useNotifications';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

const NotificationBell: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { notifications, count, loading, markAsViewed } = useNotifications();
  const navigate = useNavigate();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = (actividadId: number) => {
    handleClose();
    navigate(`/actividad-sistema/${actividadId}`);
  };

  const handleViewAll = async () => {
    await markAsViewed();
    handleClose();
    navigate('/actividad-sistema');
  };

  const formatNotificationText = (actividad: any) => {
    return `${actividad.usuario} realizó ${actividad.tipoAccion} - ${formatDistanceToNow(new Date(actividad.fechaHora), { addSuffix: true, locale: es })}`;
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        aria-label="notificaciones"
      >
        <Badge badgeContent={count} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: { width: 400, maxHeight: 500 }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6">
            Notificaciones {count > 0 && `(${count})`}
          </Typography>
        </Box>
        
        <Divider />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : notifications.length === 0 ? (
          <MenuItem>
            <Typography color="textSecondary">
              No hay notificaciones nuevas
            </Typography>
          </MenuItem>
        ) : (
          notifications.map((notification) => (
            <MenuItem
              key={notification.id}
              onClick={() => handleNotificationClick(notification.id)}
              sx={{ 
                whiteSpace: 'normal',
                maxWidth: '100%',
                '&:hover': { backgroundColor: 'action.hover' }
              }}
            >
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {formatNotificationText(notification)}
                </Typography>
                {notification.detalles && (
                  <Typography variant="caption" color="textSecondary">
                    {notification.detalles}
                  </Typography>
                )}
              </Box>
            </MenuItem>
          ))
        )}

        {notifications.length > 0 && (
          <>
            <Divider />
            <Box sx={{ p: 1 }}>
              <Button
                fullWidth
                variant="text"
                onClick={handleViewAll}
                sx={{ textTransform: 'none' }}
              >
                Ver todos
              </Button>
            </Box>
          </>
        )}
      </Menu>
    </>
  );
};

export default NotificationBell;
```

#### 2.4 Modificación del Header
**Archivo**: [`frontend/src/components/layout/Header.tsx`](frontend/src/components/layout/Header.tsx)

```typescript
import React from 'react';
import { 
  AppBar, 
  Toolbar, 
  Typography, 
  IconButton, 
  Box
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useAuth } from '../../context/AuthContext';
import NotificationBell from '../notifications/NotificationBell';

interface HeaderProps {
  open: boolean;
  toggleDrawer: () => void;
}

const Header: React.FC<HeaderProps> = ({ open, toggleDrawer }) => {
  const { user } = useAuth();

  // Solo mostrar notificaciones para ADMINISTRADOR y SUPERUSUARIO
  const showNotifications = user?.rol === 'ADMINISTRADOR' || user?.rol === 'SUPERUSUARIO';

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        transition: (theme) =>
          theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
      }}
    >
      <Toolbar>
        <IconButton
          edge="start"
          color="inherit"
          aria-label="menu"
          onClick={toggleDrawer}
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>
        
        <Typography
          component="h1"
          variant="h6"
          color="inherit"
          noWrap
          sx={{ flexGrow: 1 }}
        >
          Sistema de Gestión de Expedientes Judiciales
        </Typography>

        {showNotifications && (
          <Box sx={{ mr: 2 }}>
            <NotificationBell />
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Header;
```

### Fase 3: Integración y Navegación

#### 3.1 Modificación de ActividadSistemaPage
**Archivo**: [`frontend/src/pages/ActividadSistemaPage.tsx`](frontend/src/pages/ActividadSistemaPage.tsx)

```typescript
// Agregar useEffect para marcar como vistas al acceder a la página
useEffect(() => {
  const markNotificationsAsViewed = async () => {
    try {
      await notificacionesAPI.marcarComoVistas();
    } catch (error) {
      console.error('Error marking notifications as viewed:', error);
    }
  };

  markNotificationsAsViewed();
}, []);
```

#### 3.2 Ruta para Detalle de Actividad
**Archivo**: [`frontend/src/routes/AppRoutes.tsx`](frontend/src/routes/AppRoutes.tsx)

```typescript
// Agregar ruta para detalle de actividad específica
<Route 
  path="/actividad-sistema/:id" 
  element={
    <ProtectedRoute allowedRoles={['ADMINISTRADOR', 'SUPERUSUARIO']}>
      <ActividadDetallePage />
    </ProtectedRoute>
  } 
/>
```

### Fase 4: Optimizaciones y Seguridad

#### 4.1 Seguridad en Backend
- Verificación de roles en todos los endpoints de notificaciones
- Filtrado de actividades por permisos del usuario
- Validación de parámetros de entrada

#### 4.2 Optimizaciones de Performance
- Índices en base de datos para consultas rápidas
- Caché de notificaciones en frontend (5 minutos)
- Paginación en endpoints
- Debounce en actualizaciones automáticas

#### 4.3 Manejo de Errores
- Estados de loading y error en componentes
- Fallbacks para cuando la API no responde
- Logs detallados para debugging

## Flujo de Usuario Completo

```mermaid
sequenceDiagram
    participant U as Usuario Admin/Super
    participant H as Header
    participant API as Backend API
    participant DB as Base de Datos
    
    U->>H: Carga página
    H->>API: GET /notificaciones/count
    API->>DB: Consultar actividades no vistas
    DB-->>API: Retornar contador
    API-->>H: Contador de notificaciones
    H-->>U: Mostrar badge en campanita
    
    loop Cada 30 segundos
        H->>API: GET /notificaciones/count
        API-->>H: Contador actualizado
        H-->>U: Actualizar badge
    end
    
    U->>H: Click en campanita
    H->>API: GET /notificaciones
    API->>DB: Obtener últimas 10 no vistas
    DB-->>API: Lista de notificaciones
    API-->>H: Notificaciones
    H-->>U: Mostrar dropdown
    
    alt Click en notificación específica
        U->>H: Click en notificación
        H-->>U: Navegar a detalle (/actividad-sistema/:id)
    else Click en "Ver todos"
        U->>H: Click "Ver todos"
        H->>API: POST /marcar-vistas
        API->>DB: Actualizar timestamp usuario
        H-->>U: Navegar a página completa (/actividad-sistema)
    end
```

## Estimación de Tiempo y Recursos

### Desglose por Fase
- **Fase 1 (Backend)**: 2-3 días
  - Migración de BD: 0.5 días
  - Modificación de modelos: 0.5 días
  - Servicios y repositorios: 1 día
  - Endpoints y testing: 1 día

- **Fase 2 (Frontend)**: 3-4 días
  - Hook de notificaciones: 1 día
  - Componente NotificationBell: 1.5 días
  - Modificación del Header: 0.5 días
  - Testing y ajustes: 1 día

- **Fase 3 (Integración)**: 1-2 días
  - Navegación y rutas: 0.5 días
  - Integración con páginas existentes: 0.5 días
  - Testing de flujo completo: 1 día

- **Fase 4 (Optimizaciones)**: 1-2 días
  - Seguridad y validaciones: 1 día
  - Performance y caché: 0.5 días
  - Manejo de errores: 0.5 días

### Total Estimado
**8-11 días de desarrollo** + 2 días de testing y ajustes finales

### Recursos Necesarios
- 1 Desarrollador Full-Stack Senior
- Acceso a base de datos de desarrollo y testing
- Herramientas de testing (Postman, Jest, etc.)

## Criterios de Aceptación

### Funcionales
- ✅ Campanita visible solo para ADMINISTRADOR y SUPERUSUARIO
- ✅ Contador de notificaciones actualizado automáticamente cada 30s
- ✅ Dropdown con últimas 10 actividades del módulo EXPEDIENTES
- ✅ Click en notificación navega al detalle específico
- ✅ Botón "Ver todos" navega a página de Actividad del Sistema
- ✅ Notificaciones se marcan como vistas al acceder a "Ver todos"

### No Funcionales
- ✅ Tiempo de respuesta < 2 segundos para cargar notificaciones
- ✅ Interfaz responsive en dispositivos móviles
- ✅ Manejo graceful de errores de conectividad
- ✅ Seguridad: solo usuarios autorizados acceden a notificaciones

### Técnicos
- ✅ Código documentado y siguiendo estándares del proyecto
- ✅ Tests unitarios para servicios críticos
- ✅ Migración de BD reversible
- ✅ Logs apropiados para debugging

## Consideraciones Futuras

### Mejoras Potenciales
1. **Notificaciones Push**: Implementar Web Push API para notificaciones del navegador
2. **Filtros Avanzados**: Permitir filtrar notificaciones por tipo de actividad
3. **Notificaciones por Email**: Resumen diario/semanal por email
4. **Configuración Personal**: Permitir a usuarios configurar qué notificaciones recibir
5. **Notificaciones en Tiempo Real**: WebSockets para actualizaciones instantáneas

### Escalabilidad
- Considerar Redis para caché de notificaciones en alta concurrencia
- Implementar paginación infinita en el dropdown
- Archivado automático de notificaciones antiguas

---

**Documento creado**: Diciembre 2025  
**Versión**: 1.0  
**Estado**: Listo para implementación