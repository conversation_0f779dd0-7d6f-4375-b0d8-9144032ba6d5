# 📋 PLAN DETALLADO: MEJORA DEL SISTEMA DE ACTIVIDAD DEL SISTEMA

## 🔍 **ANÁLISIS DEL ESTADO ACTUAL**

### Backend Actual:
- Modelo [`ActividadSistema`](backend/src/main/java/com/cufre/expedientes/model/ActividadSistema.java:1) básico con campos limitados
- Controlador [`ActividadSistemaController`](backend/src/main/java/com/cufre/expedientes/controller/ActividadSistemaController.java:1) con un solo endpoint GET
- Servicio [`ActividadSistemaService`](backend/src/main/java/com/cufre/expedientes/service/ActividadSistemaService.java:1) sin funcionalidades de filtrado
- Base de datos con estructura simple

### Frontend Actual:
- Página [`ActividadSistemaPage`](frontend/src/pages/ActividadSistemaPage.tsx:1) con tabla básica
- Sin filtros avanzados
- Sin vista de detalle individual
- Sin paginación

---

## 🎯 **OBJETIVOS DEL PROYECTO**

1. **Mejorar el registro de logs** con información completa (IP, navegador, sesión, cambios detallados)
2. **Crear sistema de filtros avanzados** por módulo, usuario, fecha, y tipo de acción
3. **Implementar vista de detalle** para cada log con información técnica organizada
4. **Mejorar la interfaz visual** con diseño profesional para auditorías
5. **Agregar funcionalidades de auditoría** como exportación y búsqueda avanzada

---

## 🏗️ **ARQUITECTURA DE LA SOLUCIÓN**

```mermaid
graph TB
    subgraph "Frontend - React/TypeScript"
        A[ActividadSistemaPage] --> B[Filtros Avanzados]
        A --> C[Tabla con Paginación]
        A --> D[Vista de Detalle]
        
        B --> B1[Filtro por Usuario]
        B --> B2[Filtro por Módulo]
        B --> B3[Filtro por Fecha]
        B --> B4[Filtro por Acción]
        
        D --> D1[Información General]
        D --> D2[Detalles Técnicos]
        D --> D3[Request/Response]
        D --> D4[Cambios Realizados]
    end
    
    subgraph "Backend - Spring Boot"
        E[ActividadSistemaController] --> F[ActividadSistemaService]
        F --> G[ActividadSistemaRepository]
        F --> H[AuditInterceptor]
        
        H --> H1[Captura IP/User-Agent]
        H --> H2[Registra Request/Response]
        H --> H3[Detecta Cambios en Entidades]
    end
    
    subgraph "Base de Datos"
        I[ACTIVIDAD_SISTEMA - Mejorada]
        J[ACTIVIDAD_DETALLE - Nueva]
    end
    
    A -.->|API Calls| E
    F -.->|JPA| G
    G -.->|SQL| I
    G -.->|SQL| J
```

---

## 📊 **PLAN DE IMPLEMENTACIÓN**

### **FASE 1: MEJORAS EN EL BACKEND** 🔧

#### **1.1 Actualización del Modelo de Datos**
- Expandir tabla `ACTIVIDAD_SISTEMA` con nuevos campos:
  - `IP_CLIENTE` (VARCHAR2(45))
  - `USER_AGENT` (VARCHAR2(500))
  - `SESSION_ID` (VARCHAR2(255))
  - `ENDPOINT` (VARCHAR2(255))
  - `METODO_HTTP` (VARCHAR2(10))
  - `MODULO` (VARCHAR2(50))
  - `CATEGORIA_ACCION` (VARCHAR2(50))
  - `DURACION_MS` (NUMBER)
  - `ESTADO_RESPUESTA` (VARCHAR2(20))

- Crear nueva tabla `ACTIVIDAD_DETALLE`:
  - `ID` (PRIMARY KEY)
  - `ACTIVIDAD_ID` (FOREIGN KEY)
  - `TIPO_DETALLE` (REQUEST/RESPONSE/CAMBIO_ANTERIOR/CAMBIO_POSTERIOR)
  - `CONTENIDO_JSON` (CLOB)

#### **1.2 Interceptor de Auditoría**
- Crear [`AuditInterceptor`](backend/src/main/java/com/cufre/expedientes/interceptor/AuditInterceptor.java) para capturar automáticamente:
  - Información de la request HTTP
  - IP del cliente y User-Agent
  - Tiempo de ejecución
  - Estado de la respuesta

#### **1.3 Mejoras en el Servicio**
- Implementar métodos de filtrado avanzado
- Agregar paginación
- Crear sistema de categorización automática por endpoint

#### **1.4 Nuevos Endpoints**
- `GET /actividad-sistema` (con parámetros de filtro y paginación)
- `GET /actividad-sistema/{id}` (detalle individual)
- `GET /actividad-sistema/export` (exportación a CSV/Excel)
- `GET /actividad-sistema/stats` (estadísticas de uso)

### **FASE 2: MEJORAS EN EL FRONTEND** 🎨

#### **2.1 Rediseño de la Página Principal**
- Implementar barra superior similar a la lista de expedientes
- Agregar panel de filtros avanzados colapsable
- Mejorar la tabla con paginación y ordenamiento
- Agregar indicadores visuales por tipo de acción

#### **2.2 Sistema de Filtros Avanzados**
```mermaid
graph LR
    A[Panel de Filtros] --> B[Filtro por Usuario]
    A --> C[Filtro por Módulo]
    A --> D[Filtro por Fecha]
    A --> E[Filtro por Acción]
    A --> F[Filtro por Estado]
    
    B --> B1[Autocompletado]
    C --> C1[EXPEDIENTES]
    C --> C2[USUARIOS]
    C --> C3[SISTEMA]
    C --> C4[REPORTES]
    D --> D1[Rango de Fechas]
    E --> E1[Crear/Editar/Eliminar/Consultar]
```

#### **2.3 Página de Detalle Individual**
- Ruta: `/actividad-sistema/{id}`
- Secciones colapsables:
  - **Información General**: Usuario, fecha, acción, duración
  - **Detalles Técnicos**: IP, navegador, endpoint, método HTTP
  - **Request/Response**: Parámetros enviados y respuesta recibida
  - **Cambios Realizados**: Estado anterior vs posterior (diff visual)

#### **2.4 Funcionalidades Adicionales**
- Exportación de logs filtrados
- Búsqueda en tiempo real
- Refresh automático opcional
- Notificaciones de actividad crítica

### **FASE 3: INTEGRACIÓN Y TESTING** 🧪

#### **3.1 Integración del Sistema de Logging**
- Modificar todos los controladores existentes para usar el nuevo sistema
- Implementar logging automático en operaciones CRUD
- Agregar logging de inicio/cierre de sesión mejorado

#### **3.2 Testing y Optimización**
- Tests unitarios para nuevos servicios
- Tests de integración para endpoints
- Optimización de consultas de base de datos
- Testing de rendimiento con grandes volúmenes de logs

---

## 🎨 **DISEÑO DE LA INTERFAZ**

### **Página Principal Mejorada**
```mermaid
graph TB
    A[Header: "Actividad del Sistema"] --> B[Barra de Filtros Avanzados]
    B --> C[Estadísticas Rápidas]
    C --> D[Tabla de Logs Mejorada]
    D --> E[Paginación y Controles]
    
    B --> B1[Usuario]
    B --> B2[Módulo]
    B --> B3[Fecha]
    B --> B4[Acción]
    B --> B5[Exportar]
    
    D --> D1[Columna Usuario]
    D --> D2[Columna Acción con Color]
    D --> D3[Columna Fecha/Hora]
    D --> D4[Columna Módulo]
    D --> D5[Columna Estado]
    D --> D6[Botón Ver Detalle]
```

### **Página de Detalle**
- **Header**: Breadcrumb + título con información clave
- **Sección 1**: Información General (tarjeta con iconos)
- **Sección 2**: Detalles Técnicos (acordeón colapsable)
- **Sección 3**: Request/Response (código formateado)
- **Sección 4**: Cambios Realizados (diff visual con colores)

---

## 📋 **ESTRUCTURA DE ARCHIVOS A CREAR/MODIFICAR**

### **Backend**
```
backend/src/main/java/com/cufre/expedientes/
├── model/
│   ├── ActividadSistema.java (MODIFICAR)
│   └── ActividadDetalle.java (NUEVO)
├── repository/
│   ├── ActividadSistemaRepository.java (MODIFICAR)
│   └── ActividadDetalleRepository.java (NUEVO)
├── service/
│   ├── ActividadSistemaService.java (MODIFICAR)
│   └── AuditService.java (NUEVO)
├── controller/
│   └── ActividadSistemaController.java (MODIFICAR)
├── interceptor/
│   └── AuditInterceptor.java (NUEVO)
├── dto/
│   ├── ActividadSistemaDTO.java (NUEVO)
│   ├── ActividadDetalleDTO.java (NUEVO)
│   └── FiltroActividadDTO.java (NUEVO)
└── config/
    └── AuditConfig.java (NUEVO)
```

### **Frontend**
```
frontend/src/
├── pages/
│   ├── ActividadSistemaPage.tsx (MODIFICAR)
│   └── ActividadDetalleePage.tsx (NUEVO)
├── components/
│   ├── actividad/
│   │   ├── FiltrosAvanzados.tsx (NUEVO)
│   │   ├── TablaActividad.tsx (NUEVO)
│   │   ├── DetalleActividad.tsx (NUEVO)
│   │   └── EstadisticasActividad.tsx (NUEVO)
├── services/
│   └── actividadSistemaService.ts (MODIFICAR)
├── types/
│   └── actividad.types.ts (NUEVO)
└── hooks/
    └── useActividadSistema.ts (NUEVO)
```

---

## 🚀 **CRONOGRAMA ESTIMADO**

| Fase | Duración | Descripción |
|------|----------|-------------|
| **Fase 1** | 3-4 días | Backend: Modelo, interceptor, servicios, endpoints |
| **Fase 2** | 4-5 días | Frontend: Páginas, componentes, filtros, detalle |
| **Fase 3** | 2-3 días | Integración, testing, optimización |
| **Total** | **9-12 días** | Implementación completa |

---

## 🔧 **CONSIDERACIONES TÉCNICAS**

1. **Rendimiento**: Implementar índices en BD para consultas de filtrado
2. **Seguridad**: Validar permisos para acceso a logs sensibles
3. **Almacenamiento**: Considerar rotación de logs antiguos
4. **Escalabilidad**: Preparar para grandes volúmenes de datos
5. **Compatibilidad**: Mantener retrocompatibilidad con sistema actual

---

## 📝 **CATEGORÍAS DE ACCIONES POR MÓDULO**

### **EXPEDIENTES**
- `EXPEDIENTE_CREAR`
- `EXPEDIENTE_EDITAR`
- `EXPEDIENTE_ELIMINAR`
- `EXPEDIENTE_CONSULTAR`
- `EXPEDIENTE_EXPORTAR`

### **USUARIOS**
- `USUARIO_CREAR`
- `USUARIO_EDITAR`
- `USUARIO_ELIMINAR`
- `USUARIO_CAMBIAR_ROL`
- `USUARIO_CAMBIAR_PASSWORD`

### **SISTEMA**
- `LOGIN_EXITOSO`
- `LOGIN_FALLIDO`
- `LOGOUT_MANUAL`
- `LOGOUT_INACTIVIDAD`
- `SESION_EXTENDIDA`
- `CONFIGURACION_CAMBIO`

### **REPORTES**
- `REPORTE_GENERAR`
- `REPORTE_EXPORTAR`
- `ESTADISTICA_CONSULTAR`

---

## 🎯 **FUNCIONALIDADES CLAVE DE LA NUEVA INTERFAZ**

### **Dashboard de Actividad**
- Estadísticas en tiempo real
- Gráficos de actividad por hora/día
- Top usuarios más activos
- Alertas de actividad sospechosa

### **Filtros Avanzados**
- Filtro por rango de fechas con presets (hoy, esta semana, este mes)
- Filtro por usuario con autocompletado
- Filtro por módulo con iconos visuales
- Filtro por tipo de acción con colores
- Filtro por estado de respuesta (éxito/error)
- Combinación de múltiples filtros

### **Vista de Detalle Mejorada**
- Timeline de la acción
- Información del contexto (expediente relacionado, etc.)
- Diff visual para cambios en datos
- Información de geolocalización (si está disponible)
- Enlaces a entidades relacionadas

### **Exportación y Reportes**
- Exportar logs filtrados a CSV/Excel
- Generar reportes de auditoría
- Programar reportes automáticos
- Integración con sistema de notificaciones

---

Este plan proporciona una hoja de ruta completa para transformar el sistema básico de actividad actual en una herramienta profesional de auditoría y monitoreo, cumpliendo con todos los requerimientos solicitados y preparando el sistema para futuras expansiones.