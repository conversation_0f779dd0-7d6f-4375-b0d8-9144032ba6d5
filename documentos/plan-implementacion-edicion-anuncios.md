# Plan de Implementación: Funcionalidad de Edición de Anuncios

## 1. Resumen del Problema

Tras un análisis exhaustivo, se ha determinado que la causa raíz del error 500 al intentar guardar un anuncio no es un bug, sino una **funcionalidad incompleta** en el frontend.

- **Causa Inmediata:** La petición `POST /api/anuncios` se envía con un cuerpo (`payload`) vacío, lo que provoca un error de validación en el backend que se manifiesta como un error 500.
- **Causa de Fondo:** El flujo de "edición" de un anuncio nunca fue implementado en el frontend. La interfaz de usuario permite abrir un modal de edición, pero el código para enviar los datos actualizados al backend no existe. Esto lleva a que se intente usar la función de "crear" de forma incorrecta.

## 2. Objetivo

Implementar el flujo completo de **edición de anuncios** en el frontend, conectándolo con el endpoint `PUT /api/anuncios/{id}` que ya existe en el backend.

## 3. Diagrama del Plan

```mermaid
graph TD
    subgraph "Plan de Implementación: Completar Edición de Anuncios"
        A[Inicio] --> B[1. Definir la Ruta en `apiRoutes.ts`];
        B --> C[2. Crear la Función en `anuncioService.ts`];
        C --> D[3. Implementar la Lógica en `AnunciosAdminPage.tsx`];
        D --> E[FIN: Funcionalidad Completa];
    end
```

## 4. Pasos Detallados de Implementación

### Paso 1: Modificar `frontend/src/api/apiRoutes.ts`

**Acción:** Añadir la definición de la ruta para la petición `PUT` que actualizará un anuncio existente.

**Archivo a modificar:** [`frontend/src/api/apiRoutes.ts`](frontend/src/api/apiRoutes.ts)

**Cambio requerido:** Dentro del objeto `anuncios`, añadir la propiedad `update`:

```typescript
// ...
  anuncios: {
    getAll: `${API_BASE_URL}/api/anuncios`,
    getById: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`,
    create: `${API_BASE_URL}/api/anuncios`,
    update: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`, // <-- AÑADIR ESTA LÍNEA
    activar: (id: number) => `${API_BASE_URL}/api/anuncios/${id}/activar`,
    desactivar: (id: number) => `${API_BASE_URL}/api/anuncios/${id}/desactivar`,
    delete: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`,
    obtenerActivo: `${API_BASE_URL}/api/anuncios/activo`,
    marcarVisto: `${API_BASE_URL}/api/anuncios/visto`,
  },
// ...
```

### Paso 2: Modificar `frontend/src/api/anuncioService.ts`

**Acción:** Añadir una nueva función `update` al servicio de anuncios. Esta función se encargará de realizar la llamada `PUT` a la API.

**Archivo a modificar:** [`frontend/src/api/anuncioService.ts`](frontend/src/api/anuncioService.ts)

**Cambio requerido:** Añadir la siguiente función dentro del objeto `anuncioService`:

```typescript
  // ...
  /**
   * Actualiza un anuncio existente (solo para administradores)
   */
  update: async (id: number, anuncio: CrearAnuncioRequest): Promise<Anuncio> => {
    try {
      const response = await axiosClient.put(apiRoutes.anuncios.update(id), anuncio);
      return response.data as Anuncio;
    } catch (error: any) {
      console.error('Error al actualizar anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Activa un anuncio específico (solo para administradores)
   */
  // ...
```

### Paso 3: Modificar `frontend/src/pages/anuncios/AnunciosAdminPage.tsx`

**Acción:** Implementar la lógica de edición en el manejador del formulario, reemplazando el comentario `TODO` existente.

**Archivo a modificar:** [`frontend/src/pages/anuncios/AnunciosAdminPage.tsx`](frontend/src/pages/anuncios/AnunciosAdminPage.tsx)

**Cambio requerido:** Modificar la función `handleSubmit` para que llame a la nueva función `anuncioService.update` cuando se esté editando un anuncio.

```typescript
// ...
const handleSubmit = async () => {
  // ... (validación existente)

  try {
    setSubmitting(true);
    
    if (editingAnuncio) {
      // Reemplazar el TODO con la llamada al servicio de actualización
      await anuncioService.update(editingAnuncio.id, formData);
      setSuccess('Anuncio actualizado exitosamente');
    } else {
      await anuncioService.create(formData);
      setSuccess('Anuncio creado exitosamente');
    }
    
    handleCloseModal();
    cargarAnuncios();

  } catch (err: any) {
    setError('Error al guardar el anuncio: ' + (err.response?.data?.error || err.message));
  } finally {
    setSubmitting(false);
  }
};
// ...
```

## 5. Verificación

Una vez implementados los tres pasos, la funcionalidad se podrá verificar de la siguiente manera:
1.  Navegar a la página de "Gestión de Anuncios".
2.  Hacer clic en el botón de editar de un anuncio existente.
3.  Modificar el título o el contenido en el modal.
4.  Hacer clic en "Guardar".
5.  El sistema deberá mostrar el mensaje "Anuncio actualizado exitosamente" y los cambios se verán reflejados en la tabla.