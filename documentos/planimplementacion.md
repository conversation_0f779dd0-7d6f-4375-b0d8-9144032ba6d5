## Plan de Implementación: Permitir Edición de Número de Expediente

### 1. Objetivo

Permitir que los usuarios con rol SUPERUSUARIO o ADMINISTRADOR puedan modificar el número de expediente de un expediente existente, asegurando que el nuevo número sea único en el sistema.

### 2. Análisis de Viabilidad

Basado en el análisis del código fuente:

*   **Frontend:** El campo del número de expediente en [`frontend/src/components/expedientes/InformacionBasicaTab.tsx`](frontend/src/components/expedientes/InformacionBasicaTab.tsx:1) está marcado como `readOnly`. La lógica de roles y permisos se maneja a través de `AuthContext` y el enum `Rol`. Es viable habilitar la edición de este campo condicionalmente según el rol del usuario.
*   **Backend:** El servicio [`ExpedienteService.java`](backend/src/main/java/com/cufre/expedientes/service/ExpedienteService.java:1) maneja la creación y actualización de expedientes. El mapper [`ExpedienteMapper.java`](backend/src/main/java/com/cufre/expedientes/mapper/ExpedienteMapper.java:1) actualmente ignora el campo `numero` en las actualizaciones. Es viable modificar el servicio y el mapper para permitir la actualización del número, agregando la validación de unicidad.

La implementación es viable y no presenta impedimentos técnicos mayores.

### 3. Plan Detallado

Este plan se divide en tareas de Frontend y Backend.

#### 3.1. Tareas de Frontend

1.  **Modificar [`InformacionBasicaTab.tsx`](frontend/src/components/expedientes/InformacionBasicaTab.tsx:1):**
    *   Importar `useAuth` para acceder al contexto del usuario autenticado.
    *   Obtener el usuario y su rol dentro del componente.
    *   Determinar si el usuario tiene el rol SUPERUSUARIO o ADMINISTRADOR.
    *   Modificar la propiedad `InputProps` del `TextField` del número de expediente para que `readOnly` sea `false` si el usuario tiene los permisos adecuados.

    ```mermaid
    graph TD
        A[InformacionBasicaTab.tsx] --> B{Obtener Usuario y Rol};
        B --> C{Es SUPERUSUARIO o ADMINISTRADOR?};
        C -- Si --> D[Habilitar Edición de Número];
        C -- No --> E[Mantener Número ReadOnly];
    ```

#### 3.2. Tareas de Backend

1.  **Modificar [`ExpedienteMapper.java`](backend/src/main/java/com/cufre/expedientes/mapper/ExpedienteMapper.java:1):**
    *   Remover o comentar la anotación `@Mapping(target = "numero", ignore = true)` en el método `updateEntity`. Esto permitirá que el valor del campo `numero` del DTO se considere durante la actualización de la entidad.

2.  **Modificar [`ExpedienteService.java`](backend/src/main/java/com/cufre/expedientes/service/ExpedienteService.java:1):**
    *   En el método `update(Long id, ExpedienteDTO dto)`:
        *   Obtener el expediente existente de la base de datos.
        *   Obtener el usuario autenticado (se necesitará una forma de acceder al usuario actual, posiblemente a través de `SecurityContextHolder` de Spring Security).
        *   Verificar si el usuario tiene el rol SUPERUSUARIO o ADMINISTRADOR.
        *   Si el usuario *no* tiene los permisos, asegurarse de que el número de expediente en el DTO sea igual al número del expediente existente antes de proceder con la actualización. Si son diferentes, lanzar una excepción de seguridad o validación.
        *   Si el usuario *tiene* los permisos y el número de expediente en el DTO es diferente al del expediente existente:
            *   Validar que el nuevo número de expediente no esté ya en uso por otro expediente (excluyendo el expediente actual que se está actualizando). Utilizar el método `findByNumero` del repositorio para esta validación. Si el número ya existe, lanzar una excepción de validación.
            *   Proceder con la actualización de la entidad utilizando el mapper.
        *   Guardar la entidad actualizada en el repositorio.
        *   Registrar la actividad en el sistema, indicando que el número de expediente fue modificado (si aplica).

    ```mermaid
    graph TD
        F[ExpedienteService.java - update] --> G{Obtener Usuario Actual};
        G --> H{Es SUPERUSUARIO o ADMINISTRADOR?};
        H -- No --> I{Número en DTO == Número Existente?};
        I -- Si --> K[Proceder con Actualización];
        I -- No --> J[Lanzar Excepción de Permisos];
        H -- Si --> L{Número en DTO != Número Existente?};
        L -- Si --> M{Nuevo Número Único?};
        M -- Si --> K;
        M -- No --> N[Lanzar Excepción de Validación];
        L -- No --> K;
        K --> O[Guardar Expediente];
        O --> P[Registrar Actividad];
    ```

3.  **Consideraciones de Seguridad:**
    *   La verificación de roles debe realizarse en el backend para garantizar la seguridad, ya que las comprobaciones en el frontend pueden ser eludidas.
    *   Asegurar que solo los endpoints de actualización de expediente permitan la modificación del número bajo la validación de roles.

4.  **Consideraciones de Validación:**
    *   La validación de unicidad del número de expediente debe realizarse en el backend antes de guardar los cambios.

### 4. Reversión

*   En caso de problemas, se puede revertir a la versión anterior del frontend y backend.