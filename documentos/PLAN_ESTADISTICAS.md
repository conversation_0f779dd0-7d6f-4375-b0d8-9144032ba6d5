# Plan de corrección del Modal de Estadísticas de Actividad

## Contexto
El **modal de estadísticas** se abre desde `ActividadSistemaPage.tsx`, mostrando tres secciones:
1. Actividades por Módulo  
2. Actividades por Usuario  
3. Resumen de Actividades Recientes  

Aunque el backend (`/api/actividad-sistema/estadisticas`) responde con datos válidos, el modal muestra “No hay datos disponibles”.

## Diagnóstico
El _hook_ [`frontend/src/hooks/useEstadisticasActividad.ts`](../hooks/useEstadisticasActividad.ts) **transforma** la respuesta:

```ts
porModulo: response.data.porModulo?.map(...).slice(0, 5) || []
```

Si `porModulo` o `porUsuario` no son Arrays _estrictos_ (p. ej. llegan como `null`, `undefined` o algún wrapper JSON), el encadenado opcional devuelve `undefined`.  
En consecuencia, al aplicar `|| []` el resultado es un **array vacío**, gatillando el mensaje de falta de datos y dejando los gráficos vacíos.

## Flujo actual
```mermaid
flowchart TD
    A[Modal abre] -->|open & !data| B(cargarEstadisticas)
    B --> C[Backend responde con datos]
    C --> D{Hook transforma}
    D -->|Array mal evaluado| E[Modal muestra “No hay datos”]
```

## Objetivos
- Garantizar que `porModulo`, `porUsuario` y `actividadesRecientes` sean Arrays antes de mapearlos.  
- Registrar en consola la estructura real recibida (solo para depuración).  
- Verificar visualmente la aparición de gráficos y textos.  
- (Opcional) Ajustar sintaxis `<Grid>` para evitar _warnings_ de MUI.

## Pasos detallados

| Paso | Acción | Responsable |
|------|--------|-------------|
| 1 | Añadir `console.log(response.data)` dentro de `cargarEstadisticas` para confirmar estructura | Frontend |
| 2 | Reemplazar la transformación con una función de **normalización segura**:  | Frontend |
|   | ```ts<br>const toArray = (val:any)=>Array.isArray(val)?val:[];<br>const listaModulo = toArray(response.data.porModulo);<br>``` | |
| 3 | Repetir lógica para `porUsuario` y `actividadesRecientes` | Frontend |
| 4 | Mantener `.map` y `.slice(0,5)` sobre las listas normalizadas | Frontend |
| 5 | Probar manualmente: abrir/cerrar modal y verificar gráficos | QA |
| 6 | (Optativo) Cambiar `<Grid size={...}>` a `<Grid item xs={...}>` para cumplir con API de MUI v5 | Frontend |
| 7 | Eliminar `console.log` antes de _commit_ final | Frontend |

## Código sugerido
```ts
// Dentro de cargarEstadisticas
console.log('Estadísticas RAW', response.data);

const toArray = (val: any): any[] => Array.isArray(val) ? val : [];

const estadisticas: EstadisticasData = {
  porModulo: toArray(response.data.porModulo)
    .map((item: any[]) => ({ nombre: item[0] || 'Sin módulo', cantidad: item[1] || 0 }))
    .slice(0,5),
  porUsuario: toArray(response.data.porUsuario)
    .map((item: any[]) => ({ nombre: item[0] || 'Sin usuario', cantidad: item[1] || 0 }))
    .slice(0,5),
  actividadesRecientes: toArray(response.data.actividadesRecientes)
};
```

## Checklist de aceptación
- [ ] Gráficos de módulo y usuario se renderizan con barras.  
- [ ] Resumen muestra recuento real de actividades recientes.  
- [ ] Sin errores en consola.  
- [ ] Sin advertencias de MUI por uso de `<Grid>`.  

## Notas adicionales
- El backend ya agrupa y limita datos. Si el volumen crece, considerar paginar o lazy-load en el modal.  
- Se puede añadir manejo de error UI extra (ej. botón *Reintentar* cuando `error` no sea `null`).