# Plan de corrección: Error 500 al guardar anuncios  

**<PERSON><PERSON><PERSON><PERSON> afectado:** Backend – rutas `/api/anuncios`  
**Problema observado:** Al realizar un `POST /api/anuncios` el backend devuelve 500 Internal Server Error.  

## Diagnóstico resumido  
1. El `DispatcherServlet` asigna la petición a `ResourceHttpRequestHandler` (manejador de recursos estáticos) en vez de al `AnuncioController`.  
2. Esto ocurre porque en `WebMvcConfig.addResourceHandlers()` se registra un patrón de _fallback_ `"/**"` con prioridad 1, que coincide prematuramente con **todas** las rutas, incluidas las de la API.  
3. Al recibir un método POST, el handler estático lanza `HttpRequestMethodNotSupportedException`; el `GlobalExceptionHandler` la atrapa y la transforma en respuesta 500.  

## Árb<PERSON> de llamadas (extracto de log)  
```text
DEBUG SimpleUrlHandlerMapping : Mapped to ResourceHttpRequestHandler [classpath [static/]]
ERROR GlobalExceptionHandler : Error genérico no manejado: Request method 'POST' is not supported
```  

## Diagrama de secuencia  
```mermaid
sequenceDiagram
    participant C as Cliente (POST /api/anuncios)
    participant D as DispatcherServlet
    participant RH as ResourceHttpRequestHandler (order 1)
    participant AC as AnuncioController (order 0)

    C->>D: Solicitud POST /api/anuncios
    D-->>RH: Coincidencia patrón /** (Resource Handler)
    RH-->>D: 405 → propagado como 500
    D-->>C: 500 Internal Server Error
```  

## Solución acordada  
1. Cambiar la prioridad del handler de recursos estáticos para que tenga orden **2** (o mayor), dejando que `RequestMappingHandlerMapping` (orden 0) procese primero las rutas API.  

```java
// WebMvcConfig.java – dentro de addResourceHandlers()
// Antes
registry.setOrder(1);
// Después
registry.setOrder(2); // o Integer.MAX_VALUE
```  

2. Compilar y arrancar la aplicación; verificar que `POST /api/anuncios` llegue a `AnuncioController` y devuelva 201.  
3. Revisar logs para confirmar ausencia de `ResourceHttpRequestHandler` en la traza de la petición.  
4. Añadir una prueba de integración (Spring MockMvc) que ejecute un POST válido a `/api/anuncios` y aserte 201, evitando regresiones.  

## Pasos detallados de implementación  
1. Editar `backend/src/main/java/com/cufre/expedientes/config/WebMvcConfig.java`.  
2. Modificar la línea `registry.setOrder(1);` → `registry.setOrder(2);`.  
3. Ejecutar `mvn spring-boot:run` o la tarea equivalente en el entorno de despliegue.  
4. Probar manualmente:  
   ```bash
   curl -X POST http://localhost:8080/api/anuncios \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <JWT>" \
     -d '{"titulo":"Prueba","contenido":"Mensaje","fechaExpiracion":"2025-12-31"}'
   ```  
5. Confirmar respuesta HTTP 201/200 y registro de anuncio en base de datos.  
6. Ejecutar pruebas automatizadas.  

## Consideraciones adicionales  
- Si en el futuro se añaden más rutas de API, asegurarse de que ningún `ResourceHandler` tenga prioridad mayor que 2.  
- Como alternativa defensiva se podría reemplazar el patrón `"/**"` con uno que excluya explícitamente `api/**`, pero no es necesario por ahora.  

## Estado  
Aprobado por el equipo. Cambios listos para aplicar en rama `hotfix/anuncios-500`.