# 🚀 Guía de Despliegue CUFRE (Backend + Frontend + Nginx + <PERSON>er)

## 1. Despliegue Automático (Recomendado)

1. Da permisos de ejecución al script:
   ```bash
   chmod +x deploy-cufre.sh
   ```
2. Ejecuta el script de despliegue:
   ```bash
   ./deploy-cufre.sh
   ```

Este script compila backend y frontend, verifica la configuración de Nginx, construye y levanta los contenedores Docker, y muestra el estado y logs recientes.

---

## 2. Despliegue Manual (Referencia)

### 2.1. Pre-requisitos
- Docker y Docker Compose instalados.
- Código fuente actualizado en el servidor.
- Acceso a la base de datos Oracle (o la que corresponda).

### 2.2. Compilar el Backend
```bash
cd backend
./mvnw clean package -DskipTests
```
Esto generará un archivo `.jar` en `backend/target/`.

### 2.3. Compilar el Frontend
```bash
cd ../frontend
npm install
npm run build
```
Esto generará la carpeta `build/` con los archivos estáticos listos para Nginx.

### 2.4. Verifica la configuración de Nginx y Docker
- En `frontend/nginx.conf`:
  ```nginx
  location /api/ {
      proxy_pass http://backend:8080;
      # ...otros headers...
  }
  ```
- En `docker-compose.yml`, el servicio `nginx` debe exponer el puerto 80 y ambos servicios deben estar en la misma red.

### 2.5. Build y despliegue con Docker Compose
```bash
docker-compose build
docker-compose up -d
```
Esto levantará:
- **backend** (Spring Boot en 8080, solo accesible internamente)
- **nginx** (sirve el frontend y hace proxy al backend en el puerto 80)

### 2.6. Acceso a la aplicación
- Navegador: `http://localhost` (o la IP/URL pública del servidor)
- API: rutas bajo `/api/` (ejemplo: `http://localhost/api/expedientes`)

### 2.7. Logs y monitoreo
```bash
docker-compose logs -f backend
docker-compose logs -f nginx
```
Para detener los servicios:
```bash
docker-compose down
```

---

## 3. Notas adicionales
- **Variables de entorno:** agrégalas en `docker-compose.yml` bajo `environment:`.
- **Base de datos:** asegúrate de que el backend pueda conectarse a Oracle.
- **Pruebas:** ejecuta los tests de integración antes de desplegar en producción.
- **Swagger:** si está habilitado, accede a la documentación en `http://localhost/api/swagger-ui.html`.

---

## 4. Troubleshooting (Solución de problemas comunes)
- **Backend no conecta a la base de datos:** revisa credenciales, red y puertos.
- **Frontend muestra error 502/504:** revisa logs de Nginx y backend, asegúrate de que ambos servicios estén corriendo.
- **Cambios en el código no se ven reflejados:** vuelve a ejecutar el script o los pasos de build manualmente.
- **Problemas con rutas de la API:** asegúrate de que el frontend apunte a `/api/` y que Nginx haga proxy correctamente.

---

¡Despliegue exitoso! 🎉 