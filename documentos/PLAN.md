# Plan de Diagnóstico y Solución: Error al Guardar Anuncios

## 1. Problema Identificado

Al intentar guardar un nuevo anuncio desde la interfaz de administración, el backend responde con un error `500 (Internal Server Error)`. El análisis de los logs iniciales revela que la solicitud `POST` al endpoint `/api/anuncios` llega con un cuerpo (`request body`) vacío (`"parametros": {}`), lo que impide la creación del anuncio y genera una falla inesperada en el servidor.

## 2. Análisis Realizado

-   **Logs del Backend:** No muestran errores de validación (código 400) ni de autorización (código 403). El error 500 genérico sugiere una excepción no controlada.
-   **Código del Backend:**
    -   `AnuncioController.java`: El método `crear` espera un objeto `@Valid CrearAnuncioDTO`.
    -   `CrearAnuncioDTO.java`: Requiere que los campos `titulo` y `contenido` no estén vacíos.
    -   `GlobalExceptionHandler.java`: Carece de un manejador específico para `HttpMessageNotReadableException`, que es la excepción que probablemente se lanza cuando falla la deserialización de un cuerpo de solicitud incorrecto.
-   **Código del Frontend:**
    -   `AnunciosAdminPage.tsx`: El formulario parece recopilar los datos del título y contenido y los pasa correctamente al servicio de la API.
    -   `anuncioService.ts` y `axiosClient.ts`: La configuración para enviar la solicitud al backend es estándar y no muestra anomalías que justifiquen un cuerpo de solicitud vacío.

## 3. Hipótesis Principal

El backend no puede deserializar el cuerpo de la solicitud JSON enviado por el frontend a un objeto Java `CrearAnuncioDTO`. Esta falla de deserialización, probablemente una `HttpMessageNotReadableException`, no está siendo manejada de forma explícita, lo que provoca que se capture como una excepción genérica y se devuelva un error 500 sin detalles claros.

## 4. Plan de Acción

El objetivo es mejorar el logging del backend para obtener un mensaje de error claro que confirme la hipótesis y nos guíe hacia la solución definitiva.

### Paso 1: Mejorar el Manejador de Excepciones del Backend

Se modificará el archivo `backend/src/main/java/com/cufre/expedientes/exception/GlobalExceptionHandler.java` para añadir un manejador de excepciones específico para `HttpMessageNotReadableException`.

**Acción:** Añadir el siguiente método a la clase `GlobalExceptionHandler`:

```java
import org.springframework.http.converter.HttpMessageNotReadableException;
// ...

@ExceptionHandler(HttpMessageNotReadableException.class)
public ResponseEntity<ErrorResponse> handleHttpMessageNotReadable(HttpMessageNotReadableException ex) {
    log.error("Error de solicitud mal formada: El cuerpo de la solicitud no se pudo leer. Causa: {}", ex.getMessage());
    ErrorResponse error = new ErrorResponse(
        HttpStatus.BAD_REQUEST.value(),
        "El cuerpo de la solicitud está vacío o tiene un formato incorrecto.",
        LocalDateTime.now()
    );
    return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);
}
```

### Paso 2: Añadir Logging de Depuración en el Controlador

Para obtener una traza completa del flujo de la solicitud, se añadirá una línea de log al principio del método `crear` en `backend/src/main/java/com/cufre/expedientes/controller/AnuncioController.java`.

**Acción:** Añadir una línea de log para registrar el DTO recibido:

```java
@PostMapping
@PreAuthorize("hasAnyRole('ADMINISTRADOR', 'SUPERUSUARIO')")
public ResponseEntity<AnuncioDTO> crear(@Valid @RequestBody CrearAnuncioDTO crearAnuncioDTO) {
    log.debug("Request para crear anuncio recibido: {}", crearAnuncioDTO); // <-- AÑADIR ESTA LÍNEA
    log.info("Creando nuevo anuncio: {}", crearAnuncioDTO.getTitulo());
    AnuncioDTO anuncioCreado = anuncioService.crear(crearAnuncioDTO);
    return ResponseEntity.status(HttpStatus.CREATED).body(anuncioCreado);
}
```

### Paso 3: Probar y Analizar los Nuevos Logs

Una vez implementados los cambios anteriores:

1.  **Recompilar y reiniciar** el servicio del backend.
2.  **Replicar el error** intentando crear un anuncio desde el frontend.
3.  **Analizar los logs del backend.** Se espera ver un nuevo error claro proveniente del manejador de `HttpMessageNotReadableException` o un log que muestre un DTO nulo o incompleto.

### Paso 4: Solución Final

Basado en el resultado del análisis de los nuevos logs, la solución será:

-   **Si se confirma el error de deserialización:** La causa raíz probablemente estará en una discrepancia entre el objeto JSON enviado por el frontend y lo que espera el DTO de Java. La solución será ajustar el frontend o el backend para que coincidan.
-   **Si el problema es otro:** El nuevo logging nos dará la información necesaria para identificarlo y solucionarlo.

## 5. Diagrama de Flujo del Problema

```mermaid
sequenceDiagram
    participant Frontend
    participant Backend as AnuncioController
    participant SpringValidation as Spring Validation (@Valid)
    participant JacksonDeserializer as Deserializador Jackson
    participant AnuncioService

    Frontend->>+Backend: POST /api/anuncios (con payload JSON)
    
    Backend->>+JacksonDeserializer: Convertir JSON a CrearAnuncioDTO
    alt Falla de Deserialización (Silenciosa)
        JacksonDeserializer-->>-Backend: Lanza HttpMessageNotReadableException
        Note over Backend: La excepción no es manejada explícitamente
        Backend-->>-Frontend: Respuesta 500 Internal Server Error
    else Deserialización Exitosa
        JacksonDeserializer-->>-Backend: Devuelve objeto CrearAnuncioDTO
        Backend->>+SpringValidation: Validar objeto DTO
        alt Datos Inválidos
            SpringValidation-->>-Backend: Falla (400 Bad Request)
            Backend-->>-Frontend: Respuesta 400
        else Datos Válidos
            SpringValidation-->>-Backend: OK
            Backend->>AnuncioService: crear(anuncioDTO)
            AnuncioService->>Backend: Anuncio Creado
            Backend->>Frontend: Respuesta 201 Created
        end
    end