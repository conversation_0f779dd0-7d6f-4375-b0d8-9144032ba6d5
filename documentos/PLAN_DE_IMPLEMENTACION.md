# Plan de Implementación: Configuración de Variables de Prioridad

## 1. Resumen del Objetivo

El objetivo de esta tarea es refactorizar el sistema de cálculo de prioridad de expedientes para que los valores (pesos) de las variables sean configurables por un administrador desde la interfaz de usuario, en lugar de estar fijos en el código.

## 2. Análisis del Sistema Actual

El cálculo de la prioridad se realiza en la clase `com.cufre.expedientes.util.PriorityCalculator`. Este cálculo es una suma de valores fijos ("hardcodeados") basados en diferentes atributos del `Expediente`.

### 2.1. Variables Identificadas

A continuación se detallan las variables que influyen en el cálculo de la prioridad:

#### Variables Categóricas (Selección Múltiple)

- **Valoración del Delito:** Suma de la valoración de cada delito asociado.
- **Profesión del Prófugo:** (OFICIO: 500, PROFESIONAL: 800, etc.)
- **Tipo de Captura:** (NACIONAL: 500, INTERNACIONAL: 1000, etc.)
- **Tipo de Víctima:** (MENOR: 800, MUJER: 250, etc.)
- **Nivel de Organización de la Banda:** (SIMPLE: 250, COMPLEJA: 800)
- **Ámbito de la Banda:** (NACIONAL: 750, INTERNACIONAL: 1000, etc.)
- **Capacidad Operativa de la Banda:** (ALTA: 1000, BAJA: 500)
- **Impacto Social:** (ALTO: 500, BAJO: 250)
- **Tipo de Daño:** (FISICO: 250, PSICOLOGICO: 150, etc.)
- **Nivel de Incidencia en la Zona:** (ALTA: 500, MEDIA: 250, etc.)
- **Institución Sensible Cercana:** (ESCUELA: 600, HOSPITAL: 500, etc.)
- **Impacto en la Percepción de Seguridad:** (ALTA: 500, MEDIA: 250, etc.)

#### Variables Numéricas (Por Rangos)

- **Detenciones Previas:** (1-2: 200, 3-5: 750, +5: 1000)
- **Número de Cómplices:** (1-2: 100, 3-5: 250, +5: 1000)

#### Variables Booleanas (Sí/No)

- **Caso Mediático:** 500
- **Prófugo Reincidente:** 800
- **Prófugo Reiterante:** 500
- **Involucra Banda:** 500
- **Involucra Terrorismo:** 1000
- **Hubo Planificación:** 500 (si no, 100)
- **Conexiones con otras actividades delictivas:** 500
- **Uso de Armas de Fuego:** 500
- **Uso de Armas Blancas:** 250
- **Recursos Limitados:** 500 (si no, 200)
- **Ocurrió en Área Fronteriza:** 500 (si no, 100)
- **Tiene Recompensa:** 500

## 3. Plan de Implementación

### 3.1. Backend

1.  **Modelo de Datos:**
    -   Crear una nueva tabla en la base de datos: `parametro_prioridad`.
    -   Columnas: `id` (PK), `clave_variable` (ej: `PROFESION_PROFESIONAL`), `valor` (INT), `descripcion` (TEXT), `tipo_variable` (ENUM: `CATEGORICA`, `BOOLEANA`, `RANGO`).
    -   Crear la entidad JPA `ParametroPrioridad` correspondiente.

2.  **Capa de Persistencia y Servicio:**
    -   Crear `ParametroPrioridadRepository` extendiendo `JpaRepository`.
    -   Crear `ParametroPrioridadService` para manejar la lógica de negocio.
    -   Implementar un sistema de **caché** en `ParametroPrioridadService` para almacenar los parámetros y evitar consultas repetidas a la base de datos en cada cálculo de prioridad. La caché se invalidará cuando un parámetro sea actualizado.

3.  **Refactorización:**
    -   Modificar `PriorityCalculator` para que obtenga los valores desde `ParametroPrioridadService` (la caché) en lugar de los `Map` estáticos.

4.  **API REST:**
    -   Crear `ParametroPrioridadController`.
    -   `GET /api/parametros-prioridad`: Devuelve todos los parámetros configurables.
    -   `PUT /api/parametros-prioridad`: Recibe una lista de parámetros para actualizar. Debe estar protegido por rol (`ADMINISTRADOR` / `SUPERUSUARIO`).

### 3.2. Frontend

1.  **Ruta y Componente:**
    -   Crear una nueva ruta `/configuracion/variables`.
    -   Proteger la ruta para que solo sea accesible por roles autorizados.
    -   Crear un nuevo componente `ConfiguracionVariablesPage`.

2.  **Interfaz de Usuario:**
    -   El componente llamará a `GET /api/parametros-prioridad` al cargar.
    -   Mostrará los parámetros en una tabla, agrupados por categoría para mayor claridad.
    -   Cada fila permitirá la edición del campo `valor`.

3.  **Lógica de Actualización:**
    -   Un botón "Guardar" recolectará todos los valores modificados y los enviará al backend mediante `PUT /api/parametros-prioridad`.
    -   Se mostrará una notificación de éxito o error al usuario.

## 4. Diagrama de Flujo

```mermaid
graph TD
    subgraph Frontend
        A[Usuario Admin] --> B{Página de Configuración};
        B --> C[Tabla de Variables];
        C -- Edita valor --> D[Botón Guardar];
        D -- click --> E[Llama a API PUT /api/parametros-prioridad];
    end

    subgraph Backend
        E --> F[Controller: ParametroPrioridadController];
        F --> G[Service: ParametroPrioridadService];
        G --> H[Actualiza valor en BD];
        G --> I[Invalida Caché de Parámetros];

        J(Cálculo de Prioridad de Expediente) --> K[Service: ExpedienteService];
        K --> L[Util: PriorityCalculator];
        L -- Pide valores --> M[Caché de Parámetros];
        M -- Si está vacía, carga desde --> G;
    end

    H --> M;