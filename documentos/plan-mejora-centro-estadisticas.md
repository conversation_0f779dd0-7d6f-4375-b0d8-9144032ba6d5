# Plan de Mejora del Centro de Estadísticas

Este documento detalla el plan para mejorar el centro de estadísticas, incorporando nuevas métricas, mejorando la interactividad y ajustando el diseño según los requisitos.

## Diagrama de Flujo del Plan

```mermaid
graph TD
    A[Inicio: Mejorar Centro de Estadísticas] --> B{Fase 1: Mejoras en Backend (API)};
    B --> B1[Métricas de Expedientes: Caso más nuevo/antiguo (CAPTURA VIGENTE), 3 casos de mayor prioridad];
    B --> B2[Métricas de Usuarios: Total de usuarios registrados];
    B --> B3[Métricas Adicionales: Total personas vinculadas, Total delitos, Total fuerzas de seguridad];
    B --> B4[Filtrado Global: Modificar/Crear endpoints para aceptar filtros y aplicarlos a consultas];
    B --> C{Fase 2: Mejoras en Frontend (UI/UX)};
    C --> C1[Diseño: Implementar scroll, asegurar gráficos de donas no cortados];
    C --> C2[Visualización: Integrar nuevas métricas en "mapa de métricas clave" o nuevo panel];
    C --> C3[Interactividad: Implementar lógica de filtrado global para gráficos y rankings];
    C --> C4[Exclusión: Asegurar que no se muestren mapas];
    C --> D{Fase 3: Documentación};
    D --> D1[Escribir plan en archivo Markdown];
    D1 --> E[Fin];
```

## Plan Detallado

### Fase 1: Mejoras en el Backend (API)

1.  **Métricas de Expedientes**:
    *   **Caso más nuevo y caso más antiguo (estado "CAPTURA VIGENTE")**:
        *   En [`backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java), añadir métodos para encontrar el expediente más nuevo y el más antiguo filtrando por `estadoSituacion = "CAPTURA VIGENTE"`.
        *   En [`backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java`](backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java), añadir métodos que utilicen estos nuevos repositorios para obtener los datos y exponerlos.
        *   En [`backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java`](backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java), añadir endpoints para estas nuevas métricas.
    *   **3 primeros casos con mayor prioridad**:
        *   En [`backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java), añadir un método para obtener los 3 expedientes con mayor prioridad, filtrando por `estadoSituacion = "CAPTURA VIGENTE"`.
        *   En [`backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java`](backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java), añadir un método que utilice este nuevo repositorio.
        *   En [`backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java`](backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java), añadir un endpoint para esta métrica.

2.  **Métricas de Usuarios**:
    *   **Total de usuarios registrados**:
        *   La funcionalidad ya existe en `usuarioRepository.count()`. Se asegurará que esté expuesta a través de [`backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java`](backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java) y [`backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java`](backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java) para su uso en el dashboard.

3.  **Métricas Adicionales para el "mapa de métricas clave"**:
    *   **Total de personas vinculadas**: Ya existe `personaExpedienteRepository.countDistinctPersonasVinculadas()`. Se asegurará que esté expuesta.
    *   **Total de delitos**: Ya existe `delitoRepository.count()`. Se asegurará que esté expuesta.
    *   **Total de fuerzas de seguridad**:
        *   En [`backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/ExpedienteRepository.java), añadir una consulta para obtener la lista de todas las fuerzas de seguridad únicas (`DISTINCT fuerzaAsignada`).
        *   En [`backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java`](backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java), añadir un método para obtener el conteo de estas fuerzas.
        *   En [`backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java`](backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java), añadir un endpoint para esta métrica.

4.  **Filtrado Global**:
    *   **Modificar/Crear endpoints para aceptar filtros**:
        *   En [`backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java`](backend/src/main/java/com/cufre/expedientes/service/EstadisticaService.java), modificar los métodos existentes (`countByEstadoSituacion`, `countByFuerzaAsignada`, `countByTipoCaptura`, `rankingDelitos`, `countExpedientesPorMes`, `countDetenidosPorFuerza`) para que acepten parámetros opcionales de filtro (estado, fuerza, tipo de captura, etc.).
        *   Estos filtros se pasarán a las consultas del `ExpedienteRepository.java` para aplicar la lógica de filtrado. Esto podría requerir la creación de nuevas consultas en el repositorio o la modificación de las existentes para incluir cláusulas `WHERE` condicionales.
        *   En [`backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java`](backend/src/main/java/com/cufre/expedientes/controller/EstadisticaController.java), actualizar los endpoints correspondientes para recibir estos parámetros de filtro.

### Fase 2: Mejoras en el Frontend (UI/UX)

1.  **Diseño de la Página**:
    *   **Implementar scroll**: Modificar el CSS de [`src/styles/CentroComando.css`](frontend/src/styles/CentroComando.css) o el componente [`src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx) para permitir el scroll vertical, eliminando la restricción de "full screen" si existe.
    *   **Asegurar gráficos de donas no cortados**: Ajustar los estilos y el tamaño de los contenedores de los gráficos en [`src/components/estadisticas/PanelGraficoDona.tsx`](frontend/src/components/estadisticas/PanelGraficoDona.tsx) y [`src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx) para que se visualicen completamente.

2.  **Visualización de Nuevas Métricas**:
    *   **Integrar nuevas métricas**:
        *   En [`src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx), consumir los nuevos endpoints del backend.
        *   Crear o modificar componentes en [`src/components/estadisticas/`](frontend/src/components/estadisticas/) (posiblemente [`src/components/estadisticas/PanelMetricas.tsx`](frontend/src/components/estadisticas/PanelMetricas.tsx) o un nuevo componente) para mostrar el caso más nuevo, el caso más antiguo y los 3 casos de mayor prioridad.
        *   Actualizar el "mapa de métricas clave" para incluir el total de personas vinculadas, total de delitos y total de fuerzas de seguridad.

3.  **Interactividad de Gráficos de Donas**:
    *   **Lógica de filtrado global**:
        *   Crear un contexto de React (por ejemplo, `src/context/EstadisticasFilterContext.tsx`) para manejar el estado de los filtros seleccionados (estado, fuerza, tipo de captura).
        *   En [`src/components/estadisticas/PanelGraficoDona.tsx`](frontend/src/components/estadisticas/PanelGraficoDona.tsx), al hacer clic en una sección, actualizar el estado del filtro en el contexto.
        *   Todos los componentes de gráficos y rankings (`PanelGraficoBarras.tsx`, `PanelRanking.tsx`, etc.) que necesiten ser filtrados, deberán consumir este contexto y pasar los filtros a sus respectivas llamadas a la API (`src/api/estadisticaService.ts`).
        *   En [`src/api/estadisticaService.ts`](frontend/src/api/estadisticaService.ts), modificar las funciones para que acepten los parámetros de filtro y los pasen a los endpoints del backend.

4.  **Exclusión de Mapas**:
    *   Revisar [`src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx) y cualquier otro componente relacionado con estadísticas para asegurar que no se renderice ningún componente de mapa (como [`src/pages/estadisticas/MapaGeneralPage.tsx`](frontend/src/pages/estadisticas/MapaGeneralPage.tsx) o [`src/components/expedientes/DetencionGeoTab.tsx`](frontend/src/components/expedientes/DetencionGeoTab.tsx) si se usara en este contexto).

### Fase 3: Documentación

1.  **Escribir plan en archivo Markdown**: Este plan detallado se escribirá en un archivo Markdown (`plan-mejora-centro-estadisticas.md`).