# Guía de Manejo de Rutas en CUFRE

## Arquitectura y Configuración de Rutas

La aplicación CUFRE está compuesta por dos componentes principales:

1. **Frontend**: Una aplicación React que se ejecuta en el navegador del usuario.
2. **Backend**: Una API REST desarrollada en Java Spring Boot que proporciona datos y servicios.

Ambos componentes están desplegados como contenedores Docker y se comunican entre sí a través de HTTP.

## Estructura de Rutas

### Backend (API)

- **URL Base**: `http://backend:8080/` (dentro de la red Docker)
- **URL Externa**: `http://************:8080/` (acceso directo, no recomendado)
- **Endpoints Principales**:
  - `/api/expedientes`: Gestión de expedientes
  - `/api/delitos`: Gestión de delitos
  - `/api/auth`: Autenticación y autorización

### Frontend (Interfaz de Usuario)

- **URL**: `http://************/`
- **Rutas de Navegación**:
  - `/`: Dashboard
  - `/expedientes`: Lista de expedientes
  - `/delitos`: Lista de delitos
  - `/login`: Página de inicio de sesión

## Configuración del Proxy NGINX

El frontend utiliza NGINX como servidor web y proxy inverso. Su función principal es servir los archivos estáticos de la aplicación React y actuar como intermediario para las solicitudes a la API.

### Configuración Correcta de NGINX

```nginx
server {
    listen 80;
    server_name _;

    # Disable cache for all responses
    add_header Cache-Control "no-store, no-cache, must-revalidate" always;
    add_header Pragma "no-cache" always;
    expires -1;

    # CORS headers
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;

    # Handle OPTIONS requests
    if ($request_method = "OPTIONS") {
        return 204;
    }

    # Configuración correcta para la API
    location /api/ {
        # Quitamos el prefijo /api antes de pasar al backend para evitar la duplicación
        proxy_pass http://cufre-backend-1:8080/;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin "http://$host";
        proxy_pass_request_headers on;
    }

    # Frontend routes
    location / {
        root   /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }
}
```

### Punto Clave de la Configuración

El aspecto más importante de la configuración de NGINX es el bloque `location /api/`:

```nginx
location /api/ {
    # Quitamos el prefijo /api antes de pasar al backend para evitar la duplicación
    proxy_pass http://cufre-backend-1:8080/;
    ...
}
```

Este bloque elimina el prefijo `/api` al reenviar la solicitud al backend. Por ejemplo, una solicitud a `/api/expedientes` se redirigirá a `http://cufre-backend-1:8080/expedientes`.

## Configuración del Cliente Axios (Frontend)

En la aplicación frontend, las solicitudes a la API se manejan mediante el cliente Axios configurado en el archivo `src/api/axiosClient.ts`:

```typescript
import axios from "axios";

// Usando ruta relativa para que NGINX maneje el enrutamiento correctamente
const API_URL = process.env.REACT_APP_API_URL || "/api";

const axiosClient = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// ...interceptores y configuración adicional...

export default axiosClient;
```

## Problemas Comunes y Soluciones

### 1. URLs Hardcodeadas en el Código Compilado

**Problema**: El código JavaScript compilado contiene URLs absolutas al backend (ej. `http://************:8080/api`) en lugar de rutas relativas (`/api`).

**Síntomas**:
- Las solicitudes se envían directamente al backend en lugar de pasar por el proxy NGINX
- Errores CORS debido a solicitudes cross-origin
- Datos no cargados en las páginas

**Solución**:

1. **Durante el desarrollo**: Asegurarse de que en `axiosClient.ts` se utilicen rutas relativas:

   ```typescript
   const API_URL = process.env.REACT_APP_API_URL || "/api";
   ```

2. **Para aplicaciones ya compiladas**:
   - Modificar directamente el JavaScript compilado para reemplazar URLs absolutas por relativas:
   ```bash
   sed -i "s|http://************:8080/api|/api|g" /usr/share/nginx/html/static/js/main.*.js
   ```

### 2. Duplicación de Prefijos API

**Problema**: Si tanto el proxy NGINX como el cliente Axios añaden el prefijo `/api`, se generan URLs incorrectas como `/api/api/expedientes`.

**Síntomas**:
- Errores 404 (Not Found) en las solicitudes API
- Mensajes de error indicando rutas no encontradas

**Solución**:
1. Configurar Axios para usar `/api` como URL base
2. Configurar NGINX para quitar el prefijo `/api` al reenviar al backend:
   ```nginx
   location /api/ {
       proxy_pass http://backend:8080/;
   }
   ```

### 3. Problemas con CORS

**Problema**: Restricciones de seguridad del navegador que impiden solicitudes cross-origin.

**Síntomas**:
- Errores en la consola del navegador relacionados con CORS
- Solicitudes API bloqueadas

**Solución**:
1. Configurar NGINX para añadir encabezados CORS:
   ```nginx
   add_header Access-Control-Allow-Origin "*" always;
   add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
   add_header Access-Control-Allow-Headers "DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization" always;
   ```

2. Configurar el backend para permitir CORS si se accede directamente

### 4. Respuestas Inconsistentes de la API

**Problema**: El backend puede devolver respuestas en diferentes formatos dependiendo de la situación.

**Ejemplo**: Al obtener expedientes, la respuesta puede ser:
- Un array de expedientes: `[{...}, {...}, ...]`
- O un objeto paginado: `{ expedientes: [...], totalItems: 100, totalPages: 10 }`

**Solución**: Adaptar el frontend para manejar ambos formatos de respuesta:

```typescript
const fetchExpedientes = async () => {
    const response = await expedienteService.getExpedientes(page, rowsPerPage, searchTerm);
    if (Array.isArray(response)) {
        setExpedientes(response);
        setTotalItems(response.length);
        setTotalPages(Math.ceil(response.length / rowsPerPage));
    } else {
        setExpedientes(response.expedientes || []);
        setTotalItems(response.totalItems || 0);
        setTotalPages(response.totalPages || 0);
    }
};
```

## Mejores Prácticas

1. **Siempre usar rutas relativas** en el frontend para las solicitudes API
2. **Configurar variables de entorno** para diferentes entornos (desarrollo, producción)
3. **Verificar la configuración de NGINX** después de implementar cambios
4. **Hacer un seguimiento de los logs del contenedor** para identificar problemas de rutas
5. **Implementar manejo robusto de errores** para fallos de conexión API

## Comandos Útiles

### Ver Logs del Contenedor Frontend
```bash
docker logs cufre-frontend-1
```

### Verificar Configuración de NGINX
```bash
docker exec cufre-frontend-1 cat /etc/nginx/conf.d/default.conf
```

### Reiniciar NGINX Después de Cambios
```bash
docker exec cufre-frontend-1 nginx -s reload
```

### Buscar URLs Hardcodeadas en el Código Compilado
```bash
docker exec cufre-frontend-1 grep -r "http://" /usr/share/nginx/html/static/js/
```

---

Este documento se actualizó por última vez el 15 de mayo de 2025.
