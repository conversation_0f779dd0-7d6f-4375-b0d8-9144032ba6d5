# Plan de mejora – Actividad del Sistema

Este documento describe la implementación de dos funcionalidades solicitadas para la página `ActividadSistemaPage`:

1. Visualización de estadísticas en un modal con gráficos de barras.  
2. Eliminación masiva de registros de actividad según antigüedad (5, 10, 15 o 30 días).

---

## 1. Visualización de estadísticas

Al presionar el botón “Ver estadísticas”, se abrirá un `Dialog` con dos secciones:

| Gráfico | Descripción | Fuente de datos |
|---------|-------------|-----------------|
| **Actividades por módulo** | BarChart que muestra la cantidad de registros agrupados por `modulo`. Top 5. | `/api/actividad-sistema/estadisticas → porModulo` |
| **Actividades por usuario** | BarChart que muestra la cantidad de registros agrupados por `usuario`. Top 5. | `/api/actividad-sistema/estadisticas → porUsuario` |

La estructura de datos esperada del backend es:

```json
{
  "porModulo": [ { "nombre": "Expedientes", "cantidad": 120 }, ... ],
  "porUsuario": [ { "nombre": "<EMAIL>", "cantidad": 65 }, ... ],
  "actividadesRecientes": [ /* lista simple */ ]
}
```

### 1.1 Hook

- `useEstadisticasActividad.ts`  
  - `const { data, loading, error, cargarEstadisticas } = useEstadisticasActividad();`
  - Petición GET al endpoint.

### 1.2 Componente modal

- `EstadisticasActividadModal.tsx`
- Utiliza `Dialog`, `Tabs` y componentes de Recharts:  
  `BarChart`, `Bar`, `XAxis`, `YAxis`, `Tooltip`, `ResponsiveContainer`.
- Se monta **on-demand** (lazy) cuando se abre para no cargar datos innecesariamente.

### 1.3 Integración en la página

- Cambiar handler del `IconButton` de estadísticas para abrir el modal.  
- Mantener `refrescar()` disponible dentro del modal por si se necesita recargar.

---

## 2. Eliminación de registros antiguos

Se añade un botón con icono `DeleteOutline` más un `Menu` desplegable con las cuatro opciones:

- Eliminar últimos 5 días  
- Eliminar últimos 10 días  
- Eliminar últimos 15 días  
- Eliminar últimos 30 días  

Al seleccionar una opción se llama:

```ts
axiosClient.delete('/api/actividad-sistema/eliminar', { params: { dias: X } });
```

Después:

- Mostrar `Snackbar` con el número de registros eliminados.  
- Llamar a `refrescar()` para actualizar la tabla.

---

## 3. Cambios en backend

### 3.1 Repositorio
[`ActividadSistemaRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/ActividadSistemaRepository.java:line)
```java
long deleteByFechaHoraBefore(LocalDateTime fechaLimite);
```

### 3.2 Servicio
[`ActividadSistemaService.java`](backend/src/main/java/com/cufre/expedientes/service/ActividadSistemaService.java:line)
```java
public long eliminarRegistrosAntiguos(int dias) {
    LocalDateTime limite = LocalDateTime.now().minusDays(dias);
    return actividadSistemaRepository.deleteByFechaHoraBefore(limite);
}
```

### 3.3 Controlador
[`ActividadSistemaController.java`](backend/src/main/java/com/cufre/expedientes/controller/ActividadSistemaController.java:line)
```java
@DeleteMapping("/eliminar")
@PreAuthorize("hasAnyRole('ADMINISTRADOR','SUPERUSUARIO')")
public ResponseEntity<Map<String,Object>> eliminarActividades(@RequestParam int dias) {
    long eliminados = actividadSistemaService.eliminarRegistrosAntiguos(dias);
    return ResponseEntity.ok(Map.of("eliminados", eliminados));
}
```

---

## 4. Diagrama de secuencia

```mermaid
sequenceDiagram
    participant UI as ActividadSistemaPage
    participant FE as axiosClient
    participant BE as ActividadSistemaController
    participant SV as ActividadSistemaService
    participant REP as ActividadSistemaRepository

    UI->>FE: GET /actividad-sistema/estadisticas
    FE->>BE: /estadisticas
    BE->>SV: obtenerEstadisticas()
    SV->>REP: Aggregates
    REP-->>SV: datos
    SV-->>BE: Map
    BE-->>FE: 200 OK
    FE-->>UI: Render modal con gráficos

    UI->>FE: DELETE /actividad-sistema/eliminar?dias=X
    FE->>BE: /eliminar
    BE->>SV: eliminarRegistrosAntiguos(X)
    SV->>REP: deleteBefore(fechaLimite)
    REP-->>SV: count
    SV-->>BE: count
    BE-->>FE: 200 OK
    FE-->>UI: Snackbar + refrescar()
```

---

## 5. Tareas detalladas

| # | Tarea | Archivo |
|---|-------|---------|
| 1 | Añadir método `deleteByFechaHoraBefore` | Repository |
| 2 | Implementar lógica de borrado | Service |
| 3 | Crear endpoint DELETE | Controller |
| 4 | Crear hook `useEstadisticasActividad` | Frontend |
| 5 | Crear componente `EstadisticasActividadModal` | Frontend |
| 6 | Conectar botón de estadísticas | ActividadSistemaPage |
| 7 | Añadir botón y menú de eliminación | ActividadSistemaPage |
| 8 | Pruebas manuales & QA | – |

---

## 6. Consideraciones
- Permisos: mismo rol que ya protege consultas de actividad.  
- Paginación y filtros actuales permanecen sin cambios.  
- Opciones de días se pueden parametrizar desde variables de entorno si es necesario.  
- Se reusa la librería **Recharts** ya presente en el proyecto.

---

**Fin del documento**