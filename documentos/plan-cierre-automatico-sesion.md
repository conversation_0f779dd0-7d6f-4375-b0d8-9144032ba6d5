# Plan de Implementación: Sistema de Cierre Automático de Sesión por Inactividad

## Resumen Ejecutivo

Este documento detalla el plan para implementar un sistema de cierre automático de sesión después de 30 minutos de inactividad en el sistema CUFRE, solucionando problemas de seguridad donde las sesiones permanecen abiertas en navegadores y el problema de "NO AUTENTICADO" en los logs de actividad.

## Análisis del Problema Actual

### Problemas Identificados
1. **Tokens JWT con expiración de 10 horas** - Tiempo excesivo para seguridad
2. **Sesiones persistentes en localStorage** - Permanecen activas aunque el usuario cierre el navegador
3. **Problema de "NO AUTENTICADO"** - Ocurre cuando el token expira pero el frontend no lo detecta correctamente
4. **Riesgo de seguridad** - Cualquier persona puede acceder a cuentas abiertas en máquinas compartidas

### Configuración Actual
- JWT expiration: `36000000ms` (10 horas)
- Almacenamiento: localStorage del navegador
- Sin seguimiento de actividad del usuario
- Sin sistema de advertencias

## Solución Propuesta: Sistema Híbrido

### Arquitectura General

```mermaid
graph TD
    A[Usuario Inicia Sesión] --> B[JWT Token 2 horas]
    B --> C[Frontend: Seguimiento de Actividad]
    C --> D{¿Actividad en 30 min?}
    D -->|Sí| E[Resetear Timer]
    D -->|No| F[Mostrar Advertencia 5 min]
    F --> G{¿Usuario Responde?}
    G -->|Sí| E
    G -->|No| H[Cerrar Sesión Automáticamente]
    E --> D
    H --> I[Limpiar localStorage]
    I --> J[Redirigir a Login]
    
    K[Token Expira 2h] --> L{¿Usuario Activo?}
    L -->|Sí| M[Refresh Token Automático]
    L -->|No| H
    M --> B
```

### Componentes Principales

#### 1. Sistema de Tokens JWT Mejorado
- **Duración**: 2 horas (en lugar de 10 horas)
- **Refresh automático**: Cuando el usuario está activo
- **Validación mejorada**: Mejor manejo de errores de expiración

#### 2. Servicio de Gestión de Inactividad
- **Timeout de inactividad**: 30 minutos
- **Advertencia**: 5 minutos antes del cierre
- **Detección de actividad**: Eventos de mouse, teclado, navegación

#### 3. Sistema de Advertencias
- **Modal de advertencia**: Aparece a los 25 minutos de inactividad
- **Countdown visual**: Muestra tiempo restante
- **Opción de extensión**: Botón "Mantener sesión activa"

## Flujo Detallado de Funcionamiento

```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend
    participant B as Backend
    participant LS as LocalStorage
    
    U->>F: Inicia sesión
    F->>B: POST /auth/login
    B->>F: JWT Token (2h) + User Data
    F->>LS: Guardar token y datos
    F->>F: Iniciar timer inactividad (30min)
    
    loop Actividad del Usuario
        U->>F: Interacción (click, tecla, etc.)
        F->>F: Resetear timer inactividad
    end
    
    Note over F: 25 minutos sin actividad
    F->>U: Mostrar advertencia "Sesión expirará en 5 min"
    
    alt Usuario responde
        U->>F: Click "Mantener activa"
        F->>F: Resetear timer
    else Sin respuesta
        Note over F: 30 minutos completos
        F->>LS: Limpiar datos de sesión
        F->>U: Redirigir a login
        F->>B: Log actividad "LOGOUT_INACTIVIDAD"
    end
    
    Note over B: Token cerca de expirar (1h 50min)
    F->>B: POST /auth/refresh
    alt Refresh exitoso
        B->>F: Nuevo JWT Token (2h)
        F->>LS: Actualizar token
    else Refresh falla
        F->>LS: Limpiar datos
        F->>U: Redirigir a login
    end
```

## Implementación Técnica

### Backend - Modificaciones Requeridas

#### 1. Configuración JWT (`application.properties`)
```properties
# Cambiar de 36000000 (10h) a 7200000 (2h)
app.jwt.expiration-ms=7200000
```

#### 2. Nuevo Endpoint de Refresh (`AuthController.java`)
```java
@PostMapping("/refresh")
public ResponseEntity<?> refreshToken(HttpServletRequest request) {
    String token = getJwtFromRequest(request);
    if (tokenProvider.validateToken(token)) {
        String newToken = tokenProvider.refreshToken(token);
        return ResponseEntity.ok(Map.of("token", newToken));
    }
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
}
```

#### 3. Método de Refresh en JwtTokenProvider
```java
public String refreshToken(String token) {
    Claims claims = Jwts.parserBuilder()
        .setSigningKey(getSigningKey())
        .build()
        .parseClaimsJws(token)
        .getBody();
    
    Date now = new Date();
    Date expiryDate = new Date(now.getTime() + jwtExpirationMs);
    
    return Jwts.builder()
        .setClaims(claims)
        .setIssuedAt(now)
        .setExpiration(expiryDate)
        .signWith(getSigningKey(), SignatureAlgorithm.HS512)
        .compact();
}
```

#### 4. Mejora en obtenerUsuarioActual()
```java
private String obtenerUsuarioActual() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.isAuthenticated() 
        && !authentication.getName().equals("anonymousUser")) {
        return authentication.getName();
    }
    return "SISTEMA"; // En lugar de null o "NO AUTENTICADO"
}
```

### Frontend - Nuevos Componentes

#### 1. Servicio de Inactividad (`InactivityService.ts`)
```typescript
export class InactivityService {
  private timeoutId: NodeJS.Timeout | null = null;
  private warningTimeoutId: NodeJS.Timeout | null = null;
  private onLogout: () => void;
  private onWarning: () => void;
  
  private readonly INACTIVITY_TIME = 30 * 60 * 1000; // 30 minutos
  private readonly WARNING_TIME = 25 * 60 * 1000;   // 25 minutos
  
  constructor(onLogout: () => void, onWarning: () => void) {
    this.onLogout = onLogout;
    this.onWarning = onWarning;
    this.setupActivityListeners();
  }
  
  private setupActivityListeners() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, () => this.resetTimer(), true);
    });
  }
  
  public resetTimer() {
    this.clearTimers();
    this.startTimer();
  }
  
  private startTimer() {
    this.warningTimeoutId = setTimeout(() => {
      this.onWarning();
    }, this.WARNING_TIME);
    
    this.timeoutId = setTimeout(() => {
      this.onLogout();
    }, this.INACTIVITY_TIME);
  }
  
  private clearTimers() {
    if (this.timeoutId) clearTimeout(this.timeoutId);
    if (this.warningTimeoutId) clearTimeout(this.warningTimeoutId);
  }
}
```

#### 2. Hook de Inactividad (`useInactivityTimer.ts`)
```typescript
export const useInactivityTimer = () => {
  const { logout } = useAuth();
  const [showWarning, setShowWarning] = useState(false);
  const [countdown, setCountdown] = useState(5 * 60); // 5 minutos
  
  const handleLogout = useCallback(() => {
    logout();
    // Registrar actividad de logout por inactividad
    // actividadService.registrar('LOGOUT_INACTIVIDAD');
  }, [logout]);
  
  const handleWarning = useCallback(() => {
    setShowWarning(true);
    startCountdown();
  }, []);
  
  const extendSession = useCallback(() => {
    setShowWarning(false);
    inactivityService.resetTimer();
  }, []);
  
  useEffect(() => {
    const inactivityService = new InactivityService(handleLogout, handleWarning);
    return () => inactivityService.destroy();
  }, [handleLogout, handleWarning]);
  
  return { showWarning, countdown, extendSession };
};
```

#### 3. Modal de Advertencia (`SessionWarningModal.tsx`)
```typescript
interface SessionWarningModalProps {
  isOpen: boolean;
  countdown: number;
  onExtendSession: () => void;
}

export const SessionWarningModal: React.FC<SessionWarningModalProps> = ({
  isOpen,
  countdown,
  onExtendSession
}) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <Modal isOpen={isOpen} onClose={() => {}}>
      <div className="session-warning">
        <h3>⚠️ Sesión por Expirar</h3>
        <p>Su sesión se cerrará automáticamente en:</p>
        <div className="countdown">{formatTime(countdown)}</div>
        <button onClick={onExtendSession} className="btn-primary">
          Mantener Sesión Activa
        </button>
      </div>
    </Modal>
  );
};
```

#### 4. Interceptor de Axios Mejorado
```typescript
// En axiosClient.ts - Interceptor de respuesta mejorado
axiosClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      const originalRequest = error.config;
      
      if (!originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          const response = await axiosClient.post('/auth/refresh');
          const newToken = response.data.token;
          
          localStorage.setItem('token', newToken);
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          
          return axiosClient(originalRequest);
        } catch (refreshError) {
          // Refresh falló, cerrar sesión
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);
```

### Configuraciones y Constantes

```typescript
// src/config/session.config.ts
export const SESSION_CONFIG = {
  INACTIVITY_TIMEOUT: 30 * 60 * 1000,    // 30 minutos
  WARNING_TIME: 25 * 60 * 1000,          // 25 minutos  
  TOKEN_REFRESH_TIME: 110 * 60 * 1000,   // 1h 50min
  WARNING_COUNTDOWN: 5 * 60 * 1000        // 5 minutos
};

export const ACTIVITY_EVENTS = [
  'mousedown', 'mousemove', 'keypress', 
  'scroll', 'touchstart', 'click'
];
```

## Archivos a Modificar/Crear

### Backend
1. **Modificar**: [`application.properties`](backend/src/main/resources/application.properties)
   - Cambiar `app.jwt.expiration-ms=7200000`

2. **Modificar**: [`AuthController.java`](backend/src/main/java/com/cufre/expedientes/controller/AuthController.java)
   - Agregar endpoint `/refresh`

3. **Modificar**: [`JwtTokenProvider.java`](backend/src/main/java/com/cufre/expedientes/security/JwtTokenProvider.java)
   - Agregar método `refreshToken()`

4. **Modificar**: [`ExpedienteService.java`](backend/src/main/java/com/cufre/expedientes/service/ExpedienteService.java)
   - Mejorar método `obtenerUsuarioActual()`

5. **Modificar**: [`UsuarioService.java`](backend/src/main/java/com/cufre/expedientes/service/UsuarioService.java)
   - Mejorar método `obtenerUsuarioActual()`

### Frontend
1. **Crear**: `src/services/InactivityService.ts`
2. **Crear**: `src/hooks/useInactivityTimer.ts`
3. **Crear**: `src/components/SessionWarningModal.tsx`
4. **Crear**: `src/config/session.config.ts`
5. **Crear**: `src/utils/activityDetector.ts`
6. **Modificar**: [`axiosClient.ts`](frontend/src/api/axiosClient.ts)
7. **Modificar**: [`AuthContext.tsx`](frontend/src/context/AuthContext.tsx)
8. **Modificar**: Componentes principales para integrar el hook

## Fases de Implementación

### Fase 1: Backend - Configuración JWT y Refresh (2-3 horas)
- [ ] Modificar configuración de expiración JWT
- [ ] Implementar endpoint de refresh token
- [ ] Agregar método refreshToken en JwtTokenProvider
- [ ] Mejorar manejo de usuarios autenticados
- [ ] Testing de endpoints

### Fase 2: Frontend - Servicio Básico de Inactividad (3-4 horas)
- [ ] Crear InactivityService
- [ ] Implementar detección de actividad
- [ ] Crear hook useInactivityTimer
- [ ] Integrar con AuthContext básico
- [ ] Testing de funcionalidad básica

### Fase 3: Sistema de Advertencias (2-3 horas)
- [ ] Crear SessionWarningModal
- [ ] Implementar countdown visual
- [ ] Integrar modal con hook de inactividad
- [ ] Styling y UX del modal
- [ ] Testing de advertencias

### Fase 4: Integración Completa (2-3 horas)
- [ ] Mejorar interceptor de Axios
- [ ] Integrar refresh automático
- [ ] Conectar con todas las páginas principales
- [ ] Manejo de errores y edge cases
- [ ] Testing integral

### Fase 5: Mejoras en Logging y Monitoreo (1-2 horas)
- [ ] Registrar eventos de logout por inactividad
- [ ] Mejorar logging de actividad del sistema
- [ ] Monitoreo y métricas
- [ ] Documentación final

## Beneficios Esperados

### Seguridad
- ✅ Cierre automático después de 30 minutos de inactividad
- ✅ Tokens con duración más segura (2 horas vs 10 horas)
- ✅ Limpieza automática de datos de sesión
- ✅ Prevención de acceso no autorizado en máquinas compartidas

### Experiencia de Usuario
- ✅ Advertencia antes del cierre automático
- ✅ Opción de extender sesión activa
- ✅ Refresh automático transparente
- ✅ No interrumpe el trabajo activo del usuario

### Técnico
- ✅ Mejor logging y seguimiento de actividad
- ✅ Resolución del problema "NO AUTENTICADO"
- ✅ Arquitectura más robusta y segura
- ✅ Compatibilidad con funcionalidad existente

## Consideraciones de Testing

### Testing Backend
- [ ] Validar expiración de tokens a las 2 horas
- [ ] Testing de endpoint de refresh
- [ ] Validar manejo de tokens expirados
- [ ] Testing de logging mejorado

### Testing Frontend
- [ ] Validar detección de actividad
- [ ] Testing de timers de inactividad
- [ ] Validar modal de advertencia
- [ ] Testing de refresh automático
- [ ] Testing de limpieza de sesión

### Testing de Integración
- [ ] Flujo completo de inactividad
- [ ] Manejo de errores de red
- [ ] Comportamiento en múltiples pestañas
- [ ] Testing en diferentes navegadores

## Métricas de Éxito

1. **Seguridad**: 0 sesiones abiertas después de 30 minutos de inactividad
2. **Logging**: Eliminación completa de "NO AUTENTICADO" en logs
3. **UX**: Menos del 5% de usuarios reportan interrupciones inesperadas
4. **Performance**: Refresh de tokens sin impacto perceptible
5. **Estabilidad**: 99.9% de uptime del sistema de autenticación

## Notas de Implementación

- Mantener compatibilidad con sistema 2FA existente
- Considerar usuarios con múltiples pestañas abiertas
- Implementar logging detallado para debugging
- Documentar cambios para el equipo de desarrollo
- Preparar rollback plan en caso de problemas

---

**Fecha de creación**: 6 de diciembre de 2025  
**Versión**: 1.0  
**Estado**: Aprobado para implementación