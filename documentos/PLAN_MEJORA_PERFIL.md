# Plan de Acción para la Mejora de la Página de Perfil

El objetivo es mejorar la página de perfil (`/perfil`) con un diseño más moderno, añadir un botón para cambiar la contraseña que abra un modal, y hacer que el campo de correo electrónico sea de solo lectura.

## Fase 1: Análisis y Preparación

1.  **Revisar `frontend/src/pages/perfil/PerfilPage.tsx`**: Entender la estructura actual de la página de perfil, cómo se manejan las pestañas ("Información Personal" y "Seguridad") y cómo se renderiza el `ProfileForm`.
2.  **Revisar `frontend/src/components/perfil/ProfileForm.tsx`**: Identificar el campo de entrada del correo electrónico y cómo se manejan los datos del formulario.
3.  **Identificar estilos existentes**: Buscar archivos CSS o SCSS relevantes en `frontend/src/styles/` que puedan afectar la página de perfil o que puedan ser extendidos para las mejoras visuales.

## Fase 2: Implementación de Mejoras Visuales y Funcionales

1.  **Modificar `frontend/src/components/perfil/ProfileForm.tsx` para el campo de email de solo lectura**:
    *   Localizar el `TextField` o `Input` correspondiente al campo de correo electrónico.
    *   Añadir la propiedad `readOnly` o `disabled` a este campo.

2.  **Añadir botón de cambio de contraseña en `frontend/src/pages/perfil/PerfilPage.tsx`**:
    *   Dentro de la pestaña "Seguridad" en `PerfilPage.tsx`, añadir un botón "Cambiar Contraseña".
    *   Este botón abrirá un nuevo componente de modal.

3.  **Crear nuevo componente `ChangePasswordModal.tsx`**:
    *   Crear un nuevo archivo `frontend/src/components/perfil/ChangePasswordModal.tsx`.
    *   Este componente será un modal de Material-UI que contendrá un formulario para cambiar la contraseña.
    *   El formulario incluirá campos para "Contraseña Actual", "Nueva Contraseña" y "Confirmar Nueva Contraseña".
    *   Se necesitará lógica de validación para las contraseñas (ej. coincidencia de nueva contraseña y confirmación, requisitos de longitud/complejidad).
    *   Se necesitará un estado para manejar la visibilidad del modal.

4.  **Integración del `ChangePasswordModal` en `frontend/src/pages/perfil/PerfilPage.tsx`**:
    *   Importar `ChangePasswordModal` en `PerfilPage.tsx`.
    *   Manejar el estado de apertura/cierre del modal.
    *   Pasar una función `onClose` al modal para cerrarlo.
    *   Definir una función `handleChangePassword` en `PerfilPage.tsx` que se pasará al modal para manejar la lógica de envío del formulario de cambio de contraseña.

5.  **Implementar la lógica de cambio de contraseña en el backend (si no existe)**:
    *   Aunque la tarea se centra en el frontend, es crucial que exista un endpoint en el backend para cambiar la contraseña. Asumo que ya existe o que se creará en una fase posterior por el equipo de backend. Si no existe, se deberá crear un servicio en `frontend/src/api/authService.ts` o similar para interactuar con este endpoint.
    *   La función `handleChangePassword` en `PerfilPage.tsx` (o directamente en `ChangePasswordModal.tsx` si se maneja allí) llamará a este servicio.

6.  **Mejoras visuales en `frontend/src/pages/perfil/PerfilPage.tsx` y `frontend/src/components/perfil/ProfileForm.tsx`**:
    *   Revisar el uso de componentes de Material-UI.
    *   Ajustar `sx` props para espaciado, sombras, bordes, etc., para dar un aspecto más moderno.
    *   Considerar el uso de `Grid` para una mejor disposición de los elementos si no se está usando ya.
    *   Ajustar la tipografía y los colores si es necesario, utilizando el tema de Material-UI.
    *   Podría ser necesario crear o modificar un archivo CSS específico para la página de perfil si las mejoras no se pueden lograr solo con Material-UI (ej. `frontend/src/styles/PerfilPage.css`).

## Fase 3: Pruebas y Verificación

1.  **Pruebas de funcionalidad**:
    *   Verificar que el campo de correo electrónico es de solo lectura.
    *   Verificar que el botón "Cambiar Contraseña" abre y cierra el modal correctamente.
    *   Probar el formulario de cambio de contraseña: validaciones, envío exitoso y manejo de errores.
2.  **Pruebas visuales**:
    *   Asegurarse de que el diseño se ve "más moderno" según lo solicitado.
    *   Verificar la responsividad en diferentes tamaños de pantalla.

## Diagrama de Flujo

```mermaid
graph TD
    A[Usuario accede a /perfil] --> B{PerfilPage.tsx}
    B --> C[Carga datos de perfil]
    C --> D[Renderiza PerfilPage.tsx]
    D --> E[Muestra ProfileForm.tsx]
    E --> F[Campo de Email: Solo Lectura]
    D --> G[Pestaña de Seguridad]
    G --> H[Botón "Cambiar Contraseña"]
    H --> I{Click en Botón}
    I --> J[Abre ChangePasswordModal.tsx]
    J --> K[Formulario de Cambio de Contraseña]
    K --> L{Envío de Formulario}
    L --> M[Llamada a API de Backend]
    M --> N{Respuesta de Backend}
    N -- Éxito --> O[Cierra Modal y Muestra Mensaje de Éxito]
    N -- Error --> P[Muestra Mensaje de Error en Modal]