# Plan de Implementación: Sistema de Perfiles de Usuario

## Resumen Ejecutivo

Este documento detalla el plan para implementar un sistema completo de perfiles de usuario en la aplicación CUFRE, que permitirá a los usuarios gestionar su información personal, avatar y configuraciones de seguridad.

## Requerimientos Funcionales

### Características Principales
- **Teléfono móvil**: Campo opcional con formato argentino (+54 9 xx xxxx-xxxx)
- **Avatar personalizable**: Galería predefinida de avatares (20-30 opciones en categorías)
- **Interfaz dual**: Página dedicada `/perfil` + modal rápido desde menú lateral
- **Permisos granulares**: Usuarios editan su perfil, solo SUPERUSUARIO edita otros perfiles
- **Migración suave**: Teléfono opcional para usuarios existentes

### Campos del Perfil
- Nombre (existente)
- Apellido (existente)
- Email (existente)
- Dependencia (existente)
- Teléfono móvil (nuevo - opcional)
- Avatar (nuevo - galería predefinida)

## Arquitectura del Sistema

```mermaid
graph TB
    subgraph "Frontend"
        A[Sidebar Actualizado] --> B[Modal Perfil Rápido]
        A --> C[Página Perfil Completa]
        B --> D[UserProfile Component]
        C --> D
        D --> E[AvatarSelector Component]
        D --> F[ProfileForm Component]
    end
    
    subgraph "Backend"
        G[UsuarioController] --> H[UsuarioService]
        H --> I[Usuario Entity]
        I --> J[Base de Datos]
        G --> K[AvatarController]
        K --> L[AvatarService]
    end
    
    subgraph "Base de Datos"
        M[Tabla USUARIO] --> N[Campo telefono_movil]
        M --> O[Campo avatar_url]
        P[Tabla AVATAR_PREDEFINIDO] --> Q[Galería de Avatares]
    end
    
    D --> G
    E --> K
```

## Plan de Implementación

### **Fase 1: Backend - Modelo y Base de Datos (2-3 días)**

#### 1.1 Migración de Base de Datos
- **Archivo**: `V1007__add_perfil_fields_to_usuario.sql`
- **Cambios en tabla USUARIO**:
  ```sql
  ALTER TABLE USUARIO ADD COLUMN TELEFONO_MOVIL VARCHAR(20);
  ALTER TABLE USUARIO ADD COLUMN AVATAR_URL VARCHAR(255);
  ```
- **Nueva tabla AVATAR_PREDEFINIDO**:
  ```sql
  CREATE TABLE AVATAR_PREDEFINIDO (
      ID BIGINT PRIMARY KEY AUTO_INCREMENT,
      NOMBRE VARCHAR(100) NOT NULL,
      URL VARCHAR(255) NOT NULL,
      CATEGORIA VARCHAR(50) NOT NULL,
      ACTIVO BOOLEAN DEFAULT TRUE,
      ORDEN_DISPLAY INTEGER DEFAULT 0
  );
  ```

#### 1.2 Actualizar Modelo Usuario
- **Archivo**: `backend/src/main/java/com/cufre/expedientes/model/Usuario.java`
- **Nuevos campos**:
  ```java
  @Column(name = "TELEFONO_MOVIL", length = 20)
  private String telefonoMovil;
  
  @Column(name = "AVATAR_URL", length = 255)
  private String avatarUrl;
  ```

#### 1.3 Actualizar UsuarioDTO
- **Archivo**: `backend/src/main/java/com/cufre/expedientes/dto/UsuarioDTO.java`
- **Agregar campos**: `telefonoMovil`, `avatarUrl`

#### 1.4 Crear Entidad AvatarPredefinido
- **Archivo**: `backend/src/main/java/com/cufre/expedientes/model/AvatarPredefinido.java`
- **Campos**: `id`, `nombre`, `url`, `categoria`, `activo`, `ordenDisplay`

### **Fase 2: Backend - Controladores y Servicios (1-2 días)**

#### 2.1 Actualizar UsuarioController
- **Nuevos endpoints**:
  - `GET /usuarios/me/perfil` - Obtener perfil completo del usuario actual
  - `PUT /usuarios/me/perfil` - Actualizar perfil propio
  - `PUT /usuarios/{id}/perfil` - Actualizar perfil de otro usuario (solo SUPERUSUARIO)

#### 2.2 Crear AvatarController
- **Archivo**: `backend/src/main/java/com/cufre/expedientes/controller/AvatarController.java`
- **Endpoints**:
  - `GET /avatares/predefinidos` - Obtener galería de avatares
  - `POST /avatares/predefinidos` - Agregar avatar (solo SUPERUSUARIO)
  - `PUT /avatares/predefinidos/{id}` - Actualizar avatar (solo SUPERUSUARIO)

#### 2.3 Crear DTOs específicos
- **PerfilUsuarioDTO**: Para operaciones de perfil completo
- **AvatarPredefinidoDTO**: Para gestión de avatares
- **ActualizarPerfilDTO**: Para requests de actualización

#### 2.4 Validaciones
- **TelefonoArgentinoValidator**: Validar formato (+54 9 xx xxxx-xxxx)
- **Validación de permisos**: Según rol del usuario

### **Fase 3: Frontend - Componentes Base (3-4 días)**

#### 3.1 Actualizar Tipos TypeScript
- **Archivo**: `frontend/src/types/usuario.types.ts`
- **Nuevos campos en interface Usuario**:
  ```typescript
  telefonoMovil?: string;
  avatarUrl?: string;
  ```
- **Nuevas interfaces**:
  ```typescript
  interface AvatarPredefinido {
    id: number;
    nombre: string;
    url: string;
    categoria: string;
  }
  
  interface PerfilUsuario extends Usuario {
    // Campos adicionales específicos del perfil
  }
  ```

#### 3.2 Componente AvatarSelector
- **Archivo**: `frontend/src/components/perfil/AvatarSelector.tsx`
- **Funcionalidades**:
  - Grid responsive de avatares por categorías
  - Preview del avatar seleccionado
  - Integración con UserAvatar existente
  - Estados de loading y error

#### 3.3 Componente ProfileForm
- **Archivo**: `frontend/src/components/perfil/ProfileForm.tsx`
- **Campos**:
  - Nombre, apellido (solo lectura para usuarios normales)
  - Email (solo lectura)
  - Dependencia
  - Teléfono móvil con validación
- **Validaciones en tiempo real**
- **Estados de loading y feedback**

#### 3.4 Actualizar UserAvatar
- **Modificar**: `frontend/src/components/usuarios/UserAvatar.tsx`
- **Soporte para avatares personalizados**:
  ```typescript
  const avatarSrc = usuario.avatarUrl || null;
  // Fallback a iniciales si no hay avatar personalizado
  ```

### **Fase 4: Frontend - Páginas y Navegación (2-3 días)**

#### 4.1 Página PerfilPage
- **Archivo**: `frontend/src/pages/perfil/PerfilPage.tsx`
- **Estructura con Tabs**:
  - **Tab "Información Personal"**: ProfileForm + AvatarSelector
  - **Tab "Seguridad"**: Botón cambiar contraseña, configuraciones
- **Breadcrumbs y navegación**
- **Responsive design**

#### 4.2 Modal PerfilModal
- **Archivo**: `frontend/src/components/perfil/PerfilModal.tsx`
- **Versión compacta para acceso rápido**
- **Campos principales editables**
- **Botón "Ver Perfil Completo" → navega a `/perfil`

#### 4.3 Actualizar Sidebar
- **Modificar**: `frontend/src/components/layout/Sidebar.tsx`
- **Reemplazar sección usuario (líneas 114-126)**:
  ```tsx
  <Box sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
    <UserAvatar usuario={user} size="large" />
    <Box sx={{ ml: 2, flex: 1 }}>
      <Typography variant="subtitle1" fontWeight="bold">
        {user.nombre} {user.apellido}
      </Typography>
      <Typography variant="caption" color="text.secondary">
        {user.dependencia}
      </Typography>
    </Box>
  </Box>
  <Box sx={{ px: 2, pb: 2 }}>
    <Button fullWidth variant="outlined" onClick={openPerfilModal}>
      Perfil
    </Button>
    <Button fullWidth variant="outlined" color="error" onClick={logout}>
      Cerrar Sesión
    </Button>
  </Box>
  ```

#### 4.4 Routing
- **Agregar ruta**: `/perfil` → `PerfilPage`
- **Protección de ruta**: Requiere autenticación
- **Redirección**: Desde `/cambiar-contrasena` integrar en perfil

### **Fase 5: Servicios y API (1-2 días)**

#### 5.1 Servicios Frontend
- **perfilService.ts**:
  ```typescript
  export const perfilService = {
    obtenerPerfil: () => api.get('/usuarios/me/perfil'),
    actualizarPerfil: (datos) => api.put('/usuarios/me/perfil', datos),
    actualizarPerfilOtroUsuario: (id, datos) => api.put(`/usuarios/${id}/perfil`, datos)
  };
  ```

- **avatarService.ts**:
  ```typescript
  export const avatarService = {
    obtenerAvataresPredefinidos: () => api.get('/avatares/predefinidos'),
    // Funciones para SUPERUSUARIO
  };
  ```

#### 5.2 Integración con AuthContext
- **Actualizar**: `frontend/src/context/AuthContext.tsx`
- **Agregar función**: `actualizarPerfilUsuario`
- **Refresh automático** después de actualizar perfil
- **Sincronización** con datos del sidebar

#### 5.3 Hooks personalizados
- **usePerfilUsuario**: Para gestión de estado del perfil
- **useAvatares**: Para gestión de avatares predefinidos

### **Fase 6: UX/UI y Testing (1-2 días)**

#### 6.1 Galería de Avatares Predefinidos
- **Categorías sugeridas**:
  - **Profesional** (8 avatares): Siluetas formales, colores corporativos
  - **Casual** (8 avatares): Estilos más relajados, colores variados
  - **Iconos** (8 avatares): Símbolos, badges, insignias
  - **Diversos** (6 avatares): Representación inclusiva

#### 6.2 Validaciones y Feedback
- **Mensajes de éxito/error** con Snackbar
- **Loading states** en formularios
- **Validación en tiempo real** para teléfono
- **Confirmación** antes de cambios importantes

#### 6.3 Responsive Design
- **Mobile-first approach**
- **Breakpoints**: xs, sm, md, lg, xl
- **Touch-friendly** para selección de avatares
- **Accesibilidad**: ARIA labels, keyboard navigation

#### 6.4 Testing
- **Unit tests** para componentes principales
- **Integration tests** para flujos de perfil
- **E2E tests** para casos de uso críticos

## Estructura de Archivos

### Backend
```
backend/src/main/java/com/cufre/expedientes/
├── model/
│   └── AvatarPredefinido.java
├── dto/
│   ├── PerfilUsuarioDTO.java
│   ├── AvatarPredefinidoDTO.java
│   └── ActualizarPerfilDTO.java
├── controller/
│   └── AvatarController.java
├── service/
│   └── AvatarService.java
├── repository/
│   └── AvatarPredefinidoRepository.java
└── validator/
    └── TelefonoArgentinoValidator.java

backend/src/main/resources/
├── db/migration/
│   └── V1007__add_perfil_fields_to_usuario.sql
└── avatares/
    ├── profesional/
    ├── casual/
    ├── iconos/
    └── diversos/
```

### Frontend
```
frontend/src/
├── components/perfil/
│   ├── PerfilModal.tsx
│   ├── ProfileForm.tsx
│   ├── AvatarSelector.tsx
│   └── SecurityTab.tsx
├── pages/perfil/
│   └── PerfilPage.tsx
├── services/
│   ├── perfilService.ts
│   └── avatarService.ts
├── hooks/
│   ├── usePerfilUsuario.ts
│   └── useAvatares.ts
└── types/
    └── perfil.types.ts
```

## Cronograma de Desarrollo

| Fase | Descripción | Duración | Dependencias |
|------|-------------|----------|--------------|
| 1 | Backend - Modelo y BD | 2-3 días | - |
| 2 | Backend - Controladores | 1-2 días | Fase 1 |
| 3 | Frontend - Componentes | 3-4 días | Fase 2 |
| 4 | Frontend - Páginas | 2-3 días | Fase 3 |
| 5 | Servicios y API | 1-2 días | Fases 3-4 |
| 6 | UX/UI y Testing | 1-2 días | Todas las anteriores |

**Total estimado**: 8-12 días de desarrollo

## Consideraciones Técnicas

### Seguridad
- **Validación de permisos** en backend y frontend
- **Sanitización** de inputs de usuario
- **Rate limiting** para endpoints de perfil
- **Audit trail** para cambios de perfil

### Performance
- **Lazy loading** de avatares predefinidos
- **Cache** de datos de perfil en AuthContext
- **Optimización** de imágenes de avatares
- **Debounce** en validaciones en tiempo real

### Accesibilidad
- **ARIA labels** en todos los componentes
- **Keyboard navigation** para selector de avatares
- **Screen reader support** para cambios de estado
- **Color contrast** adecuado en todos los elementos

### Responsive Design
- **Mobile-first** approach
- **Touch targets** de mínimo 44px
- **Viewport adaptable** para diferentes dispositivos
- **Progressive enhancement**

## Migración de Datos

### Script de Migración
```sql
-- V1007__add_perfil_fields_to_usuario.sql
ALTER TABLE USUARIO ADD COLUMN TELEFONO_MOVIL VARCHAR(20);
ALTER TABLE USUARIO ADD COLUMN AVATAR_URL VARCHAR(255);

-- Crear tabla de avatares predefinidos
CREATE TABLE AVATAR_PREDEFINIDO (
    ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    NOMBRE VARCHAR(100) NOT NULL,
    URL VARCHAR(255) NOT NULL,
    CATEGORIA VARCHAR(50) NOT NULL,
    ACTIVO BOOLEAN DEFAULT TRUE,
    ORDEN_DISPLAY INTEGER DEFAULT 0
);

-- Insertar avatares predefinidos iniciales
INSERT INTO AVATAR_PREDEFINIDO (NOMBRE, URL, CATEGORIA, ORDEN_DISPLAY) VALUES
('Avatar Profesional 1', '/avatares/profesional/prof-1.svg', 'profesional', 1),
('Avatar Profesional 2', '/avatares/profesional/prof-2.svg', 'profesional', 2),
-- ... más avatares
;
```

### Estrategia de Rollback
- **Backup** de tabla USUARIO antes de migración
- **Script de rollback** para eliminar columnas agregadas
- **Validación** de integridad de datos post-migración

## Criterios de Aceptación

### Funcionales
- ✅ Usuario puede ver y editar su perfil completo
- ✅ Usuario puede seleccionar avatar de galería predefinida
- ✅ Validación de teléfono con formato argentino
- ✅ SUPERUSUARIO puede editar perfiles de otros usuarios
- ✅ Modal rápido accesible desde sidebar
- ✅ Página dedicada de perfil con tabs organizados

### No Funcionales
- ✅ Tiempo de carga < 2 segundos
- ✅ Responsive en dispositivos móviles
- ✅ Accesible según estándares WCAG 2.1
- ✅ Compatible con navegadores modernos
- ✅ Manejo de errores robusto

## Riesgos y Mitigaciones

| Riesgo | Probabilidad | Impacto | Mitigación |
|--------|--------------|---------|------------|
| Conflictos de migración BD | Media | Alto | Testing exhaustivo en ambiente dev |
| Performance en carga de avatares | Baja | Medio | Lazy loading y optimización |
| Problemas de UX en móviles | Media | Medio | Testing en dispositivos reales |
| Incompatibilidad con roles existentes | Baja | Alto | Validación con usuarios finales |

## Conclusión

Este plan proporciona una hoja de ruta completa para implementar un sistema robusto de perfiles de usuario que mejorará significativamente la experiencia del usuario en la aplicación CUFRE. La implementación por fases permite un desarrollo incremental y testing continuo, minimizando riesgos y asegurando la calidad del producto final.

La arquitectura propuesta es escalable y mantenible, siguiendo las mejores prácticas de desarrollo y los patrones ya establecidos en la aplicación existente.