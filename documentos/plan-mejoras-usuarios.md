# Plan Detallado: Mejoras de la Página de Gestión de Usuarios

## 📋 **Resumen de Cambios Requeridos**

### 1. **Eliminar Funcionalidades Masivas**
- Remover componente `BulkActions`
- Eliminar selección múltiple (checkboxes)
- Eliminar estados y funciones relacionadas con acciones masivas

### 2. **Implementar Cambio de Rol Inline**
- Convertir el chip de rol en un elemento interactivo para SUPERUSUARIOS
- Agregar desplegable con confirmación y validación de contraseña
- Implementar validaciones de jerarquía

### 3. **Agregar Funcionalidad "Forzar Cierre de Sesión"**
- Nuevo botón en acciones para SUPERUSUARIOS
- Endpoint para invalidar token en backend
- Confirmación antes de ejecutar (sin validación de contraseña)

## 🏗️ **Arquitectura de Componentes**

```mermaid
graph TD
    A[UsuariosPage] --> B[UserFilters]
    A --> C[UserStats]
    A --> D[Tabla de Usuarios]
    A --> E[UserAvatar]
    
    D --> F[RoleChip - Nuevo Componente]
    D --> G[UserActions - Modificado]
    
    F --> H[RoleChangeDialog con Validación de Contraseña]
    G --> I[ForceLogoutDialog]
    
    style F fill:#e1f5fe
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fce4ec
```

## 🔧 **Cambios Técnicos Detallados**

### **1. Modificaciones en UsuariosPage.tsx**

#### **Estados a Eliminar:**
```typescript
// Eliminar estos estados relacionados con selección múltiple
const [selectedUsers, setSelectedUsers] = useState<Usuario[]>([]);
// Todas las funciones relacionadas con selección múltiple
// handleSelectAll, handleSelectUser, isUserSelected, etc.
```

#### **Nuevos Estados a Agregar:**
```typescript
const [roleChangeDialog, setRoleChangeDialog] = useState({
  open: false,
  user: null as Usuario | null,
  newRole: Rol.USUARIOCONSULTA,
  password: '',
  loading: false
});

const [forceLogoutDialog, setForceLogoutDialog] = useState({
  open: false,
  user: null as Usuario | null,
  loading: false
});
```

### **2. Nuevo Componente: RoleChip**

```mermaid
flowchart LR
    A[Usuario hace clic en chip] --> B{¿Es SUPERUSUARIO?}
    B -->|Sí| C{¿Usuario target es SUPERUSUARIO?}
    B -->|No| D[Solo mostrar rol]
    C -->|Sí| E[No permitir cambio]
    C -->|No| F[Mostrar desplegable]
    F --> G[Usuario selecciona nuevo rol]
    G --> H[Mostrar dialog con validación de contraseña]
    H --> I[Usuario ingresa contraseña]
    I --> J[Validar contraseña en backend]
    J --> K[Confirmar cambio de rol]
    K --> L[Actualizar tabla]
```

**Propiedades del componente:**
```typescript
interface RoleChipProps {
  usuario: Usuario;
  currentUser: Usuario;
  onRoleChange: (userId: number, newRole: Rol, password: string) => Promise<void>;
  disabled?: boolean;
}
```

### **3. Nuevo Componente: RoleChangeDialog**

**Características:**
- Campo de selección de nuevo rol
- Campo de contraseña del SUPERUSUARIO (tipo password)
- Validación en tiempo real
- Botones de cancelar y confirmar
- Loading state durante la operación
- Mensajes de error específicos

```typescript
interface RoleChangeDialogProps {
  open: boolean;
  user: Usuario | null;
  currentRole: Rol;
  newRole: Rol;
  password: string;
  loading: boolean;
  onClose: () => void;
  onRoleChange: (newRole: Rol) => void;
  onPasswordChange: (password: string) => void;
  onConfirm: () => Promise<void>;
}
```

### **4. Modificaciones en UserActions**

#### **Nuevas Acciones:**
- **Forzar Cierre de Sesión**: Solo visible para SUPERUSUARIOS
  - Icono: `ExitToApp` o `PowerOff`
  - Color: warning/orange
  - Tooltip: "Forzar cierre de sesión"
- **Editar**: Mantener funcionalidad existente
- **Eliminar**: Mantener funcionalidad existente
- **Reset Password/2FA**: Mantener funcionalidad existente

### **5. Nuevos Servicios de API**

#### **En usuarioService.ts:**
```typescript
// Nuevo método para cambiar rol con validación de contraseña
updateRoleWithPassword: async (id: number, newRole: Rol, password: string): Promise<Usuario> => {
  const response = await axiosClient.patch(apiRoutes.usuarios.updateRole(id), { 
    rol: newRole,
    adminPassword: password 
  });
  return response.data as Usuario;
},

// Nuevo método para forzar cierre de sesión
forceLogout: async (id: number): Promise<void> => {
  await axiosClient.post(apiRoutes.usuarios.forceLogout(id));
}
```

#### **En apiRoutes.ts (agregar):**
```typescript
usuarios: {
  // ... rutas existentes
  updateRole: (id: number) => `/usuarios/${id}/role`,
  forceLogout: (id: number) => `/usuarios/${id}/force-logout`
}
```

## 🔐 **Lógica de Permisos y Validaciones**

### **Matriz de Permisos:**

| Acción | SUPERUSUARIO | ADMINISTRADOR | USUARIOCARGA | USUARIOCONSULTA |
|--------|--------------|---------------|--------------|-----------------|
| Cambiar rol de SUPERUSUARIO | ❌ | ❌ | ❌ | ❌ |
| Cambiar rol de ADMINISTRADOR | ✅* | ❌ | ❌ | ❌ |
| Cambiar rol de USUARIOCARGA | ✅* | ❌ | ❌ | ❌ |
| Cambiar rol de USUARIOCONSULTA | ✅* | ❌ | ❌ | ❌ |
| Forzar cierre de sesión | ✅ | ❌ | ❌ | ❌ |
| Eliminar usuario | ✅ | ❌ | ❌ | ❌ |

*\* Requiere validación de contraseña*

### **Validaciones de Seguridad:**
1. **Cambio de Rol:**
   - Verificar que el usuario actual sea SUPERUSUARIO
   - Verificar que el usuario objetivo no sea SUPERUSUARIO
   - Validar contraseña del SUPERUSUARIO en backend
   - No permitir cambio del propio rol

2. **Forzar Cierre de Sesión:**
   - Solo SUPERUSUARIOS pueden ejecutar esta acción
   - No permitir forzar cierre de la propia sesión
   - Confirmación obligatoria antes de ejecutar

## 📱 **Flujos de Usuario Actualizados**

### **Cambio de Rol con Validación:**
```mermaid
sequenceDiagram
    participant U as Usuario SUPERUSUARIO
    participant RC as RoleChip
    participant D as RoleChangeDialog
    participant API as Backend API
    participant T as Tabla

    U->>RC: Click en chip de rol
    RC->>RC: Verificar permisos
    RC->>D: Abrir dialog
    U->>D: Seleccionar nuevo rol
    U->>D: Ingresar contraseña
    U->>D: Confirmar cambio
    D->>API: PATCH /usuarios/{id}/role + password
    API->>API: Validar contraseña del admin
    API->>API: Actualizar rol del usuario
    API-->>D: Respuesta exitosa
    D->>T: Actualizar tabla
    D->>D: Cerrar dialog
    
    Note over API: Si la contraseña es incorrecta,<br/>retorna error 401
```

### **Forzar Cierre de Sesión:**
```mermaid
sequenceDiagram
    participant U as Usuario SUPERUSUARIO
    participant A as UserActions
    participant D as ForceLogoutDialog
    participant API as Backend API

    U->>A: Click "Forzar cierre de sesión"
    A->>D: Abrir dialog de confirmación
    U->>D: Confirmar acción
    D->>API: POST /usuarios/{id}/force-logout
    API->>API: Invalidar token del usuario
    API->>API: Marcar sesión como inválida
    API-->>D: Respuesta exitosa
    D->>D: Mostrar mensaje de éxito
    D->>D: Cerrar dialog
    
    Note over API: El usuario objetivo será<br/>desconectado en su próxima petición
```

## 🗂️ **Archivos a Modificar/Crear**

### **Modificar:**
1. `frontend/src/pages/usuarios/UsuariosPage.tsx`
   - Eliminar lógica de selección múltiple
   - Agregar nuevos estados y handlers
   - Remover import y uso de BulkActions

2. `frontend/src/api/usuarioService.ts`
   - Agregar `updateRoleWithPassword`
   - Agregar `forceLogout`

3. `frontend/src/api/apiRoutes.ts`
   - Agregar rutas para nuevos endpoints

### **Crear:**
1. `frontend/src/components/usuarios/RoleChip.tsx`
   - Componente interactivo para cambio de rol
   - Lógica de permisos integrada

2. `frontend/src/components/usuarios/RoleChangeDialog.tsx`
   - Dialog con validación de contraseña
   - Selección de nuevo rol
   - Manejo de errores

3. `frontend/src/components/usuarios/ForceLogoutDialog.tsx`
   - Dialog de confirmación simple
   - Sin validación de contraseña

### **Eliminar:**
1. `frontend/src/components/usuarios/BulkActions.tsx`
2. Todas las referencias a BulkActions en UsuariosPage

## 🎨 **Consideraciones de UX/UI**

### **RoleChip Interactivo:**
- **Estado Normal**: Chip con color según rol actual
- **Estado Hover** (para SUPERUSUARIOS): Cursor pointer, ligero cambio de color
- **Estado Disabled**: Para usuarios sin permisos o SUPERUSUARIOS objetivo
- **Tooltip**: Explicar funcionalidad o restricciones

### **RoleChangeDialog:**
- **Título**: "Cambiar Rol de Usuario"
- **Información del Usuario**: Nombre completo y rol actual
- **Selector de Rol**: Dropdown con roles disponibles
- **Campo de Contraseña**: 
  - Label: "Confirma tu contraseña para continuar"
  - Tipo: password
  - Validación en tiempo real
- **Botones**: 
  - "Cancelar" (secundario)
  - "Cambiar Rol" (primario, disabled hasta completar campos)

### **ForceLogoutDialog:**
- **Título**: "Forzar Cierre de Sesión"
- **Mensaje**: "¿Estás seguro de que quieres cerrar la sesión de [Nombre Usuario]? Esta acción invalidará su token y deberá iniciar sesión nuevamente."
- **Botones**:
  - "Cancelar" (secundario)
  - "Forzar Cierre" (warning/danger)

### **Feedback Visual:**
- **Loading States**: Spinners en botones durante operaciones
- **Mensajes de Éxito**: Snackbar verde con mensaje específico
- **Mensajes de Error**: Snackbar rojo con detalles del error
- **Actualización de Tabla**: Inmediata tras operaciones exitosas

### **Iconografía:**
- **Cambio de Rol**: `AdminPanelSettings` o `SwapHoriz`
- **Forzar Cierre**: `ExitToApp` o `PowerSettingsNew`
- **Validación**: `Security` o `Lock`

## 🔍 **Casos de Prueba**

### **Cambio de Rol:**
1. ✅ SUPERUSUARIO cambia rol de ADMINISTRADOR con contraseña correcta
2. ❌ SUPERUSUARIO intenta cambiar rol con contraseña incorrecta
3. ❌ ADMINISTRADOR intenta cambiar rol (sin permisos)
4. ❌ SUPERUSUARIO intenta cambiar rol de otro SUPERUSUARIO
5. ❌ Usuario intenta cambiar su propio rol

### **Forzar Cierre de Sesión:**
1. ✅ SUPERUSUARIO fuerza cierre de sesión de usuario normal
2. ❌ ADMINISTRADOR intenta forzar cierre (sin permisos)
3. ❌ SUPERUSUARIO intenta forzar su propio cierre
4. ✅ Verificar que el token se invalida correctamente

### **Interfaz:**
1. ✅ RoleChip es clickeable solo para SUPERUSUARIOS
2. ✅ Tooltips informativos para usuarios sin permisos
3. ✅ Loading states funcionan correctamente
4. ✅ Mensajes de error son claros y específicos

## 📋 **Checklist de Implementación**

### **Fase 1: Limpieza**
- [ ] Eliminar componente BulkActions
- [ ] Remover estados de selección múltiple
- [ ] Limpiar imports no utilizados
- [ ] Remover checkboxes de la tabla

### **Fase 2: Nuevos Componentes**
- [ ] Crear RoleChip con lógica de permisos
- [ ] Crear RoleChangeDialog con validación
- [ ] Crear ForceLogoutDialog
- [ ] Integrar componentes en UsuariosPage

### **Fase 3: Servicios API**
- [ ] Agregar updateRoleWithPassword
- [ ] Agregar forceLogout
- [ ] Actualizar apiRoutes
- [ ] Manejar errores específicos

### **Fase 4: Testing**
- [ ] Probar todos los casos de permisos
- [ ] Verificar validación de contraseña
- [ ] Confirmar invalidación de tokens
- [ ] Testing de UI/UX

### **Fase 5: Refinamiento**
- [ ] Ajustar estilos y animaciones
- [ ] Optimizar mensajes de usuario
- [ ] Documentar cambios
- [ ] Code review final

---

## 🎯 **Objetivos Cumplidos**

Al completar este plan, habremos logrado:

1. ✅ **Eliminación de acciones masivas** - Gestión individual de usuarios
2. ✅ **Cambio de rol seguro** - Con validación de contraseña del administrador
3. ✅ **Forzar cierre de sesión** - Funcionalidad administrativa para SUPERUSUARIOS
4. ✅ **Jerarquía de permisos clara** - Solo SUPERUSUARIOS pueden modificar roles
5. ✅ **Interfaz intuitiva** - Componentes interactivos con feedback claro
6. ✅ **Seguridad mejorada** - Validaciones múltiples y confirmaciones

Este plan asegura una gestión de usuarios más segura, controlada y fácil de usar, manteniendo la integridad del sistema y proporcionando las herramientas necesarias para la administración efectiva.