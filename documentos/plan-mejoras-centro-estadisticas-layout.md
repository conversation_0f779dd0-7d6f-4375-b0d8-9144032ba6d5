# Plan de Mejoras - Centro de Estadísticas CUFRE
## Layout y Funcionalidad

**Fecha:** 13 de enero de 2025  
**Versión:** 1.0  
**Estado:** Aprobado para implementación

---

## Resumen Ejecutivo

Este documento detalla el plan para implementar mejoras en el Centro de Estadísticas CUFRE, específicamente:

1. **Doblar la altura del panel "CASOS DESTACADOS"** para evitar corte de datos
2. **Agregar nuevo gráfico de tortas "Expedientes por Fuerza"** con funcionalidad interactiva
3. **Reorganizar el layout** de grid 2x2 a grid 2x3 para mejor distribución

---

## Análisis del Estado Actual

### Estructura Actual
- **Layout:** Grid CSS 2x2 (`grid-template-columns: 1fr 1fr`)
- **Paneles existentes:**
  - Casos Destacados (altura limitada, datos se cortan)
  - Expedientes por Estado (gráfico dona)
  - Detenidos por Fuerza (gráfico barras)
  - Evolución Temporal (línea temporal)

### Problemas Identificados
- Panel "Casos Destacados" tiene altura insuficiente
- Falta visualización específica de "Expedientes por Fuerza"
- Layout 2x2 limita la expansión de funcionalidades

### Recursos Disponibles
- ✅ Endpoint `/expedientes-por-fuerza` ya existe en backend
- ✅ Colores definidos en `COLORS_FUERZA`
- ✅ Componente `PanelGraficoDona` reutilizable
- ✅ Sistema de filtros implementado

---

## Diseño del Nuevo Layout

### Grid 2x3 Propuesto

```
┌─────────────────┬─────────────────┬─────────────────┐
│                 │  Expedientes    │  Expedientes    │
│     Casos       │  por Estado     │  por Fuerza     │
│   Destacados    │   (Dona)        │   (Dona)        │
│  (Doble altura) ├─────────────────┼─────────────────┤
│                 │  Detenidos      │   Evolución     │
│                 │  por Fuerza     │   Temporal      │
│                 │   (Barras)      │   (Línea)       │
└─────────────────┴─────────────────┴─────────────────┘
```

### Especificaciones CSS Grid
```css
grid-template-columns: 2fr 1fr 1fr;
grid-template-areas: 
  "casos-destacados expedientes-estado expedientes-fuerza"
  "casos-destacados detenidos-fuerza evolucion-temporal";
```

---

## Plan de Implementación

### Fase 1: Modificación del Layout CSS

**Archivo:** [`frontend/src/styles/CentroComando.css`](frontend/src/styles/CentroComando.css)

#### Cambios en `.centro-comando-grid` (línea ~127)
```css
.centro-comando-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr; /* Casos destacados más ancho */
  grid-template-rows: minmax(400px, 1fr) minmax(400px, 1fr);
  grid-template-areas: 
    "casos-destacados expedientes-estado expedientes-fuerza"
    "casos-destacados detenidos-fuerza evolucion-temporal";
  gap: 2rem;
  /* ... resto de propiedades existentes ... */
}
```

#### Ajustes Responsive
```css
/* Pantallas medianas (≤1366px) */
@media (max-width: 1366px) {
  .centro-comando-grid {
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 1.5rem;
  }
}

/* Pantallas pequeñas (≤768px) */
@media (max-width: 768px) {
  .centro-comando-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(5, minmax(300px, auto));
    grid-template-areas: 
      "casos-destacados"
      "expedientes-estado" 
      "expedientes-fuerza"
      "detenidos-fuerza"
      "evolucion-temporal";
  }
}
```

### Fase 2: Actualización del Componente Principal

**Archivo:** [`frontend/src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx)

#### Agregar Estado para Expedientes por Fuerza (línea ~71)
```typescript
const [expedientesPorFuerza, setExpedientesPorFuerza] = useState<any[]>([]);
```

#### Modificar `fetchAllData` (línea ~92-110)
```typescript
const [
  estadoData,
  fuerzaData,
  expedientesPorFuerzaData, // NUEVO
  evolucionData,
  delitosData
] = await Promise.all([
  // ... llamadas existentes ...
  filters.estado || filters.tipoCaptura
    ? estadisticaService.getExpedientesPorFuerzaFiltrado(filters)
    : estadisticaService.getExpedientesPorFuerza(), // NUEVO
  // ... resto de llamadas ...
]);
```

#### Procesar Datos de Expedientes por Fuerza
```typescript
// Procesar expedientes por fuerza (similar a línea 160-167)
const expedientesPorFuerzaProcessed = Array.isArray(expedientesPorFuerzaData) 
  ? expedientesPorFuerzaData.map((item: any) => {
      const fuerzaKey = (item.name?.toUpperCase() || item.fuerza?.toUpperCase() || 'SIN DATO') as keyof typeof COLORS_FUERZA;
      return {
        name: item.name || item.fuerza || 'SIN DATO',
        value: item.value || item.cantidad || 0,
        color: COLORS_FUERZA[fuerzaKey] || '#757575'
      };
    }) : [];

setExpedientesPorFuerza(expedientesPorFuerzaProcessed);
```

#### Reorganizar JSX con Grid Areas (línea ~437-498)
```typescript
<Box className="centro-comando-grid">
  {/* Panel de casos destacados - ocupa 2 filas */}
  <Box sx={{ gridArea: 'casos-destacados' }}>
    <PanelCasosDestacados
      casoMasNuevo={casosDestacados.casoMasNuevo}
      casoMasAntiguo={casosDestacados.casoMasAntiguo}
      top3Prioridad={casosDestacados.top3Prioridad}
      isLoading={loading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onRefresh={refreshNow}
    />
  </Box>

  {/* Panel de expedientes por estado */}
  <Box sx={{ gridArea: 'expedientes-estado' }}>
    <PanelGraficoDona
      title="Expedientes por Estado"
      data={expedientesPorEstado}
      isLoading={loading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={() => handlePanelClick('/estadisticas')}
      onRefresh={refreshNow}
      onSegmentClick={(data) => {
        if (filters.estado === data.name) {
          setFilter('estado', undefined);
        } else {
          setFilter('estado', data.name);
        }
      }}
    />
  </Box>

  {/* NUEVO: Panel de expedientes por fuerza */}
  <Box sx={{ gridArea: 'expedientes-fuerza' }}>
    <PanelGraficoDona
      title="Expedientes por Fuerza"
      data={expedientesPorFuerza}
      isLoading={loading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={() => handlePanelClick('/estadisticas')}
      onRefresh={refreshNow}
      onSegmentClick={(data) => {
        if (filters.fuerza === data.name) {
          setFilter('fuerza', undefined);
        } else {
          setFilter('fuerza', data.name);
        }
      }}
    />
  </Box>

  {/* Panel de detenidos por fuerza */}
  <Box sx={{ gridArea: 'detenidos-fuerza' }}>
    <PanelGraficoBarras
      title="Detenidos por Fuerza"
      data={detenidosPorFuerza}
      isLoading={loading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={() => handlePanelClick('/estadisticas/detenidos-por-fuerza')}
      onRefresh={refreshNow}
      layout="horizontal"
      onBarClick={(data) => {
        if (filters.fuerza === data.name) {
          setFilter('fuerza', undefined);
        } else {
          setFilter('fuerza', data.name);
        }
      }}
    />
  </Box>

  {/* Panel de evolución temporal */}
  <Box sx={{ gridArea: 'evolucion-temporal' }}>
    <PanelLineaTemporal
      title="Evolución Temporal"
      data={evolucionTemporal}
      isLoading={loading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={() => handlePanelClick('/estadisticas/evolucion-expedientes')}
      onRefresh={refreshNow}
    />
  </Box>
</Box>
```

### Fase 3: Verificar Servicio API

**Archivo:** [`frontend/src/api/estadisticaService.ts`](frontend/src/api/estadisticaService.ts)

Verificar que existan los métodos (agregar si no existen):
```typescript
getExpedientesPorFuerza: async () => {
  const response = await apiClient.get('/estadisticas/expedientes-por-fuerza');
  return response.data;
},

getExpedientesPorFuerzaFiltrado: async (filters: any) => {
  const params = new URLSearchParams();
  if (filters.estado) params.append('estado', filters.estado);
  if (filters.tipoCaptura) params.append('tipoCaptura', filters.tipoCaptura);
  
  const response = await apiClient.get(`/estadisticas/expedientes-por-fuerza-filtrado?${params}`);
  return response.data;
},
```

---

## Funcionalidades del Nuevo Panel

### Gráfico "Expedientes por Fuerza"
- **Tipo:** Gráfico de dona (reutiliza `PanelGraficoDona`)
- **Datos:** Endpoint `/estadisticas/expedientes-por-fuerza`
- **Colores:** Utiliza `COLORS_FUERZA` existente
- **Interactividad:** 
  - Click en segmento aplica/quita filtro por fuerza
  - Sincronización con filtros globales
  - Actualización automática cada 30 segundos

### Integración con Sistema de Filtros
- **Filtro por fuerza:** Se aplica a todos los paneles
- **Filtros cruzados:** Estado + Fuerza + Tipo de Captura
- **Indicadores visuales:** Chips de filtros activos en header

---

## Beneficios Esperados

### Usabilidad
- ✅ **Mejor visualización:** Panel "Casos Destacados" con altura completa
- ✅ **Información adicional:** Insights de distribución por fuerza
- ✅ **Navegación intuitiva:** Filtrado interactivo entre paneles

### Técnicos
- ✅ **Reutilización:** Aprovecha componentes existentes
- ✅ **Performance:** Sin impacto significativo (endpoint ya existe)
- ✅ **Mantenibilidad:** Consistencia con arquitectura actual
- ✅ **Responsive:** Adaptación automática a dispositivos

### Visuales
- ✅ **Consistencia:** Mantiene tema cinematográfico CUFRE
- ✅ **Equilibrio:** Distribución balanceada en grid 2x3
- ✅ **Accesibilidad:** Colores y contrastes apropiados

---

## Consideraciones de Implementación

### Orden de Desarrollo
1. **CSS Grid Layout** - Base fundamental
2. **Componente Principal** - Lógica y datos
3. **Servicio API** - Verificación/adición de métodos
4. **Testing** - Validación en diferentes resoluciones

### Puntos de Atención
- **Responsive Design:** Verificar comportamiento en móviles
- **Performance:** Monitorear tiempo de carga con panel adicional
- **Filtros:** Asegurar sincronización correcta entre paneles
- **Datos:** Manejar casos de error/sin datos apropiadamente

### Testing Requerido
- [ ] Layout en resoluciones: 1920x1080, 1366x768, 768x1024
- [ ] Funcionalidad de filtros cruzados
- [ ] Carga de datos y estados de error
- [ ] Navegación entre paneles
- [ ] Actualización automática

---

## Checklist de Implementación

### Fase 1: CSS Layout
- [ ] Modificar `.centro-comando-grid` con nuevo grid 2x3
- [ ] Agregar `grid-template-areas` para posicionamiento
- [ ] Implementar reglas responsive para diferentes pantallas
- [ ] Verificar que no se rompan estilos existentes

### Fase 2: Componente React
- [ ] Agregar estado `expedientesPorFuerza`
- [ ] Modificar `fetchAllData` para incluir datos de fuerza
- [ ] Procesar datos con colores `COLORS_FUERZA`
- [ ] Reorganizar JSX con `gridArea` properties
- [ ] Implementar funcionalidad de filtrado por fuerza

### Fase 3: Servicios API
- [ ] Verificar existencia de métodos en `estadisticaService`
- [ ] Agregar métodos faltantes si es necesario
- [ ] Probar endpoints con diferentes filtros

### Fase 4: Testing y Validación
- [ ] Probar layout en diferentes resoluciones
- [ ] Validar funcionalidad de filtros cruzados
- [ ] Verificar actualización automática
- [ ] Comprobar estados de carga y error
- [ ] Revisar accesibilidad y usabilidad

---

## Archivos Afectados

| Archivo | Tipo de Cambio | Descripción |
|---------|----------------|-------------|
| [`frontend/src/styles/CentroComando.css`](frontend/src/styles/CentroComando.css) | Modificación | Grid layout 2x3, responsive rules |
| [`frontend/src/pages/estadisticas/CentroComandoPage.tsx`](frontend/src/pages/estadisticas/CentroComandoPage.tsx) | Modificación | Estado, datos, JSX reorganizado |
| [`frontend/src/api/estadisticaService.ts`](frontend/src/api/estadisticaService.ts) | Verificación/Adición | Métodos para expedientes por fuerza |

---

## Conclusión

Este plan proporciona una hoja de ruta clara para implementar las mejoras solicitadas en el Centro de Estadísticas CUFRE. La implementación aprovecha la infraestructura existente mientras agrega funcionalidad valiosa y mejora la experiencia del usuario.

**Próximo paso:** Cambiar a modo código para implementar los cambios detallados en este documento.

---

*Documento generado por Kilo Code - Arquitecto de Software*  
*Proyecto: CUFRE - Centro Unificado de Fuerzas de Seguridad*