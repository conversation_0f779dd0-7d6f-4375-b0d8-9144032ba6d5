# README_MAIL

## Problemas comunes de envío de mails en desarrollo vs producción

Es frecuente que el envío de mails funcione correctamente en desarrollo (local) pero falle en producción. Esto suele deberse a diferencias en la configuración o restricciones del entorno de producción.

---

## Causas más comunes

1. **Configuración SMTP diferente o incorrecta**
   - En desarrollo puedes tener configurado un servidor SMTP (Gmail, Mailtrap, etc.) que funciona.
   - En producción puede que:
     - No esté configurado el SMTP.
     - Los datos (usuario, contraseña, host, puerto) sean incorrectos.
     - El servidor de producción bloquee la salida por el puerto SMTP (25, 465, 587).

2. **Variables de entorno/configuración**
   - Asegúrate de que en el archivo de configuración de producción (`application-prod.properties`) o en las variables de entorno estén correctamente seteados:
     ```
     spring.mail.host=
     spring.mail.port=
     spring.mail.username=
     spring.mail.password=
     spring.mail.properties.mail.smtp.auth=
     spring.mail.properties.mail.smtp.starttls.enable=
     ```
   - Si usas servicios como Gmail, revisa que no haya bloqueos de seguridad.

3. **Firewall o restricciones del hosting**
   - Algunos hostings/clouds bloquean el envío de mails salientes por defecto.
   - Verifica que el servidor pueda conectarse al SMTP (puedes probar con `telnet smtp.tuservidor.com 587`).

4. **Errores en logs**
   - Revisa los logs del backend en producción. Si hay errores de envío de mail, ahí aparecerán (por ejemplo, "Connection refused", "Authentication failed", etc.).

---

## ¿Qué hacer si no llegan los mails en producción?

1. **Verifica la configuración SMTP en producción.**
2. **Revisa los logs del backend en producción para ver errores de envío de mail.**
3. **Haz una prueba manual de conexión SMTP desde el servidor de producción.**
4. **Asegúrate de que las variables de entorno o el archivo de propiedades de producción tengan los datos correctos.**

---

## Ejemplo de configuración SMTP en `application-prod.properties`

```
spring.mail.host=smtp.tuservidor.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=tu_contraseña
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
```

---

Si necesitas ayuda para configurar el envío de mails en producción, revisa estos puntos y consulta los logs para identificar el error exacto. 