# Plan de Implementación: Sistema de Anuncios Globales

Este documento detalla los pasos técnicos para implementar un sistema de anuncios globales en la aplicación.

## 1. Resumen de la Funcionalidad

El objetivo es permitir que usuarios con rol de `ADMIN` o `SUPERUSUARIO` puedan crear anuncios que se mostrarán en un modal a todos los demás usuarios una única vez al iniciar sesión.

-   **Gestión:** Los administradores tendrán una interfaz para crear, ver y activar anuncios.
-   **Visualización:** Los usuarios verán el anuncio activo en un modal la primera vez que accedan al sistema después de la activación del anuncio.
-   **Contenido:** Los anuncios soportarán contenido enriquecido (HTML).

## 2. Plan de Implementación Detallado

### Parte 1: Cambios en el Backend (Java/Spring Boot)

#### Paso 1: Migración de la Base de Datos (Flyway)

-   **Archivo a crear:** `src/main/resources/db/migration/V1011__create_anuncios_tables.sql`
-   **Contenido:**
    ```sql
    -- Tabla para almacenar los anuncios globales
    CREATE TABLE ANUNCIOS (
        ID NUMBER(19,0) GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
        TITULO VARCHAR2(255) NOT NULL,
        CONTENIDO CLOB NOT NULL, -- CLOB para soportar HTML largo
        ACTIVO NUMBER(1,0) DEFAULT 0 NOT NULL,
        FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CREADO_POR_ID NUMBER(19,0) NOT NULL,
        CONSTRAINT FK_ANUNCIOS_USUARIO FOREIGN KEY (CREADO_POR_ID) REFERENCES USUARIOS(ID)
    );

    -- Tabla para rastrear qué usuarios han visto qué anuncios
    CREATE TABLE ANUNCIOS_VISTOS (
        USUARIO_ID NUMBER(19,0) NOT NULL,
        ANUNCIO_ID NUMBER(19,0) NOT NULL,
        FECHA_VISTO TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (USUARIO_ID, ANUNCIO_ID),
        CONSTRAINT FK_VISTOS_USUARIO FOREIGN KEY (USUARIO_ID) REFERENCES USUARIOS(ID) ON DELETE CASCADE,
        CONSTRAINT FK_VISTOS_ANUNCIO FOREIGN KEY (ANUNCIO_ID) REFERENCES ANUNCIOS(ID) ON DELETE CASCADE
    );
    ```

#### Paso 2: Entidades JPA (Modelo)

-   Crear `src/main/java/com/cufre/expedientes/model/Anuncio.java`.
-   Crear `src/main/java/com/cufre/expedientes/model/AnuncioVisto.java` (con su `AnuncioVistoId` como `@EmbeddableId`).

#### Paso 3: Repositorios (Spring Data JPA)

-   Crear `src/main/java/com/cufre/expedientes/repository/AnuncioRepository.java`.
-   Crear `src/main/java/com/cufre/expedientes/repository/AnuncioVistoRepository.java`.

#### Paso 4: DTOs y Mappers

-   Crear `src/main/java/com/cufre/expedientes/dto/AnuncioDTO.java`.
-   Crear `src/main/java/com/cufre/expedientes/mapper/AnuncioMapper.java`.

#### Paso 5: Lógica de Negocio (Service)

-   Crear `src/main/java/com/cufre/expedientes/service/AnuncioService.java` para implementar la lógica de negocio principal.

#### Paso 6: Controlador y Seguridad

-   Crear `src/main/java/com/cufre/expedientes/controller/AnuncioController.java`.
-   **Endpoints:**
    -   `GET /api/anuncios`: Protegido para `ADMINISTRADOR` / `SUPERUSUARIO`.
    -   `POST /api/anuncios`: Protegido para `ADMINISTRADOR` / `SUPERUSUARIO`.
    -   `GET /api/anuncios/activo`: Protegido para cualquier usuario autenticado.
    -   `POST /api/anuncios/visto`: Protegido para cualquier usuario autenticado.

### Parte 2: Cambios en el Frontend (React)

#### Paso 7: Interfaz de Administración de Anuncios

-   **Ruta:** `/administracion/anuncios` (protegida por rol).
-   **Componente:** `AnunciosAdminPage.tsx` para listar anuncios y permitir la creación.
-   **Formulario:** Usar un editor WYSIWYG como `react-quill` para el campo de contenido.

#### Paso 8: Lógica de Visualización del Modal

-   **Ubicación:** En un componente de `Layout` principal o en `AuthContext.tsx`.
-   **Lógica:** Usar `useEffect` para llamar a `GET /api/anuncios/activo` al autenticar.
-   **Componente:** `AnuncioModal.tsx` que se muestra condicionalmente y renderiza el contenido HTML. Al cerrar, llama a `POST /api/anuncios/visto`.

## 3. Diagramas de Flujo

### Diagrama de Entidad-Relación

```mermaid
erDiagram
    Usuarios {
        INTEGER id PK
        VARCHAR nombre
    }
    Anuncios {
        INTEGER id PK
        VARCHAR titulo
        TEXT contenido
        BOOLEAN activo
        INTEGER creado_por_id FK
    }
    AnunciosVistos {
        INTEGER usuario_id PK, FK
        INTEGER anuncio_id PK, FK
    }
    Usuarios ||--o{ Anuncios : "crea"
    Usuarios ||--|{ AnunciosVistos : "ha_visto"
    Anuncios ||--|{ AnunciosVistos : "es_visto_por"
```

### Diagrama de Secuencia

```mermaid
sequenceDiagram
    participant Admin
    participant FrontendAdmin
    participant Usuario
    participant FrontendUsuario
    participant Backend
    participant BaseDeDatos

    Admin->>FrontendAdmin: Accede a /admin/anuncios y crea uno nuevo
    FrontendAdmin->>Backend: POST /api/anuncios (con título, HTML, activo=true)
    Backend->>BaseDeDatos: Desactiva anuncios antiguos y guarda el nuevo
    
    %% Flujo del usuario normal %%
    Usuario->>FrontendUsuario: Inicia sesión
    FrontendUsuario->>Backend: GET /api/anuncios/activo
    Backend->>BaseDeDatos: Busca anuncio activo y si el usuario ya lo vio
    Backend-->>FrontendUsuario: JSON del anuncio
    FrontendUsuario->>Usuario: Muestra Modal con contenido HTML
    
    Usuario->>FrontendUsuario: Clic en "Cerrar"
    FrontendUsuario->>Backend: POST /api/anuncios/visto
    Backend->>BaseDeDatos: INSERT en ANUNCIOS_VISTOS