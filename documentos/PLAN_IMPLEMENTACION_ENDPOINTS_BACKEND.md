# 📋 Plan de Implementación de Endpoints Backend para Gestión de Usuarios

## 🎯 Objetivo

Implementar los endpoints backend faltantes para completar la funcionalidad de gestión de usuarios del sistema CUFRE, específicamente para:
- Cambio de rol con validación de contraseña
- Forzar cierre de sesión de usuarios

## 📊 Análisis de la Situación Actual

### ✅ Lo que YA está implementado:
- `UsuarioController` con endpoints básicos CRUD
- `UsuarioService` con métodos de gestión de usuarios
- `UsuarioDTO` con estructura completa
- Sistema de autenticación y autorización básico
- Endpoint `POST /{id}/reset-password` para reseteo administrativo

### ❌ Lo que FALTA implementar:
- Endpoint `PUT /usuarios/{id}/role` para cambio de rol con validación de contraseña
- Endpoint `POST /usuarios/{id}/force-logout` para forzar cierre de sesión
- DTOs específicos para estas operaciones
- Validaciones de permisos y seguridad robustas
- Manejo de sesiones activas y blacklist de tokens JWT

## 🏗️ Arquitectura de la Solución

```mermaid
graph TD
    A[Frontend UsuariosPage] --> B[UsuarioController]
    B --> C[UsuarioService]
    C --> D[UsuarioRepository]
    C --> E[TokenBlacklistService]
    C --> F[ActividadSistemaService]
    
    B --> G[UpdateRoleRequestDTO]
    B --> H[UpdateRoleResponseDTO]
    B --> I[ForceLogoutResponseDTO]
    
    C --> J[PasswordEncoder]
    C --> K[SecurityContextHolder]
    
    E --> L[Redis/Cache]
    F --> M[Base de Datos]
```

## 📋 Plan de Implementación Detallado

### **Fase 1: Creación de DTOs Específicos**

```mermaid
graph TD
    A[Crear DTOs] --> B[UpdateRoleRequestDTO]
    A --> C[UpdateRoleResponseDTO]
    A --> D[ForceLogoutResponseDTO]
    
    B --> B1[newRole: Rol]
    B --> B2[password: String]
    
    C --> C1[success: boolean]
    C --> C2[message: String]
    C --> C3[updatedUser: UsuarioDTO]
    
    D --> D1[success: boolean]
    D --> D2[message: String]
    D --> D3[sessionsTerminated: int]
```

#### Archivos a crear:

1. **`UpdateRoleRequestDTO.java`**
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleRequestDTO {
    @NotNull(message = "El nuevo rol es obligatorio")
    private Rol newRole;
    
    @NotBlank(message = "La contraseña es obligatoria")
    private String password;
}
```

2. **`UpdateRoleResponseDTO.java`**
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleResponseDTO {
    private boolean success;
    private String message;
    private UsuarioDTO updatedUser;
}
```

3. **`ForceLogoutResponseDTO.java`**
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForceLogoutResponseDTO {
    private boolean success;
    private String message;
    private int sessionsTerminated;
}
```

### **Fase 2: Extensión del UsuarioService**

```mermaid
graph TD
    A[UsuarioService] --> B[updateRoleWithPassword]
    A --> C[forceLogout]
    A --> D[validatePasswordForUser]
    A --> E[invalidateUserSessions]
    
    B --> B1[Validar permisos]
    B --> B2[Verificar contraseña]
    B --> B3[Actualizar rol]
    B --> B4[Registrar actividad]
    
    C --> C1[Validar permisos]
    C --> C2[Invalidar tokens JWT]
    C --> C3[Limpiar sesiones]
    C --> C4[Registrar actividad]
```

#### Métodos a implementar en UsuarioService:

1. **`updateRoleWithPassword(Long userId, Rol newRole, String password)`**
   - Validar que el usuario actual sea SUPERUSUARIO
   - Verificar que no esté intentando cambiar el rol de otro SUPERUSUARIO
   - Validar la contraseña del usuario actual
   - Actualizar el rol del usuario objetivo
   - Registrar la actividad en el sistema

2. **`forceLogout(Long userId)`**
   - Validar que el usuario actual sea SUPERUSUARIO
   - Verificar que no esté intentando forzar su propio logout
   - Invalidar todos los tokens JWT del usuario objetivo
   - Registrar la actividad en el sistema

3. **`validatePasswordForCurrentUser(String password)`**
   - Obtener el usuario actual del SecurityContext
   - Verificar la contraseña usando PasswordEncoder

### **Fase 3: Implementación de Endpoints en UsuarioController**

```mermaid
graph TD
    A[UsuarioController] --> B[PUT /usuarios/{id}/role]
    A --> C[POST /usuarios/{id}/force-logout]
    
    B --> B1[@PreAuthorize SUPERUSUARIO]
    B --> B2[Validar request body]
    B --> B3[Llamar service.updateRoleWithPassword]
    B --> B4[Retornar UpdateRoleResponseDTO]
    
    C --> C1[@PreAuthorize SUPERUSUARIO]
    C --> C2[Validar usuario objetivo]
    C --> C3[Llamar service.forceLogout]
    C --> C4[Retornar ForceLogoutResponseDTO]
```

#### Endpoints a implementar:

1. **`PUT /usuarios/{id}/role`**
```java
@PutMapping("/{id}/role")
@PreAuthorize("hasRole('SUPERUSUARIO')")
public ResponseEntity<UpdateRoleResponseDTO> updateRole(
    @PathVariable Long id,
    @Valid @RequestBody UpdateRoleRequestDTO request) {
    // Implementación
}
```

2. **`POST /usuarios/{id}/force-logout`**
```java
@PostMapping("/{id}/force-logout")
@PreAuthorize("hasRole('SUPERUSUARIO')")
public ResponseEntity<ForceLogoutResponseDTO> forceLogout(@PathVariable Long id) {
    // Implementación
}
```

### **Fase 4: Seguridad y Validaciones**

```mermaid
graph TD
    A[Validaciones de Seguridad] --> B[Permisos de Rol]
    A --> C[Validación de Contraseña]
    A --> D[Prevención de Auto-modificación]
    A --> E[Auditoría de Actividades]
    
    B --> B1[Solo SUPERUSUARIO puede cambiar roles]
    B --> B2[No puede cambiar rol de otro SUPERUSUARIO]
    
    C --> C1[Verificar contraseña del usuario actual]
    C --> C2[Usar PasswordEncoder para validar]
    
    D --> D1[Usuario no puede forzar su propio logout]
    D --> D2[Usuario no puede cambiar su propio rol]
    
    E --> E1[Registrar cambios de rol]
    E --> E2[Registrar forzado de logout]
```

#### Reglas de Negocio:

1. **Cambio de Rol:**
   - Solo SUPERUSUARIOS pueden cambiar roles
   - No se puede cambiar el rol de otro SUPERUSUARIO
   - Se requiere validación de contraseña del usuario actual
   - Se debe registrar la actividad

2. **Forzar Logout:**
   - Solo SUPERUSUARIOS pueden forzar logout
   - Un usuario no puede forzar su propio logout
   - Se deben invalidar todos los tokens JWT del usuario
   - Se debe registrar la actividad

### **Fase 5: Manejo de Sesiones JWT**

```mermaid
graph TD
    A[Gestión de Sesiones] --> B[Identificar Tokens Activos]
    A --> C[Invalidar Tokens]
    A --> D[Blacklist de Tokens]
    
    B --> B1[Buscar por usuario ID]
    B --> B2[Verificar tokens válidos]
    
    C --> C1[Marcar como inválidos]
    C --> C2[Actualizar blacklist]
    
    D --> D1[Redis/Cache para blacklist]
    D --> D2[TTL basado en expiración JWT]
```

#### TokenBlacklistService (Opcional - Implementación Simplificada):

Para una implementación inicial, podemos usar un enfoque simplificado:
- Cambiar el secret JWT del usuario específico
- Invalidar tokens basándose en timestamp de emisión
- Usar cache en memoria para blacklist temporal

## 📁 Estructura de Archivos

```
backend/src/main/java/com/cufre/expedientes/
├── dto/
│   ├── UpdateRoleRequestDTO.java          [NUEVO]
│   ├── UpdateRoleResponseDTO.java         [NUEVO]
│   └── ForceLogoutResponseDTO.java        [NUEVO]
├── service/
│   ├── UsuarioService.java                [MODIFICAR]
│   └── TokenBlacklistService.java         [NUEVO - OPCIONAL]
└── controller/
    └── UsuarioController.java             [MODIFICAR]
```

## 🔒 Consideraciones de Seguridad

1. **Autenticación y Autorización:**
   - Usar `@PreAuthorize("hasRole('SUPERUSUARIO')")` en endpoints críticos
   - Validar permisos a nivel de servicio también

2. **Validación de Contraseñas:**
   - Usar `PasswordEncoder` existente para verificar contraseñas
   - No almacenar contraseñas en logs

3. **Auditoría:**
   - Registrar todas las operaciones críticas en `ActividadSistemaService`
   - Incluir información del usuario que ejecuta la acción

4. **Manejo de Errores:**
   - Usar excepciones específicas para diferentes tipos de errores
   - Integrar con `GlobalExceptionHandler` existente

## 🧪 Testing

### Casos de Prueba a Implementar:

1. **Cambio de Rol:**
   - ✅ SUPERUSUARIO cambia rol de ADMINISTRADOR a USUARIOCARGA
   - ❌ ADMINISTRADOR intenta cambiar rol (sin permisos)
   - ❌ SUPERUSUARIO intenta cambiar rol de otro SUPERUSUARIO
   - ❌ Contraseña incorrecta
   - ❌ Usuario inexistente

2. **Forzar Logout:**
   - ✅ SUPERUSUARIO fuerza logout de ADMINISTRADOR
   - ❌ ADMINISTRADOR intenta forzar logout (sin permisos)
   - ❌ Usuario intenta forzar su propio logout
   - ❌ Usuario inexistente

## 📈 Métricas de Éxito

- [ ] Endpoints responden correctamente según especificación
- [ ] Validaciones de seguridad funcionan correctamente
- [ ] Frontend puede cambiar roles sin errores
- [ ] Frontend puede forzar logout sin errores
- [ ] Todas las actividades se registran en auditoría
- [ ] Tokens JWT se invalidan correctamente
- [ ] Tests unitarios pasan al 100%

## 🚀 Orden de Implementación

1. **Crear DTOs** (15 min)
2. **Implementar métodos en UsuarioService** (45 min)
3. **Agregar endpoints en UsuarioController** (30 min)
4. **Implementar validaciones de seguridad** (30 min)
5. **Testing y validación** (30 min)

**Tiempo total estimado: 2.5 horas**

---

*Este plan asegura una implementación robusta y segura de los endpoints faltantes, manteniendo la consistencia con la arquitectura existente del sistema CUFRE.*