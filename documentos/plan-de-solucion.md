# Plan de Solución: Errores en API de Anuncios y UI de Prioridad

## 1. Resumen Ejecutivo

Se han identificado dos problemas críticos en la aplicación:

1.  **Error 500 en Backend:** Al intentar guardar un anuncio (`POST /api/anuncios`), el servidor responde con un error 500 debido a que la solicitud es manejada incorrectamente por el gestor de archivos estáticos en lugar del controlador de la API.
2.  **Error en Frontend:** La página de "Configurar Variables de Prioridad" muestra un error al cargar los datos, a pesar de que la API del backend responde con un código 200 OK. Esto se debe a una inconsistencia entre los datos que el frontend espera recibir y los que el backend realmente envía.

Este documento detalla el diagnóstico de cada problema y el plan de acción para solucionarlos.

## 2. Diagnóstico y Solución

### 2.1. Problema 1: Error 500 en `POST /api/anuncios` (Backend)

#### Diagnóstico

La causa raíz es un **conflicto de enrutamiento en la configuración de Spring MVC**.

-   El archivo `WebMvcConfig.java` contiene una configuración para el "fallback" de la Single Page Application (SPA) que es demasiado permisiva.
-   La regla `registry.addResourceHandler("/**")` intercepta todas las rutas, incluidas las que comienzan con `/api/`.
-   Aunque hay un intento de excluir las rutas de la API dentro del `PathResourceResolver`, la configuración de `PathMatchConfigurer` no es lo suficientemente estricta, lo que permite que el manejador de recursos estáticos procese la solicitud `POST /api/anuncios`.
-   Como el manejador de recursos estáticos no soporta el método `POST`, lanza una `HttpRequestMethodNotSupportedException`, resultando en el error 500.

#### Plan de Solución

La solución consiste en hacer la configuración de enrutamiento de Spring MVC más estricta y explícita.

-   **Archivo a Modificar:** `backend/src/main/java/com/cufre/expedientes/config/WebMvcConfig.java`
-   **Acción:** Actualizar el método `configurePathMatch` para que utilice el `PathPatternParser`, que es el motor de coincidencia de patrones de URL recomendado y más predecible en las versiones modernas de Spring.

```java
// Cambio propuesto en WebMvcConfig.java
@Override
public void configurePathMatch(PathMatchConfigurer configurer) {
    // Este cambio asegura que las rutas se interpreten de manera estricta,
    // evitando que el manejador de recursos estáticos intercepte las rutas de la API.
    configurer.setPatternParser(new PathPatternParser());
}
```

### 2.2. Problema 2: Error al Cargar Parámetros de Prioridad (Frontend)

#### Diagnóstico

La causa raíz es una **inconsistencia en el contrato de la API** entre el frontend y el backend.

-   El componente de React (`ConfigurarVariablesPage.tsx`) llama al método `parametrosPrioridadService.obtenerTodos()`.
-   Este método del servicio (`parametrosPrioridadService.ts`) asume que el backend devolverá un objeto JSON con los parámetros agrupados por categoría (`ParametrosPorTipoResponse`).
-   Sin embargo, el controlador del backend (`ParametroPrioridadController.java`) tiene un método `obtenerTodosLosParametros` que devuelve una **lista plana** de parámetros (`List<ParametroPrioridadDTO>`).
-   Esta discrepancia en la estructura de los datos provoca un error de deserialización en el cliente (axios), que lanza una excepción y activa el mensaje de "Error al cargar los parámetros".

#### Plan de Solución

La solución es alinear la llamada del frontend con lo que el backend realmente provee.

-   **Archivo a Modificar:** `frontend/src/api/parametrosPrioridadService.ts`
-   **Acción:** Modificar el servicio para que llame directamente al endpoint que devuelve la lista plana, eliminando la lógica innecesaria de procesamiento en el lado del cliente.

```typescript
// Cambio propuesto en parametrosPrioridadService.ts

// 1. Eliminar o comentar la función 'obtenerParametros' que espera datos agrupados.
/*
  async obtenerParametros(): Promise<ParametrosPorTipoResponse> {
    const response = await axiosClient.get<ParametrosPorTipoResponse>(this.baseUrl);
    return response.data;
  }
*/

// 2. Modificar 'obtenerTodos' para que haga la llamada directa.
  async obtenerTodos(): Promise<ParametroPrioridadDTO[]> {
    // Llamada directa al endpoint que devuelve una lista plana.
    const response = await axiosClient.get<ParametroPrioridadDTO[]>(this.baseUrl);
    return response.data;
  }
```

## 3. Diagrama del Plan de Acción

```mermaid
graph TD
    subgraph "Diagnóstico Backend"
        A[Error 500 en POST /api/anuncios] --> B{Causa: Conflicto de enrutamiento};
        B --> C[WebMvcConfig intercepta la ruta de la API];
    end

    subgraph "Solución Backend"
        C --> D[**Acción:** Modificar WebMvcConfig.java];
        D --> E[Configurar `PathPatternParser` en `configurePathMatch`];
    end

    subgraph "Diagnóstico Frontend"
        F[Error al cargar parámetros de prioridad] --> G{Causa: Inconsistencia API};
        G --> H[El frontend espera datos agrupados, el backend envía una lista plana];
    end

    subgraph "Solución Frontend"
        H --> I[**Acción:** Modificar parametrosPrioridadService.ts];
        I --> J[Ajustar `obtenerTodos` para que llame al endpoint correcto y espere `ParametroPrioridadDTO[]`];
    end

    subgraph "Implementación"
        E --> K{Aplicar Cambios};
        J --> K;
        K --> L[Validar ambas soluciones];
        L --> M[Tarea Completada];
    end