# Plan de Corrección: Error de Carga de Variables de Prioridad

## 1. Resumen del Problema

Al acceder a la página "Configurar Variables", la aplicación muestra un error "Error al cargar los parámetros de prioridad". El análisis de la red del navegador revela que la solicitud `GET /parametros-prioridad` está fallando con un código de estado **404 Not Found**.

Esto ocurre a pesar de que el usuario está autenticado con el rol `SUPERUSUARIO`, que teóricamente tiene los permisos necesarios para acceder a este recurso.

## 2. An<PERSON><PERSON>is y Diagnóstico

Tras una investigación exhaustiva del código fuente del backend (Java/Spring Boot), se ha determinado la causa raíz del problema.

### Flujo de Autenticación y Causa del Error

El error no se debe a que la ruta no exista, sino a un fallo en la cadena de autorización que ocurre durante el login.

```mermaid
sequenceDiagram
    participant Frontend
    participant AuthController
    participant AuthService
    participant UsuarioService
    participant JwtTokenProvider

    Frontend->>+AuthController: POST /auth/login (con credenciales)
    AuthController->>+AuthService: login(loginDTO)
    AuthService->>+UsuarioService: findByEmail(email)
    
    Note over UsuarioService: 1. Busca la entidad 'Usuario' en la BD (con rol).<br/>2. **ERROR:** Mapea la entidad a 'UsuarioDTO' pero<br/> **OMITE** el campo 'rol'.
    
    UsuarioService-->>-AuthService: Devuelve un UsuarioDTO **incompleto** (sin rol)
    AuthService->>+JwtTokenProvider: generateToken(usuarioDTO_incompleto)
    JwtTokenProvider-->>-AuthService: Devuelve un token JWT **sin el claim de roles**
    AuthService-->>-AuthController: Devuelve respuesta con el token sin roles
    AuthController-->>-Frontend: HTTP 200 OK (con token defectuoso)

    Note over Frontend: El usuario está "logueado" pero su token no tiene permisos.

    Frontend->>Backend: GET /parametros-prioridad (con token sin roles)
    Note over Backend: El filtro de seguridad no encuentra roles en el token.
    Backend-->>Frontend: HTTP 404 Not Found
```

**La Causa Raíz:** El componente responsable de mapear la entidad `Usuario` de la base de datos a un `UsuarioDTO` (`UsuarioMapper`) no está incluyendo el campo `rol` en la conversión. Como resultado, el token JWT se genera sin los permisos (roles) del usuario. Cuando el frontend intenta acceder a una ruta protegida, el backend no encuentra los roles necesarios en el token y deniega el acceso, devolviendo un `404`.

## 3. Plan de Acción para la Implementación

La solución es precisa y se centra en corregir el mapeo de datos.

1.  **Inspeccionar el Mapper:** El primer paso será leer el archivo `backend/src/main/java/com/cufre/expedientes/mapper/UsuarioMapper.java` para confirmar visualmente que el mapeo del campo `rol` está ausente.

2.  **Aplicar la Corrección:** Se modificará el `UsuarioMapper` para añadir la lógica necesaria que copie el valor del campo `rol` desde la entidad `Usuario` al `UsuarioDTO`.

3.  **Verificación:**
    *   Recompilar y reiniciar el servidor de backend.
    *   En el frontend, cerrar la sesión actual.
    *   Iniciar sesión nuevamente para obtener un nuevo token JWT, que ahora contendrá los roles correctos.
    *   Navegar a la página "Configurar Variables" y verificar que los datos se cargan correctamente.

Este plan resuelve el problema de raíz y restaurará la funcionalidad esperada.

## 4. Plan Detallado de Implementación

### Confirmación del Diagnóstico

Tras la investigación del código fuente, se ha confirmado que:

1. **La entidad `Usuario`** SÍ tiene el campo `rol` definido (línea 26)
2. **El DTO `UsuarioDTO`** SÍ tiene el campo `rol` definido (línea 16)
3. **El `UsuarioMapper`** NO está mapeando explícitamente el campo `rol` en el método `toDto()`
4. **El `JwtTokenProvider`** SÍ está intentando usar el rol del usuario (línea 71)

**Problema confirmado**: MapStruct no está copiando automáticamente el campo `rol` porque no hay un mapeo explícito definido en el mapper.

### Flujo de Implementación

```mermaid
flowchart TD
    A[Inicio] --> B[Inspeccionar UsuarioMapper actual]
    B --> C[Agregar mapeo explícito del campo 'rol']
    C --> D[Compilar y probar el backend]
    D --> E{¿Compilación exitosa?}
    E -->|No| F[Corregir errores de compilación]
    F --> D
    E -->|Sí| G[Reiniciar servidor backend]
    G --> H[Probar login en frontend]
    H --> I[Verificar que el token contiene roles]
    I --> J[Probar acceso a /parametros-prioridad]
    J --> K{¿Funciona correctamente?}
    K -->|No| L[Revisar logs y depurar]
    L --> H
    K -->|Sí| M[Verificación completa exitosa]
    M --> N[Fin]
```

### Pasos Específicos de Implementación

#### Paso 1: Modificación del UsuarioMapper
- **Archivo**: `backend/src/main/java/com/cufre/expedientes/mapper/UsuarioMapper.java`
- **Línea objetivo**: 21 (método `toDto()`)
- **Acción**: Agregar mapeo explícito del campo `rol`
- **Código a agregar**:
```java
@Mapping(target = "rol", source = "rol")
```

#### Paso 2: Verificación y Pruebas
1. Compilar el proyecto backend
2. Reiniciar el servidor backend
3. Realizar login desde el frontend
4. Verificar que el token JWT contiene el claim de roles
5. Probar acceso a la página "Configurar Variables"

#### Paso 3: Flujo de Corrección Esperado

```mermaid
sequenceDiagram
    participant Frontend
    participant AuthController
    participant AuthService
    participant UsuarioService
    participant UsuarioMapper
    participant JwtTokenProvider

    Frontend->>+AuthController: POST /auth/login
    AuthController->>+AuthService: login(loginDTO)
    AuthService->>+UsuarioService: findByEmail(email)
    UsuarioService->>+UsuarioMapper: toDto(usuario_entity)
    
    Note over UsuarioMapper: ✅ CORREGIDO: Ahora mapea<br/>correctamente el campo 'rol'
    
    UsuarioMapper-->>-UsuarioService: UsuarioDTO completo (con rol)
    UsuarioService-->>-AuthService: UsuarioDTO completo
    AuthService->>+JwtTokenProvider: generateToken(usuarioDTO_completo)
    
    Note over JwtTokenProvider: ✅ Ahora puede acceder a<br/>usuario.getRol().name()
    
    JwtTokenProvider-->>-AuthService: Token JWT con roles
    AuthService-->>-AuthController: Respuesta con token válido
    AuthController-->>-Frontend: HTTP 200 OK (token con roles)

    Frontend->>Backend: GET /parametros-prioridad (con token válido)
    Note over Backend: ✅ El filtro encuentra roles en el token
    Backend-->>Frontend: HTTP 200 OK (datos de parámetros)
```

### Criterios de Éxito

- ✅ El usuario puede hacer login correctamente
- ✅ El token JWT generado contiene el claim de roles
- ✅ El acceso a `/parametros-prioridad` devuelve HTTP 200 con datos
- ✅ La página "Configurar Variables" carga sin errores
- ✅ No se muestran errores 404 en la consola del navegador

### Archivos Involucrados

1. **Archivo principal a modificar**:
   - `backend/src/main/java/com/cufre/expedientes/mapper/UsuarioMapper.java`

2. **Archivos relacionados (solo lectura)**:
   - `backend/src/main/java/com/cufre/expedientes/model/Usuario.java`
   - `backend/src/main/java/com/cufre/expedientes/dto/UsuarioDTO.java`
   - `backend/src/main/java/com/cufre/expedientes/security/JwtTokenProvider.java`
   - `backend/src/main/java/com/cufre/expedientes/service/AuthService.java`

## 5. Próximos Pasos

Una vez completada esta corrección, se recomienda cambiar al modo **Code** para implementar la solución siguiendo este plan detallado.