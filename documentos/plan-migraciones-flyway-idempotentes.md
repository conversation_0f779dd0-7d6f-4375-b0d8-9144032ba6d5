# Plan Detallado: Solución Completa de Migraciones Flyway Idempotentes

## 📊 **Análisis del Problema**

Las migraciones de Flyway fallan porque no son idempotentes. Los errores principales identificados son:

1. **ORA-01430**: "la columna que se está agregando ya existe en la tabla"
2. **ORA-00955**: "este nombre ya lo está utilizando otro objeto existente"
3. **Migraciones no verifican existencia** antes de crear objetos

## 🎯 **Objetivo**

Convertir todas las migraciones de Flyway (V1005 en adelante) en migraciones idempotentes que puedan ejecutarse múltiples veces sin errores.

## 📋 **Estado Actual de Migraciones**

### ✅ **Migraciones Ya Corregidas**
- **V1003** - `mejoras_actividad_sistema.sql` - ✅ **COMPLETADA**
- **V1004** - `recompensa_boolean_y_monto_oracle.sql` - ✅ **COMPLETADA**

### ❌ **Migraciones Pendientes de Corrección**
- **V1005** - `create_expediente_bloqueo_table.sql`
- **V1006** - `add_ultima_visita_notificaciones.sql`
- **V1007** - `add_perfil_fields_to_usuario.sql`
- **V1008** - `fix_avatar_data.sql`
- **V1010** - `drop_avatar_tables_and_columns.sql`
- **V1011** - `create_anuncios_tables.sql`
- **V1012** - `insert_parametros_prioridad.sql`
- **V1013** - `fix_parametro_prioridad_constraints.sql`

## 🔧 **Estrategia de Implementación**

```mermaid
flowchart TD
    A[Inicio] --> B[Analizar Migración Actual]
    B --> C{Tipo de Operación}
    
    C -->|CREATE TABLE| D[Verificar si tabla existe]
    C -->|ALTER TABLE ADD| E[Verificar si columna existe]
    C -->|CREATE INDEX| F[Verificar si índice existe]
    C -->|CREATE SEQUENCE| G[Verificar si secuencia existe]
    C -->|INSERT| H[Verificar si datos existen]
    C -->|DROP| I[Verificar si objeto existe antes de eliminar]
    
    D --> J[Crear solo si no existe]
    E --> K[Agregar solo si no existe]
    F --> L[Crear solo si no existe]
    G --> M[Crear solo si no existe]
    H --> N[Insertar solo si no existe]
    I --> O[Eliminar solo si existe]
    
    J --> P[Aplicar Migración Idempotente]
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[Reparar Flyway]
    Q --> R[Probar Migración]
    R --> S{¿Éxito?}
    
    S -->|No| T[Siguiente Migración]
    S -->|Sí| U[Migración Completada]
    
    T --> B
    U --> V[Fin]
```

## 📝 **Plan de Ejecución Detallado**

### **Fase 1: Preparación (15 min)**
1. **Crear respaldos** de todas las migraciones originales
2. **Documentar estado actual** de Flyway
3. **Preparar plantillas** de código idempotente

### **Fase 2: Conversión de Migraciones (60-90 min)**

#### **Plantillas de Código Idempotente**

##### **Patrón para CREATE TABLE**
```sql
DECLARE
    table_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_exists 
    FROM user_tables 
    WHERE table_name = 'NOMBRE_TABLA';
    
    IF table_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE TABLE NOMBRE_TABLA (...)';
    END IF;
END;
/
```

##### **Patrón para ALTER TABLE ADD**
```sql
DECLARE
    column_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO column_exists 
    FROM user_tab_columns 
    WHERE table_name = 'NOMBRE_TABLA' AND column_name = 'NOMBRE_COLUMNA';
    
    IF column_exists = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE NOMBRE_TABLA ADD NOMBRE_COLUMNA TIPO';
    END IF;
END;
/
```

##### **Patrón para CREATE INDEX**
```sql
DECLARE
    index_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO index_exists 
    FROM user_indexes 
    WHERE index_name = 'NOMBRE_INDICE';
    
    IF index_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX NOMBRE_INDICE ON TABLA(COLUMNA)';
    END IF;
END;
/
```

##### **Patrón para CREATE SEQUENCE**
```sql
DECLARE
    sequence_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO sequence_exists 
    FROM user_sequences 
    WHERE sequence_name = 'NOMBRE_SECUENCIA';
    
    IF sequence_exists = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE NOMBRE_SECUENCIA START WITH 1 INCREMENT BY 1';
    END IF;
END;
/
```

##### **Patrón para INSERT (evitar duplicados)**
```sql
DECLARE
    record_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO record_exists 
    FROM TABLA 
    WHERE CONDICION_UNICA;
    
    IF record_exists = 0 THEN
        EXECUTE IMMEDIATE 'INSERT INTO TABLA VALUES (...)';
    END IF;
END;
/
```

##### **Patrón para DROP (verificar existencia)**
```sql
DECLARE
    object_exists NUMBER;
BEGIN
    SELECT COUNT(*) INTO object_exists 
    FROM user_tables 
    WHERE table_name = 'NOMBRE_TABLA';
    
    IF object_exists > 0 THEN
        EXECUTE IMMEDIATE 'DROP TABLE NOMBRE_TABLA';
    END IF;
END;
/
```

### **Fase 3: Validación y Pruebas (30 min)**
1. **Reparar Flyway** después de cada migración corregida
2. **Ejecutar aplicación** para validar
3. **Verificar integridad** de datos
4. **Documentar cambios**

## 🔄 **Proceso Iterativo por Migración**

```mermaid
sequenceDiagram
    participant Dev as Desarrollador
    participant File as Archivo Migración
    participant DB as Base de Datos
    participant Flyway as Flyway
    participant App as Aplicación Spring Boot

    Dev->>File: 1. Respaldar migración original
    Dev->>File: 2. Convertir a idempotente
    Dev->>Flyway: 3. Reparar historial
    Dev->>App: 4. Ejecutar aplicación
    App->>DB: 5. Aplicar migración
    DB-->>App: 6. Resultado
    App-->>Dev: 7. Éxito/Error
    
    alt Error
        Dev->>File: 8. Corregir migración
        Dev->>Flyway: 9. Reparar nuevamente
    else Éxito
        Dev->>Dev: 10. Continuar siguiente migración
    end
```

## 📊 **Cronograma Estimado**

| Migración | Complejidad | Tiempo Estimado | Tipo de Operaciones |
|-----------|-------------|-----------------|-------------------|
| V1005 | Media | 15 min | CREATE TABLE, SEQUENCE, INDEX |
| V1006 | Baja | 10 min | ALTER TABLE ADD, CREATE INDEX |
| V1007 | Baja | 10 min | ALTER TABLE ADD |
| V1008 | Media | 15 min | UPDATE, posibles ALTER |
| V1010 | Alta | 20 min | DROP operations (cuidado especial) |
| V1011 | Alta | 20 min | CREATE múltiples tablas |
| V1012 | Media | 15 min | INSERT statements |
| V1013 | Media | 15 min | ALTER constraints |

**Total Estimado: 2-3 horas**

## ⚠️ **Consideraciones Especiales**

### **V1005 - CREATE EXPEDIENTE_BLOQUEO_TABLE**
- Verificar existencia de tabla `EXPEDIENTE_BLOQUEO`
- Verificar existencia de secuencia `EXPEDIENTE_BLOQUEO_SEQ`
- Verificar existencia de índices antes de crearlos

### **V1006 - ADD ULTIMA_VISITA_NOTIFICACIONES**
- Verificar si columna `ULTIMA_VISITA_NOTIFICACIONES` ya existe en tabla `USUARIO`
- Verificar existencia de índice `IDX_ACTIVIDAD_SISTEMA_NOTIF`

### **V1010 - DROP AVATAR TABLES**
- **CUIDADO ESPECIAL**: Verificar existencia antes de eliminar
- Asegurar que no se pierdan datos importantes
- Verificar dependencias antes del DROP

### **V1011 - CREATE ANUNCIOS TABLES**
- Verificar existencia de tabla `ANUNCIOS`
- Verificar existencia de tabla `ANUNCIOS_VISTOS`
- Verificar todos los índices y constraints

### **V1012 - INSERT PARAMETROS_PRIORIDAD**
- Verificar duplicados antes de insertar
- Usar condiciones WHERE para evitar inserts duplicados

### **V1013 - FIX PARAMETRO_PRIORIDAD_CONSTRAINTS**
- Verificar existencia de constraints antes de modificar
- Manejar constraints que ya existen

## 🎯 **Criterios de Éxito**

- ✅ Todas las migraciones se ejecutan sin errores
- ✅ La aplicación Spring Boot inicia correctamente
- ✅ Las migraciones son verdaderamente idempotentes (pueden ejecutarse múltiples veces)
- ✅ No se pierden datos existentes
- ✅ El esquema final es consistente

## 📋 **Entregables**

1. **Migraciones corregidas** (archivos .sql idempotentes)
2. **Documentación** de cambios realizados
3. **Script de validación** para verificar idempotencia
4. **Guía de mejores prácticas** para futuras migraciones

## 🚀 **Comandos de Ejecución**

### **Comando para reparar Flyway**
```bash
cd backend && ./repair-flyway-local.sh
```

### **Comando para ejecutar aplicación**
```bash
cd backend && JAVA_HOME=/opt/homebrew/Cellar/openjdk@17/17.0.15/libexec/openjdk.jdk/Contents/Home mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## 📝 **Notas de Implementación**

1. **Siempre crear respaldo** antes de modificar una migración
2. **Probar cada migración individualmente** antes de continuar
3. **Documentar todos los cambios** realizados
4. **Verificar integridad de datos** después de cada migración
5. **Mantener logs detallados** del proceso

---

**Fecha de creación**: 20 de junio de 2025  
**Estado**: Listo para implementación  
**Próximo paso**: Cambiar a modo Code para implementar las correcciones