# Plan de Acción: Corrección del Sistema de Prioridad de Expedientes

## 1. Resumen del Problema

Se han identificado dos problemas críticos que impiden el correcto funcionamiento del sistema de priorización de expedientes:

1.  **Error al Guardar la Configuración:** Al modificar los "pesos" de las variables de prioridad en la página de configuración y presionar "Guardar", la operación falla con un error en el backend.
2.  **Cálculo de Prioridad Incorrecto:** Los expedientes en el sistema no reflejan los pesos configurados, resultando en una prioridad de `0` o un valor basado únicamente en la suma de la valoración de los delitos asociados.

## 2. Análisis y Diagnóstico

### 2.1. Causa Raíz del "Error al Guardar"

- **Diagnóstico:** Existe una discrepancia en el "contrato de API" entre el frontend y el backend.
- **Flujo Incorrecto (Actual):**
    - El **frontend** envía los datos como un **Array de objetos JSON**.
      ```json
      [
        { "id": 1, "valor": 10 },
        { "id": 2, "valor": 20 }
      ]
      ```
    - El **backend** espera recibir un **Objeto JSON** que contenga un **Mapa (Map)** de IDs a valores, encapsulado dentro de un DTO (`ActualizacionParametrosDTO`).
      ```java
      // Espera un JSON que se mapee a esto:
      public class ActualizacionParametrosDTO {
          private Map<Long, Integer> parametros;
      }
      ```
- **Error Resultante:** `JSON parse error: Cannot deserialize value of type 'java.util.LinkedHashMap' from Array value`.

### 2.2. Causa Raíz del "Cálculo de Prioridad Incorrecto"

- **Diagnóstico:** El servicio `ParametroPrioridadService` depende de una caché en memoria que no se está cargando o utilizando de manera robusta, lo que causa que el `PriorityCalculator` falle silenciosamente.
- **Flujo Incorrecto (Actual):**
    1.  `PriorityCalculator` solicita el valor de un parámetro (p. ej., "TIPO_CAPTURA_FEDERAL") a `ParametroPrioridadService`.
    2.  `ParametroPrioridadService` busca este valor en su caché interna.
    3.  **La caché está vacía**, probablemente porque no se cargó correctamente al inicio o la consulta a la base de datos no devolvió resultados.
    4.  Al no encontrar el valor en la caché, el servicio **retorna `0`** en lugar de lanzar un error o consultar la base de datos.
    5.  `PriorityCalculator` recibe `0` y lo suma a la prioridad total.
- **Resultado:** Este proceso se repite para todas las variables, y la prioridad final es incorrecta, reflejando solo los valores que no dependen de este mecanismo (como la valoración de los delitos).

## 3. Plan de Solución

### Objetivo 1: Corregir el Contrato de la API (Solucionar Error al Guardar)

- **Acción:** Modificar el código del **frontend** que realiza la llamada `PUT` a `/api/parametros-prioridad`.
- **Implementación:** Transformar el array de parámetros en un objeto que contenga un mapa, cumpliendo con la estructura que el `ActualizacionParametrosDTO` del backend espera.

  **Ejemplo de la transformación en el frontend:**

  - **Estructura de datos ANTES de enviar:**
    `const paramsArray = [ {id: 1, valor: 10}, {id: 2, valor: 20} ];`

  - **Transformación necesaria:**
    ```javascript
    const paramsMap = paramsArray.reduce((acc, param) => {
      acc[param.id] = param.valor;
      return acc;
    }, {});
    const bodyPayload = { parametros: paramsMap };
    ```

  - **Llamada a la API:**
    `fetch('/api/parametros-prioridad', { method: 'PUT', body: JSON.stringify(bodyPayload), ... })`

### Objetivo 2: Robustecer el Cálculo de Prioridad

- **Acción 1: Implementar Carga Proactiva (Eager Loading) de la Caché.**
  - **Descripción:** Asegurar que la caché de parámetros se cargue completamente en el momento en que la aplicación se inicia, en lugar de esperar a la primera solicitud. Esto hace que los errores de carga sean visibles inmediatamente.
  - **Implementación:** En `ParametroPrioridadService`, añadir una anotación `@PostConstruct` a un método que llame a `cargarCache()`.
    ```java
    @PostConstruct
    public void inicializarCache() {
        log.info("Iniciando carga proactiva de la caché de parámetros de prioridad...");
        cargarCache();
    }
    ```

- **Acción 2: Mejorar el Logging en la Carga de la Caché.**
  - **Descripción:** Añadir logs explícitos para detectar si la caché se carga sin datos.
  - **Implementación:** En el método `cargarCache()` de `ParametroPrioridadService`, después de la consulta a la base de datos:
    ```java
    if (parametros.isEmpty()) {
        log.warn("¡ATENCIÓN! La caché de parámetros de prioridad se ha cargado vacía. No se encontraron parámetros en la base de datos.");
    }
    ```

- **Acción 3 (Opcional pero Recomendada): Hacer `getValorParametro` más Resiliente.**
  - **Descripción:** Como medida de seguridad, si por alguna razón un parámetro no está en la caché, intentar buscarlo directamente en la base de datos antes de devolver 0.
  - **Implementación:** Modificar `getValorParametro` en `ParametroPrioridadService`.
    ```java
    // Lógica actual
    Integer valor = cacheParametros.get(claveVariable);
    if (valor == null) {
        // Nueva lógica
        log.warn("Parámetro '{}' no encontrado en caché. Intentando buscar en BD.", claveVariable);
        Optional<ParametroPrioridad> parametroDB = parametroPrioridadRepository.findByClaveVariable(claveVariable);
        if (parametroDB.isPresent()) {
            valor = parametroDB.get().getValor();
            cacheParametros.put(claveVariable, valor); // Actualizar caché
            return valor;
        }
        log.error("Parámetro '{}' no encontrado ni en caché ni en BD. Se devolverá 0.", claveVariable);
        return 0; // Valor por defecto si no se encuentra en ningún lado
    }
    return valor;