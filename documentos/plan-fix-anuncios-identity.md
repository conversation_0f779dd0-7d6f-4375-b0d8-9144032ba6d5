# Plan de corrección ORA-32795 en /api/anuncios

## Resumen del problema

- `POST /api/anuncios` devuelve **503 Service Unavailable**.  
- Log muestra `ORA-32795: no se puede insertar una columna de identidad siempre generada`.  
- Hibernate incluye la columna `ID` en el `INSERT` sobre la tabla `ANUNCIOS`.  
- La columna `ID` fue creada como `GENERATED ALWAYS AS IDENTITY`. Con esa configuración Oracle no permite que se nombre la columna en la inserción.

## Diagnóstico

```log
insert into anuncios (..., id, ...) values (?,?,?,?,?,?)
ORA-32795: no se puede insertar una columna de identidad siempre generada
```

El script Flyway `V1011__create_anuncios_tables.sql` crea la columna como `GENERATED BY DEFAULT AS IDENTITY`, pero en algunas bases existentes pudo haberse creado manualmente con **ALWAYS**, generando la incompatibilidad.

## Diagrama de flujo

```mermaid
flowchart TD
    A[Controller POST /api/anuncios] --> B[AnuncioService.crear()]
    B --> C[Repository.save()]
    C -->|INSERT incluye ID| D[(Oracle)]
    D -- ORA-32795 --> E[Exception Handler]
    E --> F[HTTP 503]
    style D fill:#fdd
```

## Solución implementada

1. **Script Flyway V1015**: Corrige la configuración de identidad en la base de datos
   - Cambia `GENERATED ALWAYS` a `GENERATED BY DEFAULT ON NULL AS IDENTITY`
   - Incluye verificaciones para ser idempotente

2. **Corrección en entidad Anuncio.java**:
   - Cambió de `GenerationType.SEQUENCE` a `GenerationType.IDENTITY`
   - Eliminó la configuración de secuencia `@SequenceGenerator`

3. **Script Flyway V1016**: Limpia la secuencia ANUNCIOS_SEQ que ya no es necesaria

4. **Pasos para aplicar**:
   - Ejecutar `flyway migrate` para aplicar las correcciones
   - Reiniciar la aplicación para que tome los cambios de la entidad
   - Probar creación de anuncios

5. **Pruebas recomendadas**:
   - Crear anuncio vía POST /api/anuncios
   - Obtener anuncio activo vía GET /api/anuncios/activo
   - Verificar que no se produzca ORA-32795

## Alternativas evaluadas

- Mantener `GENERATED ALWAYS` y cambiar la estrategia de Hibernate por secuencia explícita (mayor esfuerzo).  
- Usar trigger + secuencia en lugar de identidad (compatible con Oracle 11g, pero no necesario).

## Riesgos

- Bases con restricciones ad-hoc que dependan del modo `ALWAYS`; se debe probar en ambiente de staging.

## Próximos pasos

- Aprobar plan.  
- Cambiar a modo **code** y agregar el script Flyway.