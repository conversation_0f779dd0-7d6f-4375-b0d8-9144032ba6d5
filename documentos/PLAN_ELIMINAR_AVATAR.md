# Plan para Eliminar la Función de Avatar y Reemplazarla con Iconos de Iniciales

Este documento detalla el plan para eliminar la funcionalidad de gestión de avatares en la aplicación y reemplazarla por un sistema que genere iconos con las iniciales del nombre y apellido del usuario. Los avatares existentes serán eliminados de la base de datos y del sistema de archivos.

## Resumen de la Información Recopilada

*   **Objetivo:** Eliminar la funcionalidad de avatar y reemplazarla con un icono de iniciales (primera letra del nombre y primera letra del apellido).
*   **Avatares existentes:** Deben ser eliminados completamente de la base de datos y del sistema de archivos.
*   **Disponibilidad de nombres/apellidos:** Los nombres y apellidos de los usuarios están disponibles en el objeto de usuario tanto en el frontend como en el backend.
*   **Componentes de UI:** Existen múltiples componentes de UI que muestran avatares, no solo los inicialmente identificados. Se requerirá una búsqueda exhaustiva.
*   **Estilo del icono de iniciales:** Se puede usar un estilo predeterminado o simple.

## Archivos y Directorios Relevantes Identificados

### Frontend
*   [`components/perfil/AvatarSelector.tsx`](frontend/src/components/perfil/AvatarSelector.tsx)
*   [`components/perfil/PerfilModal.tsx`](frontend/src/components/perfil/PerfilModal.tsx)
*   [`components/perfil/ProfileForm.tsx`](frontend/src/components/perfil/ProfileForm.tsx)
*   [`components/usuarios/UserAvatar.tsx`](frontend/src/components/usuarios/UserAvatar.tsx)
*   [`hooks/useAvatares.ts`](frontend/src/hooks/useAvatares.ts)
*   [`services/avatarService.ts`](frontend/src/services/avatarService.ts)
*   [`types/perfil.types.ts`](frontend/src/types/perfil.types.ts)
*   [`pages/perfil/PerfilPage.tsx`](frontend/src/pages/perfil/PerfilPage.tsx)

### Backend
*   [`src/main/java/com/cufre/expedientes/controller/AvatarController.java`](backend/src/main/java/com/cufre/expedientes/controller/AvatarController.java)
*   [`src/main/java/com/cufre/expedientes/dto/AvatarPredefinidoDTO.java`](backend/src/main/java/com/cufre/expedientes/dto/AvatarPredefinidoDTO.java)
*   [`src/main/java/com/cufre/expedientes/model/AvatarPredefinido.java`](backend/src/main/java/com/cufre/expedientes/model/AvatarPredefinido.java)
*   [`src/main/java/com/cufre/expedientes/repository/AvatarPredefinidoRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/AvatarPredefinidoRepository.java)
*   [`src/main/java/com/cufre/expedientes/service/AvatarService.java`](backend/src/main/java/com/cufre/expedientes/service/AvatarService.java)
*   [`src/main/resources/static/avatares/`](backend/src/main/resources/static/avatares/)
*   [`src/main/resources/db/migration/V1008__fix_avatar_data.sql`](backend/src/main/resources/db/migration/V1008__fix_avatar_data.sql)
*   [`src/main/java/com/cufre/expedientes/model/Usuario.java`](backend/src/main/java/com/cufre/expedientes/model/Usuario.java)
*   [`src/main/java/com/cufre/expedientes/dto/PerfilUsuarioDTO.java`](backend/src/main/java/com/cufre/expedientes/dto/PerfilUsuarioDTO.java)
*   [`src/main/resources/db/migration/V1007__add_perfil_fields_to_usuario.sql`](backend/src/main/resources/db/migration/V1007__add_perfil_fields_to_usuario.sql)

## Plan de Acción Detallado

El plan se dividirá en tres fases principales: Backend, Base de Datos y Frontend.

### Fase 1: Backend (Eliminación de lógica de avatar)

1.  **Eliminar el controlador de Avatar:**
    *   Eliminar [`src/main/java/com/cufre/expedientes/controller/AvatarController.java`](backend/src/main/java/com/cufre/expedientes/controller/AvatarController.java).
    *   Eliminar cualquier referencia a `AvatarController` en otros archivos (por ejemplo, en configuraciones de Spring).
2.  **Eliminar el servicio de Avatar:**
    *   Eliminar [`src/main/java/com/cufre/expedientes/service/AvatarService.java`](backend/src/main/java/com/cufre/expedientes/service/AvatarService.java).
    *   Eliminar cualquier inyección o referencia a `AvatarService` en otros servicios o controladores.
3.  **Eliminar el repositorio de Avatar Predefinido:**
    *   Eliminar [`src/main/java/com/cufre/expedientes/repository/AvatarPredefinidoRepository.java`](backend/src/main/java/com/cufre/expedientes/repository/AvatarPredefinidoRepository.java).
    *   Eliminar cualquier referencia a `AvatarPredefinidoRepository`.
4.  **Eliminar el modelo y DTO de Avatar Predefinido:**
    *   Eliminar [`src/main/java/com/cufre/expedientes/model/AvatarPredefinido.java`](backend/src/main/java/com/cufre/expedientes/model/AvatarPredefinido.java).
    *   Eliminar [`src/main/java/com/cufre/expedientes/dto/AvatarPredefinidoDTO.java`](backend/src/main/java/com/cufre/expedientes/dto/AvatarPredefinidoDTO.java).
5.  **Actualizar el modelo `Usuario` y `PerfilUsuarioDTO`:**
    *   Modificar [`src/main/java/com/cufre/expedientes/model/Usuario.java`](backend/src/main/java/com/cufre/expedientes/model/Usuario.java) para eliminar el campo `avatarUrl` (o similar) y cualquier lógica relacionada con avatares.
    *   Modificar [`src/main/java/com/cufre/expedientes/dto/PerfilUsuarioDTO.java`](backend/src/main/java/com/cufre/expedientes/dto/PerfilUsuarioDTO.java) para eliminar el campo `avatarUrl` (o similar).
    *   Asegurarse de que los campos `nombre` y `apellido` estén disponibles y sean accesibles en `Usuario` y `PerfilUsuarioDTO`.
6.  **Limpieza de archivos estáticos:**
    *   Eliminar el directorio [`src/main/resources/static/avatares/`](backend/src/main/resources/static/avatares/) y todo su contenido.

### Fase 2: Base de Datos (Limpieza de datos y esquema)

1.  **Eliminar la tabla de avatares predefinidos:**
    *   Crear un nuevo script de migración de Flyway (por ejemplo, `VXXX__drop_avatar_tables.sql`) para eliminar la tabla `avatar_predefinido` (o como se llame la tabla de avatares).
    *   Eliminar la columna `avatar_url` (o similar) de la tabla `usuario`.
2.  **Revisar migraciones existentes:**
    *   Revisar [`src/main/resources/db/migration/V1008__fix_avatar_data.sql`](backend/src/main/resources/db/migration/V1008__fix_avatar_data.sql) y [`src/main/resources/db/migration/V1007__add_perfil_fields_to_usuario.sql`](backend/src/main/resources/db/migration/V1007__add_perfil_fields_to_usuario.sql) para entender cómo se manejaban los avatares y asegurar que no haya dependencias residuales. Si es posible, se podrían modificar o crear nuevas migraciones para revertir estos cambios de forma limpia.

### Fase 3: Frontend (Adaptación de UI y lógica)

1.  **Crear un componente de Avatar de Iniciales:**
    *   Crear un nuevo componente (por ejemplo, `components/common/InitialsAvatar.tsx`) que reciba el nombre y el apellido del usuario y genere un icono con las iniciales. Este componente debe ser reutilizable.
    *   Implementar la lógica para extraer la primera letra del nombre y la primera letra del apellido.
    *   Aplicar un estilo simple (por ejemplo, un círculo con un color de fondo y texto blanco centrado).
2.  **Actualizar componentes de perfil:**
    *   Modificar [`components/perfil/PerfilModal.tsx`](frontend/src/components/perfil/PerfilModal.tsx) y [`components/perfil/ProfileForm.tsx`](frontend/src/components/perfil/ProfileForm.tsx) para:
        *   Eliminar cualquier UI relacionada con la selección o visualización de avatares.
        *   Reemplazar las referencias al avatar existente con el nuevo componente `InitialsAvatar`, pasándole el nombre y apellido del usuario.
    *   Modificar [`pages/perfil/PerfilPage.tsx`](frontend/src/pages/perfil/PerfilPage.tsx) para reflejar los cambios en los componentes de perfil.
3.  **Eliminar componentes y hooks de avatar:**
    *   Eliminar [`components/perfil/AvatarSelector.tsx`](frontend/src/components/perfil/AvatarSelector.tsx).
    *   Eliminar [`hooks/useAvatares.ts`](frontend/src/hooks/useAvatares.ts).
    *   Eliminar [`services/avatarService.ts`](frontend/src/services/avatarService.ts).
    *   Eliminar cualquier importación o referencia a estos archivos en el resto del código.
4.  **Actualizar el componente `UserAvatar`:**
    *   Modificar [`components/usuarios/UserAvatar.tsx`](frontend/src/components/usuarios/UserAvatar.tsx) para que, en lugar de mostrar un avatar de imagen, utilice el nuevo componente `InitialsAvatar`. Este componente es clave ya que es probable que se use en múltiples lugares.
5.  **Actualizar tipos de perfil:**
    *   Modificar [`types/perfil.types.ts`](frontend/src/types/perfil.types.ts) para eliminar cualquier propiedad relacionada con el avatar.
6.  **Búsqueda y reemplazo exhaustiva en el frontend:**
    *   Realizar una búsqueda global en el directorio `frontend/src/` para cualquier referencia a "avatar", "avatarUrl", "fotoPerfil" o términos similares para identificar otros componentes de UI que puedan estar mostrando avatares y actualizarlos para usar `InitialsAvatar`.
7.  **Pruebas:**
    *   Realizar pruebas exhaustivas para asegurar que la eliminación de la funcionalidad de avatar no introduzca errores y que el nuevo icono de iniciales se muestre correctamente en todos los lugares relevantes.

## Diagrama de Flujo del Plan

```mermaid
graph TD
    A[Inicio] --> B{Recopilación de Información};
    B --> C{Análisis de Archivos Relevantes};
    C --> D[Fase 1: Backend - Eliminación de Lógica];
    D --> D1[Eliminar AvatarController];
    D --> D2[Eliminar AvatarService];
    D --> D3[Eliminar AvatarPredefinidoRepository];
    D --> D4[Eliminar AvatarPredefinido Model/DTO];
    D --> D5[Actualizar Usuario Model/PerfilUsuarioDTO];
    D --> D6[Limpiar Archivos Estáticos de Avatares];
    D --> E[Fase 2: Base de Datos - Limpieza];
    E --> E1[Crear Migración para Eliminar Tabla/Columna];
    E --> E2[Revisar Migraciones Existentes];
    E --> F[Fase 3: Frontend - Adaptación de UI];
    F --> F1[Crear InitialsAvatar Component];
    F --> F2[Actualizar Componentes de Perfil];
    F --> F3[Eliminar Componentes/Hooks/Servicios de Avatar];
    F --> F4[Actualizar UserAvatar Component];
    F --> F5[Actualizar Tipos de Perfil];
    F --> F6[Búsqueda Exhaustiva y Reemplazo];
    F --> G[Pruebas Exhaustivas];
    G --> H[Fin];