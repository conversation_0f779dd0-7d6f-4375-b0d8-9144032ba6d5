# Plan de Implementación: Menú P.N Recompensas

## Resumen Ejecutivo

Este documento detalla el plan completo para implementar el nuevo menú "P.N Recompensas" en el sistema CUFRE. El objetivo es crear una interfaz dedicada para gestionar las recompensas de expedientes con capacidades de edición inline, filtrado avanzado y soporte multi-moneda.

## Análisis del Sistema Actual

### Componentes Existentes Identificados

#### Backend
- ✅ **Campo `recompensa`** (Boolean) en [`Expediente.java:51`](backend/src/main/java/com/cufre/expedientes/model/Expediente.java)
- ✅ **Campo `montoRecompensa`** (String) en [`Expediente.java:54`](backend/src/main/java/com/cufre/expedientes/model/Expediente.java)
- ✅ **Sistema de prioridades** que considera recompensas en [`PriorityCalculator.java:226`](backend/src/main/java/com/cufre/expedientes/util/PriorityCalculator.java)
- ✅ **Endpoint "más buscados"** ordenado por prioridad en [`ExpedienteService.findMasBuscados():539`](backend/src/main/java/com/cufre/expedientes/service/ExpedienteService.java)

#### Frontend
- ✅ **Menú Tutoriales** en [`Sidebar.tsx:415`](frontend/src/components/layout/Sidebar.tsx) (referencia para nuevo menú)
- ✅ **Filtros existentes** en [`ExpedientesPage.tsx:383`](frontend/src/pages/expedientes/ExpedientesPage.tsx)
- ✅ **Display de recompensas** en múltiples componentes:
  - [`IterarMasBuscadosPage.tsx:200`](frontend/src/pages/expedientes/IterarMasBuscadosPage.tsx)
  - [`MasBuscadosPage.tsx:359`](frontend/src/pages/MasBuscadosPage.tsx)
  - [`ExpedienteDetallePage.tsx:526`](frontend/src/pages/expedientes/ExpedienteDetallePage.tsx)
  - [`ModalWanted.tsx:180`](frontend/src/components/expedientes/ModalWanted.tsx)

## Objetivos del Proyecto

1. **Crear menú "P.N Recompensas"** junto al menú "Tutoriales"
2. **Implementar tabla con 4 columnas**: Número Expediente, Nombre Prófugo, Causa, Recompensa
3. **Habilitar edición inline** del campo recompensa con selector de monedas
4. **Reutilizar filtros existentes** y agregar ordenamiento por prioridad
5. **Soporte multi-moneda** con formateo automático de símbolos

## Arquitectura de la Solución

```mermaid
graph TD
    A[Usuario accede a P.N Recompensas] --> B[PNRecompensasPage.tsx]
    B --> C[Carga datos via expedienteService]
    C --> D[Backend: /api/expedientes/pn-recompensas]
    D --> E[ExpedienteService.findPNRecompensas()]
    E --> F[Retorna expedientes paginados]
    F --> G[Tabla con RecompensaEditableCell]
    G --> H[Usuario edita recompensa inline]
    H --> I[PUT /api/expedientes/{id}/recompensa]
    I --> J[Actualiza BD y recalcula prioridad]
    J --> K[Refresca vista]
```

## Plan de Implementación Detallado

### Fase 1: Backend - Nuevos Endpoints

#### 1.1 Endpoint para P.N Recompensas
**Archivo:** [`ExpedienteController.java`](backend/src/main/java/com/cufre/expedientes/controller/ExpedienteController.java)

```java
@GetMapping("/pn-recompensas")
public ResponseEntity<Page<ExpedienteDTO>> getPNRecompensas(
    @RequestParam(defaultValue = "0") int page,
    @RequestParam(defaultValue = "10") int size,
    @RequestParam(defaultValue = "id") String sortBy,
    @RequestParam(defaultValue = "desc") String sortDir,
    // Filtros existentes reutilizados
    @RequestParam(required = false) String profugo,
    @RequestParam(required = false) String numero,
    @RequestParam(required = false) String fuerzaAsignada,
    @RequestParam(required = false) String estadoSituacion,
    @RequestParam(required = false) String fechaDesde,
    @RequestParam(required = false) String fechaHasta,
    @RequestParam(required = false) Long delitoId
) {
    return ResponseEntity.ok(expedienteService.findPNRecompensas(/* parámetros */));
}
```

#### 1.2 Endpoint para actualizar recompensa
```java
@PutMapping("/{id}/recompensa")
public ResponseEntity<ExpedienteDTO> actualizarRecompensa(
    @PathVariable Long id,
    @RequestBody RecompensaUpdateDTO recompensaDto
) {
    return ResponseEntity.ok(expedienteService.actualizarRecompensa(id, recompensaDto));
}
```

#### 1.3 Nuevo DTO para actualización
**Archivo:** `RecompensaUpdateDTO.java`
```java
public class RecompensaUpdateDTO {
    private Boolean recompensa;
    private String montoRecompensa;
    private String moneda;
    
    // getters y setters
}
```

#### 1.4 Método en ExpedienteService
**Archivo:** [`ExpedienteService.java`](backend/src/main/java/com/cufre/expedientes/service/ExpedienteService.java)
```java
@Transactional(readOnly = true)
public Page<ExpedienteDTO> findPNRecompensas(/* parámetros */) {
    // Implementar lógica similar a findAll pero optimizada para P.N Recompensas
    // Incluir joins necesarios para obtener nombre prófugo y causa
}

@Transactional
public ExpedienteDTO actualizarRecompensa(Long id, RecompensaUpdateDTO dto) {
    // Actualizar expediente
    // Recalcular prioridad usando PriorityCalculator
    // Registrar actividad del sistema
}
```

### Fase 2: Frontend - Nuevo Menú

#### 2.1 Actualizar Sidebar
**Archivo:** [`Sidebar.tsx`](frontend/src/components/layout/Sidebar.tsx)

**Ubicación:** Después del menú "Tutoriales" (línea 440)

```tsx
{/* P.N Recompensas */}
{canSee('pn-recompensas') && (
<ListItem disablePadding>
  <ListItemButton 
    selected={location.pathname.startsWith('/pn-recompensas')}
    onClick={() => handleNavigation('/pn-recompensas')}
    sx={{
      borderRadius: 2,
      mb: 1,
      px: 2,
      py: 1.5,
      color: '#fff',
      backgroundColor: location.pathname.startsWith('/pn-recompensas') ? 'rgba(255,255,255,0.08)' : 'transparent',
      '&:hover': {
        backgroundColor: 'rgba(255,255,255,0.15)',
        color: '#fff'
      }
    }}
  >
    <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
      <MonetizationOnIcon fontSize="medium" />
    </ListItemIcon>
    <ListItemText primary="P.N Recompensas" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
  </ListItemButton>
</ListItem>
)}
```

#### 2.2 Actualizar permisos
Agregar 'pn-recompensas' a la función `canSee()` con los mismos permisos que 'expedientes'.

### Fase 3: Frontend - Nueva Página

#### 3.1 Crear PNRecompensasPage.tsx
**Ubicación:** `frontend/src/pages/PNRecompensasPage.tsx`

**Estructura de la página:**
```tsx
const PNRecompensasPage: React.FC = () => {
  // Estados para datos y filtros (similar a ExpedientesPage)
  const [expedientes, setExpedientes] = useState<Expediente[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  
  // Estados para filtros (reutilizar lógica de ExpedientesPage)
  const [filtros, setFiltros] = useState({...});
  
  // Estado para ordenamiento con nueva opción "prioridad"
  const [orderBy, setOrderBy] = useState<string>('id');
  const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>('desc');

  return (
    <Box>
      {/* Header similar a ExpedientesPage */}
      <Paper sx={{ /* estilos header */ }}>
        <Typography variant="h4">P.N Recompensas</Typography>
      </Paper>
      
      {/* Barra de filtros reutilizada */}
      <FiltrosComponent />
      
      {/* Dropdown de ordenamiento con nueva opción */}
      <OrdenamientoDropdown opciones={[
        'id', 'numero', 'profugo', 'fechaIngreso', 'prioridad'
      ]} />
      
      {/* Tabla principal */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Número Expediente</TableCell>
              <TableCell>Nombre Prófugo</TableCell>
              <TableCell>Causa</TableCell>
              <TableCell>Recompensa</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {expedientes.map(exp => (
              <TableRow key={exp.id}>
                <TableCell>{exp.numero}</TableCell>
                <TableCell>{obtenerNombreProfugo(exp)}</TableCell>
                <TableCell>{obtenerCausa(exp)}</TableCell>
                <TableCell>
                  <RecompensaEditableCell 
                    expediente={exp}
                    onUpdate={handleUpdateRecompensa}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};
```

### Fase 4: Frontend - Componente de Edición Inline

#### 4.1 Crear RecompensaEditableCell.tsx
**Ubicación:** `frontend/src/components/expedientes/RecompensaEditableCell.tsx`

```tsx
interface RecompensaEditableCellProps {
  expediente: Expediente;
  onUpdate: (id: number, data: RecompensaUpdateData) => Promise<void>;
}

const RecompensaEditableCell: React.FC<RecompensaEditableCellProps> = ({ expediente, onUpdate }) => {
  const [editing, setEditing] = useState(false);
  const [recompensa, setRecompensa] = useState(expediente.recompensa || false);
  const [monto, setMonto] = useState(expediente.montoRecompensa || '');
  const [moneda, setMoneda] = useState('ARS'); // Por defecto Pesos Argentinos

  const handleSave = async () => {
    await onUpdate(expediente.id, { recompensa, montoRecompensa: monto, moneda });
    setEditing(false);
  };

  if (!editing) {
    return (
      <Box onClick={() => setEditing(true)} sx={{ cursor: 'pointer' }}>
        {recompensa && monto ? formatearRecompensa(monto, moneda) : 'Sin recompensa'}
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
      <Checkbox 
        checked={recompensa}
        onChange={(e) => setRecompensa(e.target.checked)}
      />
      {recompensa && (
        <>
          <TextField 
            size="small"
            value={monto}
            onChange={(e) => setMonto(e.target.value)}
            placeholder="Monto"
          />
          <Select
            size="small"
            value={moneda}
            onChange={(e) => setMoneda(e.target.value)}
          >
            {MONEDAS.map(m => (
              <MenuItem key={m.codigo} value={m.codigo}>
                {m.simbolo} {m.nombre}
              </MenuItem>
            ))}
          </Select>
        </>
      )}
      <IconButton size="small" onClick={handleSave}>
        <SaveIcon />
      </IconButton>
      <IconButton size="small" onClick={() => setEditing(false)}>
        <CancelIcon />
      </IconButton>
    </Box>
  );
};
```

### Fase 5: Frontend - Configuración de Monedas

#### 5.1 Crear monedaUtils.ts
**Ubicación:** `frontend/src/utils/monedaUtils.ts`

```typescript
export interface Moneda {
  codigo: string;
  simbolo: string;
  nombre: string;
}

export const MONEDAS: Moneda[] = [
  { codigo: 'ARS', simbolo: '$', nombre: 'Pesos Argentinos' },
  { codigo: 'USD', simbolo: 'USD', nombre: 'Dólares Estadounidenses' },
  { codigo: 'EUR', simbolo: '€', nombre: 'Euros' },
  { codigo: 'BRL', simbolo: 'R$', nombre: 'Real Brasileño' },
  { codigo: 'CLP', simbolo: '$', nombre: 'Peso Chileno' },
  { codigo: 'UYU', simbolo: '$U', nombre: 'Peso Uruguayo' },
  { codigo: 'BOB', simbolo: 'Bs', nombre: 'Boliviano' },
  { codigo: 'PYG', simbolo: '₲', nombre: 'Guaraní Paraguayo' }
];

/**
 * Formatea el monto de recompensa con el símbolo de moneda correspondiente
 * Ejemplo: formatearRecompensa("500000", "ARS") => "$500000"
 */
export const formatearRecompensa = (monto: string, moneda: string = 'ARS'): string => {
  if (!monto) return '';
  
  const monedaConfig = MONEDAS.find(m => m.codigo === moneda);
  const simbolo = monedaConfig?.simbolo || '$';
  
  // Formatear número con separadores de miles
  const montoFormateado = Number(monto).toLocaleString('es-AR');
  
  return `${simbolo}${montoFormateado}`;
};

/**
 * Extrae el monto numérico de un string formateado
 */
export const extraerMonto = (montoFormateado: string): string => {
  return montoFormateado.replace(/[^\d]/g, '');
};
```

### Fase 6: Frontend - Servicios API

#### 6.1 Actualizar expedienteService.ts
**Archivo:** [`expedienteService.ts`](frontend/src/api/expedienteService.ts)

```typescript
// Interfaces
interface PNRecompensasParams {
  page?: number;
  size?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
  profugo?: string;
  numero?: string;
  fuerzaAsignada?: string;
  estadoSituacion?: string;
  fechaDesde?: string;
  fechaHasta?: string;
  delitoId?: number;
}

interface RecompensaUpdateData {
  recompensa: boolean;
  montoRecompensa?: string;
  moneda?: string;
}

// Nuevos métodos
const expedienteService = {
  // ... métodos existentes ...

  // Obtener expedientes para P.N Recompensas
  getPNRecompensas: async (params: PNRecompensasParams): Promise<PaginatedResponse<Expediente>> => {
    try {
      const response = await axiosClient.get(apiRoutes.expedientes.pnRecompensas, { params });
      return response.data;
    } catch (error) {
      console.error('Error al obtener P.N Recompensas:', error);
      throw error;
    }
  },

  // Actualizar recompensa de expediente
  actualizarRecompensa: async (id: number, recompensaData: RecompensaUpdateData): Promise<Expediente> => {
    try {
      const response = await axiosClient.put(apiRoutes.expedientes.actualizarRecompensa(id), recompensaData);
      return response.data;
    } catch (error) {
      console.error(`Error al actualizar recompensa del expediente ${id}:`, error);
      throw error;
    }
  }
};
```

#### 6.2 Actualizar apiRoutes
```typescript
const apiRoutes = {
  expedientes: {
    // ... rutas existentes ...
    pnRecompensas: '/expedientes/pn-recompensas',
    actualizarRecompensa: (id: number) => `/expedientes/${id}/recompensa`
  }
};
```

### Fase 7: Frontend - Rutas y Navegación

#### 7.1 Actualizar rutas
**Archivo:** `frontend/src/routes/AppRoutes.tsx` (o archivo de rutas correspondiente)

```tsx
import PNRecompensasPage from '../pages/PNRecompensasPage';

// Agregar ruta
<Route path="/pn-recompensas" element={<PNRecompensasPage />} />
```

### Fase 8: Actualización de Componentes Existentes

#### 8.1 Actualizar componentes que muestran recompensas
Actualizar los siguientes archivos para usar la nueva función `formatearRecompensa`:

1. **IterarMasBuscadosPage.tsx** (línea 204)
2. **MasBuscadosPage.tsx** (línea 359)
3. **ExpedienteDetallePage.tsx** (línea 526)
4. **ModalWanted.tsx** (línea 180)

Cambiar de:
```tsx
{expediente.montoRecompensa}
```

A:
```tsx
{formatearRecompensa(expediente.montoRecompensa, expediente.moneda || 'ARS')}
```

### Fase 9: Base de Datos

#### 9.1 Migración para campo moneda (opcional)
Si se decide almacenar la moneda por separado:

**Archivo:** `V1006__add_moneda_to_expediente.sql`
```sql
ALTER TABLE EXPEDIENTE ADD COLUMN MONEDA VARCHAR(3) DEFAULT 'ARS';
```

### Fase 10: Testing y Validación

#### 10.1 Casos de prueba
1. **Navegación:** Verificar que el menú aparece y navega correctamente
2. **Carga de datos:** Verificar que la tabla carga expedientes correctamente
3. **Filtros:** Verificar que todos los filtros funcionan
4. **Ordenamiento:** Verificar ordenamiento por prioridad
5. **Edición inline:** Verificar edición de recompensas
6. **Formateo de monedas:** Verificar que se muestran correctamente
7. **Permisos:** Verificar que solo usuarios autorizados pueden acceder

#### 10.2 Validaciones backend
1. **Validación de montos:** Solo números positivos
2. **Validación de monedas:** Solo códigos válidos
3. **Recálculo de prioridad:** Verificar que se actualiza correctamente
4. **Auditoría:** Verificar que se registran los cambios

## Estructura de Archivos Nuevos

```
frontend/src/
├── pages/
│   └── PNRecompensasPage.tsx
├── components/
│   └── expedientes/
│       └── RecompensaEditableCell.tsx
├── types/
│   └── recompensa.types.ts
└── utils/
    └── monedaUtils.ts

backend/src/main/java/com/cufre/expedientes/
├── dto/
│   └── RecompensaUpdateDTO.java
├── controller/
│   └── ExpedienteController.java (modificar)
└── service/
    └── ExpedienteService.java (modificar)
```

## Consideraciones Técnicas

### Seguridad
- Validar permisos de usuario para editar recompensas
- Sanitizar inputs de montos
- Auditar cambios en el sistema de actividad existente

### Performance
- Implementar paginación eficiente
- Optimizar queries con joins necesarios
- Considerar caché para lista de monedas

### UX/UI
- Feedback visual durante edición
- Validación en tiempo real
- Mensajes de error claros
- Responsive design

### Mantenibilidad
- Código reutilizable entre componentes
- Documentación de APIs
- Tests unitarios para funciones críticas

## Cronograma Estimado

1. **Fase 1-2 (Backend + Menú):** 1-2 días
2. **Fase 3-4 (Página + Edición):** 2-3 días
3. **Fase 5-6 (Monedas + API):** 1 día
4. **Fase 7-8 (Rutas + Updates):** 1 día
5. **Fase 9-10 (BD + Testing):** 1-2 días

**Total estimado:** 6-9 días de desarrollo

## Conclusión

Este plan proporciona una implementación completa del menú "P.N Recompensas" que:

- ✅ Reutiliza la infraestructura existente
- ✅ Mantiene consistencia con el diseño actual
- ✅ Proporciona funcionalidad de edición eficiente
- ✅ Soporta múltiples monedas con formateo correcto
- ✅ Incluye filtros y ordenamiento avanzado
- ✅ Mantiene la integridad del sistema de prioridades

La implementación seguirá las mejores prácticas del proyecto existente y proporcionará una experiencia de usuario fluida para la gestión de recompensas.