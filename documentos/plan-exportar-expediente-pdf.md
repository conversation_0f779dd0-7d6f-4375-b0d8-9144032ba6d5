### Plan de Implementación: Exportar Expediente a PDF

El objetivo es reemplazar la funcionalidad de "imprimir informe" por una de "Exportar a PDF" en la vista de detalles del expediente. Este informe PDF debe ser elegante, bien organizado, incluir todos los detalles del expediente (nombre, apellido, foto principal del prófugo, galería de fotos, documentos adjuntos), adaptarse a A4, y ser confidencial. Además, se debe registrar cada descarga de informe en la sección "Actividad del sistema".

**Consideraciones Clave:**

*   **Generación de PDF en el Backend:** La complejidad del diseño y la inclusión de imágenes y documentos adjuntos hacen que la generación del PDF en el backend (Java) sea la opción más robusta y controlable. Se utilizará **Apache PDFBox** para construir el documento.
*   **Diseño del Informe:** El informe debe ser visualmente atractivo y fácil de leer, adaptándose a un formato A4. Se priorizará la información del prófugo (nombre, apellido, foto principal) y luego el resto de los detalles del expediente.
*   **Inclusión de Contenido:**
    *   **Texto:** Todos los campos relevantes del expediente.
    *   **Imágenes:** Foto principal del prófugo y galería de fotos. Se deberán obtener las URLs de las imágenes y cargarlas en el backend para incrustarlas en el PDF.
    *   **Documentos Adjuntos:** Los documentos adjuntos se incluirán como archivos adjuntos al PDF principal. Esto requerirá que el backend pueda acceder a estos documentos.
*   **Confidencialidad:** Se incluirá una marca de agua o texto visible que indique "CONFIDENCIAL" en el informe.
*   **Registro de Actividad:** Cada vez que se descargue un informe, se registrará una entrada en la tabla `ActividadSistema` para que sea visible en la página "Actividad del sistema".

---

#### **Fases del Plan:**

**Fase 1: Desarrollo Backend (Java - Spring Boot)**

1.  **Añadir Dependencia para Generación de PDF:**
    *   Se agregará la dependencia de Apache PDFBox al `pom.xml` del proyecto `backend`.

    ```xml
    <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox</artifactId>
        <version>2.0.29</version> <!-- Usar la última versión estable -->
    </dependency>
    <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>fontbox</artifactId>
        <version>2.0.29</version> <!-- Necesario para manejar fuentes -->
    </dependency>
    ```

2.  **Crear un Nuevo Endpoint en `ExpedienteController`:**
    *   Se creará un nuevo método `GET` en `ExpedienteController.java` (o un nuevo controlador `ReporteController.java` si se prefiere una separación de responsabilidades más clara para reportes) que reciba el ID del expediente y devuelva el PDF generado.
    *   **Ruta Sugerida:** `/api/expedientes/{id}/pdf`
    *   Este endpoint deberá:
        *   Recuperar todos los detalles del expediente utilizando `ExpedienteService`, incluyendo la información de la persona (prófugo), fotografías y documentos adjuntos.
        *   Manejar la obtención de las imágenes de las fotografías. Si las imágenes están almacenadas localmente, se accederá a ellas directamente. Si son URLs externas, se deberá implementar una lógica para descargarlas temporalmente.
        *   Manejar la obtención de los documentos adjuntos. Se asumirá que los documentos están almacenados localmente y se accederá a ellos por su ruta.
        *   Invocar al `PdfGeneratorService` (a crear en el siguiente paso) para generar el PDF.
        *   Establecer los encabezados HTTP adecuados para la descarga del PDF (`Content-Type: application/pdf`, `Content-Disposition: attachment; filename="expediente_{id}.pdf"`).

3.  **Implementar Servicio de Generación de PDF (`PdfGeneratorService.java`):**
    *   Se creará un nuevo servicio `PdfGeneratorService.java` que contenga la lógica para construir el PDF utilizando Apache PDFBox.
    *   Este servicio se encargará de:
        *   Inicializar un nuevo documento `PDDocument`.
        *   Crear páginas `PDPage` con formato A4.
        *   Utilizar `PDPageContentStream` para dibujar texto, imágenes y formas.
        *   **Diseño del Informe:**
            *   **Encabezado:** Título del informe, logo de la institución (si aplica).
            *   **Información del Prófugo:** Nombre, apellido, y la foto principal de manera destacada. Se deberá manejar el escalado de la imagen para que se ajuste a la página.
            *   **Galería de Fotos:** Miniaturas o imágenes de tamaño reducido de la galería de fotos.
            *   **Detalles del Expediente:** Todos los campos relevantes del expediente formateados de manera legible (ej. en tablas o listas).
            *   **Pie de Página:** Numeración de página, fecha de generación.
        *   **Confidencialidad:** Añadir una marca de agua "CONFIDENCIAL" en cada página. Esto se puede lograr dibujando texto con baja opacidad o utilizando una imagen de marca de agua.
        *   **Adjuntar Documentos:** Utilizar la funcionalidad de adjuntos de PDFBox para incrustar los documentos originales (PDFs, imágenes, etc.) como archivos adjuntos al PDF principal. Esto es diferente a incrustar el contenido visualmente.
        *   Devolver un `ByteArrayOutputStream` con el contenido del PDF.

4.  **Integración con `ActividadSistemaService`:**
    *   Dentro del nuevo endpoint de generación de PDF en `ExpedienteController` (o `ReporteController`), se invocará al `ActividadSistemaService` para registrar la acción de "descarga de informe".
    *   Se definirá un nuevo `TipoAccion` y `CategoriaAccion` para esta actividad en los enums correspondientes (ej. `TIPO_ACCION.DESCARGA_INFORME`, `CATEGORIA_ACCION.EXPEDIENTE`).
    *   El registro incluirá el ID del expediente y el usuario que realizó la descarga.

**Fase 2: Desarrollo Frontend (React - TypeScript)**

1.  **Modificar `ExpedienteDetallePage.tsx`:**
    *   Se identificará el botón o la acción actual de "imprimir informe".
    *   Se modificará el `onClick` de este botón para que, en lugar de `window.print()`, realice una llamada asíncrona al nuevo endpoint del backend (`/api/expedientes/{id}/pdf`).
    *   Se utilizará `fetch` o `axios` (si ya está en el proyecto) para realizar la llamada.
    *   La respuesta del backend será un `blob` (el archivo PDF). Se creará un URL de objeto (`URL.createObjectURL`) para el blob y se utilizará para crear un enlace de descarga (`<a>` tag) que se activará programáticamente (`link.click()`).
    *   Se manejarán posibles estados de carga y errores en la UI.

2.  **Actualizar `expedienteService.ts` (o crear `reporteService.ts`):**
    *   Se añadirá un nuevo método (ej. `exportarExpedienteAPDF(id: string)`) en `frontend/src/api/expedienteService.ts` (o un nuevo archivo de servicio como `frontend/src/api/reporteService.ts` si se decide separar la lógica de reportes).
    *   Este método realizará la llamada HTTP al backend y configurará la respuesta para ser tratada como un `blob` (`response.blob()`).
    *   Ejemplo de estructura:

    ```typescript
    // frontend/src/api/expedienteService.ts
    import axios from 'axios'; // O fetch

    const API_URL = '/api/expedientes';

    export const expedienteService = {
        // ... otros métodos existentes
        exportarExpedienteAPDF: async (id: string): Promise<Blob> => {
            try {
                const response = await axios.get(`${API_URL}/${id}/pdf`, {
                    responseType: 'blob', // Importante para recibir el archivo como blob
                });
                return response.data;
            } catch (error) {
                console.error('Error al exportar expediente a PDF:', error);
                throw error;
            }
        },
    };
    ```

**Fase 3: Pruebas y Refinamiento**

1.  **Pruebas Unitarias y de Integración:**
    *   **Backend:**
        *   Pruebas unitarias para `PdfGeneratorService` para verificar la generación correcta del PDF, inclusión de contenido, marca de agua y adjuntos.
        *   Pruebas de integración para el endpoint en `ExpedienteController` para asegurar que recupera los datos correctamente, invoca al servicio de PDF y devuelve la respuesta HTTP adecuada.
    *   **Frontend:**
        *   Pruebas unitarias para el método `exportarExpedienteAPDF` en `expedienteService.ts`.
        *   Pruebas de integración para `ExpedienteDetallePage.tsx` para verificar que el botón activa la descarga del PDF correctamente.
2.  **Pruebas de Diseño y Contenido:**
    *   Descargar PDFs generados con diferentes tipos de expedientes (con y sin fotos, con y sin documentos adjuntos, con muchos detalles, etc.).
    *   Verificar que el PDF generado cumpla con los requisitos de diseño (A4, elegancia, organización, fuentes legibles).
    *   Confirmar que todos los detalles del expediente, imágenes y documentos adjuntos se incluyan correctamente y sean visibles.
    *   Asegurar que la marca de agua de confidencialidad sea visible en todas las páginas.
3.  **Pruebas de Registro de Actividad:**
    *   Realizar descargas de informes y verificar que cada acción quede registrada en la tabla `ActividadSistema` y sea visible en la página "Actividad del sistema" con los `TipoAccion` y `CategoriaAccion` correctos.

---

#### **Diagrama de Flujo (Mermaid):**

```mermaid
graph TD
    A[Usuario en ExpedienteDetallePage] --> B{Clic en "Exportar a PDF"};
    B --> C[Frontend: Llama a API de Backend para generar PDF];
    C --> D[Backend: ExpedienteController/ReporteController];
    D --> E[Backend: Recupera datos del Expediente (Persona, Fotos, Documentos) via ExpedienteService];
    E --> F[Backend: PdfGeneratorService (usando Apache PDFBox)];
    F --> G[PdfGeneratorService: Genera PDF (A4, diseño, imágenes, marca de agua)];
    F --> H[PdfGeneratorService: Adjunta documentos originales al PDF];
    G & H --> I[Backend: Envía PDF como respuesta HTTP (blob)];
    I --> J[Frontend: Recibe PDF y lo descarga (URL.createObjectURL)];
    D --> K[Backend: ActividadSistemaService.registrarActividad()];
    K --> L[Base de Datos: Registra actividad de descarga];
    L --> M[Página "Actividad del sistema": Muestra el log];