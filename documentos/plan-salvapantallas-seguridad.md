# Plan de Implementación: Salvapantallas de Seguridad

## Objetivo
Implementar un salvapantallas que se active automáticamente después de 2 minutos de inactividad del usuario para proteger información sensible. El salvapantallas mostrará un fondo blanco con el logo de CUFRE centrado.

## Análisis del Sistema Actual

### Tecnologías Identificadas
- **Frontend**: React 18.3.1 con TypeScript
- **UI Framework**: Material-UI (MUI) v7.1.0
- **Routing**: React Router DOM v6.30.0
- **Estado**: Context API (AuthContext, ModalContext)
- **Gestión de Sesión**: SessionManager existente

### Estructura del Proyecto
```
frontend/src/
├── App.tsx (Punto de entrada principal)
├── components/
│   └── SessionManager.tsx (Gestión de sesiones existente)
├── context/
│   ├── AuthContext.tsx
│   └── ModalContext.tsx
├── hooks/
├── pages/
└── styles/
```

### Logo Disponible
- **Ubicación**: `/Users/<USER>/Documents/CodigoFuente/cufre/backend/src/images/Logo CUFRE.jpg`
- **Necesidad**: Copiar al directorio público del frontend para acceso web

## Diseño de la Solución

### 1. Componente Salvapantallas (`ScreenSaver.tsx`)

#### Funcionalidades
- **Detección de Inactividad**: Monitoreo de eventos del mouse, teclado y touch
- **Activación Automática**: Después de 2 minutos (120,000ms) sin actividad
- **Interfaz Visual**: Fondo blanco con logo CUFRE centrado
- **Desactivación**: Al detectar cualquier actividad del usuario
- **Integración**: Con el sistema de autenticación existente

#### Eventos a Monitorear
```typescript
const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove', 
  'keypress',
  'scroll',
  'touchstart',
  'click'
];
```

#### Estados del Componente
- `isActive`: Boolean que indica si el salvapantallas está activo
- `lastActivity`: Timestamp de la última actividad detectada
- `timer`: Referencia al temporizador de inactividad

### 2. Hook Personalizado (`useIdleTimer.ts`)

#### Responsabilidades
- Gestión del temporizador de inactividad
- Registro y limpieza de event listeners
- Lógica de detección de actividad
- Callbacks para activación/desactivación

#### Configuración
```typescript
interface IdleTimerConfig {
  timeout: number; // 120000ms (2 minutos)
  onIdle: () => void;
  onActive: () => void;
  events: string[];
}
```

### 3. Context de Salvapantallas (`ScreenSaverContext.tsx`)

#### Estado Global
- Control centralizado del estado del salvapantallas
- Integración con AuthContext para manejo de sesiones
- Configuración global de timeouts

#### Funciones Expuestas
```typescript
interface ScreenSaverContextType {
  isScreenSaverActive: boolean;
  activateScreenSaver: () => void;
  deactivateScreenSaver: () => void;
  resetTimer: () => void;
}
```

## Implementación Técnica

### Fase 1: Preparación de Assets
1. **Copiar Logo**: Mover `Logo CUFRE.jpg` a `frontend/public/images/`
2. **Optimización**: Verificar tamaño y formato del logo
3. **Fallback**: Preparar logo alternativo en caso de error de carga

### Fase 2: Desarrollo de Componentes

#### 2.1 Hook useIdleTimer
```typescript
// frontend/src/hooks/useIdleTimer.ts
export const useIdleTimer = (config: IdleTimerConfig) => {
  // Implementación de detección de inactividad
  // Gestión de event listeners
  // Control de temporizadores
};
```

#### 2.2 Componente ScreenSaver
```typescript
// frontend/src/components/ScreenSaver.tsx
export const ScreenSaver: React.FC = () => {
  // Interfaz visual del salvapantallas
  // Fondo blanco con logo centrado
  // Animaciones suaves de entrada/salida
};
```

#### 2.3 Context Provider
```typescript
// frontend/src/context/ScreenSaverContext.tsx
export const ScreenSaverProvider: React.FC<{children: ReactNode}> = ({children}) => {
  // Estado global del salvapantallas
  // Integración con useIdleTimer
  // Gestión de activación/desactivación
};
```

### Fase 3: Integración con App Principal

#### 3.1 Modificación de App.tsx
```typescript
// Agregar ScreenSaverProvider al árbol de contextos
<BrowserRouter>
  <AuthProvider>
    <ModalProvider>
      <ScreenSaverProvider>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <SessionManager>
            <AppRoutes />
            <ScreenSaver /> {/* Componente global */}
          </SessionManager>
        </ThemeProvider>
      </ScreenSaverProvider>
    </ModalProvider>
  </AuthProvider>
</BrowserRouter>
```

#### 3.2 Estilos CSS
```css
/* frontend/src/styles/ScreenSaver.css */
.screensaver-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: white;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.5s ease-in-out;
}

.screensaver-logo {
  max-width: 300px;
  max-height: 200px;
  object-fit: contain;
  animation: pulse 2s infinite;
}
```

### Fase 4: Configuración y Optimización

#### 4.1 Configuración de Timeouts
```typescript
// frontend/src/config/screensaver.ts
export const SCREENSAVER_CONFIG = {
  IDLE_TIMEOUT: 2 * 60 * 1000, // 2 minutos
  LOGO_PATH: '/images/Logo CUFRE.jpg',
  FADE_DURATION: 500, // ms
  PULSE_ANIMATION: true
};
```

#### 4.2 Integración con SessionManager
- Coordinar con el sistema de gestión de sesiones existente
- Evitar conflictos con timeouts de autenticación
- Mantener sincronización con el estado de autenticación

## Consideraciones de Seguridad

### 1. Protección de Información
- **Ocultación Inmediata**: El salvapantallas debe cubrir completamente la pantalla
- **Sin Información Sensible**: No mostrar datos del usuario en el salvapantallas
- **Desactivación Segura**: Requerir actividad explícita para desactivar

### 2. Integración con Autenticación
- **Respeto de Sesiones**: No interferir con el sistema de autenticación JWT
- **Logout Automático**: Considerar logout después de períodos prolongados
- **Sincronización**: Coordinar con timeouts de sesión del backend

### 3. Prevención de Bypass
- **Event Listeners Globales**: Capturar todos los tipos de actividad
- **Z-Index Alto**: Asegurar que el salvapantallas esté siempre en primer plano
- **Deshabilitación de Interacciones**: Bloquear interacciones con elementos subyacentes

## Casos de Uso y Escenarios

### Escenario 1: Usuario Inactivo
1. Usuario deja de interactuar con la aplicación
2. Después de 2 minutos, se activa el salvapantallas
3. Pantalla se cubre con fondo blanco y logo CUFRE
4. Usuario mueve el mouse → salvapantallas se desactiva

### Escenario 2: Navegación Activa
1. Usuario navega activamente por la aplicación
2. Timer se resetea con cada interacción
3. Salvapantallas nunca se activa mientras hay actividad

### Escenario 3: Sesión Expirada
1. Salvapantallas activo por período prolongado
2. Sesión JWT expira en el backend
3. Al desactivar salvapantallas, usuario es redirigido al login

## Pruebas y Validación

### Pruebas Funcionales
1. **Activación por Timeout**: Verificar activación después de 2 minutos
2. **Desactivación por Actividad**: Confirmar desactivación con diferentes eventos
3. **Reset de Timer**: Validar que la actividad resetea el contador
4. **Carga de Logo**: Verificar carga correcta del logo CUFRE

### Pruebas de Integración
1. **Compatibilidad con SessionManager**: Sin conflictos con gestión de sesiones
2. **Navegación**: Funcionamiento correcto en todas las rutas
3. **Responsive**: Comportamiento adecuado en diferentes tamaños de pantalla

### Pruebas de Seguridad
1. **Cobertura Completa**: Verificar que no se puede ver contenido subyacente
2. **Bypass Prevention**: Intentar evadir el salvapantallas con diferentes métodos
3. **Performance**: Impacto mínimo en el rendimiento de la aplicación

## Cronograma de Implementación

### Día 1: Preparación y Setup
- [ ] Copiar y optimizar logo CUFRE
- [ ] Crear estructura de archivos
- [ ] Configurar tipos TypeScript

### Día 2: Desarrollo Core
- [ ] Implementar hook useIdleTimer
- [ ] Desarrollar componente ScreenSaver
- [ ] Crear ScreenSaverContext

### Día 3: Integración
- [ ] Integrar con App.tsx
- [ ] Conectar con SessionManager
- [ ] Implementar estilos CSS

### Día 4: Testing y Refinamiento
- [ ] Pruebas funcionales
- [ ] Ajustes de UX/UI
- [ ] Optimización de performance

### Día 5: Deployment y Documentación
- [ ] Pruebas finales
- [ ] Documentación de uso
- [ ] Deploy a producción

## Archivos a Crear/Modificar

### Nuevos Archivos
1. `frontend/src/hooks/useIdleTimer.ts`
2. `frontend/src/components/ScreenSaver.tsx`
3. `frontend/src/context/ScreenSaverContext.tsx`
4. `frontend/src/styles/ScreenSaver.css`
5. `frontend/src/config/screensaver.ts`
6. `frontend/public/images/Logo CUFRE.jpg`

### Archivos a Modificar
1. `frontend/src/App.tsx` - Integrar ScreenSaverProvider
2. `frontend/src/components/SessionManager.tsx` - Coordinación opcional

## Consideraciones Adicionales

### Accesibilidad
- Soporte para lectores de pantalla
- Navegación por teclado para desactivación
- Contraste adecuado para usuarios con discapacidades visuales

### Performance
- Uso eficiente de event listeners
- Limpieza adecuada de timers y listeners
- Lazy loading del logo si es necesario

### Configurabilidad
- Timeout configurable por rol de usuario
- Posibilidad de deshabilitar para ciertos usuarios
- Configuración de logo alternativo por organización

## Conclusión

Esta implementación proporcionará una capa adicional de seguridad para proteger información sensible cuando los usuarios dejen sus estaciones de trabajo desatendidas. La solución se integra de manera no intrusiva con la arquitectura existente y mantiene la experiencia de usuario fluida mientras garantiza la protección de datos confidenciales.

La implementación seguirá las mejores prácticas de React y TypeScript, asegurando mantenibilidad y escalabilidad del código. El sistema será robusto, eficiente y fácil de configurar según las necesidades específicas de seguridad de CUFRE.