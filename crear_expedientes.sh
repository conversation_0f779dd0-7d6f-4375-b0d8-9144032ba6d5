#!/bin/bash

TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.MGgSfM-hdzI-W6U2jh7a-jxrAGSrq7CjmQKLVMEv6Bw"
PERSONA_ID=1

FUERZAS=("S/D" "GNA" "PFA" "PSA" "PNA" "SPF" "POL LOCAL" "INTERPOL" "AMERIPOL" "EUROPOL" "BLOQUE DE BÚSQUEDA CUFRE")

for i in $(seq 21 40); do
  NUM=$(printf "%04d" $i)
  if [ $((i % 2)) -eq 0 ]; then
    RECOMPENSA="false"
    MONTO=""
  else
    RECOMPENSA="true"
    MONTO=', "montoRecompensa": "10000"'
  fi

  FUERZA=${FUERZAS[$(( (i-21) % ${#FUERZAS[@]} ))]}

  JSON='{'
  JSON+="\"numero\": \"EXP-$NUM\"," 
  JSON+=" \"descripcion\": \"Expediente de prueba $i\"," 
  JSON+=" \"recompensa\": $RECOMPENSA"
  JSON+="$MONTO,"
  JSON+=" \"fuerzaAsignada\": \"$FUERZA\"," 
  JSON+=" \"estadoSituacion\": \"CAPTURA VIGENTE\"," 
  JSON+=" \"personaExpedientes\": ["
  JSON+="{\"personaId\": $PERSONA_ID, \"tipoRelacion\": \"IMPUTADO\"}"
  JSON+=']}'

  curl -s -X POST http://localhost:8080/api/expedientes \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "$JSON"
  echo
  sleep 0.2
  # Pequeña pausa para evitar saturar el backend

done 