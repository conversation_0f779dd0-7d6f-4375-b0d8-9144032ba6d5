#!/bin/bash

set -e

echo "==== [CUFRE] Despliegue automatizado ===="

# 1. Compilar backend
echo "==> Compilando backend..."
cd backend
mvn clean package -DskipTests

# 2. Compilar frontend
echo "==> Compilando frontend..."
cd ../frontend
npm install --legacy-peer-deps
npm run build

# 3. Verificar configuración de Nginx
echo "==> Verificando configuración de Nginx..."
if grep -q "proxy_pass http://backend:8080;" nginx.conf; then
  echo "Nginx configurado correctamente para backend:8080"
else
  echo "ERROR: nginx.conf no tiene proxy_pass http://backend:8080;"
  exit 1
fi

# 4. Volver a la raíz del proyecto
cd ..

# 5. Build y despliegue con Docker Compose
echo "==> Construyendo imágenes Docker..."
docker compose build

echo "==> Levantando servicios..."
docker compose up -d

# 6. Mostrar estado de los contenedores
echo "==> Estado de los contenedores:"
docker compose ps

echo "==> Logs recientes de backend:"
docker compose logs --tail=20 backend

echo "==> Logs recientes de nginx:"
docker compose logs --tail=20 nginx

echo "==== [CUFRE] Despliegue finalizado ===="
echo "Accede a la app en: http://localhost"
echo "API disponible en: http://localhost/api/" 