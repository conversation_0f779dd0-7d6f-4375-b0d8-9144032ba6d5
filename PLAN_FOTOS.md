# Plan para Solucionar Visualización de Imágenes en ExpedienteDetallePage

## 1. Diagnóstico del Problema

Las imágenes no se muestran en la página `ExpedienteDetallePage` porque se está utilizando una etiqueta `<img>` estándar para cargar las fotografías. Los recursos de imagen en esta aplicación están protegidos y requieren una cabecera de autenticación (`Authorization`) en la solicitud HTTP para poder acceder a ellos. La etiqueta `<img>` no envía esta cabecera por defecto.

La solución correcta, ya implementada en otras partes de la aplicación como `FotografiasTab.tsx`, es usar el componente personalizado `AuthenticatedImage`, que está diseñado para obtener la imagen adjuntando el token de autenticación necesario.

## 2. Plan de Acción Detallado

El objetivo es reemplazar todas las instancias de `<img>` usadas para mostrar fotografías del expediente por el componente `AuthenticatedImage` en el archivo `frontend/src/pages/expedientes/ExpedienteDetallePage.tsx`.

### Paso 1: Importar el Componente Necesario

Añadir la siguiente línea de importación al principio del archivo `frontend/src/pages/expedientes/ExpedienteDetallePage.tsx`:

```javascript
import AuthenticatedImage from '../../components/common/AuthenticatedImage';
```

### Paso 2: Actualizar la Foto Principal del Prófugo

Localizar el bloque de código que renderiza la foto principal (aproximadamente en la línea 441) y reemplazar la etiqueta `<img>` por `AuthenticatedImage`.

**Código Actual:**
```jsx
{fotoPrincipal && (
  <img
    src={getFotoUrl(fotoPrincipal)}
    alt="Foto principal del prófugo"
    style={{ height: 90, width: 70, objectFit: 'cover', borderRadius: 8, border: '2px solid #1976d2', background: '#fff' }}
  />
)}
```

**Código Modificado:**
```jsx
{fotoPrincipal && (
  <AuthenticatedImage
    src={getFotoUrl(fotoPrincipal)}
    alt="Foto principal del prófugo"
    style={{ height: 90, width: 70, objectFit: 'cover', borderRadius: 8, border: '2px solid #1976d2', background: '#fff' }}
  />
)}
```

### Paso 3: Actualizar la Galería de Fotos

Localizar el bloque de código que renderiza la galería de fotos (aproximadamente en la línea 455) y reemplazar la etiqueta `<img>` por `AuthenticatedImage`.

**Código Actual:**
```jsx
{fotosGaleria.map((foto, idx) => (
  <Box key={foto.id || idx} sx={{ cursor: 'pointer', ... }}
    onClick={() => { /* ... */ }}
  >
    <img src={getFotoUrl(foto)} alt={`Foto ${idx + 1}`} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
  </Box>
))}
```

**Código Modificado:**
```jsx
{fotosGaleria.map((foto, idx) => (
  <Box key={foto.id || idx} sx={{ cursor: 'pointer', ... }}
    onClick={() => { /* ... */ }}
  >
    <AuthenticatedImage 
      src={getFotoUrl(foto)} 
      alt={`Foto ${idx + 1}`} 
      style={{ width: '100%', height: '100%', objectFit: 'cover' }} 
    />
  </Box>
))}
```

### Paso 4: Actualizar el Modal de Vista Previa de la Galería

Finalmente, actualizar la imagen dentro del `Dialog` (modal) que muestra la foto en tamaño grande.

**Código Actual:**
```jsx
{fotoSeleccionada && (
  <img src={fotoSeleccionada} alt="Foto grande" style={{ maxWidth: '90vw', maxHeight: '80vh', ... }} />
)}
```

**Código Modificado:**
```jsx
{fotoSeleccionada && (
  <AuthenticatedImage 
    src={fotoSeleccionada} 
    alt="Foto grande" 
    style={{ maxWidth: '90vw', maxHeight: '80vh', ... }} 
  />
)}
```

## 3. Diagrama del Flujo de Trabajo

```mermaid
graph TD
    A[Inicio: Fotos no se muestran] --> B{Análisis: Se usa <img> sin autenticación};
    B --> C[Plan: Reemplazar <img> por AuthenticatedImage];
    C --> D{Paso 1: Importar AuthenticatedImage};
    D --> E{Paso 2: Modificar Foto Principal};
    E --> F{Paso 3: Modificar Galería};
    F --> G{Paso 4: Modificar Modal de Galería};
    G --> H[Fin: Fotos se muestran correctamente];
end