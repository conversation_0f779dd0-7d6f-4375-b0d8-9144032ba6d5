// Service Worker para redirigir solicitudes al puerto 8080 al puerto 80
self.addEventListener("fetch", (event) => {
  const url = new URL(event.request.url);
  
  // Si la solicitud es al puerto 8080, redirigirla al puerto 80
  if (url.port === "8080" && url.hostname === "************") {
    url.port = "80";
    const modifiedRequest = new Request(url.toString(), {
      method: event.request.method,
      headers: event.request.headers,
      body: event.request.body,
      mode: "cors",
      credentials: "include",
      redirect: "follow"
    });
    
    event.respondWith(
      fetch(modifiedRequest)
        .then(response => response)
        .catch(error => {
          console.error("Error en SW:", error);
          return new Response(JSON.stringify({ error: "Error de conexión" }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        })
    );
  }
});
