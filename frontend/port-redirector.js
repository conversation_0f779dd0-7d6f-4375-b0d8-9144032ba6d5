// Service Worker para redirigir peticiones al puerto 8080
console.log("Inyectando script de redirección para puerto 8080");

// Sobrescribir XMLHttpRequest para interceptar peticiones al puerto 8080
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
  // Verificar si la URL contiene el puerto 8080
  if (typeof url === "string" && url.includes(":8080")) {
    // Reemplazar puerto 8080 con puerto 80
    const newUrl = url.replace(":8080", "");
    console.log(`Redirigiendo petición de ${url} a ${newUrl}`);
    url = newUrl;
  }
  
  return originalXHROpen.apply(this, arguments);
};

// Sobrescribir fetch para interceptar peticiones al puerto 8080
const originalFetch = window.fetch;
window.fetch = function(resource, init) {
  if (typeof resource === "string" && resource.includes(":8080")) {
    const newResource = resource.replace(":8080", "");
    console.log(`Redirigiendo petición fetch de ${resource} a ${newResource}`);
    resource = newResource;
  } else if (resource instanceof Request && resource.url.includes(":8080")) {
    const newUrl = resource.url.replace(":8080", "");
    console.log(`Redirigiendo petición fetch de ${resource.url} a ${newUrl}`);
    resource = new Request(newUrl, resource);
  }
  
  return originalFetch.call(this, resource, init);
};

console.log("Script de redirección para puerto 8080 inyectado exitosamente");
