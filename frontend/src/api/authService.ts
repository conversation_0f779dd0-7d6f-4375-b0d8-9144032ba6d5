import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { Usuario } from '../types/usuario.types';

interface AuthResponse {
  token: string;
  // Agrega aquí otros campos que retorne tu backend si es necesario
}

const authService = {
  login: async (email: string, password: string) => {
    try {
      const response = await axiosClient.post(apiRoutes.auth.login, { email, password });
      const data = response.data as AuthResponse;
      if (data.token) {
        localStorage.setItem('user', JSON.stringify(data));
      }
      return data;
    } catch (error) {
      console.error('Error al iniciar sesión:', error);
      throw error;
    }
  },
  
  register: async (userData: any) => {
    try {
      const response = await axiosClient.post(apiRoutes.auth.register, userData);
      return response.data;
    } catch (error) {
      console.error('Error al registrar usuario:', error);
      throw error;
    }
  },
  
  logout: () => {
    localStorage.removeItem('user');
  },
  
  getCurrentUser: () => {
    const userStr = localStorage.getItem('user');
    if (userStr) return JSON.parse(userStr);
    return null;
  },
  
  updateProfile: async (userId: number, userData: any) => {
    try {
      const response = await axiosClient.put(apiRoutes.usuarios.update(userId), userData);
      return response.data;
    } catch (error) {
      console.error('Error al actualizar perfil:', error);
      throw error;
    }
  },
  
  getProfile: async () => {
    try {
      const userId = authService.getCurrentUser()?.usuario.id;
      if (!userId) {
        throw new Error('Usuario no autenticado');
      }
      const response = await axiosClient.get(apiRoutes.usuarios.getById(userId));
      return response.data;
    } catch (error) {
      console.error('Error al obtener perfil:', error);
      throw error;
    }
  },

  getUserInfo: async () => {
    try {
      const response = await axiosClient.get(apiRoutes.auth.me);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Error al obtener información del usuario');
      }
      throw new Error('Error al conectar con el servidor');
    }
  },

  verifyToken: async (token: string) => {
    try {
      const response = await axiosClient.post(apiRoutes.auth.verify, { token });
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Token inválido');
      }
      throw new Error('Error al conectar con el servidor');
    }
  },

  refreshToken: async () => {
    try {
      const response = await axiosClient.post(apiRoutes.auth.refresh);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Error al renovar la sesión');
      }
      throw new Error('Error al conectar con el servidor');
    }
  },

  changePassword: async (newPassword: string) => {
    await axiosClient.post(apiRoutes.auth.changePassword, { newPassword });
  },

  cambiarContrasena: async (currentPassword: string, newPassword: string) => {
    try {
      const response = await axiosClient.post(apiRoutes.auth.changePassword, {
        currentPassword,
        newPassword
      });
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Error al cambiar la contraseña');
      }
      throw new Error('Error al conectar con el servidor');
    }
  },

  obtenerQr2FA: async (): Promise<string> => {
    const response = await axiosClient.get(apiRoutes.auth.setup2FA);
    const data = response.data as { qrUrl: string };
    return data.qrUrl;
  },

  activar2FA: async (codigo: string): Promise<void> => {
    await axiosClient.post(apiRoutes.auth.activar2FA, { code: codigo });
  },

  validar2FA: async (codigo: string): Promise<{ token: string }> => {
    const response = await axiosClient.post(apiRoutes.auth.validar2FA, { code: codigo });
    return response.data as { token: string };
  },

  primerCambioContrasena: async (email: string, currentPassword: string, newPassword: string): Promise<any> => {
    const response = await axiosClient.post(apiRoutes.auth.firstPasswordChange, {
      email,
      currentPassword,
      newPassword
    });
    return response.data;
  },
};

export default authService; 