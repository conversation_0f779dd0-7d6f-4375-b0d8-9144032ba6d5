import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { 
  <PERSON><PERSON><PERSON>, 
  CrearAnuncioRequest, 
  MarcarVistoRequest, 
  AnuncioResponse 
} from '../types/anuncio.types';

const anuncioService = {
  /**
   * Obtiene todos los anuncios (solo para administradores)
   */
  getAll: async (): Promise<Anuncio[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.anuncios.getAll);
      return response.data as Anuncio[];
    } catch (error: any) {
      console.error('Error al obtener anuncios:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Obtiene un anuncio por ID (solo para administradores)
   */
  getById: async (id: number): Promise<Anuncio> => {
    try {
      const response = await axiosClient.get(apiRoutes.anuncios.getById(id));
      return response.data as <PERSON><PERSON><PERSON>;
    } catch (error: any) {
      console.error('Error al obtener anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Crea un nuevo anuncio (solo para administradores)
   */
  create: async (anuncio: CrearAnuncioRequest): Promise<Anuncio> => {
    try {
      const response = await axiosClient.post(apiRoutes.anuncios.create, anuncio);
      return response.data as Anuncio;
    } catch (error: any) {
      console.error('Error al crear anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Actualiza un anuncio existente (solo para administradores)
   */
  update: async (id: number, anuncio: CrearAnuncioRequest): Promise<Anuncio> => {
    try {
      const response = await axiosClient.put(apiRoutes.anuncios.update(id), anuncio);
      return response.data as Anuncio;
    } catch (error: any) {
      console.error('Error al actualizar anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Activa un anuncio específico (solo para administradores)
   */
  activar: async (id: number): Promise<Anuncio> => {
    try {
      const response = await axiosClient.put(apiRoutes.anuncios.activar(id));
      return response.data as Anuncio;
    } catch (error: any) {
      console.error('Error al activar anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Desactiva un anuncio específico (solo para administradores)
   */
  desactivar: async (id: number): Promise<Anuncio> => {
    try {
      const response = await axiosClient.put(apiRoutes.anuncios.desactivar(id));
      return response.data as Anuncio;
    } catch (error: any) {
      console.error('Error al desactivar anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Elimina un anuncio (solo para administradores)
   */
  delete: async (id: number): Promise<AnuncioResponse> => {
    try {
      const response = await axiosClient.delete(apiRoutes.anuncios.delete(id));
      return response.data as AnuncioResponse;
    } catch (error: any) {
      console.error('Error al eliminar anuncio:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Obtiene el anuncio activo para el usuario actual
   * Retorna null si no hay anuncio activo o si el usuario ya lo vio
   */
  obtenerActivo: async (): Promise<Anuncio | null> => {
    try {
      const response = await axiosClient.get(apiRoutes.anuncios.obtenerActivo);
      
      // Si la respuesta es 204 No Content, no hay anuncio activo
      if (response.status === 204 || !response.data) {
        return null;
      }
      
      return response.data as Anuncio;
    } catch (error: any) {
      // Si es un error 204, no hay anuncio activo
      if (error.response?.status === 204) {
        return null;
      }
      
      console.error('Error al obtener anuncio activo:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Marca un anuncio como visto por el usuario actual
   */
  marcarVisto: async (anuncioId: number): Promise<AnuncioResponse> => {
    try {
      const request: MarcarVistoRequest = { anuncioId };
      const response = await axiosClient.post(apiRoutes.anuncios.marcarVisto, request);
      return response.data as AnuncioResponse;
    } catch (error: any) {
      console.error('Error al marcar anuncio como visto:', error.response?.data || error.message);
      throw error;
    }
  },
};

export default anuncioService;