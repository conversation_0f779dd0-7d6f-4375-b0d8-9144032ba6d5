// Centralización de rutas de la API para CUFRE
// Usamos una cadena vacía para evitar duplicación de prefijos /api
// Las variables de entorno se manejarán en tiempo de compilación
const API_BASE_URL = '';

export const apiRoutes = {
  auth: {
    login: `${API_BASE_URL}/api/auth/login`,
    register: `${API_BASE_URL}/api/auth/register`,
    changePassword: `${API_BASE_URL}/api/auth/change-password`,
    firstPasswordChange: `${API_BASE_URL}/api/auth/first-password-change`,
    setup2FA: `${API_BASE_URL}/api/auth/2fa-setup`,
    activar2FA: `${API_BASE_URL}/api/auth/activar-2fa`,
    validar2FA: `${API_BASE_URL}/api/auth/validar-2fa`,
    me: `${API_BASE_URL}/api/auth/me`,
    verify: `${API_BASE_URL}/api/auth/verify`,
    refresh: `${API_BASE_URL}/api/auth/refresh`,
  },
  usuarios: {
    getAll: `${API_BASE_URL}/api/usuarios`,
    getById: (id: number) => `${API_BASE_URL}/api/usuarios/${id}`,
    create: `${API_BASE_URL}/api/usuarios`,
    update: (id: number) => `${API_BASE_URL}/api/usuarios/${id}`,
    delete: (id: number) => `${API_BASE_URL}/api/usuarios/${id}`,
    changePassword: (id: number) => `${API_BASE_URL}/api/usuarios/${id}/password`,
    findByEmail: (email: string) => `${API_BASE_URL}/api/usuarios/search/email?email=${encodeURIComponent(email)}`,
    findByRol: (rol: string) => `${API_BASE_URL}/api/usuarios/search/rol?rol=${encodeURIComponent(rol)}`,
    resetPasswordAnd2FA: (id: number) => `${API_BASE_URL}/api/usuarios/${id}/reset-password`,
    updateRoleWithPassword: (id: number) => `${API_BASE_URL}/api/usuarios/${id}/role`,
    forceLogout: (id: number) => `${API_BASE_URL}/api/usuarios/${id}/force-logout`,
    me: `${API_BASE_URL}/api/usuarios/me`,
  },
  expedientes: {
    base: `${API_BASE_URL}/api/expedientes`,
    getAll: `${API_BASE_URL}/api/expedientes`,
    getById: (id: number) => `${API_BASE_URL}/api/expedientes/${id}`,
    byId: (id: number) => `${API_BASE_URL}/api/expedientes/${id}`,
    create: `${API_BASE_URL}/api/expedientes`,
    update: (id: number) => `${API_BASE_URL}/api/expedientes/${id}`,
    delete: (id: number) => `${API_BASE_URL}/api/expedientes/${id}`,
    buscar: `${API_BASE_URL}/api/expedientes/buscar`,
    // Rutas para el sistema de bloqueo
    bloquear: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/bloquear`,
    desbloquear: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/bloquear`,
    verificarBloqueo: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/bloqueo`,
    // Rutas relacionadas
    fotografias: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/fotografias`,
    documentos: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/documentos`,
    personas: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/personas`,
    delitos: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/delitos`,
    // Rutas existentes
    getPaginated: () => `${API_BASE_URL}/api/expedientes/paginated`,
    searchByNumero: (numero: string) => `${API_BASE_URL}/api/expedientes/search/numero?numero=${encodeURIComponent(numero)}`,
    searchByPersona: (personaId: number) => `${API_BASE_URL}/api/expedientes/search/persona/${personaId}`,
    searchByDelito: (delitoId: number) => `${API_BASE_URL}/api/expedientes/search/delito/${delitoId}`,
    searchByFecha: (fechaInicio: string, fechaFin: string) => `${API_BASE_URL}/api/expedientes/search/fecha?fechaInicio=${fechaInicio}&fechaFin=${fechaFin}`,
    vinculos: `${API_BASE_URL}/api/expedientes/vinculos`,
    searchByCaratula: (caratula: string) => `${API_BASE_URL}/api/expedientes/search/caratula?caratula=${encodeURIComponent(caratula)}`,
    masBuscados: (limit: number = 10) => `${API_BASE_URL}/api/expedientes/mas-buscados?limit=${limit}`,
    busquedaAvanzada: `${API_BASE_URL}/api/expedientes/busqueda-avanzada`,
    pnRecompensas: `${API_BASE_URL}/api/expedientes/pn-recompensas`,
    actualizarRecompensa: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/recompensa`,
    exportarPDF: (id: number) => `${API_BASE_URL}/api/expedientes/${id}/pdf`,
  },
  delitos: {
    getAll: `${API_BASE_URL}/api/delitos`,
    getById: (id: number) => `${API_BASE_URL}/api/delitos/${id}`,
    create: `${API_BASE_URL}/api/delitos`,
    update: (id: number) => `${API_BASE_URL}/api/delitos/${id}`,
    delete: (id: number) => `${API_BASE_URL}/api/delitos/${id}`,
    searchDelitos: (query: string) => `${API_BASE_URL}/api/delitos/buscar?q=${encodeURIComponent(query)}`,
    getByLey: (ley: string) => `${API_BASE_URL}/api/delitos/ley/${encodeURIComponent(ley)}`,
    searchByNombre: (nombre: string) => `${API_BASE_URL}/api/delitos/search/nombre?nombre=${encodeURIComponent(nombre)}`,
    searchByCodigoCP: (codigoCP: string) => `${API_BASE_URL}/api/delitos/search/codigoCP?codigoCP=${encodeURIComponent(codigoCP)}`,
    searchByExpediente: (expedienteId: number) => `${API_BASE_URL}/api/delitos/search/expediente/${expedienteId}`,
  },
  personas: {
    getAll: `${API_BASE_URL}/api/personas`,
    getById: (id: number) => `${API_BASE_URL}/api/personas/${id}`,
    create: `${API_BASE_URL}/api/personas`,
    update: (id: number) => `${API_BASE_URL}/api/personas/${id}`,
    delete: (id: number) => `${API_BASE_URL}/api/personas/${id}`,
    search: (query: string) => `${API_BASE_URL}/api/personas/buscar?q=${encodeURIComponent(query)}`,
    getByDocumento: (documento: string) => `${API_BASE_URL}/api/personas/documento/${encodeURIComponent(documento)}`,
    getMediosComunicacion: (personaId: number) => `${API_BASE_URL}/api/personas/${personaId}/medios-comunicacion`,
    searchByDocumento: (numeroDocumento: string) => `${API_BASE_URL}/api/personas/search/documento?numeroDocumento=${encodeURIComponent(numeroDocumento)}`,
    searchByNombre: (nombre: string, apellido: string) => `${API_BASE_URL}/api/personas/search/nombre?nombre=${encodeURIComponent(nombre)}&apellido=${encodeURIComponent(apellido)}`,
    searchByExpediente: (expedienteId: number) => `${API_BASE_URL}/api/personas/search/expediente/${expedienteId}`,
  },
  estadisticas: {
    provincia: `${API_BASE_URL}/api/estadisticas/provincia`,
    estado: `${API_BASE_URL}/api/estadisticas/estado`,
    dashboard: `${API_BASE_URL}/api/estadisticas/dashboard`,
    generales: `${API_BASE_URL}/api/estadisticas/generales`,
    periodo: (inicio: string, fin: string) => `${API_BASE_URL}/api/estadisticas/periodo?inicio=${encodeURIComponent(inicio)}&fin=${encodeURIComponent(fin)}`,
    expedientesPorEstado: `${API_BASE_URL}/api/estadisticas/expedientes-por-estado`,
    expedientesPorFuerza: `${API_BASE_URL}/api/estadisticas/expedientes-por-fuerza`,
    expedientesPorEstadoYFuerza: `${API_BASE_URL}/api/estadisticas/expedientes-por-estado-y-fuerza/:fuerza`,
    expedientesPorFuerzaYEstado: `${API_BASE_URL}/api/estadisticas/expedientes-por-fuerza-y-estado/:estado`,
    delitosPorTipo: `${API_BASE_URL}/api/estadisticas/delitos-por-tipo`,
    expedientesPorPeriodo: `${API_BASE_URL}/api/estadisticas/expedientes-por-periodo`,
    expedientesPorProvincia: `${API_BASE_URL}/api/estadisticas/expedientes-por-provincia`,
    expedientesPorDelito: `${API_BASE_URL}/api/estadisticas/expedientes-por-delito`,
    expedientesPorTipoCaptura: `${API_BASE_URL}/api/estadisticas/expedientes-por-tipo-captura`,
    expedientesPorTiempo: `${API_BASE_URL}/api/estadisticas/expedientes-por-tiempo`,
    personas: `${API_BASE_URL}/api/estadisticas/personas`,
    reporteCompleto: `${API_BASE_URL}/api/estadisticas/reporte-completo`,
    exportar: `${API_BASE_URL}/api/estadisticas/exportar`,
    expedientesPorMes: `${API_BASE_URL}/api/estadisticas/expedientes-por-mes`,
    detenidosPorFuerza: `${API_BASE_URL}/api/estadisticas/detenidos-por-fuerza`,
    // Nuevos endpoints para el centro de comando mejorado
    metricasClave: `${API_BASE_URL}/api/estadisticas/metricas-clave`,
    expedientesPorEstadoFiltrado: `${API_BASE_URL}/api/estadisticas/expedientes-por-estado-filtrado`,
    expedientesPorFuerzaFiltrado: `${API_BASE_URL}/api/estadisticas/expedientes-por-fuerza-filtrado`,
    expedientesPorTipoCapturaFiltrado: `${API_BASE_URL}/api/estadisticas/expedientes-por-tipo-captura-filtrado`,
    expedientesPorMesFiltrado: `${API_BASE_URL}/api/estadisticas/expedientes-por-mes-filtrado`,
    delitosPorTipoFiltrado: `${API_BASE_URL}/api/estadisticas/delitos-por-tipo-filtrado`,
  },
  archivos: {
    subirFotografia: (expedienteId: number) => `/api/archivos/fotografias/${expedienteId}`,
    subirDocumento: (expedienteId: number) => `/api/archivos/documentos/${expedienteId}`,
    descargarFotografia: (id: number) => `/api/archivos/fotografias/${id}`,
    descargarDocumento: (id: number) => `/api/archivos/documentos/${id}`,
  },
  domicilios: {
    persona: (personaId: number) => `${API_BASE_URL}/api/domicilios/persona/${personaId}`,
    getById: (domicilioId: number) => `${API_BASE_URL}/api/domicilios/${domicilioId}`,
    create: `${API_BASE_URL}/api/domicilios`,
    update: (domicilioId: number) => `${API_BASE_URL}/api/domicilios/${domicilioId}`,
    delete: (domicilioId: number) => `${API_BASE_URL}/api/domicilios/${domicilioId}`,
  },
  mediosComunicacion: {
    persona: (personaId: number) => `${API_BASE_URL}/api/medios-comunicacion/persona/${personaId}`,
    getById: (medioId: number) => `${API_BASE_URL}/api/medios-comunicacion/${medioId}`,
    create: `${API_BASE_URL}/api/medios-comunicacion`,
    update: (medioId: number) => `${API_BASE_URL}/api/medios-comunicacion/${medioId}`,
    delete: (medioId: number) => `${API_BASE_URL}/api/medios-comunicacion/${medioId}`,
  },
  documentos: {
    getById: (id: number) => `/api/documentos/${id}`,
    delete: (id: number) => `/api/documentos/${id}`,
  },
  expedienteDelitos: {
    getDelitosPorExpediente: (expedienteId: number) => `${API_BASE_URL}/api/expedientes-delito/expediente/${expedienteId}`,
    delete: (id: number) => `${API_BASE_URL}/api/expedientes-delito/${id}`,
  },
  actividadSistema: {
    getAll: `${API_BASE_URL}/api/actividad-sistema`,
  },
  anuncios: {
    getAll: `${API_BASE_URL}/api/anuncios`,
    getById: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`,
    create: `${API_BASE_URL}/api/anuncios`,
    update: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`,
    activar: (id: number) => `${API_BASE_URL}/api/anuncios/${id}/activar`,
    desactivar: (id: number) => `${API_BASE_URL}/api/anuncios/${id}/desactivar`,
    delete: (id: number) => `${API_BASE_URL}/api/anuncios/${id}`,
    obtenerActivo: `${API_BASE_URL}/api/anuncios/activo`,
    marcarVisto: `${API_BASE_URL}/api/anuncios/visto`,
  },
};