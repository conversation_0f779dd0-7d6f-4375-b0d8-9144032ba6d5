import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { Expediente, <PERSON><PERSON><PERSON>, ExpedienteDelito } from '../types/expediente.types';
import { PNRecompensasParams, RecompensaUpdateData, PaginatedResponse } from '../types/recompensa.types';

const expedienteService = {
  // Obtener expedientes paginados
  getExpedientes: async (page = 0, size = 20, search = '', filters = {}): Promise<any> => {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        size: size.toString(),
        ...(search && { search })
      });
      
      // Agregar filtros si existen
      if (Object.keys(filters).length > 0) {
        Object.entries(filters as Record<string, any>).forEach(([key, value]) => {
          // Manejar específicamente los parámetros de ordenación
          if (key === 'sort' || key === 'orderBy') {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.set('sort', String(value));
            }
          } 
          else if (key === 'direction' || key === 'orderDirection') {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.set('direction', String(value));
            }
          }
          // Manejar el resto de filtros normalmente
          else if (value !== undefined && value !== null && value !== '') {
            queryParams.append(key, String(value));
          }
        });
      }
      
      // Asegurar que siempre haya valores por defecto para ordenación si no están definidos
      if (!queryParams.has('sort')) {
        queryParams.append('sort', 'id');
      }
      
      if (!queryParams.has('direction')) {
        queryParams.append('direction', 'desc');
      }
      
      // Construir la URL base del endpoint paginado
      const baseUrl = apiRoutes.expedientes.getPaginated();
      const finalUrl = `${baseUrl}?${queryParams}`;
      console.log('URL de solicitud:', finalUrl);
      
      const response = await axiosClient.get(finalUrl);
      console.log('Respuesta del endpoint paginado:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error al obtener expedientes paginados:', error);
      throw error;
    }
  },

  // Alias para getExpedientes sin paginación - para compatibilidad
  getAll: async (): Promise<Expediente[]> => {
    try {
      // Usar el endpoint paginado para evitar sobrecarga
      const response = await expedienteService.getExpedientes(0, 20);
      return response.expedientes || [];
    } catch (error) {
      console.error('Error al obtener todos los expedientes:', error);
      throw error;
    }
  },

  // Obtener un expediente por ID
  getExpedienteById: async (id: number): Promise<Expediente> => {
    try {
      const response = await axiosClient.get(apiRoutes.expedientes.getById(id));
      // Asegurarse de que las arrays existan aunque vengan vacías del servidor
      const data = response.data as Record<string, any>;
      
      // Construir el expediente con una aserción de tipo explícita
      const expediente = {
        ...data,
        fotografias: data.fotografias || [],
        documentos: data.documentos || [],
        personas: data.personas || [],
        delitos: data.delitos || []
      } as Expediente; // Aseguramos a TypeScript que el objeto cumple con la interfaz Expediente
      
      return expediente;
    } catch (error) {
      console.error(`Error al obtener expediente con ID ${id}:`, error);
      throw error;
    }
  },

  // Crear un nuevo expediente
  createExpediente: async (expediente: Expediente): Promise<Expediente> => {
    try {
      const response = await axiosClient.post(apiRoutes.expedientes.getAll, expediente);
      return response.data as Expediente;
    } catch (error) {
      console.error('Error al crear expediente:', error);
      throw error;
    }
  },

  // Alias para createExpediente - para compatibilidad
  create: async (expediente: Expediente): Promise<Expediente> => {
    return expedienteService.createExpediente(expediente);
  },

  // Actualizar un expediente existente
  updateExpediente: async (id: number, expediente: Expediente): Promise<Expediente> => {
    try {
      const response = await axiosClient.put(apiRoutes.expedientes.getById(id), expediente);
      return response.data as Expediente;
    } catch (error) {
      console.error(`Error al actualizar expediente con ID ${id}:`, error);
      throw error;
    }
  },

  // Eliminar un expediente
  deleteExpediente: async (id: number): Promise<void> => {
    try {
      await axiosClient.delete(apiRoutes.expedientes.getById(id));
    } catch (error) {
      console.error(`Error al eliminar expediente con ID ${id}:`, error);
      throw error;
    }
  },

  // Alias para deleteExpediente - para compatibilidad
  delete: async (id: number): Promise<void> => {
    return expedienteService.deleteExpediente(id);
  },

  // Gestión de fotografías
  getFotografias: async (expedienteId: number): Promise<any[]> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.getById(expedienteId)}/fotografias`);
      return response.data as any[];
    } catch (error) {
      console.error(`Error al obtener fotografías del expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  uploadFotografia: async (expedienteId: number, formData: FormData): Promise<any> => {
    try {
      const url = apiRoutes.archivos.subirFotografia(expedienteId);
      console.log('Subiendo fotografía a URL:', url);
      const response = await axiosClient.post(
        url,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error al subir fotografía al expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  deleteFotografia: async (expedienteId: number, fotografiaId: number): Promise<void> => {
    try {
      await axiosClient.delete(`/api/expedientes/${expedienteId}/fotografias/${fotografiaId}`);
    } catch (error) {
      console.error(`Error al eliminar fotografía con ID ${fotografiaId}:`, error);
      throw error;
    }
  },

  // Gestión de documentos
  getDocumentos: async (expedienteId: number): Promise<any[]> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.getById(expedienteId)}/documentos`);
      return response.data as any[];
    } catch (error) {
      console.error(`Error al obtener documentos del expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  uploadDocumento: async (expedienteId: number, formData: FormData): Promise<any> => {
    try {
      const response = await axiosClient.post(
        apiRoutes.archivos.subirDocumento(expedienteId),
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error al subir documento al expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  deleteDocumento: async (expedienteId: number, documentoId: number): Promise<void> => {
    try {
      await axiosClient.delete(`/api/expedientes/${expedienteId}/documentos/${documentoId}`);
    } catch (error) {
      console.error(`Error al eliminar documento con ID ${documentoId}:`, error);
      throw error;
    }
  },

  // Gestión de personas relacionadas
  getPersonas: async (expedienteId: number): Promise<any[]> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.getById(expedienteId)}/personas`);
      return response.data as any[];
    } catch (error) {
      console.error(`Error al obtener personas del expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  addPersona: async (expedienteId: number, persona: any): Promise<any> => {
    try {
      const response = await axiosClient.post(`${apiRoutes.expedientes.getById(expedienteId)}/personas`, persona);
      return response.data;
    } catch (error) {
      console.error(`Error al agregar persona al expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  updatePersona: async (id: number, persona: any): Promise<any> => {
    try {
      // Usar apiRoutes para evitar problemas con la URL
      console.log(`Actualizando persona con ID ${id}`, persona);
      const response = await axiosClient.put(apiRoutes.personas.update(id), persona);
      return response.data;
    } catch (error) {
      console.error(`Error al actualizar persona con ID ${id}:`, error);
      throw error;
    }
  },

  deletePersona: async (id: number): Promise<void> => {
    try {
      await axiosClient.delete(`${apiRoutes.personas}/${id}`);
    } catch (error) {
      console.error(`Error al eliminar persona con ID ${id}:`, error);
      throw error;
    }
  },

  addDomicilio: async (personaId: number, domicilio: any): Promise<any> => {
    try {
      // Eliminar el campo id si existe
      const { id, ...domicilioSinId } = domicilio;
      const response = await axiosClient.post(`${apiRoutes.domicilios.persona(personaId)}`, domicilioSinId);
      return response.data;
    } catch (error) {
      console.error(`Error al agregar domicilio a la persona con ID ${personaId}:`, error);
      throw error;
    }
  },

  updateDomicilio: async (domicilioId: number, domicilio: any): Promise<any> => {
    try {
      const response = await axiosClient.put(`${apiRoutes.domicilios.getById(domicilioId)}`, domicilio);
      return response.data;
    } catch (error) {
      console.error(`Error al actualizar domicilio con ID ${domicilioId}:`, error);
      throw error;
    }
  },

  deleteDomicilio: async (domicilioId: number): Promise<void> => {
    try {
      await axiosClient.delete(`${apiRoutes.domicilios.delete(domicilioId)}`);
    } catch (error) {
      console.error(`Error al eliminar domicilio con ID ${domicilioId}:`, error);
      throw error;
    }
  },

  // Búsquedas específicas
  searchByNumero: async (numero: string) => {
    const response = await axiosClient.get(`${apiRoutes.expedientes.searchByNumero}?numero=${numero}`);
    return response.data;
  },
  
  searchByCaratula: async (caratula: string) => {
    const response = await axiosClient.get(`${apiRoutes.expedientes.searchByCaratula}?caratula=${caratula}`);
    return response.data;
  },
  
  // Vínculos con personas
  getVinculos: async (expedienteId: number) => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.getById(expedienteId)}/vinculos`);
      return response.data;
    } catch (error: any) {
      console.error(`Error al obtener vínculos del expediente ${expedienteId}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener vínculos');
    }
  },
  
  createVinculo: async (vinculo: Vinculo) => {
    try {
      const response = await axiosClient.post(`${apiRoutes.expedientes.vinculos}`, vinculo);
      return response.data;
    } catch (error: any) {
      console.error('Error al crear vínculo:', error);
      throw new Error(error.response?.data?.message || 'Error al crear vínculo');
    }
  },
  
  addVinculo: async (vinculo: Vinculo) => {
    const response = await axiosClient.post(`${apiRoutes.expedientes.vinculos}`, vinculo);
    return response.data;
  },
  
  deleteVinculo: async (id: number) => {
    try {
      const response = await axiosClient.delete(`${apiRoutes.expedientes.vinculos}/${id}`);
      return response.data;
    } catch (error: any) {
      console.error(`Error al eliminar vínculo ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al eliminar vínculo');
    }
  },
  
  // Delitos asociados
  getDelitos: async (expedienteId: number) => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.getById(expedienteId)}/delitos`);
      return response.data;
    } catch (error: any) {
      console.error(`Error al obtener delitos del expediente ${expedienteId}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener delitos');
    }
  },
  
  createExpedienteDelito: async (expedienteDelito: ExpedienteDelito) => {
    const response = await axiosClient.post(`${apiRoutes.expedientes.getById(expedienteDelito.expedienteId)}/delitos`, expedienteDelito);
    return response.data;
  },
  
  deleteExpedienteDelito: async (id: number) => {
    const response = await axiosClient.delete(`${apiRoutes.expedientes.delitos}/${id}`);
    return response.data;
  },
  
  // Métodos específicos de búsqueda y filtrado
  buscarExpedientes: async (params: any) => {
    const response = await axiosClient.get(`${apiRoutes.expedientes.buscar}`, { params });
    return response.data;
  },
  
  // Otros métodos de utilidad
  changeStatus: async (id: number, estado: string) => {
    const response = await axiosClient.patch(`${apiRoutes.expedientes.getById(id)}/estado`, { estado });
    return response.data;
  },
  
  // Método para actualizar solo ciertos campos
  patchExpediente: async (id: number, datos: Partial<Expediente>) => {
    const response = await axiosClient.patch(apiRoutes.expedientes.getById(id), datos);
    return response.data;
  },
  
  // Método para eliminar de forma lógica
  deleteLogico: async (id: number) => {
    const response = await axiosClient.delete(`${apiRoutes.expedientes.getById(id)}/logico`);
    return response.data;
  },
  
  // Método para delitos asociados
  deleteDelitoAsociado: async (id: number) => {
    const response = await axiosClient.delete(`${apiRoutes.expedientes.delitos}/${id}`);
    return response.data;
  },

  // Método para marcar una foto como principal en un expediente
  setFotoPrincipal: async (expedienteId: number, fotoId: number) => {
    try {
      const response = await axiosClient.put(`${apiRoutes.expedientes.getById(expedienteId)}/foto-principal/${fotoId}`);
      return response.data;
    } catch (error) {
      console.error(`Error al marcar fotografía con ID ${fotoId} como principal en el expediente ${expedienteId}:`, error);
      throw error;
    }
  },

  // Gestión de medios de comunicación
  getMediosComunicacion: async (personaId: number): Promise<any[]> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.mediosComunicacion.persona(personaId)}`);
      return response.data as any[];
    } catch (error) {
      console.error(`Error al obtener medios de comunicación de la persona ${personaId}:`, error);
      throw error;
    }
  },

  addMedioComunicacion: async (personaId: number, medio: any): Promise<any> => {
    try {
      const { id, ...medioSinId } = medio;
      const response = await axiosClient.post(`${apiRoutes.mediosComunicacion.persona(personaId)}`, medioSinId);
      return response.data;
    } catch (error) {
      console.error(`Error al agregar medio de comunicación a la persona con ID ${personaId}:`, error);
      throw error;
    }
  },

  updateMedioComunicacion: async (medioId: number, medio: any): Promise<any> => {
    try {
      const response = await axiosClient.put(`${apiRoutes.mediosComunicacion.getById(medioId)}`, medio);
      return response.data;
    } catch (error) {
      console.error(`Error al actualizar medio de comunicación con ID ${medioId}:`, error);
      throw error;
    }
  },

  deleteMedioComunicacion: async (medioId: number): Promise<void> => {
    try {
      await axiosClient.delete(`${apiRoutes.mediosComunicacion.delete(medioId)}`);
    } catch (error) {
      console.error(`Error al eliminar medio de comunicación con ID ${medioId}:`, error);
      throw error;
    }
  },

  // Obtener los expedientes más buscados
  getMasBuscados: async (limit = 10): Promise<Expediente[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.expedientes.masBuscados(limit));
      return response.data as Expediente[];
    } catch (error) {
      console.error('Error al obtener los más buscados:', error);
      throw error;
    }
  },

  // Obtener detalle de expediente por ID
  getExpedienteDetalle: async (id: number): Promise<Expediente> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes}/${id}`);
      return response.data as Expediente;
    } catch (error) {
      console.error('Error al obtener detalle de expediente:', error);
      throw error;
    }
  },

  // Búsqueda avanzada de expedientes/personas
  buscarAvanzado: async (params: {
    nombre?: string;
    apellido?: string;
    numeroExpediente?: string;
    tipoBusqueda?: string;
    numeroIdentificacion?: string;
  }): Promise<any[]> => {
    try {
      const response = await axiosClient.get(`${apiRoutes.expedientes.busquedaAvanzada}`, { params });
      return response.data as any[];
    } catch (error) {
      console.error('Error en búsqueda avanzada:', error);
      throw error;
    }
  },

  // Búsqueda avanzada de expedientes
  busquedaAvanzada: async (params: any): Promise<any[]> => {
    try {
      const response = await axiosClient.post(apiRoutes.expedientes.busquedaAvanzada, params);
      return response.data as any[];
    } catch (error) {
      console.error('Error en búsqueda avanzada:', error);
      throw error;
    }
  },

  // Obtener el próximo número de expediente (autoincremental a partir de 5000)
  getNextExpedienteNumber: async (): Promise<number> => {
    try {
      console.log('Obteniendo el próximo número de expediente...');
      
      // Obtener todos los expedientes ordenados por número de forma descendente
      // Usamos una consulta más específica para asegurar que obtenemos el número más alto
      const response = await axiosClient.get(`${apiRoutes.expedientes.getAll}?sort=numero&direction=desc&size=100`);
      
      // Log completo de la respuesta para depuración
      console.log('Respuesta completa de la API:', response);
      
      // Definir una interfaz para la respuesta esperada
      interface ExpedienteResponse {
        expedientes?: Array<{numero: string}>;
        // Para respuestas paginadas de Spring
        content?: Array<{numero: string}>;
        // Para respuestas de array directo
        [index: number]: {numero: string};
      }
      
      // Usar una aserción de tipo para indicar a TypeScript la estructura
      const data = response.data as ExpedienteResponse;
      console.log('Respuesta de la API para números de expediente (data):', data);
      
      // Manejar diferentes formatos de respuesta posibles
      let expedientes: Array<{numero: string}> = [];
      
      if (Array.isArray(data)) {
        // Si la respuesta es un array directamente
        console.log('La respuesta es un array');
        expedientes = data;
      } else if (data.content && Array.isArray(data.content)) {
        // Si la respuesta tiene un campo content (formato paginado)
        console.log('La respuesta tiene un campo content');
        expedientes = data.content;
      } else if (data.expedientes && Array.isArray(data.expedientes)) {
        // Si la respuesta tiene un campo expedientes
        console.log('La respuesta tiene un campo expedientes');
        expedientes = data.expedientes;
      } else {
        // Si no podemos identificar el formato, intentamos buscar cualquier array en la respuesta
        console.log('Formato de respuesta no reconocido, buscando arrays en la respuesta');
        for (const key in data) {
          if (Array.isArray(data[key])) {
            console.log(`Encontrado array en campo: ${key}`);
            // Convertir a unknown primero para evitar errores de tipo
            const arrayData = data[key] as unknown;
            // Luego convertir al tipo esperado
            expedientes = arrayData as Array<{numero: string}>;
            break;
          }
        }
      }
      
      console.log('Expedientes extraídos:', expedientes);
      
      if (expedientes && expedientes.length > 0) {
        // Extraer todos los números de expediente y convertirlos a enteros
        const numeros = expedientes
          .map(exp => {
            console.log('Procesando expediente:', exp);
            if (!exp) return NaN;
            if (!exp.numero) return NaN;
            const parsed = parseInt(exp.numero, 10);
            console.log(`Número extraído: ${exp.numero} -> ${parsed}`);
            return parsed;
          })
          .filter(num => !isNaN(num)); // Filtrar valores no numéricos
        
        console.log('Números de expediente encontrados:', numeros);
        
        if (numeros.length > 0) {
          // Encontrar el número más alto
          const maxNumero = Math.max(...numeros);
          console.log('Número más alto encontrado:', maxNumero);
          
          // Asegurar que el próximo número sea al menos 5000
          const nextNumero = Math.max(maxNumero + 1, 5000);
          console.log('Próximo número de expediente:', nextNumero);
          
          return nextNumero;
        } else {
          console.log('No se encontraron números válidos en los expedientes');
        }
      } else {
        console.log('No se encontraron expedientes en la respuesta');
      }
      
      // Si no hay expedientes o no se encontraron números válidos, comenzar desde 5000
      console.log('No se encontraron expedientes válidos, comenzando desde 5000');
      return 5000;
    } catch (error) {
      console.error('Error al obtener el próximo número de expediente:', error);
      // En caso de error, devolver 5000 como valor predeterminado
      return 5000;
    }
  },

  // Los métodos del sistema de bloqueo han sido eliminados
  // Obtener expedientes para P.N Recompensas
  getPNRecompensas: async (params: PNRecompensasParams): Promise<PaginatedResponse<Expediente>> => {
    try {
      const queryParams = new URLSearchParams();
      
      // Agregar parámetros de paginación
      if (params.page !== undefined) queryParams.set('page', params.page.toString());
      if (params.size !== undefined) queryParams.set('size', params.size.toString());
      if (params.sortBy) queryParams.set('sortBy', params.sortBy);
      if (params.sortDir) queryParams.set('sortDir', params.sortDir);
      
      // Agregar filtros
      if (params.profugo) queryParams.set('profugo', params.profugo);
      if (params.numero) queryParams.set('numero', params.numero);
      if (params.fuerzaAsignada) queryParams.set('fuerzaAsignada', params.fuerzaAsignada);
      if (params.estadoSituacion) queryParams.set('estadoSituacion', params.estadoSituacion);
      if (params.fechaDesde) queryParams.set('fechaDesde', params.fechaDesde);
      if (params.fechaHasta) queryParams.set('fechaHasta', params.fechaHasta);
      if (params.delitoId) queryParams.set('delitoId', params.delitoId.toString());
      
      const response = await axiosClient.get(`${apiRoutes.expedientes.pnRecompensas}?${queryParams}`);
      return response.data as PaginatedResponse<Expediente>;
    } catch (error) {
      console.error('Error al obtener P.N Recompensas:', error);
      throw error;
    }
  },

  // Actualizar recompensa de expediente
  actualizarRecompensa: async (id: number, recompensaData: RecompensaUpdateData): Promise<Expediente> => {
    try {
      const response = await axiosClient.put(apiRoutes.expedientes.actualizarRecompensa(id), recompensaData);
      return response.data as Expediente;
    } catch (error) {
      console.error(`Error al actualizar recompensa del expediente ${id}:`, error);
      throw error;
    }
  },

  // Exportar expediente a PDF
  exportarExpedienteAPDF: async (id: number): Promise<Blob> => {
    try {
      const response = await axiosClient.get(apiRoutes.expedientes.exportarPDF(id), {
        responseType: 'blob', // Importante para recibir el archivo como blob
      });
      return response.data;
    } catch (error) {
      console.error('Error al exportar expediente a PDF:', error);
      throw error;
    }
  }
};

export default expedienteService;