import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { Usuario, LoginRequest, AuthResponse, UpdateRoleRequest, UpdateRoleResponse, ForceLogoutResponse, Rol } from '../types/usuario.types';

const usuarioService = {
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await axiosClient.post(apiRoutes.auth.login, credentials);
    return response.data as AuthResponse;
  },
  
  register: async (user: Usuario): Promise<AuthResponse> => {
    const response = await axiosClient.post(apiRoutes.auth.register, user);
    return response.data as AuthResponse;
  },
  
  getAll: async (): Promise<Usuario[]> => {
    const response = await axiosClient.get(apiRoutes.usuarios.getAll);
    return response.data as Usuario[];
  },
  
  getById: async (id: number): Promise<Usuario> => {
    const response = await axiosClient.get(apiRoutes.usuarios.getById(id));
    return response.data as Usuario;
  },
  
  create: async (usuario: Usuario): Promise<Usuario> => {
    try {
      const response = await axiosClient.post(apiRoutes.usuarios.create, usuario);
      return response.data as Usuario;
    } catch (error: any) {
      console.error('Error al crear usuario:', error.response?.data || error.message);
      throw error;
    }
  },
  
  update: async (id: number, usuario: Usuario): Promise<Usuario> => {
    const response = await axiosClient.put(apiRoutes.usuarios.update(id), usuario);
    return response.data as Usuario;
  },
  
  delete: async (id: number): Promise<void> => {
    await axiosClient.delete(apiRoutes.usuarios.delete(id));
  },
  
  changePassword: async (id: number, newPassword: string): Promise<void> => {
    await axiosClient.patch(apiRoutes.usuarios.changePassword(id), { newPassword });
  },
  
  findByEmail: async (email: string): Promise<Usuario> => {
    const response = await axiosClient.get(apiRoutes.usuarios.findByEmail(email));
    return response.data as Usuario;
  },
  
  findByRol: async (rol: string): Promise<Usuario[]> => {
    const response = await axiosClient.get(apiRoutes.usuarios.findByRol(rol));
    return response.data as Usuario[];
  },
  
  cambiarContrasena: async (nuevaContrasena: string): Promise<void> => {
    await axiosClient.post(apiRoutes.auth.changePassword, { newPassword: nuevaContrasena });
  },

  obtenerQr2FA: async (): Promise<string> => {
    const response = await axiosClient.get(apiRoutes.auth.setup2FA);
    const data = response.data as { qrUrl: string };
    return data.qrUrl;
  },

  activar2FA: async (codigo: string): Promise<any> => {
    const response = await axiosClient.post(apiRoutes.auth.activar2FA, { code: codigo });
    return response.data as any;
  },

  validar2FA: async (codigo: string): Promise<{ token: string }> => {
    const response = await axiosClient.post(apiRoutes.auth.validar2FA, { code: codigo });
    return response.data as { token: string };
  },

  resetPasswordAnd2FA: async (id: number): Promise<void> => {
    await axiosClient.post(apiRoutes.usuarios.resetPasswordAnd2FA(id));
  },
  
  primerCambioContrasena: async (email: string, currentPassword: string, newPassword: string): Promise<any> => {
    const response = await axiosClient.post(apiRoutes.auth.firstPasswordChange, {
      email,
      currentPassword,
      newPassword
    });
    return response.data as any;
  },

  getMe: async (): Promise<Usuario> => {
    const response = await axiosClient.get(apiRoutes.usuarios.me);
    return response.data as Usuario;
  },

  updateRoleWithPassword: async (id: number, newRole: Rol, password: string): Promise<UpdateRoleResponse> => {
    try {
      const requestData: UpdateRoleRequest = {
        newRole,
        password
      };
      const response = await axiosClient.put(apiRoutes.usuarios.updateRoleWithPassword(id), requestData);
      return response.data as UpdateRoleResponse;
    } catch (error: any) {
      console.error('Error al cambiar rol con contraseña:', error.response?.data || error.message);
      throw error;
    }
  },

  forceLogout: async (id: number): Promise<ForceLogoutResponse> => {
    try {
      const response = await axiosClient.post(apiRoutes.usuarios.forceLogout(id));
      return response.data as ForceLogoutResponse;
    } catch (error: any) {
      console.error('Error al forzar cierre de sesión:', error.response?.data || error.message);
      throw error;
    }
  }
};

export default usuarioService; 