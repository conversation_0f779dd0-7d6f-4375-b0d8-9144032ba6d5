import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MedioComunicacion } from '../types/persona.types';

const personaService = {
  // Operaciones CRUD básicas para personas
  getAll: async (): Promise<Persona[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.personas.getAll);
      return response.data as Persona[];
    } catch (error: any) {
      console.error('Error al obtener personas:', error);
      throw new Error(error.response?.data?.message || 'Error al obtener personas');
    }
  },
  
  getById: async (id: number): Promise<Persona> => {
    try {
      const response = await axiosClient.get(apiRoutes.personas.getById(id));
      return response.data as Persona;
    } catch (error: any) {
      console.error(`Error al obtener persona ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener la persona');
    }
  },
  
  create: async (persona: Persona): Promise<Persona> => {
    try {
      const response = await axiosClient.post(apiRoutes.personas.create, persona);
      return response.data as Persona;
    } catch (error: any) {
      console.error('Error al crear persona:', error);
      throw new Error(error.response?.data?.message || 'Error al crear la persona');
    }
  },
  
  update: async (id: number, persona: Persona): Promise<Persona> => {
    try {
      const response = await axiosClient.put(apiRoutes.personas.update(id), persona);
      return response.data as Persona;
    } catch (error: any) {
      console.error(`Error al actualizar persona ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al actualizar la persona');
    }
  },
  
  delete: async (id: number): Promise<any> => {
    try {
      const response = await axiosClient.delete(apiRoutes.personas.delete(id));
      return response.data as any;
    } catch (error: any) {
      console.error(`Error al eliminar persona ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al eliminar la persona');
    }
  },
  
  // Búsquedas específicas
  searchPersonas: async (query: string): Promise<Persona[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.personas.search(query));
      return response.data as Persona[];
    } catch (error: any) {
      console.error('Error al buscar personas:', error);
      throw new Error(error.response?.data?.message || 'Error al buscar personas');
    }
  },
  
  getByDocumento: async (documento: string): Promise<Persona> => {
    try {
      const response = await axiosClient.get(apiRoutes.personas.getByDocumento(documento));
      return response.data as Persona;
    } catch (error: any) {
      console.error(`Error al buscar persona con documento ${documento}:`, error);
      throw new Error(error.response?.data?.message || 'Error al buscar por documento');
    }
  },
  
  // Operaciones para domicilios
  getDomicilios: async (personaId: number): Promise<Domicilio[]> => {
    const response = await axiosClient.get(apiRoutes.domicilios.persona(personaId));
    return response.data as Domicilio[];
  },
  
  createDomicilio: async (domicilio: Domicilio): Promise<Domicilio> => {
    const response = await axiosClient.post(apiRoutes.domicilios.create, domicilio);
    return response.data as Domicilio;
  },
  
  updateDomicilio: async (id: number, domicilio: Domicilio): Promise<Domicilio> => {
    const response = await axiosClient.put(apiRoutes.domicilios.update(id), domicilio);
    return response.data as Domicilio;
  },
  
  deleteDomicilio: async (id: number): Promise<any> => {
    await axiosClient.delete(apiRoutes.domicilios.delete(id));
  },
  
  // Operaciones para medios de comunicación
  getMediosComunicacion: async (personaId: number): Promise<MedioComunicacion[]> => {
    const response = await axiosClient.get(apiRoutes.personas.getMediosComunicacion(personaId));
    return response.data as MedioComunicacion[];
  },
  
  createMedioComunicacion: async (medioComunicacion: MedioComunicacion): Promise<MedioComunicacion> => {
    const response = await axiosClient.post(apiRoutes.mediosComunicacion.create, medioComunicacion);
    return response.data as MedioComunicacion;
  },
  
  updateMedioComunicacion: async (id: number, medioComunicacion: MedioComunicacion): Promise<MedioComunicacion> => {
    const response = await axiosClient.put(apiRoutes.mediosComunicacion.update(id), medioComunicacion);
    return response.data as MedioComunicacion;
  },
  
  deleteMedioComunicacion: async (id: number): Promise<any> => {
    await axiosClient.delete(apiRoutes.mediosComunicacion.delete(id));
  }
};

export default personaService; 