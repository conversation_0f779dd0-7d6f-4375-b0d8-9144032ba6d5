import axios from "axios";
import { ActivityLogger } from '../services/activityLogger';

// Usando ruta relativa para que NGINX maneje el enrutamiento correctamente
// No añadimos prefijo /api aquí para evitar duplicación
// Las variables de entorno se manejarán en tiempo de compilación
const API_URL = "";

const axiosClient = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor simplificado que solo maneja autenticación, sin modificar URLs
axiosClient.interceptors.request.use(
  (config) => {
    // IMPORTANTE: No modificamos las URLs para evitar problemas
    console.log('Enviando petición a:', config.url);

    // --- AUTENTICACIÓN ---
    // Verificar si la URL es para 2FA y tenemos un token temporal
    const tempToken = localStorage.getItem("temp_token");
    if (
      tempToken &&
      (config.url?.includes("/auth/2fa-setup") ||
        config.url?.includes("/auth/activar-2fa") ||
        config.url?.includes("/auth/validar-2fa"))
    ) {
      if (config.headers) {
        config.headers["Authorization"] = `Bearer ${tempToken}`;
      } else {
        config.headers = { Authorization: `Bearer ${tempToken}` } as any;
      }
      return config;
    }

    // Intentar usar token normal
    const token = localStorage.getItem("token");
    if (token) {
      if (config.headers) {
        config.headers["Authorization"] = `Bearer ${token}`;
      } else {
        config.headers = { Authorization: `Bearer ${token}` } as any;
      }
      return config;
    }

    // Si no hay token, buscar en el objeto user
    const userStr = localStorage.getItem("user");
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.token) {
          if (config.headers) {
            config.headers["Authorization"] = `Bearer ${user.token}`;
          } else {
            config.headers = { Authorization: `Bearer ${user.token}` } as any;
          }
        }
      } catch (e) {
        console.error("Error parsing user from localStorage", e);
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Variable para evitar múltiples intentos de refresh simultáneos
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// Interceptor para manejar errores de respuesta globalmente con refresh automático
axiosClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response) {
      // --- MANEJO GLOBAL DE ERRORES DE AUTENTICACIÓN ---
      if (error.response.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // Si ya se está refrescando, agregar a la cola
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return axiosClient(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          // Intentar refrescar el token
          const token = localStorage.getItem('token');
          
          if (!token) {
            throw new Error('No token available');
          }

          console.log('🔄 Intentando refrescar token...');
          const response = await axiosClient.post('/auth/refresh', {}, {
            headers: { Authorization: `Bearer ${token}` }
          });

          const newToken = response.data.token;
          
          if (newToken) {
            console.log('✅ Token refrescado exitosamente');
            localStorage.setItem('token', newToken);
            
            // Registrar refresh exitoso
            ActivityLogger.logTokenRefresh();
            
            // Actualizar header de la petición original
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            
            // Procesar cola de peticiones pendientes
            processQueue(null, newToken);
            
            // Reintentar petición original
            return axiosClient(originalRequest);
          } else {
            throw new Error('No token received');
          }
        } catch (refreshError) {
          console.error('❌ Error al refrescar token:', refreshError);
          
          // Registrar fallo de refresh
          const errorMessage = refreshError instanceof Error ? refreshError.message : 'Error desconocido';
          ActivityLogger.logTokenRefreshFailed(errorMessage);
          
          // Procesar cola con error
          processQueue(refreshError, null);
          
          // Limpiar datos de sesión
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          localStorage.removeItem("temp_token");
          
          // Redirigir a login
          window.location.href = "/login";
          
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }
      
      // Manejo de 403 - Problemas de autorización
      if (error.response.status === 403) {
        console.error("Error de autorización. Es posible que no tengas permisos suficientes.");
      }
    }
    
    return Promise.reject(error);
  }
);

// --- DOCUMENTACIÓN ---
// Este interceptor asegura:
// 1. Que nunca se duplique el prefijo /api en las rutas.
// 2. Que todas las peticiones lleven el token JWT si existe.
// 3. Que los errores de autenticación se manejen globalmente.
// 4. Que el sistema sea fácil de mantener si cambias el prefijo /api.

export default axiosClient; 
