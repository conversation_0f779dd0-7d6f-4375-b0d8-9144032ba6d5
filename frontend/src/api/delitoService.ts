import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { Delito } from '../types/delito.types';
import { ExpedienteDelito } from '../types/expediente.types';

const delitoService = {
  getAll: async (): Promise<Delito[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.delitos.getAll);
      return response.data as Delito[];
    } catch (error: any) {
      console.error('Error al obtener delitos:', error);
      throw new Error(error.response?.data?.message || 'Error al obtener delitos');
    }
  },
  
  getById: async (id: number): Promise<Delito> => {
    try {
      const response = await axiosClient.get(apiRoutes.delitos.getById(id));
      return response.data as Delito;
    } catch (error: any) {
      console.error(`Error al obtener delito ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener el delito');
    }
  },
  
  create: async (delito: Delito): Promise<Delito> => {
    try {
      const response = await axiosClient.post(apiRoutes.delitos.create, delito);
      return response.data as Delito;
    } catch (error: any) {
      console.error('Error al crear delito:', error);
      throw new Error(error.response?.data?.message || 'Error al crear el delito');
    }
  },
  
  update: async (id: number, delito: Delito): Promise<Delito> => {
    try {
      const response = await axiosClient.put(apiRoutes.delitos.update(id), delito);
      return response.data as Delito;
    } catch (error: any) {
      console.error(`Error al actualizar delito ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al actualizar el delito');
    }
  },
  
  delete: async (id: number): Promise<any> => {
    try {
      const response = await axiosClient.delete(apiRoutes.delitos.delete(id));
      return response.data as any;
    } catch (error: any) {
      console.error(`Error al eliminar delito ${id}:`, error);
      throw new Error(error.response?.data?.message || 'Error al eliminar el delito');
    }
  },
  
  searchDelitos: async (query: string): Promise<Delito[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.delitos.searchByNombre(query));
      return response.data as Delito[];
    } catch (error: any) {
      console.error('Error al buscar delitos:', error);
      throw new Error(error.response?.data?.message || 'Error al buscar delitos');
    }
  },
  
  getByLey: async (ley: string): Promise<Delito[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.delitos.getByLey(ley));
      return response.data as Delito[];
    } catch (error: any) {
      console.error(`Error al obtener delitos por ley ${ley}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener delitos por ley');
    }
  },
  
  getDelitosPorExpediente: async (expedienteId: number): Promise<Delito[]> => {
    try {
      const response = await axiosClient.get(apiRoutes.expedienteDelitos.getDelitosPorExpediente(expedienteId));
      return response.data as Delito[];
    } catch (error: any) {
      console.error(`Error al obtener delitos del expediente ${expedienteId}:`, error);
      throw new Error(error.response?.data?.message || 'Error al obtener delitos del expediente');
    }
  },
  
  asociarDelitoExpediente: async (expedienteDelito: ExpedienteDelito): Promise<any> => {
    try {
      const response = await axiosClient.post(
        apiRoutes.expedientes.getById(expedienteDelito.expedienteId) + '/delitos',
        expedienteDelito
      );
      return response.data as any;
    } catch (error: any) {
      if (error.response?.status === 409 && error.response?.data?.mensaje) {
        throw new Error(error.response.data.mensaje);
      }
      console.error('Error al asociar delito a expediente:', error);
      throw new Error(error.response?.data?.message || 'Error al asociar delito');
    }
  },
  
  desasociarDelitoExpediente: async (expedienteDelitoId: number): Promise<any> => {
    try {
      const response = await axiosClient.delete(apiRoutes.expedienteDelitos.delete(expedienteDelitoId));
      return response.data as any;
    } catch (error: any) {
      console.error(`Error al desasociar delito ${expedienteDelitoId}:`, error);
      throw new Error(error.response?.data?.message || 'Error al desasociar delito');
    }
  }
};

export default delitoService; 