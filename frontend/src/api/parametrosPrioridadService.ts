import axiosClient from './axiosClient';

export interface ParametroPrioridadDTO {
  id: number;
  claveVariable: string;
  valor: number;
  descripcion: string;
  tipoVariable: string;
  fechaCreacion: string;
  fechaActualizacion: string;
}

export interface ActualizacionParametrosDTO {
  parametros: { [id: number]: number };
}

export interface ParametrosPorTipoResponse {
  [tipoVariable: string]: ParametroPrioridadDTO[];
}

class ParametrosPrioridadService {
  private readonly baseUrl = '/api/parametros-prioridad';

  /**
   * Obtiene todos los parámetros de prioridad agrupados por tipo
   * NOTA: Función comentada debido a inconsistencia con el backend
   */
  /*
  async obtenerParametros(): Promise<ParametrosPorTipoResponse> {
    const response = await axiosClient.get<ParametrosPorTipoResponse>(this.baseUrl);
    return response.data;
  }
  */

  /**
   * Obtiene parámetros de un tipo específico
   */
  async obtenerParametrosPorTipo(tipoVariable: string): Promise<ParametroPrioridadDTO[]> {
    const response = await axiosClient.get<ParametroPrioridadDTO[]>(`${this.baseUrl}/tipo/${tipoVariable}`);
    return response.data;
  }

  /**
   * Actualiza múltiples parámetros de prioridad
   */
  async actualizarParametros(actualizacion: ActualizacionParametrosDTO): Promise<void> {
    await axiosClient.put(this.baseUrl, actualizacion);
  }

  /**
   * Obtiene un parámetro específico por su clave
   */
  async obtenerParametroPorClave(claveVariable: string): Promise<ParametroPrioridadDTO> {
    const response = await axiosClient.get<ParametroPrioridadDTO>(`${this.baseUrl}/clave/${claveVariable}`);
    return response.data;
  }

  /**
   * Obtiene todos los parámetros como lista plana
   * Llamada directa al endpoint que devuelve una lista plana
   */
  async obtenerTodos(): Promise<ParametroPrioridadDTO[]> {
    // Llamada directa al endpoint que devuelve una lista plana.
    const response = await axiosClient.get<ParametroPrioridadDTO[]>(this.baseUrl);
    return response.data;
  }

  /**
   * Actualiza múltiples parámetros (alias para compatibilidad)
   */
  async actualizarMultiples(parametros: Array<{ id: number; valor: number }>): Promise<void> {
    // Transformar el array en un mapa como espera el backend
    const paramsMap = parametros.reduce((acc, param) => {
      acc[param.id] = param.valor;
      return acc;
    }, {} as { [id: number]: number });

    await this.actualizarParametros({ parametros: paramsMap });
  }
}

export const parametrosPrioridadService = new ParametrosPrioridadService();
export default parametrosPrioridadService;

// Exportación vacía para hacer que el archivo sea un módulo
export {};