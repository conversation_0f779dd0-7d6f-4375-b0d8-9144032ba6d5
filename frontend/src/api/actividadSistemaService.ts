import axiosClient from './axiosClient';
import { apiRoutes } from './apiRoutes';
import { 
  ActividadSistema, 
  FiltroActividad, 
  PaginatedResponse, 
  EstadisticasActividad 
} from '../types/actividad.types';

const actividadSistemaService = {
  /**
   * Obtiene actividades con filtros y paginación
   */
  getActividades: async (filtros: FiltroActividad = {}): Promise<PaginatedResponse<ActividadSistema>> => {
    const params = new URLSearchParams();
    
    // Agregar parámetros de filtro
    if (filtros.usuario) params.append('usuario', filtros.usuario);
    if (filtros.modulo) params.append('modulo', filtros.modulo);
    if (filtros.categoriaAccion) params.append('categoriaAccion', filtros.categoriaAccion);
    if (filtros.estadoRespuesta) params.append('estadoRespuesta', filtros.estadoRespuesta);
    if (filtros.ipCliente) params.append('ipCliente', filtros.ipCliente);
    if (filtros.fechaInicio) params.append('fechaInicio', filtros.fechaInicio);
    if (filtros.fechaFin) params.append('fechaFin', filtros.fechaFin);
    
    // Parámetros de paginación
    params.append('page', (filtros.page || 0).toString());
    params.append('size', (filtros.size || 20).toString());
    params.append('sortBy', filtros.sortBy || 'fechaHora');
    params.append('sortDirection', filtros.sortDirection || 'desc');
    
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}?${params.toString()}`);
    return response.data as PaginatedResponse<ActividadSistema>;
  },

  /**
   * Obtiene una actividad específica por ID con todos sus detalles
   */
  getActividadById: async (id: number): Promise<ActividadSistema> => {
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}/${id}`);
    return response.data as ActividadSistema;
  },

  /**
   * Obtiene estadísticas generales de actividad
   */
  getEstadisticas: async (): Promise<EstadisticasActividad> => {
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}/estadisticas`);
    return response.data as EstadisticasActividad;
  },

  /**
   * Obtiene actividades recientes (últimas 24 horas)
   */
  getActividadesRecientes: async (): Promise<ActividadSistema[]> => {
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}/recientes`);
    return response.data as ActividadSistema[];
  },

  /**
   * Obtiene actividades sospechosas para una IP
   */
  getActividadesSospechosas: async (ip: string): Promise<ActividadSistema[]> => {
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}/sospechosas?ip=${ip}`);
    return response.data as ActividadSistema[];
  },

  /**
   * Exporta actividades (placeholder)
   */
  exportarActividades: async (filtros: FiltroActividad): Promise<{ mensaje: string; estado: string }> => {
    const response = await axiosClient.post(`${apiRoutes.actividadSistema.getAll}/exportar`, filtros);
    return response.data;
  },

  /**
   * Método legacy para compatibilidad
   */
  getAll: async (): Promise<ActividadSistema[]> => {
    const response = await axiosClient.get(`${apiRoutes.actividadSistema.getAll}/legacy`);
    return response.data as ActividadSistema[];
  },
};

export default actividadSistemaService;