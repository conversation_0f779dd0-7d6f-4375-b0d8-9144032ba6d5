import { Rol } from './usuario.types';

export interface PerfilUsuario {
  id?: number;
  nombre: string;
  apellido: string;
  email: string;
  dependencia?: string;
  telefonoMovil?: string;
  rol: Rol;
}

export interface ActualizarPerfilRequest {
  nombre?: string;
  apellido?: string;
  email?: string;
  dependencia?: string;
  telefonoMovil?: string;
}

export interface ValidacionTelefono {
  esValido: boolean;
  mensaje?: string;
}

// Constantes para validación
export const REGEX_TELEFONO_ARGENTINO = /^\+54\s9\s\d{2}\s\d{4}-\d{4}$/;