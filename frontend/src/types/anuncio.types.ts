// Tipos para el sistema de anuncios globales

export interface Anuncio {
  id: number;
  titulo: string;
  contenido: string;
  activo: boolean;
  fechaCreacion: string;
  creadoPorId: number;
  creadoPorNombre?: string;
  totalVistas?: number;
}

export interface CrearAnuncioRequest {
  titulo: string;
  contenido: string;
  activo: boolean;
}

export interface MarcarVistoRequest {
  anuncioId: number;
}

export interface AnuncioResponse {
  mensaje: string;
}

export interface AnuncioError {
  error: string;
}