export interface RecompensaUpdateData {
  recompensa: boolean;
  montoRecompensa?: string;
  moneda?: string;
}

export interface PNRecompensasParams {
  page?: number;
  size?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
  profugo?: string;
  numero?: string;
  fuerzaAsignada?: string;
  estadoSituacion?: string;
  fechaDesde?: string;
  fechaHasta?: string;
  delitoId?: number;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
}

export {};