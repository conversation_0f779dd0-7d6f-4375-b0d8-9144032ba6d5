export interface ActividadSistema {
  id: number;
  usuario: string;
  tipoAccion: string;
  fechaHora: string;
  detalles: string;
  ipCliente?: string;
  userAgent?: string;
  sessionId?: string;
  endpoint?: string;
  metodoHttp?: string;
  modulo?: string;
  categoriaAccion?: string;
  duracionMs?: number;
  estadoRespuesta?: string;
  actividadDetalles?: ActividadDetalle[];
}

export interface ActividadDetalle {
  id: number;
  actividadId: number;
  tipoDetalle: string;
  contenidoJson?: string;
  fechaCreacion: string;
}

export interface FiltroActividad {
  usuario?: string;
  modulo?: string;
  categoriaAccion?: string;
  estadoRespuesta?: string;
  ipCliente?: string;
  fechaInicio?: string;
  fechaFin?: string;
  page?: number;
  size?: number;
  sortBy?: string;
  sortDirection?: string;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
}

export interface EstadisticasActividad {
  porModulo: [string, number][];
  porUsuario: [string, number][];
  actividadesRecientes: ActividadSistema[];
}

// Enums para constantes
export enum ModuloActividad {
  EXPEDIENTES = 'EXPEDIENTES',
  USUARIOS = 'USUARIOS',
  SISTEMA = 'SISTEMA',
  REPORTES = 'REPORTES'
}

export enum EstadoRespuesta {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  WARNING = 'WARNING'
}

export enum TipoDetalle {
  REQUEST = 'REQUEST',
  RESPONSE = 'RESPONSE',
  CAMBIO_ANTERIOR = 'CAMBIO_ANTERIOR',
  CAMBIO_POSTERIOR = 'CAMBIO_POSTERIOR',
  METADATA = 'METADATA',
  ERROR = 'ERROR'
}

// Tipos para filtros predefinidos
export interface FiltroRapido {
  label: string;
  value: Partial<FiltroActividad>;
  icon?: string;
  color?: string;
}

// Configuración de colores para diferentes tipos de acciones
export interface ConfiguracionAccion {
  color: 'success' | 'primary' | 'error' | 'info' | 'warning' | 'default';
  icon: string;
  descripcion: string;
}

export type ConfiguracionAcciones = {
  [key: string]: ConfiguracionAccion;
};