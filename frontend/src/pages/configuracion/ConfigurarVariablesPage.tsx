import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  CircularProgress,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  RestoreFromTrash as RestoreIcon,
  Warning as WarningIcon,
  AccountBalance as ExpedienteIcon,
  Person as PersonaIcon,
  Gavel as DelitoIcon,
  Business as OrganizacionIcon,
  TrendingUp as ImpactoIcon,
  Settings as OtrosIcon
} from '@mui/icons-material';
import { parametrosPrioridadService } from '../../api/parametrosPrioridadService';
import { ParametroPrioridadDTO } from '../../api/parametrosPrioridadService';
import ProfessionalHeader from '../../components/common/ProfessionalHeader';

interface GroupedParameters {
  [categoria: string]: ParametroPrioridadDTO[];
}

const ConfigurarVariablesPage: React.FC = () => {
  const [parametros, setParametros] = useState<ParametroPrioridadDTO[]>([]);
  const [parametrosModificados, setParametrosModificados] = useState<Map<number, number>>(new Map());
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [expandedPanels, setExpandedPanels] = useState<Set<string>>(new Set(['EXPEDIENTE']));
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({
    open: false,
    title: '',
    message: '',
    onConfirm: () => {}
  });

  useEffect(() => {
    cargarParametros();
  }, []);

  const cargarParametros = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await parametrosPrioridadService.obtenerTodos();
      setParametros(data);
      setParametrosModificados(new Map());
    } catch (err) {
      setError('Error al cargar los parámetros de prioridad');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const agruparParametrosPorCategoria = (parametros: ParametroPrioridadDTO[]): GroupedParameters => {
    return parametros.reduce((grupos, parametro) => {
      const categoria = parametro.tipoVariable || 'OTROS';
      if (!grupos[categoria]) {
        grupos[categoria] = [];
      }
      grupos[categoria].push(parametro);
      return grupos;
    }, {} as GroupedParameters);
  };

  const handleParametroChange = (parametroId: number, nuevoValor: string) => {
    const valor = parseFloat(nuevoValor);
    if (!isNaN(valor)) {
      setParametrosModificados(prev => new Map(prev.set(parametroId, valor)));
    } else {
      setParametrosModificados(prev => {
        const newMap = new Map(prev);
        newMap.delete(parametroId);
        return newMap;
      });
    }
  };

  const getValorActual = (parametro: ParametroPrioridadDTO): number => {
    return parametrosModificados.get(parametro.id) ?? parametro.valor;
  };

  const hasChanges = (): boolean => {
    return parametrosModificados.size > 0;
  };

  const guardarCambios = async () => {
    if (!hasChanges()) return;

    try {
      setSaving(true);
      setError(null);

      const actualizaciones = Array.from(parametrosModificados.entries()).map(
        ([id, valor]) => ({ id, valor })
      );

      await parametrosPrioridadService.actualizarMultiples(actualizaciones);
      
      setSuccess(`Se actualizaron ${actualizaciones.length} parámetros correctamente`);
      await cargarParametros(); // Recargar para obtener los valores actualizados
    } catch (err) {
      setError('Error al guardar los cambios');
      console.error('Error:', err);
    } finally {
      setSaving(false);
    }
  };

  const confirmarGuardarCambios = () => {
    setConfirmDialog({
      open: true,
      title: 'Confirmar cambios',
      message: `¿Está seguro de que desea guardar ${parametrosModificados.size} cambios? Esto afectará el cálculo de prioridad de todos los expedientes.`,
      onConfirm: () => {
        setConfirmDialog(prev => ({ ...prev, open: false }));
        guardarCambios();
      }
    });
  };

  const descartarCambios = () => {
    setParametrosModificados(new Map());
  };

  const confirmarDescartarCambios = () => {
    setConfirmDialog({
      open: true,
      title: 'Descartar cambios',
      message: '¿Está seguro de que desea descartar todos los cambios no guardados?',
      onConfirm: () => {
        setConfirmDialog(prev => ({ ...prev, open: false }));
        descartarCambios();
      }
    });
  };

  const togglePanel = (categoria: string) => {
    setExpandedPanels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoria)) {
        newSet.delete(categoria);
      } else {
        newSet.add(categoria);
      }
      return newSet;
    });
  };

  const getCategoriaColor = (categoria: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    const colorMap: Record<string, "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning"> = {
      'EXPEDIENTE': 'primary',
      'PERSONA': 'secondary',
      'DELITO': 'error',
      'ORGANIZACION': 'warning',
      'IMPACTO': 'info',
      'OTROS': 'success'
    };
    return colorMap[categoria] || 'default';
  };

  const getCategoriaIcon = (categoria: string) => {
    const iconMap: Record<string, React.ReactElement> = {
      'EXPEDIENTE': <ExpedienteIcon />,
      'PERSONA': <PersonaIcon />,
      'DELITO': <DelitoIcon />,
      'ORGANIZACION': <OrganizacionIcon />,
      'IMPACTO': <ImpactoIcon />,
      'OTROS': <OtrosIcon />
    };
    return iconMap[categoria] || <OtrosIcon />;
  };

  const getCategoriaDescripcion = (categoria: string): string => {
    const descripciones: Record<string, string> = {
      'EXPEDIENTE': 'Parámetros relacionados con las características del expediente',
      'PERSONA': 'Parámetros relacionados con las personas involucradas',
      'DELITO': 'Parámetros relacionados con los tipos de delitos',
      'ORGANIZACION': 'Parámetros relacionados con organizaciones criminales',
      'IMPACTO': 'Parámetros relacionados con el impacto social y mediático',
      'OTROS': 'Otros parámetros de configuración'
    };
    return descripciones[categoria] || 'Parámetros de configuración';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const parametrosAgrupados = agruparParametrosPorCategoria(parametros);

  return (
    <Box>
      <ProfessionalHeader
        title="Configurar Variables de Prioridad"
        showButton={false}
      />
      
      <Box sx={{ p: 3 }}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Configure los parámetros que determinan el cálculo de prioridad de los expedientes
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Recargar parámetros">
              <IconButton onClick={cargarParametros} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {hasChanges() && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'warning.light', color: 'warning.contrastText' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2">
              Tienes {parametrosModificados.size} cambios sin guardar
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<RestoreIcon />}
                onClick={confirmarDescartarCambios}
                disabled={saving}
              >
                Descartar
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<SaveIcon />}
                onClick={confirmarGuardarCambios}
                disabled={saving}
              >
                {saving ? 'Guardando...' : 'Guardar'}
              </Button>
            </Box>
          </Box>
        </Paper>
      )}

        <Box sx={{ mb: 2 }}>
          <Alert
            severity="error"
            icon={<WarningIcon />}
            sx={{
              fontWeight: 'bold',
              '& .MuiAlert-message': {
                fontWeight: 'bold'
              }
            }}
          >
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              <strong>Importante:</strong> Los cambios en estos parámetros afectarán inmediatamente el cálculo de prioridad
              de todos los expedientes. Se recomienda realizar cambios graduales y monitorear el impacto.
            </Typography>
          </Alert>
        </Box>

      {Object.entries(parametrosAgrupados).map(([categoria, parametrosCategoria]) => (
        <Accordion
          key={categoria}
          expanded={expandedPanels.has(categoria)}
          onChange={() => togglePanel(categoria)}
          sx={{
            mb: 2,
            borderRadius: 2,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            '&:before': {
              display: 'none',
            },
            '&.Mui-expanded': {
              boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
            },
            '&:hover': {
              boxShadow: '0 3px 12px rgba(0,0,0,0.12)',
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            sx={{
              borderRadius: 2,
              minHeight: 64,
              '&.Mui-expanded': {
                minHeight: 64,
              },
              '& .MuiAccordionSummary-content': {
                margin: '16px 0',
                '&.Mui-expanded': {
                  margin: '16px 0',
                }
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <Box sx={{ color: `${getCategoriaColor(categoria)}.main` }}>
                  {getCategoriaIcon(categoria)}
                </Box>
                <Chip
                  label={categoria}
                  color={getCategoriaColor(categoria)}
                  size="medium"
                  sx={{
                    fontWeight: 'bold',
                    fontSize: '0.875rem',
                    height: 32
                  }}
                />
              </Box>
              <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 600 }}>
                {categoria.charAt(0) + categoria.slice(1).toLowerCase().replace(/_/g, ' ')}
              </Typography>
              <Chip
                label={`${parametrosCategoria.length} parámetros`}
                variant="outlined"
                size="small"
                color={getCategoriaColor(categoria)}
                sx={{ fontWeight: 500 }}
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ pt: 2, pb: 3 }}>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3, fontStyle: 'italic' }}>
              {getCategoriaDescripcion(categoria)}
            </Typography>
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
              gap: 3
            }}>
              {parametrosCategoria.map((parametro) => {
                const valorActual = getValorActual(parametro);
                const hasChanged = parametrosModificados.has(parametro.id);
                
                return (
                  <Paper
                    key={parametro.id}
                    sx={{
                      p: 3,
                      height: 'fit-content',
                      borderRadius: 2,
                      boxShadow: '0 1px 4px rgba(0,0,0,0.08)',
                      border: '1px solid',
                      borderColor: 'divider',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
                        borderColor: getCategoriaColor(categoria) + '.light'
                      }
                    }}
                  >
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: 'text.primary' }}>
                        {parametro.claveVariable.replace(/_/g, ' ')}
                      </Typography>
                      {parametro.descripcion && (
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.5 }}>
                          {parametro.descripcion}
                        </Typography>
                      )}
                      <Chip
                        label={parametro.tipoVariable}
                        size="small"
                        color={getCategoriaColor(categoria)}
                        variant="filled"
                        sx={{ mb: 2, fontWeight: 500 }}
                      />
                    </Box>
                    
                    <TextField
                      fullWidth
                      size="small"
                      type={parametro.tipoVariable === 'DECIMAL' ? 'number' : 'text'}
                      value={valorActual}
                      onChange={(e) => handleParametroChange(parametro.id, e.target.value)}
                      variant="outlined"
                      inputProps={{
                        step: parametro.tipoVariable === 'DECIMAL' ? '0.01' : undefined,
                        min: parametro.tipoVariable === 'DECIMAL' ? '0' : undefined
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: hasChanged ? 'action.hover' : 'background.paper',
                          borderRadius: 2,
                          '&:hover': {
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: getCategoriaColor(categoria) + '.main'
                            }
                          },
                          '&.Mui-focused': {
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: getCategoriaColor(categoria) + '.main',
                              borderWidth: 2
                            }
                          }
                        }
                      }}
                    />
                    
                    {hasChanged && (
                      <Typography variant="caption" color="warning.main" sx={{ mt: 1, display: 'block', fontWeight: 600 }}>
                        ✓ Valor modificado
                      </Typography>
                    )}
                  </Paper>
                );
              })}
            </Box>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Dialog de confirmación */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog(prev => ({ ...prev, open: false }))}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{confirmDialog.title}</DialogTitle>
        <DialogContent>
          <Typography>{confirmDialog.message}</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialog(prev => ({ ...prev, open: false }))}
            disabled={saving}
          >
            Cancelar
          </Button>
          <Button
            onClick={confirmDialog.onConfirm}
            variant="contained"
            disabled={saving}
          >
            Confirmar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para mensajes de éxito */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
      </Box>
    </Box>
  );
};

export default ConfigurarVariablesPage;