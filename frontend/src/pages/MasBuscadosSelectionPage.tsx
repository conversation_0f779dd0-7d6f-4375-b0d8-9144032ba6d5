import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActionArea,
  Grid,
  Container,
  Paper
} from '@mui/material';
import {
  PersonSearchRounded as PersonSearchRoundedIcon,
  GroupRounded as GroupRoundedIcon,
  RefreshRounded as RefreshRoundedIcon
} from '@mui/icons-material';

const MasBuscadosSelectionPage: React.FC = () => {
  const navigate = useNavigate();

  const opciones = [
    {
      id: 'cufre',
      titulo: 'CUFRE',
      descripcion: 'Comando Unificado Federal de Recaptura de Evadidos',
      icono: <PersonSearchRoundedIcon sx={{ fontSize: 48, color: '#1976d2' }} />,
      ruta: '/expedientes/cufre',
      color: '#1976d2'
    },
    {
      id: 'externos',
      titulo: 'Enlaces Externos',
      descripcion: 'Enlaces a sistemas externos de búsqueda',
      icono: <GroupRoundedIcon sx={{ fontSize: 48, color: '#388e3c' }} />,
      ruta: '/externos',
      color: '#388e3c'
    },
    {
      id: 'iterar',
      titulo: 'Iterar Más Buscados',
      descripcion: 'Herramienta de iteración de expedientes',
      icono: <RefreshRoundedIcon sx={{ fontSize: 48, color: '#f57c00' }} />,
      ruta: '/expedientes/iterar-mas-buscados',
      color: '#f57c00'
    }
  ];

  const handleNavigation = (ruta: string) => {
    navigate(ruta);
  };

  return (
    <Box>
      {/* Encabezado profesional */}
      <Paper sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2, mb: 2, bgcolor: '#002856', color: '#fff', borderRadius: 3, boxShadow: 4 }}>
        <Box sx={{ flex: '0 0 80px', display: 'flex', alignItems: 'center' }}>
          <img src="/images/logo-cufre-2.png" alt="Logo CUFRE" style={{ height: 56, objectFit: 'contain' }} />
        </Box>
        <Typography variant="h4" sx={{ flex: 1, textAlign: 'center', fontWeight: 'bold', letterSpacing: 1 }}>Más Buscados</Typography>
        <Box sx={{ flex: '0 0 80px' }}></Box> {/* Espaciador para centrar el título */}
      </Paper>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={0} sx={{ p: 4, backgroundColor: 'transparent' }}>
          {/* Subtítulo */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto' }}
            >
              Selecciona el sistema de búsqueda que deseas utilizar
            </Typography>
          </Box>

        {/* Grid de Opciones */}
        <Grid container spacing={4} justifyContent="center">
          {opciones.map((opcion) => (
            <Grid key={opcion.id} size={{ xs: 12, sm: 6, md: 4 }}>
              <Card 
                elevation={3}
                sx={{ 
                  height: '100%',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    elevation: 8,
                    transform: 'translateY(-4px)',
                    '& .card-icon': {
                      transform: 'scale(1.1)',
                    }
                  }
                }}
              >
                <CardActionArea 
                  onClick={() => handleNavigation(opcion.ruta)}
                  sx={{ height: '100%', p: 3 }}
                >
                  <CardContent sx={{ textAlign: 'center', height: '100%' }}>
                    {/* Icono */}
                    <Box 
                      className="card-icon"
                      sx={{ 
                        mb: 3,
                        transition: 'transform 0.3s ease-in-out'
                      }}
                    >
                      {opcion.icono}
                    </Box>

                    {/* Título */}
                    <Typography 
                      variant="h5" 
                      component="h2" 
                      gutterBottom
                      sx={{ 
                        fontWeight: 600,
                        color: opcion.color,
                        mb: 2
                      }}
                    >
                      {opcion.titulo}
                    </Typography>

                    {/* Descripción */}
                    <Typography 
                      variant="body1" 
                      color="text.secondary"
                      sx={{ 
                        lineHeight: 1.6,
                        minHeight: 48 // Para mantener altura consistente
                      }}
                    >
                      {opcion.descripcion}
                    </Typography>

                    {/* Indicador de acción */}
                    <Box sx={{ mt: 3 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          color: opcion.color,
                          fontWeight: 500,
                          textTransform: 'uppercase',
                          letterSpacing: 1
                        }}
                      >
                        Acceder →
                      </Typography>
                    </Box>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Información adicional */}
        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ fontStyle: 'italic' }}
          >
            Cada sistema proporciona diferentes herramientas y bases de datos para la búsqueda de personas
          </Typography>
        </Box>
      </Paper>
    </Container>
    </Box>
  );
};

export default MasBuscadosSelectionPage;