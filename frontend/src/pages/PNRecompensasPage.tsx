import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  MenuItem,
  CircularProgress,
  Alert,
  Autocomplete,
  Button
} from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { es } from 'date-fns/locale';
import expedienteService from '../api/expedienteService';
import delitoService from '../api/delitoService';
import { Expediente } from '../types/expediente.types';
import { Delito } from '../types/delito.types';
import { PNRecompensasParams, RecompensaUpdateData } from '../types/recompensa.types';
import RecompensaEditableCell from '../components/expedientes/RecompensaEditableCell';
import { useAuth } from '../context/AuthContext';
import { Rol } from '../types/usuario.types';

const fuerzasAsignadas = [
  'S/D', 'GNA', 'PFA', 'PSA', 'PNA', 'SPF',
  'POL LOCAL', 'INTERPOL', 'AMERIPOL', 'EUROPOL', 'BLOQUE DE BÚSQUEDA CUFRE'
];

const estados = [
  'SIN EFECTO', 'CAPTURA VIGENTE', 'DETENIDO', 'SIN DATO'
];

const opcionesOrdenamiento = [
  { value: 'id', label: 'ID' },
  { value: 'numero', label: 'Número de Expediente' },
  { value: 'fechaIngreso', label: 'Fecha de Ingreso' },
  { value: 'prioridad', label: 'Prioridad' }
];

const PNRecompensasPage: React.FC = () => {
  const [expedientes, setExpedientes] = useState<Expediente[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Paginación
  const [page, setPage] = useState<number>(parseInt(searchParams.get('page') || '0'));
  const [rowsPerPage, setRowsPerPage] = useState<number>(parseInt(searchParams.get('rowsPerPage') || '10'));
  const [totalItems, setTotalItems] = useState<number>(0);
  
  // Ordenamiento
  const [orderBy, setOrderBy] = useState<string>(searchParams.get('orderBy') || 'id');
  const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>(
    (searchParams.get('orderDirection') as 'asc' | 'desc') || 'desc'
  );
  
  // Filtros
  const fechaDesdeParam = searchParams.get('fechaDesde');
  const fechaHastaParam = searchParams.get('fechaHasta');
  
  const [fechaDesde, setFechaDesde] = useState<Date | null>(
    fechaDesdeParam ? new Date(fechaDesdeParam) : null
  );
  const [fechaHasta, setFechaHasta] = useState<Date | null>(
    fechaHastaParam ? new Date(fechaHastaParam) : null
  );
  
  const [filtroProfugo, setFiltroProfugo] = useState(searchParams.get('profugo') || '');
  const [filtroNumero, setFiltroNumero] = useState(searchParams.get('numero') || '');
  const [filtroFuerza, setFiltroFuerza] = useState(searchParams.get('fuerzaAsignada') || '');
  const [filtroEstado, setFiltroEstado] = useState(searchParams.get('estadoSituacion') || '');
  const [filtroDelito, setFiltroDelito] = useState<Delito | null>(null);
  
  // Estados para autocompletado de delitos
  const [delitosOptions, setDelitosOptions] = useState<Delito[]>([]);
  const [loadingDelitos, setLoadingDelitos] = useState(false);

  const { user } = useAuth();
  const isConsulta = user?.rol === Rol.USUARIOCONSULTA;

  // Función para obtener el nombre del prófugo
  const obtenerNombreProfugo = (expediente: Expediente): string => {
    const imputado = expediente.personaExpedientes?.find(p =>
      (p.tipoRelacion || '').toLowerCase() === 'imputado'
    );
    if (imputado && imputado.persona) {
      return `${imputado.persona.nombre || ''} ${imputado.persona.apellido || ''}`.trim();
    }
    if (expediente.personaExpedientes && expediente.personaExpedientes.length > 0 && expediente.personaExpedientes[0].persona) {
      return `${expediente.personaExpedientes[0].persona.nombre || ''} ${expediente.personaExpedientes[0].persona.apellido || ''}`.trim();
    }
    return 'S/D';
  };

  // Función para obtener la causa
  const obtenerCausa = (expediente: Expediente): string => {
    if (expediente.delitos && Array.isArray(expediente.delitos) && expediente.delitos.length > 0) {
      const delito = expediente.delitos[0] as any;
      return delito.nombre || delito.delitoNombre || 'S/D';
    }
    return 'S/D';
  };

  // Función para cargar expedientes
  const fetchExpedientes = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params: PNRecompensasParams = {
        page,
        size: rowsPerPage,
        sortBy: orderBy,
        sortDir: orderDirection,
        profugo: filtroProfugo || undefined,
        numero: filtroNumero || undefined,
        fuerzaAsignada: filtroFuerza || undefined,
        estadoSituacion: filtroEstado || undefined,
        fechaDesde: fechaDesde ? fechaDesde.toISOString().split('T')[0] : undefined,
        fechaHasta: fechaHasta ? fechaHasta.toISOString().split('T')[0] : undefined,
        delitoId: filtroDelito?.id || undefined
      };

      const response = await expedienteService.getPNRecompensas(params);
      setExpedientes(response.content);
      setTotalItems(response.totalElements);
      
      // Actualizar URL con parámetros actuales
      const newSearchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          newSearchParams.set(key, String(value));
        }
      });
      setSearchParams(newSearchParams);
      
    } catch (err: any) {
      console.error('Error al cargar expedientes:', err);
      setError(err.response?.data?.message || 'Error al cargar los expedientes');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, orderBy, orderDirection, filtroProfugo, filtroNumero, 
      filtroFuerza, filtroEstado, fechaDesde, fechaHasta, filtroDelito, setSearchParams]);

  // Efecto para cargar expedientes cuando cambien los parámetros
  useEffect(() => {
    fetchExpedientes();
  }, [fetchExpedientes]);

  // Función para manejar actualización de recompensa
  const handleUpdateRecompensa = async (id: number, data: RecompensaUpdateData) => {
    try {
      await expedienteService.actualizarRecompensa(id, data);
      // Recargar la lista para mostrar los cambios
      await fetchExpedientes();
    } catch (error) {
      console.error('Error al actualizar recompensa:', error);
      throw error; // Re-lanzar para que el componente hijo pueda manejarlo
    }
  };

  // Función para buscar delitos
  const buscarDelitos = useCallback(async (inputValue: string) => {
    if (!inputValue || inputValue.length < 2) {
      setDelitosOptions([]);
      return;
    }

    try {
      setLoadingDelitos(true);
      const delitos = await delitoService.searchDelitos(inputValue);
      const opcionSinDato: Delito = { id: -1, nombre: 'SIN DATO', codigoPenal: '', ley: '' };
      const delitosConSinDato = [opcionSinDato, ...delitos];
      setDelitosOptions(delitosConSinDato);
    } catch (error) {
      console.error('Error al buscar delitos:', error);
      setDelitosOptions([]);
    } finally {
      setLoadingDelitos(false);
    }
  }, []);

  // Función para limpiar filtros
  const limpiarFiltros = () => {
    setFechaDesde(null);
    setFechaHasta(null);
    setFiltroProfugo('');
    setFiltroNumero('');
    setFiltroFuerza('');
    setFiltroEstado('');
    setFiltroDelito(null);
    setDelitosOptions([]);
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  if (loading && expedientes.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Encabezado */}
      <Paper sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between', 
        p: 2, 
        mb: 2, 
        bgcolor: '#002856', 
        color: '#fff', 
        borderRadius: 3, 
        boxShadow: 4 
      }}>
        <Box sx={{ flex: '0 0 80px', display: 'flex', alignItems: 'center' }}>
          <img src="/images/logo-cufre-2.png" alt="Logo CUFRE" style={{ height: 56, objectFit: 'contain' }} />
        </Box>
        <Typography variant="h4" sx={{ flex: 1, textAlign: 'center', fontWeight: 'bold', letterSpacing: 1 }}>
          Programa Nacional de Recompensas
        </Typography>
      </Paper>

      {/* Barra de filtros */}
      <Paper sx={{ mb: 3, p: 2, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', boxShadow: 2, borderRadius: 2 }}>
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
          <DatePicker
            label="Fecha de inicio desde"
            value={fechaDesde}
            onChange={setFechaDesde}
            slotProps={{ textField: { size: 'small', sx: { minWidth: 170 } } }}
          />
          <DatePicker
            label="hasta"
            value={fechaHasta}
            onChange={setFechaHasta}
            slotProps={{ textField: { size: 'small', sx: { minWidth: 170 } } }}
          />
        </LocalizationProvider>
        
        <TextField
          label="Nombre de prófugo"
          value={filtroProfugo}
          onChange={(e) => setFiltroProfugo(e.target.value)}
          size="small"
          sx={{ minWidth: 200 }}
        />
        
        <TextField
          label="Número de expediente"
          value={filtroNumero}
          onChange={(e) => setFiltroNumero(e.target.value)}
          size="small"
          sx={{ minWidth: 180 }}
        />
        
        <TextField
          select
          label="Fuerza asignada"
          value={filtroFuerza}
          onChange={(e) => setFiltroFuerza(e.target.value)}
          size="small"
          sx={{ minWidth: 150 }}
        >
          <MenuItem value="">Todas</MenuItem>
          {fuerzasAsignadas.map((fuerza) => (
            <MenuItem key={fuerza} value={fuerza}>{fuerza}</MenuItem>
          ))}
        </TextField>
        
        <TextField
          select
          label="Estado"
          value={filtroEstado}
          onChange={(e) => setFiltroEstado(e.target.value)}
          size="small"
          sx={{ minWidth: 150 }}
        >
          <MenuItem value="">Todos</MenuItem>
          {estados.map((estado) => (
            <MenuItem key={estado} value={estado}>{estado}</MenuItem>
          ))}
        </TextField>

        <TextField
          select
          label="Ordenar por"
          value={orderBy}
          onChange={(e) => setOrderBy(e.target.value)}
          size="small"
          sx={{ minWidth: 150 }}
        >
          {opcionesOrdenamiento.map((opcion) => (
            <MenuItem key={opcion.value} value={opcion.value}>{opcion.label}</MenuItem>
          ))}
        </TextField>

        <TextField
          select
          label="Dirección"
          value={orderDirection}
          onChange={(e) => setOrderDirection(e.target.value as 'asc' | 'desc')}
          size="small"
          sx={{ minWidth: 120 }}
        >
          <MenuItem value="desc">Descendente</MenuItem>
          <MenuItem value="asc">Ascendente</MenuItem>
        </TextField>
        
        <Autocomplete
          options={delitosOptions}
          getOptionLabel={(option) => option.nombre}
          value={filtroDelito}
          onChange={(event, newValue) => setFiltroDelito(newValue)}
          onInputChange={(event, newInputValue) => buscarDelitos(newInputValue)}
          loading={loadingDelitos}
          size="small"
          sx={{ minWidth: 200 }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Delito"
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {loadingDelitos ? <CircularProgress color="inherit" size={20} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
        
        <Button variant="outlined" onClick={limpiarFiltros}>
          Limpiar Filtros
        </Button>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabla principal */}
      <TableContainer component={Paper} sx={{ boxShadow: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Table sx={{ width: '100%', tableLayout: 'auto' }}>
          <TableHead>
            <TableRow sx={{
              backgroundColor: '#1976d2',
              '& .MuiTableCell-head': {
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                borderBottom: '2px solid #1565c0'
              }
            }}>
              <TableCell sx={{ width: '12%', minWidth: 120 }}>Número Expediente</TableCell>
              <TableCell sx={{ width: '20%', minWidth: 180 }}>Nombre Prófugo</TableCell>
              <TableCell sx={{ width: '40%', minWidth: 200 }}>Causa</TableCell>
              <TableCell sx={{ width: '28%', minWidth: 220 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  💰 Recompensa
                </Box>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {expedientes.map((expediente, index) => (
              <TableRow
                key={expediente.id}
                hover
                sx={{
                  backgroundColor: index % 2 === 0 ? '#fafafa' : 'white',
                  '&:hover': {
                    backgroundColor: '#e3f2fd !important',
                    transform: 'scale(1.001)',
                    transition: 'all 0.2s ease-in-out'
                  },
                  '& .MuiTableCell-root': {
                    borderBottom: '1px solid #e0e0e0',
                    padding: '12px 16px'
                  }
                }}
              >
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', color: '#1976d2' }}>
                    {expediente.numero}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    {obtenerNombreProfugo(expediente)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.4
                    }}
                    title={obtenerCausa(expediente)}
                  >
                    {obtenerCausa(expediente)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ width: '100%', maxWidth: '100%' }}>
                    {!isConsulta ? (
                      <RecompensaEditableCell
                        expediente={expediente}
                        onUpdate={handleUpdateRecompensa}
                      />
                    ) : (
                      <Box sx={{
                        p: 1.5,
                        backgroundColor: '#f5f5f5',
                        borderRadius: 1,
                        border: '1px solid #e0e0e0',
                        width: '100%'
                      }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          {expediente.recompensa && expediente.montoRecompensa
                            ? `$${expediente.montoRecompensa}`
                            : 'Sin recompensa'
                          }
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Paginación */}
      <Paper sx={{ mt: 2, borderRadius: 2, boxShadow: 1 }}>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalItems}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Filas por página:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`}
          sx={{
            '& .MuiTablePagination-toolbar': {
              backgroundColor: '#f8f9fa',
              borderRadius: 2
            },
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontWeight: 'medium',
              color: '#1976d2'
            },
            '& .MuiIconButton-root': {
              '&:hover': {
                backgroundColor: '#e3f2fd'
              }
            }
          }}
        />
      </Paper>
    </Box>
  );
};

export default PNRecompensasPage;

export {};