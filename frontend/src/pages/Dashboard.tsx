import React from 'react';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Paper,
  Typography,
  Box,
  CircularProgress,
  Divider,
  Button,
  Card,
  CardContent,
  CardActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Folder as FolderIcon,
  Gavel as GavelIcon,
  Person as PersonIcon,
  CalendarToday as CalendarTodayIcon,
  AccessTime as AccessTimeIcon,
  Search as SearchIcon,
  BarChart as BarChartIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  FlashOn as FlashOnIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import estadisticaService from '../api/estadisticaService';
import { useAuth } from '../context/AuthContext';
import { Rol } from '../types/usuario.types';
import CountUp from 'react-countup';

interface DashboardStat {
  title: string;
  value: number;
  description: string;
  color: string;
  icon: React.ReactNode;
  path: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<any>(null);
  const { user } = useAuth();
  const [fechaHora, setFechaHora] = useState(new Date());
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const dashboardData = await estadisticaService.getDashboardData();
        setStats(dashboardData);
      } catch (error) {
        console.error('Error al cargar datos del dashboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => setFechaHora(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const mockStats: DashboardStat[] = [
    {
      title: 'Expedientes',
      value: stats?.totalExpedientes ?? 1256,
      description: 'Total de expedientes en el sistema',
      color: '#1976d2',
      icon: <FolderIcon />,
      path: '/expedientes'
    },
    {
      title: 'Delitos',
      value: stats?.totalDelitos ?? 87,
      description: 'Tipos de delitos registrados',
      color: '#dc004e',
      icon: <GavelIcon />,
      path: '/delitos'
    },
    {
      title: 'Usuarios',
      value: stats?.totalUsuarios ?? 120,
      description: 'Usuarios con acceso al sistema',
      color: '#ff9800',
      icon: <PersonIcon />,
      path: '/usuarios'
    },
    {
      title: 'Personas',
      value: stats?.totalPersonas ?? 3254,
      description: 'Personas vinculadas a expedientes',
      color: '#4caf50',
      icon: <PersonIcon />,
      path: '/personas'
    }
  ];

  const getSaludo = () => {
    const hora = new Date().getHours();
    if (hora >= 6 && hora < 12) return 'Buen día';
    if (hora >= 12 && hora < 19) return 'Buenas tardes';
    return 'Buenas noches';
  };

  // Función para verificar si el usuario puede crear expedientes
  const puedeCrearExpediente = () => {
    if (!user) return false;
    return [Rol.SUPERUSUARIO, Rol.ADMINISTRADOR, Rol.USUARIOCARGA].includes(user.rol);
  };

  // Definir los accesos rápidos
  const accesosRapidos = [
    {
      titulo: 'Ir a Más Buscados',
      descripcion: 'Acceder a los más buscados del CUFRE',
      icono: <SearchIcon />,
      color: '#e91e63',
      ruta: '/mas-buscados',
      habilitado: true,
      especial: false
    },
    {
      titulo: 'Centro de Estadísticas',
      descripcion: 'Ver estadísticas y métricas del sistema',
      icono: <BarChartIcon />,
      color: '#2196f3',
      ruta: '/estadisticas/centro-comando',
      habilitado: true,
      especial: true // Marcar como especial para manejar el estado
    },
    {
      titulo: 'Iterar Más Buscados',
      descripcion: 'Gestionar iteración de más buscados',
      icono: <RefreshIcon />,
      color: '#ff9800',
      ruta: '/expedientes/iterar-mas-buscados',
      habilitado: true,
      especial: false
    },
    {
      titulo: 'Crear Expediente',
      descripcion: 'Crear un nuevo expediente en el sistema',
      icono: <AddIcon />,
      color: '#4caf50',
      ruta: '/expedientes/crear',
      habilitado: puedeCrearExpediente(),
      especial: false
    }
  ];

  const manejarAccesoRapido = (ruta: string, habilitado: boolean, especial: boolean) => {
    if (habilitado) {
      // Si es el centro de estadísticas, navegar con el mismo comportamiento que el sidebar
      if (especial && ruta === '/estadisticas/centro-comando') {
        navigate(ruta, { state: { fromSidebar: true } });
      } else {
        navigate(ruta);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ width: '100%', maxWidth: 900, mx: 'auto', display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 4 }}>
        <Paper elevation={3} sx={{ p: 3, width: '100%', textAlign: 'center', mb: 2, bgcolor: '#f5faff' }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            {getSaludo()}, <span style={{ color: '#1976d2' }}>{user?.nombre} {user?.apellido}</span>.
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Aquí tienes un resumen del sistema.
          </Typography>
        </Paper>
        <Paper elevation={1} sx={{ p: 2, width: '100%', display: 'flex', justifyContent: 'space-around', alignItems: 'center', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarTodayIcon color="primary" />
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {fechaHora.toLocaleDateString('es-AR', { weekday: 'long' }).charAt(0).toUpperCase() +
                fechaHora.toLocaleDateString('es-AR', { weekday: 'long' }).slice(1)}
            </Typography>
          </Box>
          <Divider orientation="vertical" flexItem />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CalendarTodayIcon color="primary" />
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {fechaHora.toLocaleDateString()}
            </Typography>
          </Box>
          <Divider orientation="vertical" flexItem />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AccessTimeIcon color="primary" />
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {fechaHora.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false })}
            </Typography>
          </Box>
        </Paper>

      </Box>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4, justifyContent: 'center' }}>
        {mockStats.map((stat, index) => (
          <Box key={index} sx={{ width: { xs: '100%', sm: '47%', md: '22%' } }}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                borderLeft: `4px solid ${stat.color}`,
                userSelect: 'none',
                boxShadow: 2,
                transition: 'box-shadow 0.3s',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Box sx={{ 
                  borderRadius: '50%', 
                  bgcolor: `${stat.color}20`, 
                  p: 1, 
                  display: 'flex',
                  color: stat.color 
                }}>
                  {stat.icon}
                </Box>
                <Typography variant="h6" sx={{ ml: 2, fontWeight: 'bold' }}>
                  {stat.title}
                </Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                <CountUp end={stat.value} duration={1.2} />
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {stat.description}
              </Typography>
            </Paper>
          </Box>
        ))}
      </Box>

      {/* Sección de Accesos Rápidos */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            width: { xs: '100%', sm: '400px', md: '450px' },
            maxWidth: '500px'
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: '#1976d2', display: 'flex', alignItems: 'center' }}>
            <FlashOnIcon sx={{ mr: 1 }} />
            Accesos Rápidos
          </Typography>
          
          <List sx={{ p: 0 }}>
            {accesosRapidos.map((acceso, index) => (
              <React.Fragment key={index}>
                <ListItem
                  sx={{
                    px: 0,
                    cursor: acceso.habilitado ? 'pointer' : 'not-allowed',
                    opacity: acceso.habilitado ? 1 : 0.6,
                    borderRadius: 1,
                    transition: 'all 0.2s ease',
                    '&:hover': acceso.habilitado ? {
                      bgcolor: 'rgba(25, 118, 210, 0.04)',
                      transform: 'translateX(4px)'
                    } : {}
                  }}
                  onClick={() => manejarAccesoRapido(acceso.ruta, acceso.habilitado, acceso.especial)}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Box
                      sx={{
                        borderRadius: '50%',
                        bgcolor: `${acceso.color}20`,
                        p: 1,
                        display: 'flex',
                        color: acceso.color,
                        width: 32,
                        height: 32,
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {React.cloneElement(acceso.icono, { fontSize: 'small' })}
                    </Box>
                  </ListItemIcon>
                  <ListItemText
                    primary={acceso.titulo}
                    secondary={!acceso.habilitado ? 'Sin permisos suficientes' : acceso.descripcion}
                    primaryTypographyProps={{
                      fontWeight: 500,
                      color: acceso.habilitado ? 'text.primary' : 'text.disabled'
                    }}
                    secondaryTypographyProps={{
                      color: !acceso.habilitado ? 'error.main' : 'text.secondary',
                      fontSize: '0.75rem'
                    }}
                  />
                  {acceso.habilitado && (
                    <Box sx={{ color: acceso.color }}>
                      <ChevronRightIcon />
                    </Box>
                  )}
                </ListItem>
                {index < accesosRapidos.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      </Box>
    </Box>
  );
};

export default Dashboard;