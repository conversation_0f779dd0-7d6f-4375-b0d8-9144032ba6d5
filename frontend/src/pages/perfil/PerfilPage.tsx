import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Tabs,
  Tab,
  Typography,
  Breadcrumbs,
  Link,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  <PERSON>ton,
  Card,
  CardContent,
  Divider,
  Chip
} from '@mui/material';
import {
  Home,
  Person,
  Security,
  VpnKey,
  Shield,
  AccountCircle
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { PerfilUsuario } from '../../types/perfil.types';
import { usePerfilUsuario } from '../../hooks/usePerfilUsuario';
import ProfileForm from '../../components/perfil/ProfileForm';
import UserAvatar from '../../components/usuarios/UserAvatar';
import ChangePasswordModal from '../../components/perfil/ChangePasswordModal';
import authService from '../../api/authService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`perfil-tabpanel-${index}`}
      aria-labelledby={`perfil-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const PerfilPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  
  const [tabActual, setTabActual] = useState(0);
  const [perfilLocal, setPerfilLocal] = useState<PerfilUsuario | null>(null);
  const [modalCambiarContrasena, setModalCambiarContrasena] = useState(false);
  const [errorCambioContrasena, setErrorCambioContrasena] = useState<string | null>(null);
  const [successCambioContrasena, setSuccessCambioContrasena] = useState<string | null>(null);

  const {
    perfil,
    loading: loadingPerfil,
    error: errorPerfil,
    success: successPerfil,
    obtenerPerfil,
    actualizarPerfil
  } = usePerfilUsuario();

  // Cargar perfil al montar
  useEffect(() => {
    obtenerPerfil();
  }, [obtenerPerfil]);

  // Actualizar perfil local cuando cambie el perfil del hook
  useEffect(() => {
    if (perfil) {
      setPerfilLocal(perfil);
    }
  }, [perfil]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabActual(newValue);
  };

  const handleGuardarPerfil = async (perfilActualizado: PerfilUsuario) => {
    await actualizarPerfil(perfilActualizado);
    
    // Actualizar perfil local
    setPerfilLocal(perfilActualizado);
  };

  const handleAbrirModalCambiarContrasena = () => {
    setModalCambiarContrasena(true);
    setErrorCambioContrasena(null);
    setSuccessCambioContrasena(null);
  };

  const handleCerrarModalCambiarContrasena = () => {
    setModalCambiarContrasena(false);
    setErrorCambioContrasena(null);
    setSuccessCambioContrasena(null);
  };

  const handleCambiarContrasena = async (currentPassword: string, newPassword: string) => {
    try {
      setErrorCambioContrasena(null);
      
      // Llamada al servicio de cambio de contraseña
      await authService.cambiarContrasena(currentPassword, newPassword);
      
      setSuccessCambioContrasena('Contraseña cambiada exitosamente');
      
      // Cerrar modal después de un breve delay
      setTimeout(() => {
        handleCerrarModalCambiarContrasena();
      }, 1500);
      
    } catch (error: any) {
      setErrorCambioContrasena(
        error.message ||
        'Error al cambiar la contraseña. Verifica que la contraseña actual sea correcta.'
      );
      throw error; // Re-lanzar para que el modal maneje el estado de loading
    }
  };

  if (loadingPerfil && !perfil) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (errorPerfil && !perfil) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorPerfil}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          color="inherit"
          href="/"
          onClick={(e) => {
            e.preventDefault();
            navigate('/');
          }}
          sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
        >
          <Home sx={{ mr: 0.5 }} fontSize="inherit" />
          Inicio
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <Person sx={{ mr: 0.5 }} fontSize="inherit" />
          Mi Perfil
        </Typography>
      </Breadcrumbs>

      {/* Header con información del usuario */}
      {perfilLocal && (
        <Paper
          elevation={3}
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              zIndex: 0
            }
          }}
        >
          <Box sx={{
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            alignItems: isMobile ? 'center' : 'flex-start',
            gap: 4,
            position: 'relative',
            zIndex: 1
          }}>
            <Box sx={{ position: 'relative' }}>
              <UserAvatar
                usuario={{
                  ...user!,
                  username: user!.email,
                }}
                size="large"
              />
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -8,
                  right: -8,
                  backgroundColor: 'success.main',
                  borderRadius: '50%',
                  width: 24,
                  height: 24,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '3px solid white'
                }}
              >
                <AccountCircle sx={{ fontSize: 16, color: 'white' }} />
              </Box>
            </Box>
            
            <Box sx={{ flex: 1, textAlign: isMobile ? 'center' : 'left' }}>
              <Typography variant="h3" gutterBottom sx={{ fontWeight: 'bold', mb: 1 }}>
                {perfilLocal.nombre} {perfilLocal.apellido}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2, justifyContent: isMobile ? 'center' : 'flex-start' }}>
                <Chip
                  icon={<Shield />}
                  label={perfilLocal.rol}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 'bold',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              </Box>
              
              {perfilLocal.dependencia && (
                <Typography variant="h6" sx={{ mb: 2, opacity: 0.9 }}>
                  📍 {perfilLocal.dependencia}
                </Typography>
              )}
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  📧 {perfilLocal.email}
                </Typography>
                
                {perfilLocal.telefonoMovil && (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    📱 {perfilLocal.telefonoMovil}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Paper>
      )}

      {/* Mensajes de estado */}
      {successPerfil && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successPerfil}
        </Alert>
      )}

      {errorPerfil && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorPerfil}
        </Alert>
      )}

      {/* Contenido principal con tabs */}
      <Paper
        elevation={3}
        sx={{
          width: '100%',
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider', backgroundColor: 'grey.50' }}>
          <Tabs
            value={tabActual}
            onChange={handleTabChange}
            variant={isMobile ? 'fullWidth' : 'standard'}
            aria-label="Tabs de perfil"
          >
            <Tab
              icon={<Person />}
              label="Información Personal"
              id="perfil-tab-0"
              aria-controls="perfil-tabpanel-0"
            />
            <Tab
              icon={<Security />}
              label="Seguridad"
              id="perfil-tab-1"
              aria-controls="perfil-tabpanel-1"
            />
          </Tabs>
        </Box>

        {/* Tab Panel - Información Personal */}
        <TabPanel value={tabActual} index={0}>
          <Box sx={{ p: 4 }}>
            {perfilLocal && (
              <ProfileForm
                perfil={perfilLocal}
                onGuardar={handleGuardarPerfil}
                loading={loadingPerfil}
                error={errorPerfil || undefined}
                success={successPerfil || undefined}
                esEdicionCompleta={user?.rol === 'SUPERUSUARIO'}
              />
            )}
          </Box>
        </TabPanel>

        {/* Tab Panel - Seguridad */}
        <TabPanel value={tabActual} index={1}>
          <Box sx={{ p: 4 }}>
            <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <Security color="primary" />
              Configuración de Seguridad
            </Typography>
            
            <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 4 }}>
              Gestiona la seguridad de tu cuenta y mantén tu información protegida.
            </Typography>

            {/* Mensajes de estado para cambio de contraseña */}
            {successCambioContrasena && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {successCambioContrasena}
              </Alert>
            )}

            {errorCambioContrasena && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {errorCambioContrasena}
              </Alert>
            )}
            
            {/* Sección de Contraseña */}
            <Card elevation={2} sx={{ mb: 3, borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <VpnKey color="primary" />
                  <Typography variant="h6">
                    Contraseña
                  </Typography>
                </Box>
                
                <Typography variant="body2" color="text.secondary" paragraph>
                  Cambia tu contraseña regularmente para mantener tu cuenta segura.
                  La nueva contraseña debe cumplir con los requisitos de seguridad.
                </Typography>
                
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      Última modificación
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Información no disponible
                    </Typography>
                  </Box>
                  
                  <Button
                    variant="contained"
                    startIcon={<VpnKey />}
                    onClick={handleAbrirModalCambiarContrasena}
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      px: 3,
                      py: 1.5
                    }}
                  >
                    Cambiar Contraseña
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Información adicional de seguridad */}
            <Card elevation={1} sx={{ borderRadius: 2, backgroundColor: 'grey.50' }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Shield color="info" />
                  <Typography variant="h6">
                    Consejos de Seguridad
                  </Typography>
                </Box>
                
                <Typography variant="body2" color="text.secondary" component="ul" sx={{ m: 0, pl: 2 }}>
                  <li>Usa una contraseña única y compleja</li>
                  <li>No compartas tu contraseña con nadie</li>
                  <li>Cambia tu contraseña si sospechas que fue comprometida</li>
                  <li>Cierra sesión cuando uses computadoras compartidas</li>
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </TabPanel>
      </Paper>

      {/* Modal de Cambio de Contraseña */}
      <ChangePasswordModal
        open={modalCambiarContrasena}
        onClose={handleCerrarModalCambiarContrasena}
        onChangePassword={handleCambiarContrasena}
        loading={loadingPerfil}
      />
    </Container>
  );
};

export default PerfilPage;