import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  TextField,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  MenuItem,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';
import expedienteService from '../../api/expedienteService';
import delitoService from '../../api/delitoService';
import { Expediente } from '../../types/expediente.types';
import { Delito } from '../../types/delito.types';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { es } from 'date-fns/locale';
import { useAuth } from '../../context/AuthContext';
import { Rol } from '../../types/usuario.types';

const fuerzasAsignadas = [
  'S/D', 'GNA', 'PFA', 'PSA', 'PNA', 'SPF',
  'POL LOCAL', 'INTERPOL', 'AMERIPOL', 'EUROPOL', 'BLOQUE DE BÚSQUEDA CUFRE'
];

const estados = [
  'SIN EFECTO', 'CAPTURA VIGENTE', 'DETENIDO', 'SIN DATO'
];

const ExpedientesPage: React.FC = () => {
  const [expedientes, setExpedientes] = useState<Expediente[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Estado para controlar la ordenación
  const [orderBy, setOrderBy] = useState<string>(searchParams.get('orderBy') || 'id');
  const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>(
    (searchParams.get('orderDirection') as 'asc' | 'desc') || 'desc'
  );
  
  // Paginación
  const [page, setPage] = useState<number>(parseInt(searchParams.get('page') || '0'));
  const [rowsPerPage, setRowsPerPage] = useState<number>(parseInt(searchParams.get('rowsPerPage') || '10'));
  const [totalItems, setTotalItems] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  
  const navigate = useNavigate();

  // Inicializar fechas desde URL si existen
  const fechaDesdeParam = searchParams.get('fechaDesde');
  const fechaHastaParam = searchParams.get('fechaHasta');
  
  const [fechaDesde, setFechaDesde] = useState<Date | null>(
    fechaDesdeParam ? new Date(fechaDesdeParam) : null
  );
  const [fechaHasta, setFechaHasta] = useState<Date | null>(
    fechaHastaParam ? new Date(fechaHastaParam) : null
  );
  
  const [filtroProfugo, setFiltroProfugo] = useState(searchParams.get('profugo') || '');
  const [filtroNumero, setFiltroNumero] = useState(searchParams.get('numero') || '');
  const [filtroFuerza, setFiltroFuerza] = useState(searchParams.get('fuerzaAsignada') || '');
  const [filtroEstado, setFiltroEstado] = useState(searchParams.get('estadoSituacion') || '');
  const [filtroDelito, setFiltroDelito] = useState<Delito | null>(null);

  // Estados para debouncing
  const [filtroProfugoDebounced, setFiltroProfugoDebounced] = useState(searchParams.get('profugo') || '');
  const [filtroNumeroDebounced, setFiltroNumeroDebounced] = useState(searchParams.get('numero') || '');
  
  // Estados para autocompletado de delitos
  const [delitosOptions, setDelitosOptions] = useState<Delito[]>([]);
  const [loadingDelitos, setLoadingDelitos] = useState(false);

  const { user } = useAuth();
  const isCarga = user?.rol === Rol.USUARIOCARGA;
  const isConsulta = user?.rol === Rol.USUARIOCONSULTA;

  // --- NUEVO: Input para salto rápido de página ---
  const [goToPage, setGoToPage] = useState<string>('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Efectos para debouncing de filtros de texto
  useEffect(() => {
    const timer = setTimeout(() => {
      setFiltroProfugoDebounced(filtroProfugo);
    }, 500);
    return () => clearTimeout(timer);
  }, [filtroProfugo]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setFiltroNumeroDebounced(filtroNumero);
    }, 500);
    return () => clearTimeout(timer);
  }, [filtroNumero]);

  // Función para actualizar los query parameters en la URL
  const updateUrlParams = useCallback(() => {
    const params = new URLSearchParams();
    
    // Añadir parámetros de paginación y ordenación
    params.set('page', page.toString());
    params.set('rowsPerPage', rowsPerPage.toString());
    params.set('orderBy', orderBy);
    params.set('orderDirection', orderDirection);
    
    // Añadir filtros activos (usar valores debounced para texto)
    if (filtroNumeroDebounced) params.set('numero', filtroNumeroDebounced);
    if (filtroProfugoDebounced) params.set('profugo', filtroProfugoDebounced);
    if (filtroFuerza) params.set('fuerzaAsignada', filtroFuerza);
    if (filtroEstado) params.set('estadoSituacion', filtroEstado);
    if (filtroDelito) params.set('delitoNombre', filtroDelito.nombre || '');
    
    // Añadir fechas si existen
    if (fechaDesde) params.set('fechaDesde', fechaDesde.toISOString().split('T')[0]);
    if (fechaHasta) params.set('fechaHasta', fechaHasta.toISOString().split('T')[0]);
    
    setSearchParams(params);
  }, [page, rowsPerPage, orderBy, orderDirection, filtroNumeroDebounced, filtroProfugoDebounced, filtroFuerza, filtroEstado, filtroDelito, fechaDesde, fechaHasta, setSearchParams]);

  // Usar useCallback para evitar recreaciones innecesarias de la función
  const fetchExpedientes = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      // Construir objeto de filtros usando valores debounced
      const filters: Record<string, any> = {};
      if (filtroNumeroDebounced) filters.numero = filtroNumeroDebounced;
      if (filtroProfugoDebounced) filters.profugo = filtroProfugoDebounced;
      if (filtroFuerza) filters.fuerzaAsignada = filtroFuerza;
      // Manejo especial para el filtro de estado
      if (filtroEstado) {
        // Enviamos el filtro de estado tal cual, incluso si es 'SIN DATO'
        // El backend debe interpretarlo correctamente
        filters.estadoSituacion = filtroEstado;
      }
      // Agregar filtro por delito
      if (filtroDelito) {
        if (filtroDelito.nombre === 'SIN DATO') {
          filters.delitoNombre = 'SIN_DATO';
        } else {
          filters.delitoNombre = filtroDelito.nombre;
        }
      }
      if (fechaDesde) filters.fechaDesde = fechaDesde.toISOString().split('T')[0];
      if (fechaHasta) filters.fechaHasta = fechaHasta.toISOString().split('T')[0];
      
      // Incluir parámetros de ordenación en los filtros
      const filtersWithSort = {
        ...filters,
        orderBy: orderBy,
        orderDirection: orderDirection
      };
      
      console.log('Solicitando expedientes con filtros:', filtersWithSort);
      
      // Actualizar URL con los parámetros actuales
      updateUrlParams();
      
      const response = await expedienteService.getExpedientes(page, rowsPerPage, '', filtersWithSort);
      if (Array.isArray(response)) {
        setExpedientes(response);
        setTotalItems(response.length);
        setTotalPages(Math.ceil(response.length / rowsPerPage));
      } else {
        setExpedientes(response.content || []);
        setTotalItems(response.totalElements || 0);
        setTotalPages(response.totalPages || 0);
      }
    } catch (err: any) {
      console.log('Error al obtener expedientes:', err);
      setError('Error al cargar los expedientes. Por favor, intente nuevamente.');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, orderBy, orderDirection, filtroNumeroDebounced, filtroProfugoDebounced, filtroFuerza, filtroEstado, filtroDelito, fechaDesde, fechaHasta, updateUrlParams]);
  
  // Efecto para cargar los expedientes cuando cambien los parámetros
  useEffect(() => {
    fetchExpedientes();
  }, [fetchExpedientes]);;

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
    // fetchExpedientes será llamado por el efecto al cambiar página
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    // fetchExpedientes será llamado por el efecto al cambiar rowsPerPage
  };

  // El array expedientes ya contiene los datos de la página actual, no aplicar más filtros locales
  // const filteredExpedientes = expedientes;

  const handleCreate = () => {
    navigate('/expedientes/crear');
  };

  const handleEdit = (id: number) => {
    // Al editar, pasamos a la URL del formulario el estado actual de los filtros
    // para poder volver exactamente al mismo punto
    navigate(`/expedientes/editar/${id}?${searchParams.toString()}`);
  };

  const handleViewDetails = (id: number) => {
    navigate(`/expedientes/${id}`);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('¿Está seguro que desea eliminar este expediente?')) {
      try {
        await expedienteService.delete(id);
        fetchExpedientes(); // Recargar la lista
      } catch (err: any) {
        console.error('Error al eliminar expediente:', err);
        setError(err.response?.data?.message || 'Error al eliminar el expediente');
      }
    }
  };

  // Nueva función para obtener el label y color de estado
  const getEstadoInfo = (estado?: string) => {
    const valor = (estado || '').toUpperCase();
    switch (valor) {
      case 'CAPTURA VIGENTE':
        return { label: 'CAPTURA VIGENTE', color: '#ffcccc' };
      case 'DETENIDO':
        return { label: 'DETENIDO', color: '#d0f5d8' };
      case 'SIN EFECTO':
        return { label: 'SIN EFECTO', color: '#ffe5b4' };
      default:
        return { label: 'SIN DATO', color: 'inherit' };
    }
  };

  // Mapeo de fuerza asignada a imagen
  const fuerzaIconos: Record<string, { src: string; alt: string }> = {
    PFA: { src: '/images/icon1.png', alt: 'Policía Federal Argentina' },
    GNA: { src: '/images/Insignia_de_la_Gendarmería_de_Argentina.svg.png', alt: 'Gendarmería Nacional Argentina' },
    PNA: { src: '/images/icon3.png', alt: 'Prefectura Naval Argentina' },
    PSA: { src: '/images/icon4.png', alt: 'Policía de Seguridad Aeroportuaria' },
    INTERPOOL: { src: '/images/interpol.png', alt: 'Interpol' },
    SPF: { src: '/images/Logo_SPF.png', alt: 'Servicio Penitenciario Federal' },
    CUFRE: { src: '/images/logo-cufre-2.png', alt: 'CUFRE' },
  };

  // Filtro avanzado
  // const expedientesFiltrados = expedientes.filter(expediente => {
  //   // Filtro por número de expediente
  //   if (filtroNumero && !expediente.numero.toLowerCase().includes(filtroNumero.toLowerCase())) return false;
  //   // Filtro por nombre de prófugo
  //   if (filtroProfugo && !(Array.isArray(expediente.profugos) && expediente.profugos.join(' ').toLowerCase().includes(filtroProfugo.toLowerCase()))) return false;
  //   // Filtro por fuerza
  //   if (
  //     filtroFuerza &&
  //     (expediente.fuerzaAsignada || '').trim().toUpperCase() !== filtroFuerza.trim().toUpperCase()
  //   ) return false;
  //   // Filtro por estado
  //   if (
  //     filtroEstado &&
  //     (expediente.estadoSituacion || '').trim().toUpperCase() !== filtroEstado.trim().toUpperCase()
  //   ) return false;
  //   // Filtro por fecha de inicio
  //   const fechaStr = expediente.fechaIngreso || expediente.fechaInicio;
  //   // ...
  //   return true;
  // });

  // Función para manejar el cambio de ordenación
  const handleSort = (field: string) => {
    // Si ya estamos ordenando por este campo, cambiamos la dirección
    if (orderBy === field) {
      const newDirection = orderDirection === 'asc' ? 'desc' : 'asc';
      setOrderDirection(newDirection);
      console.log(`Ordenando por ${field} en dirección ${newDirection}`);
    } else {
      // Si es un nuevo campo, establecemos el campo y la dirección por defecto (descendente)
      setOrderBy(field);
      setOrderDirection('desc');
      console.log(`Ordenando por ${field} en dirección desc`);
    }
    // Resetear a la primera página cuando cambia la ordenación
    setPage(0);
  };

  const handleGoToPage = (e: React.FormEvent) => {
    e.preventDefault();
    const num = parseInt(goToPage, 10);
    if (!isNaN(num) && num > 0 && num <= totalPages) {
      setPage(num - 1);
      setGoToPage('');
      inputRef.current?.blur();
    }
  };

  // Función para buscar delitos para el autocompletado
  const buscarDelitos = useCallback(async (inputValue: string) => {
    if (!inputValue || inputValue.length < 2) {
      setDelitosOptions([]);
      return;
    }

    try {
      setLoadingDelitos(true);
      const delitos = await delitoService.searchDelitos(inputValue);
      // Agregar opción "SIN DATO" si no está ya incluida
      const opcionSinDato: Delito = { id: -1, nombre: 'SIN DATO', codigoPenal: '', ley: '' };
      const delitosConSinDato = [opcionSinDato, ...delitos];
      setDelitosOptions(delitosConSinDato);
    } catch (error) {
      console.error('Error al buscar delitos:', error);
      setDelitosOptions([]);
    } finally {
      setLoadingDelitos(false);
    }
  }, []);

  // Función para limpiar todos los filtros
  const limpiarFiltros = () => {
    setFechaDesde(null);
    setFechaHasta(null);
    setFiltroProfugo('');
    setFiltroNumero('');
    setFiltroFuerza('');
    setFiltroEstado('');
    setFiltroDelito(null);
    setDelitosOptions([]);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Encabezado profesional */}
      <Paper sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2, mb: 2, bgcolor: '#002856', color: '#fff', borderRadius: 3, boxShadow: 4 }}>
        <Box sx={{ flex: '0 0 80px', display: 'flex', alignItems: 'center' }}>
          <img src="/images/logo-cufre-2.png" alt="Logo CUFRE" style={{ height: 56, objectFit: 'contain' }} />
        </Box>
        <Typography variant="h4" sx={{ flex: 1, textAlign: 'center', fontWeight: 'bold', letterSpacing: 1 }}>Lista de Expedientes</Typography>
        {!isConsulta && (
        <Button
          variant="contained"
          color="secondary"
          startIcon={<AddIcon />}
          onClick={handleCreate}
          sx={{ fontWeight: 'bold', boxShadow: 2 }}
        >
          Nuevo Expediente
        </Button>
        )}
      </Paper>
      {/* Barra de filtros */}
      <Paper sx={{ mb: 3, p: 2, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', boxShadow: 2, borderRadius: 2 }}>
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
          <DatePicker
            label="Fecha de inicio desde"
            value={fechaDesde}
            onChange={setFechaDesde}
            slotProps={{ textField: { size: 'small', sx: { minWidth: 170 } } }}
          />
          <DatePicker
            label="hasta"
            value={fechaHasta}
            onChange={setFechaHasta}
            slotProps={{ textField: { size: 'small', sx: { minWidth: 170 } } }}
          />
        </LocalizationProvider>
        <TextField
          label="Nombre de prófugo"
          size="small"
          value={filtroProfugo}
          onChange={e => setFiltroProfugo(e.target.value)}
          sx={{ minWidth: 180 }}
        />
        <TextField
          label="Número de expediente"
          size="small"
          value={filtroNumero}
          onChange={e => setFiltroNumero(e.target.value)}
          sx={{ minWidth: 180 }}
        />
        <TextField
          label="Fuerza asignada"
          size="small"
          select
          value={filtroFuerza}
          onChange={e => setFiltroFuerza(e.target.value)}
          sx={{ minWidth: 160 }}
        >
          <MenuItem value="">Todas</MenuItem>
          {fuerzasAsignadas.map(f => <MenuItem key={f} value={f}>{f}</MenuItem>)}
        </TextField>
        <TextField
          label="Estado"
          size="small"
          select
          value={filtroEstado}
          onChange={e => setFiltroEstado(e.target.value)}
          sx={{ minWidth: 140 }}
        >
          <MenuItem value="">Todos</MenuItem>
          {estados.map(e => <MenuItem key={e} value={e}>{e}</MenuItem>)}
        </TextField>
        <Autocomplete
          options={delitosOptions}
          getOptionLabel={(option) => option.nombre || ''}
          value={filtroDelito}
          onChange={(event, newValue) => setFiltroDelito(newValue)}
          onInputChange={(event, newInputValue) => {
            buscarDelitos(newInputValue);
          }}
          loading={loadingDelitos}
          loadingText="Buscando delitos..."
          noOptionsText="No se encontraron delitos"
          renderInput={(params) => (
            <TextField
              {...params}
              label="Delito"
              size="small"
              sx={{ minWidth: 200 }}
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {loadingDelitos ? <CircularProgress color="inherit" size={20} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
          isOptionEqualToValue={(option, value) => option.id === value.id}
        />
        <Button variant="outlined" color="secondary" onClick={limpiarFiltros}>
          Limpiar filtros
        </Button>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 3, boxShadow: 3, borderRadius: 2 }}>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#e3f2fd' }}>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => handleSort('id')}>
                    Número Expediente
                    {orderBy === 'id' && (
                      <IconButton size="small" sx={{ ml: 0.5 }}>
                        {orderDirection === 'asc' ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />}
                      </IconButton>
                    )}
                  </Box>
                </TableCell>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>Número Causa</TableCell>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>Fecha de Inicio</TableCell>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>Estado</TableCell>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>Prófugo</TableCell>
                <TableCell sx={{ color: '#1565c0', fontWeight: 'bold' }}>Fuerza Asignada</TableCell>
                <TableCell align="center" sx={{ color: '#1565c0', fontWeight: 'bold' }}>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {expedientes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    No se encontraron expedientes
                  </TableCell>
                </TableRow>
              ) : (
                expedientes.map((expediente, idx) => {
                  const estadoInfo = getEstadoInfo(expediente.estadoSituacion);
                  // Chip de estado
                  let chipColor = 'default';
                  if (estadoInfo.label === 'CAPTURA VIGENTE') chipColor = 'error';
                  else if (estadoInfo.label === 'DETENIDO') chipColor = 'success';
                  else if (estadoInfo.label === 'SIN EFECTO') chipColor = 'warning';
                  // Prófugo visual
                  const tieneProfugo = Array.isArray(expediente.profugos) && expediente.profugos.length > 0;
                  // Alternar color de fila
                  const rowBg = idx % 2 === 0 ? '#fff' : '#fafafa';
                  return (
                    <TableRow
                      key={expediente.id}
                      hover
                      sx={{
                        backgroundColor: rowBg,
                        '&:hover': { backgroundColor: '#e3f2fd' },
                        transition: 'background 0.2s',
                      }}
                    >
                      <TableCell sx={{ fontWeight: 'bold' }}>{expediente.numero}</TableCell>
                      <TableCell>{expediente.numeroCausa}</TableCell>
                      <TableCell>{expediente.fechaIngreso || expediente.fechaInicio}</TableCell>
                      <TableCell>
                        <Chip
                          label={estadoInfo.label}
                          color={chipColor as any}
                          size="small"
                          sx={{ fontWeight: 'bold', fontSize: 14 }}
                        />
                      </TableCell>
                      <TableCell>
                        {tieneProfugo ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <span style={{ color: '#1565c0', fontWeight: 700, fontSize: 15 }}>
                              <span role="img" aria-label="persona">👤</span>
                              {expediente.profugos!.map(n => n.toUpperCase()).join(', ')}
                            </span>
                          </Box>
                        ) : (
                          <span style={{ color: '#888' }}>S/D</span>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {(() => {
                          const fuerza = (expediente.fuerzaAsignada || '').toUpperCase();
                          const icono = fuerzaIconos[fuerza];
                          if (icono) {
                            return (
                              <Tooltip title={fuerza} arrow>
                                <img src={icono.src} alt={icono.alt} style={{ height: 32, display: 'block', margin: '0 auto' }} />
                              </Tooltip>
                            );
                          }
                          return expediente.fuerzaAsignada || '-';
                        })()}
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                          <Tooltip title="Ver detalles">
                            <IconButton onClick={() => handleViewDetails(expediente.id!)}>
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Editar">
                            <IconButton onClick={() => handleEdit(expediente.id!)}>
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          {!(isCarga || isConsulta) && (
                          <Tooltip title="Eliminar">
                            <IconButton 
                              onClick={() => handleDelete(expediente.id!)} 
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', px: 2, py: 1 }}>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50, 100]}
            component="div"
            count={totalItems}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="Filas por página"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
          />
          {/* NUEVO: Salto rápido de página */}
          <form onSubmit={handleGoToPage} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: 14 }}>Ir a página:</span>
            <input
              ref={inputRef}
              type="number"
              min={1}
              max={totalPages}
              value={goToPage}
              onChange={e => setGoToPage(e.target.value)}
              style={{ width: 60, padding: 4, borderRadius: 4, border: '1px solid #ccc', fontSize: 14 }}
            />
            <Button type="submit" variant="outlined" size="small" sx={{ minWidth: 36, fontSize: 13, py: 0.5 }}>Ir</Button>
            <span style={{ fontSize: 14, marginLeft: 8 }}>
              Página {page + 1} de {totalPages}
            </span>
          </form>
        </Box>
      </Paper>
    </Box>
  );
};

export default ExpedientesPage; 