import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  But<PERSON>,
  Tooltip,
  Container,
  Card,
  CardContent,
  CardActions,
  Chip,
  Paper
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Public as PublicIcon,
  Security as SecurityIcon,
  Gavel as GavelIcon,
  Flag as FlagIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  minHeight: '400px',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
  borderRadius: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(3),
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  '&:hover': {
    transform: 'scale(1.02)',
  },
  transition: 'all 0.2s ease-in-out',
}));

const HeaderBox = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  padding: theme.spacing(4),
  borderRadius: theme.spacing(2),
  marginBottom: theme.spacing(4),
  textAlign: 'center',
}));

const EnlacesExternosPage: React.FC = () => {
  const enlaces = [
    {
      nombre: 'Interpol',
      url: 'https://www.interpol.int/es/Como-trabajamos/Notificaciones/Notificaciones-rojas/Ver-las-notificaciones-rojas',
      descripcion: 'Ver las notificaciones rojas de Interpol.',
      categoria: 'Internacional',
      pais: 'Global',
      icon: <PublicIcon />,
      color: '#1976d2'
    },
    {
      nombre: 'Ministerio de Seguridad de la Provincia de Buenos Aires',
      nombreCorto: 'Min. Seguridad Buenos Aires',
      url: 'https://www.mseg.gba.gov.ar/areas/recompensas/profugos.html',
      descripcion: 'Prófugos con recompensa del Ministerio de Seguridad de la Provincia de Buenos Aires.',
      categoria: 'Nacional',
      pais: 'Argentina',
      icon: <SecurityIcon />,
      color: '#388e3c'
    },
    {
      nombre: 'Programa Nacional de Recompensas',
      nombreCorto: 'Programa Nacional de Recompensas',
      url: 'https://www.argentina.gob.ar/seguridad/recompensas',
      descripcion: 'Programa Nacional de Recompensas del Ministerio de Seguridad de la Nación Argentina.',
      categoria: 'Nacional',
      pais: 'Argentina',
      icon: <SecurityIcon />,
      color: '#388e3c'
    },
    {
      nombre: 'Dirección Nacional de Registro de Reincidencia',
      nombreCorto: 'DNREC - Los Más Buscados',
      url: 'https://www.dnrec.jus.gov.ar/masbuscados',
      descripcion: 'Los más buscados por la Dirección Nacional de Registro de Reincidencia de Argentina.',
      categoria: 'Nacional',
      pais: 'Argentina',
      icon: <GavelIcon />,
      color: '#388e3c'
    },
    {
      nombre: 'Policía Nacional de Colombia',
      nombreCorto: 'Policía Nacional Colombia',
      url: 'https://www.policia.gov.co/categorias-volante-mas-buscado/lo-mas-buscados-colombia',
      descripcion: 'Los más buscados por la Policía Nacional de Colombia.',
      categoria: 'Internacional',
      pais: 'Colombia',
      icon: <FlagIcon />,
      color: '#fbc02d'
    },
    {
      nombre: 'FBI – Ten Most Wanted Fugitives',
      nombreCorto: 'FBI Most Wanted',
      url: 'https://www.fbi.gov/wanted/topten',
      descripcion: 'Lista oficial de los 10 fugitivos más buscados por el FBI.',
      categoria: 'Internacional',
      pais: 'Estados Unidos',
      icon: <SecurityIcon />,
      color: '#d32f2f'
    },
    {
      nombre: 'DEA – Most Wanted',
      nombreCorto: 'DEA Most Wanted',
      url: 'https://www.dea.gov/fugitives',
      descripcion: 'Lista de criminales buscados por la Agencia Antidrogas de EE.UU.',
      categoria: 'Internacional',
      pais: 'Estados Unidos',
      icon: <SecurityIcon />,
      color: '#d32f2f'
    },
    {
      nombre: 'ICE – Los más buscados por ICE',
      nombreCorto: 'ICE Most Wanted',
      url: 'https://www.ice.gov/es/los-mas-buscados-por-ice',
      descripcion: 'Los más buscados por Inmigración y Control de Aduanas de EE.UU.',
      categoria: 'Internacional',
      pais: 'Estados Unidos',
      icon: <SecurityIcon />,
      color: '#d32f2f'
    },
    {
      nombre: 'ATF – Agencia de Alcohol, Tabaco, Armas de Fuego y Explosivos',
      nombreCorto: 'ATF Most Wanted',
      url: 'https://www.atf.gov/es/most-wanted',
      descripcion: 'Los más buscados por la ATF de EE.UU.',
      categoria: 'Internacional',
      pais: 'Estados Unidos',
      icon: <SecurityIcon />,
      color: '#d32f2f'
    },
    {
      nombre: 'Ministerio de Gobierno de Ecuador',
      nombreCorto: 'Gobierno Ecuador',
      url: 'https://www.ministeriodegobierno.gob.ec/programa-de-los-mas-buscados-continua-con-resultados-positivos/',
      descripcion: 'Programa de los más buscados del Ministerio de Gobierno de Ecuador.',
      categoria: 'Internacional',
      pais: 'Ecuador',
      icon: <FlagIcon />,
      color: '#fbc02d'
    },
    {
      nombre: 'Procurados Brasil',
      nombreCorto: 'Procurados Brasil',
      url: 'https://procurados.org.br/',
      descripcion: 'Los más buscados de Brasil.',
      categoria: 'Internacional',
      pais: 'Brasil',
      icon: <FlagIcon />,
      color: '#388e3c'
    },
    {
      nombre: 'Policía de Bolivia',
      nombreCorto: 'Policía Bolivia',
      url: 'https://www.policia.bo/buscados/',
      descripcion: 'Los más buscados por la policía de Bolivia.',
      categoria: 'Internacional',
      pais: 'Bolivia',
      icon: <FlagIcon />,
      color: '#fbc02d'
    },
  ];

  const getCategoryColor = (categoria: string) => {
    return categoria === 'Internacional' ? 'primary' : 'success';
  };

  return (
    <Container maxWidth="lg">
      <HeaderBox>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
          Enlaces a Organismos Externos
        </Typography>
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          Acceda a sitios web de organismos nacionales e internacionales especializados en la búsqueda de personas
        </Typography>
      </HeaderBox>

      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(3, 1fr)'
          },
          gap: 3
        }}
      >
        {enlaces.map((enlace, index) => (
          <Tooltip
            key={index}
            title={enlace.descripcion}
            arrow
            placement="top"
            componentsProps={{
              tooltip: {
                sx: {
                  bgcolor: 'rgba(0, 0, 0, 0.9)',
                  fontSize: '0.875rem',
                  maxWidth: 300,
                }
              }
            }}
          >
            <StyledCard sx={{ height: '100%' }}>
              <CardContent sx={{ flexGrow: 1, p: 3, display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      color: enlace.color,
                      mr: 2,
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {enlace.icon}
                  </Box>
                  <Chip
                    label={enlace.categoria}
                    color={getCategoryColor(enlace.categoria)}
                    size="small"
                    variant="outlined"
                  />
                </Box>
                
                <Typography
                  variant="h6"
                  component="h2"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    lineHeight: 1.3,
                    minHeight: '3.2em',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                >
                  {enlace.nombreCorto || enlace.nombre}
                </Typography>
                
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    flexGrow: 1
                  }}
                >
                  {enlace.descripcion}
                </Typography>
                
                <Paper
                  elevation={0}
                  sx={{
                    bgcolor: 'grey.50',
                    p: 1,
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mt: 'auto'
                  }}
                >
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
                    {enlace.pais}
                  </Typography>
                </Paper>
              </CardContent>
              
              <CardActions sx={{ p: 3, pt: 0 }}>
                <StyledButton
                  variant="contained"
                  fullWidth
                  onClick={() => window.open(enlace.url, '_blank', 'noopener,noreferrer')}
                  endIcon={<OpenInNewIcon />}
                  sx={{
                    bgcolor: enlace.color,
                    '&:hover': {
                      bgcolor: enlace.color,
                      filter: 'brightness(0.9)',
                    }
                  }}
                >
                  Visitar Sitio
                </StyledButton>
              </CardActions>
            </StyledCard>
          </Tooltip>
        ))}
      </Box>

      <Box sx={{ mt: 6, textAlign: 'center' }}>
        <Paper 
          elevation={1} 
          sx={{ 
            p: 3, 
            bgcolor: 'grey.50',
            borderRadius: 2
          }}
        >
          <Typography variant="body2" color="text.secondary">
            <strong>Nota:</strong> Los enlaces externos se abren en una nueva pestaña. 
            Verifique siempre la autenticidad de los sitios web antes de proporcionar información sensible.
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default EnlacesExternosPage;