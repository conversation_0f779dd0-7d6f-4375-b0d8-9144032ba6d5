import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  TableSortLabel,
  Tooltip,
  Skeleton,
  Snackbar,
  TextField
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PersonAddRounded,
  GetApp as ExportIcon,
  ExitToApp as LogoutIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import usuarioService from '../../api/usuarioService';
import { Usuario, Rol } from '../../types/usuario.types';
import { useAuth } from '../../context/AuthContext';

// Importar los nuevos componentes
import UserFilters, { UserFiltersState } from '../../components/usuarios/UserFilters';
import UserStats from '../../components/usuarios/UserStats';
import UserAvatar from '../../components/usuarios/UserAvatar';
import RoleChip from '../../components/usuarios/RoleChip';
import RoleChangeDialog from '../../components/usuarios/RoleChangeDialog';
import ForceLogoutDialog from '../../components/usuarios/ForceLogoutDialog';

// Importar utilidades
import { filterUsers, sortUsers, getUniqueValues, saveFiltersToStorage, loadFiltersFromStorage } from '../../utils/userFilters';
import { exportUsersToCSV } from '../../utils/userExport';

type SortField = 'nombre' | 'apellido' | 'email' | 'rol' | 'dependencia';
type SortOrder = 'asc' | 'desc';

const UsuariosPage: React.FC = () => {
  const { user } = useAuth();
  
  // Estados principales
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Estados de filtros
  const [filters, setFilters] = useState<UserFiltersState>({
    searchTerm: '',
    selectedRoles: [],
    selectedDependencia: '',
    showFilters: false,
    ...loadFiltersFromStorage()
  });
  
  // Estados de ordenamiento
  const [sortField, setSortField] = useState<SortField>('nombre');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  
  // Estados de paginación
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  
  
  // Estados del modal de creación/edición
  const [openDialog, setOpenDialog] = useState(false);
  const [currentUsuario, setCurrentUsuario] = useState<Usuario>({
    nombre: '',
    apellido: '',
    email: '',
    username: '',
    password: '',
    rol: Rol.USUARIOCONSULTA,
    dependencia: ''
  });
  const [isEditMode, setIsEditMode] = useState(false);

  // Estados para los nuevos diálogos
  const [roleChangeDialog, setRoleChangeDialog] = useState({
    open: false,
    usuario: null as Usuario | null,
    newRole: null as Rol | null
  });
  
  const [forceLogoutDialog, setForceLogoutDialog] = useState({
    open: false,
    usuario: null as Usuario | null
  });

  const rolLabels: Record<string, string> = {
    [Rol.SUPERUSUARIO]: 'Superusuario',
    [Rol.ADMINISTRADOR]: 'Administrador',
    [Rol.USUARIOCARGA]: 'Usuario Carga',
    [Rol.USUARIOCONSULTA]: 'Usuario Consulta',
  };

  // Cargar usuarios al montar el componente
  useEffect(() => {
    fetchUsuarios();
  }, []);

  // Guardar filtros cuando cambien
  useEffect(() => {
    saveFiltersToStorage({
      searchTerm: filters.searchTerm,
      selectedRoles: filters.selectedRoles,
      selectedDependencia: filters.selectedDependencia
    });
  }, [filters.searchTerm, filters.selectedRoles, filters.selectedDependencia]);

  const fetchUsuarios = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await usuarioService.getAll();
      setUsuarios(data);
    } catch (err: any) {
      console.error('Error al cargar usuarios:', err);
      setError(err.response?.data?.message || 'Error al cargar la lista de usuarios');
    } finally {
      setLoading(false);
    }
  };

  // Filtrar y ordenar usuarios
  const filteredAndSortedUsers = React.useMemo(() => {
    const filtered = filterUsers(usuarios, {
      searchTerm: filters.searchTerm,
      selectedRoles: filters.selectedRoles,
      selectedDependencia: filters.selectedDependencia
    });
    return sortUsers(filtered, sortField, sortOrder);
  }, [usuarios, filters, sortField, sortOrder]);

  // Obtener dependencias únicas para el filtro
  const uniqueDependencias = React.useMemo(() => {
    return getUniqueValues(usuarios, 'dependencia');
  }, [usuarios]);

  // Handlers de paginación
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handlers de ordenamiento
  const handleSort = (field: SortField) => {
    const isAsc = sortField === field && sortOrder === 'asc';
    setSortOrder(isAsc ? 'desc' : 'asc');
    setSortField(field);
  };


  // Handlers del modal
  const handleOpenDialog = (usuario?: Usuario) => {
    if (usuario) {
      setCurrentUsuario({...usuario, password: ''});
      setIsEditMode(true);
    } else {
      setCurrentUsuario({
        nombre: '',
        apellido: '',
        email: '',
        username: '',
        password: 'Minseg2025-',
        rol: Rol.USUARIOCONSULTA,
        dependencia: ''
      });
      setIsEditMode(false);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentUsuario({...currentUsuario, [name]: value});
  };

  const handleSelectChange = (e: any) => {
    const value = e.target.value;
    setCurrentUsuario({
      ...currentUsuario,
      rol: value as Rol
    });
  };

  const handleSaveUsuario = async () => {
    if (!currentUsuario.nombre || !currentUsuario.apellido || !currentUsuario.email) {
      setError('Por favor complete todos los campos obligatorios');
      return;
    }

    const usuarioParaCrear = {
      ...currentUsuario,
      username: currentUsuario.username && currentUsuario.username.trim() !== '' 
        ? currentUsuario.username 
        : currentUsuario.email
    };

    try {
      setLoading(true);
      setError(null);
      if (isEditMode) {
        await usuarioService.update(usuarioParaCrear.id!, usuarioParaCrear);
        setSuccess('Usuario actualizado correctamente');
      } else {
        await usuarioService.create(usuarioParaCrear);
        setSuccess('Usuario creado correctamente');
      }
      setOpenDialog(false);
      fetchUsuarios();
    } catch (err: any) {
      let mensaje = 'Error al guardar el usuario';
      if (err?.response?.data?.mensaje) {
        mensaje = err.response.data.mensaje;
      } else if (err?.message) {
        mensaje = err.message;
      }
      setError(mensaje);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('¿Está seguro que desea eliminar este usuario? Esta acción no se puede deshacer.')) {
      try {
        setLoading(true);
        await usuarioService.delete(id);
        setSuccess('Usuario eliminado correctamente');
        fetchUsuarios();
      } catch (err: any) {
        console.error('Error al eliminar usuario:', err);
        setError(err.response?.data?.message || 'Error al eliminar el usuario');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleResetPasswordAnd2FA = async (usuario: Usuario) => {
    if (!window.confirm(`¿Seguro que deseas resetear la contraseña y el 2FA de ${usuario.nombre} ${usuario.apellido}?`)) return;
    try {
      setLoading(true);
      await usuarioService.resetPasswordAnd2FA(usuario.id!);
      setSuccess('Contraseña y 2FA reseteados correctamente. El usuario deberá cambiar la contraseña y reconfigurar 2FA al ingresar.');
      fetchUsuarios();
    } catch (err: any) {
      setError('Error al resetear la contraseña y 2FA');
    } finally {
      setLoading(false);
    }
  };

  // Handlers para los nuevos diálogos
  const handleRoleChange = (usuario: Usuario, newRole: Rol) => {
    setRoleChangeDialog({
      open: true,
      usuario,
      newRole
    });
  };

  const handleRoleChangeConfirm = async (password: string) => {
    if (!roleChangeDialog.usuario || !roleChangeDialog.newRole) return;

    try {
      setLoading(true);
      await usuarioService.updateRoleWithPassword(
        roleChangeDialog.usuario.id!,
        roleChangeDialog.newRole,
        password
      );
      setSuccess(`Rol actualizado correctamente para ${roleChangeDialog.usuario.nombre} ${roleChangeDialog.usuario.apellido}`);
      setRoleChangeDialog({ open: false, usuario: null, newRole: null });
      fetchUsuarios();
    } catch (err: any) {
      console.error('Error al cambiar rol:', err);
      throw new Error(err.response?.data?.message || 'Error al cambiar el rol del usuario');
    } finally {
      setLoading(false);
    }
  };

  const handleForceLogout = (usuario: Usuario) => {
    setForceLogoutDialog({
      open: true,
      usuario
    });
  };

  const handleForceLogoutConfirm = async () => {
    if (!forceLogoutDialog.usuario) return;

    try {
      setLoading(true);
      await usuarioService.forceLogout(forceLogoutDialog.usuario.id!);
      setSuccess(`Sesión cerrada forzosamente para ${forceLogoutDialog.usuario.nombre} ${forceLogoutDialog.usuario.apellido}`);
      setForceLogoutDialog({ open: false, usuario: null });
    } catch (err: any) {
      console.error('Error al forzar cierre de sesión:', err);
      setError(err.response?.data?.message || 'Error al forzar el cierre de sesión');
    } finally {
      setLoading(false);
    }
  };

  // Funciones de utilidad
  const canEdit = (targetUsuario: Usuario) => {
    if (!user) return false;
    if (user.rol === Rol.SUPERUSUARIO) return true;
    if (user.rol === Rol.ADMINISTRADOR && targetUsuario.rol !== Rol.SUPERUSUARIO) return true;
    return user.id === targetUsuario.id;
  };

  const getChipColor = (rol: Rol): "error" | "warning" | "primary" | "info" | "default" => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return 'error';
      case Rol.ADMINISTRADOR: return 'warning';
      case Rol.USUARIOCARGA: return 'primary';
      case Rol.USUARIOCONSULTA: return 'info';
      default: return 'default';
    }
  };


  const currentPageUsers = filteredAndSortedUsers.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );


  // Loading skeleton
  if (loading && usuarios.length === 0) {
    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="rectangular" width={150} height={36} />
        </Box>
        {[...Array(5)].map((_, index) => (
          <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 1 }} />
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3,
        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        color: 'white',
        p: 3,
        borderRadius: 2,
        boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)'
      }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
            Gestión de Usuarios
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            Administra usuarios del sistema CUFRE
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<ExportIcon />}
            onClick={() => exportUsersToCSV(filteredAndSortedUsers)}
            sx={{
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },
              borderRadius: 2
            }}
          >
            Exportar
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            sx={{
              bgcolor: 'rgba(255, 255, 255, 0.9)',
              color: 'primary.main',
              '&:hover': { bgcolor: 'white' },
              borderRadius: 2,
              fontWeight: 600
            }}
          >
            Nuevo Usuario
          </Button>
        </Box>
      </Box>

      {/* Alerts */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      </Snackbar>

      {/* Estadísticas */}
      <UserStats users={usuarios} filteredUsers={filteredAndSortedUsers} />

      {/* Filtros */}
      <UserFilters
        filters={filters}
        onFiltersChange={setFilters}
        dependencias={uniqueDependencias}
        totalResults={usuarios.length}
        filteredResults={filteredAndSortedUsers.length}
      />

      {/* Tabla */}
      <Paper elevation={3} sx={{ borderRadius: 3, overflow: 'hidden' }}>
        <Box sx={{ width: '100%' }}>
          <Table stickyHeader sx={{ minWidth: 'auto', tableLayout: 'fixed', width: '100%' }}>
            <TableHead>
              <TableRow sx={{ '& .MuiTableCell-head': { backgroundColor: 'grey.100', fontWeight: 600 } }}>
                <TableCell>Avatar</TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortField === 'nombre'}
                    direction={sortField === 'nombre' ? sortOrder : 'asc'}
                    onClick={() => handleSort('nombre')}
                  >
                    Nombre
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortField === 'apellido'}
                    direction={sortField === 'apellido' ? sortOrder : 'asc'}
                    onClick={() => handleSort('apellido')}
                  >
                    Apellido
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortField === 'email'}
                    direction={sortField === 'email' ? sortOrder : 'asc'}
                    onClick={() => handleSort('email')}
                  >
                    Email
                  </TableSortLabel>
                </TableCell>
                <TableCell align="center">
                  <TableSortLabel
                    active={sortField === 'rol'}
                    direction={sortField === 'rol' ? sortOrder : 'asc'}
                    onClick={() => handleSort('rol')}
                  >
                    Rol
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortField === 'dependencia'}
                    direction={sortField === 'dependencia' ? sortOrder : 'asc'}
                    onClick={() => handleSort('dependencia')}
                  >
                    Dependencia
                  </TableSortLabel>
                </TableCell>
                <TableCell align="center">Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentPageUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      No se encontraron usuarios
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                currentPageUsers.map((usuario, index) => (
                  <TableRow
                    key={usuario.id}
                    hover
                    sx={{
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#f8f9fa',
                      '&:hover': {
                        backgroundColor: index % 2 === 0 ? '#f5f5f5' : '#e9ecef',
                        transform: 'scale(1.001)',
                        transition: 'all 0.2s ease-in-out'
                      }
                    }}
                  >
                    <TableCell>
                      <UserAvatar usuario={usuario} size="small" />
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500 }}>{usuario.nombre}</TableCell>
                    <TableCell sx={{ fontWeight: 500 }}>{usuario.apellido}</TableCell>
                    <TableCell>{usuario.email}</TableCell>
                    <TableCell align="center">
                      <RoleChip
                        currentRole={usuario.rol}
                        canEdit={canEdit(usuario) && user?.rol === Rol.SUPERUSUARIO && usuario.rol !== Rol.SUPERUSUARIO}
                        onRoleChange={(newRole: Rol) => handleRoleChange(usuario, newRole)}
                        disabled={user?.id === usuario.id}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {usuario.dependencia || 'Sin dependencia'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                        {/* Verificar si es el usuario actual */}
                        {user?.id === usuario.id ? (
                          // Mostrar ícono de prohibido para el usuario actual
                          <Tooltip title="No puedes realizar acciones sobre tu propia cuenta">
                            <IconButton
                              size="small"
                              disabled
                              sx={{
                                color: 'text.disabled',
                                cursor: 'not-allowed'
                              }}
                            >
                              <BlockIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          // Mostrar acciones normales para otros usuarios
                          canEdit(usuario) && (
                            <>
                              <Tooltip title="Editar usuario">
                                <IconButton
                                  size="small"
                                  onClick={() => handleOpenDialog(usuario)}
                                  sx={{
                                    '&:hover': {
                                      backgroundColor: 'primary.light',
                                      color: 'primary.main'
                                    }
                                  }}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Eliminar usuario">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDelete(usuario.id!)}
                                  sx={{
                                    '&:hover': {
                                      backgroundColor: 'error.light',
                                      color: 'error.main'
                                    }
                                  }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Resetear contraseña y 2FA">
                                <Button
                                  variant="outlined"
                                  color="secondary"
                                  size="small"
                                  onClick={() => handleResetPasswordAnd2FA(usuario)}
                                  sx={{
                                    ml: 1,
                                    minWidth: 'auto',
                                    px: 1,
                                    fontSize: '0.75rem',
                                    borderRadius: 1
                                  }}
                                >
                                  Reset
                                </Button>
                              </Tooltip>
                              {user?.rol === Rol.SUPERUSUARIO && (
                                <Tooltip title="Forzar cierre de sesión">
                                  <IconButton
                                    size="small"
                                    color="warning"
                                    onClick={() => handleForceLogout(usuario)}
                                    sx={{
                                      ml: 1,
                                      '&:hover': {
                                        backgroundColor: 'warning.light',
                                        color: 'warning.main'
                                      }
                                    }}
                                  >
                                    <LogoutIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </>
                          )
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Box>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          component="div"
          count={filteredAndSortedUsers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Filas por página"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
          sx={{
            borderTop: '1px solid',
            borderColor: 'divider',
            backgroundColor: 'grey.50'
          }}
        />
      </Paper>


      {/* Diálogo para crear/editar usuario */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 2, fontSize: 28, fontWeight: 700, pb: 0 }}>
          <PersonAddRounded color="primary" sx={{ fontSize: 36 }} />
          {isEditMode ? 'Editar Usuario' : 'Crear Nuevo Usuario'}
        </DialogTitle>
        <Typography variant="subtitle1" sx={{ pl: 7, pb: 2, color: 'text.secondary' }}>
          Complete los datos del usuario
        </Typography>
        <DialogContent sx={{ background: 'linear-gradient(135deg, #f8fafc 0%, #e3e8ee 100%)', p: 0 }}>
          <Box component={Paper} elevation={3} sx={{ borderRadius: 3, p: { xs: 2, sm: 4 }, m: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                <TextField
                  fullWidth
                  required
                  label="Nombre"
                  name="nombre"
                  value={currentUsuario.nombre}
                  onChange={handleInputChange}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                <TextField
                  fullWidth
                  required
                  label="Apellido"
                  name="apellido"
                  value={currentUsuario.apellido}
                  onChange={handleInputChange}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Box sx={{ width: '100%' }}>
                <TextField
                  fullWidth
                  required
                  label="Email"
                  name="email"
                  type="email"
                  value={currentUsuario.email}
                  onChange={handleInputChange}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="rol-label">Rol</InputLabel>
                  <Select
                    labelId="rol-label"
                    name="rol"
                    value={currentUsuario.rol}
                    label="Rol"
                    onChange={handleSelectChange}
                  >
                    {user?.rol === Rol.SUPERUSUARIO && (
                      <MenuItem value={Rol.SUPERUSUARIO}>{rolLabels[Rol.SUPERUSUARIO]}</MenuItem>
                    )}
                    {(user?.rol === Rol.SUPERUSUARIO || user?.rol === Rol.ADMINISTRADOR) && (
                      <MenuItem value={Rol.ADMINISTRADOR}>{rolLabels[Rol.ADMINISTRADOR]}</MenuItem>
                    )}
                    <MenuItem value={Rol.USUARIOCARGA}>{rolLabels[Rol.USUARIOCARGA]}</MenuItem>
                    <MenuItem value={Rol.USUARIOCONSULTA}>{rolLabels[Rol.USUARIOCONSULTA]}</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                <TextField
                  fullWidth
                  required={!isEditMode}
                  label={isEditMode ? 'Nueva Contraseña (opcional)' : 'Contraseña'}
                  name="password"
                  type="text"
                  value={currentUsuario.password}
                  onChange={handleInputChange}
                  helperText={isEditMode ? 'Dejar en blanco para mantener la actual' : 'Contraseña por defecto: Minseg2025-'}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Box sx={{ width: '100%' }}>
                <TextField
                  fullWidth
                  label="Dependencia"
                  name="dependencia"
                  value={currentUsuario.dependencia || ''}
                  onChange={handleInputChange}
                  sx={{ mb: 2 }}
                />
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 4, pb: 3, pt: 0 }}>
          <Button onClick={handleCloseDialog} variant="outlined" color="inherit" sx={{ borderRadius: 2, px: 3, py: 1, fontWeight: 500 }}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSaveUsuario} 
            variant="contained" 
            color="primary"
            disabled={loading}
            sx={{ borderRadius: 2, px: 3, py: 1, fontWeight: 600 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para cambio de rol */}
      {roleChangeDialog.open && roleChangeDialog.usuario && roleChangeDialog.newRole && (
        <RoleChangeDialog
          open={roleChangeDialog.open}
          onClose={() => setRoleChangeDialog({ open: false, usuario: null, newRole: null })}
          onConfirm={handleRoleChangeConfirm}
          targetUser={{
            id: roleChangeDialog.usuario.id!,
            nombre: roleChangeDialog.usuario.nombre,
            apellido: roleChangeDialog.usuario.apellido,
            usuario: roleChangeDialog.usuario.username || roleChangeDialog.usuario.email,
            currentRole: roleChangeDialog.usuario.rol
          }}
          newRole={roleChangeDialog.newRole}
        />
      )}

      {/* Diálogo para forzar cierre de sesión */}
      {forceLogoutDialog.open && forceLogoutDialog.usuario && (
        <ForceLogoutDialog
          open={forceLogoutDialog.open}
          onClose={() => setForceLogoutDialog({ open: false, usuario: null })}
          onConfirm={handleForceLogoutConfirm}
          targetUser={{
            id: forceLogoutDialog.usuario.id!,
            nombre: forceLogoutDialog.usuario.nombre,
            apellido: forceLogoutDialog.usuario.apellido,
            usuario: forceLogoutDialog.usuario.username || forceLogoutDialog.usuario.email,
            email: forceLogoutDialog.usuario.email,
            rol: forceLogoutDialog.usuario.rol,
            activo: true // Asumimos que está activo si está en la lista
          }}
        />
      )}
    </Box>
  );
};

export default UsuariosPage;