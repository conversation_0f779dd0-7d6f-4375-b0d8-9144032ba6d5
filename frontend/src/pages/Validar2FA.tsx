import React, { useState, useEffect } from 'react';
import './Validar2FA.css';
import usuarioService from '../api/usuarioService';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import axiosClient from '../api/axiosClient';
// Importamos la función jwtDecode adecuadamente
import { jwtDecode } from 'jwt-decode';
import { Usuario } from '../types/usuario.types';

const Validar2FA: React.FC = () => {
  const [codigo, setCodigo] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth(); // Accedemos al contexto de autenticación

  // Efecto para redirigir al dashboard si ya está autenticado
  useEffect(() => {
    if (isAuthenticated) {
      console.log("Usuario ya autenticado, redirigiendo al dashboard");
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Eliminar espacios y validar que solo contiene dígitos
    const codigoLimpio = codigo.trim();

    if (codigoLimpio.length !== 6) {
      setError('El código debe tener exactamente 6 dígitos.');
      setLoading(false);
      return;
    }

    if (!/^\d+$/.test(codigoLimpio)) {
      setError('El código debe contener solo dígitos.');
      setLoading(false);
      return;
    }

    try {
      console.log("Enviando código 2FA para validación:", codigoLimpio);

      const response = await usuarioService.validar2FA(codigoLimpio);

      console.log("Respuesta de validación 2FA:", response);

      if (response && response.token) {
        // Guardar token JWT en localStorage
        localStorage.setItem('token', response.token);
        
        // Configurar el token en el cliente de axios para futuras solicitudes
        axiosClient.defaults.headers.common['Authorization'] = `Bearer ${response.token}`;

        // Eliminar token temporal si existe
        localStorage.removeItem('temp_token');
        
        // Decodificar el token JWT para obtener la información del usuario
        try {
          // Extraer los datos del token
          interface JwtPayload {
            sub: string; // email del usuario
            roles: string; // roles del usuario
            exp: number;
            iat: number;
          }
          
          const decodedToken = jwtDecode<JwtPayload>(response.token);
          console.log("Token decodificado:", decodedToken);
          
          if (decodedToken && decodedToken.sub) {
            const email = decodedToken.sub;
            // Extraer el rol de la propiedad roles (por ejemplo, "ROLE_SUPERUSUARIO")
            const rolString = decodedToken.roles || '';
            const rol = rolString.replace('ROLE_', '');
            
            // Crear los datos del usuario a partir del token
            const userFromToken = {
              id: 0, // No podemos obtener el ID del token, usará 0 como valor por defecto
              nombre: email.split('@')[0], // Usamos la parte inicial del email como nombre provisional
              email: email,
              rol: rol as any // Casting a any porque no tenemos el enum Rol disponible aquí
            };
            
            console.log("Guardando datos del usuario desde el token:", userFromToken);
            localStorage.setItem('user', JSON.stringify(userFromToken));
            
            // Mostrar mensaje de éxito
            setSuccess(true);
            
            // Redirigir al dashboard después de un breve retraso
            setTimeout(() => {
              console.log("Redirigiendo al dashboard con datos del token...");
              window.location.href = '/dashboard';
            }, 1000);
            
            return; // Salir de la función, ya está manejado
          }
        } catch (jwtError) {
          console.error("Error al decodificar el token JWT:", jwtError);
          // Continuar con el flujo alternativo si hay error
        }
        
        // Plan B: Si no podemos extraer datos del token, intentamos con findByEmail
        const email = localStorage.getItem('last_email');
        if (email) {
          try {
            const userData = await usuarioService.findByEmail(email) as Usuario;
            if (userData) {
              const userToSave = {
                id: userData.id,
                nombre: userData.nombre,
                apellido: userData.apellido || '', // Añadido el apellido que faltaba
                email: userData.email,
                rol: userData.rol
              };
              
              console.log("Plan B: Guardando datos del usuario desde API:", userToSave);
              localStorage.setItem('user', JSON.stringify(userToSave));
            }
          } catch (userError) {
            console.error("Error en Plan B al obtener datos del usuario:", userError);
          }
        }
        
        // Independientemente de si obtuvimos los datos o no, mostrar éxito y redirigir
        setSuccess(true);
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1000);
      } else {
        throw new Error("Respuesta inválida del servidor");
      }
    } catch (error: any) {
      console.error("Error en validación 2FA:", error);
      setError(error.response?.data?.error || error.response?.data?.mensaje || 'Código incorrecto. Verifique y vuelva a intentar.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <img src="/logo-cufre-2.png" alt="CUFRE Logo" style={{ display: 'block', margin: '0 auto 16px auto', height: 64 }} />
        <h2>Verificación 2FA</h2>
        <p>Ingresa el código de 6 dígitos generado por Google Authenticator para continuar.</p>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label style={{ display: 'block', textAlign: 'center', width: '100%' }}><strong>Código 2FA</strong></label>
            <input type="text" value={codigo} onChange={e => setCodigo(e.target.value)} maxLength={6} required />
          </div>
          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">Código verificado. Acceso concedido.</div>}
          <button type="submit" className="btn-primary" disabled={success || loading}>
            {loading ? 'Validando...' : 'Validar'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Validar2FA; 