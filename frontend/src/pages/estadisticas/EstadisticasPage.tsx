import React from 'react';
import { Box, Alert } from '@mui/material';
import PieChartIcon from '@mui/icons-material/PieChart';
import CinematicLayout from '../../components/layout/CinematicLayout';
import { useEstadisticasData } from '../../hooks/useEstadisticasData';
import EstadisticasControls from '../../components/estadisticas/EstadisticasControls';
import EstadisticasFilters from '../../components/estadisticas/EstadisticasFilters';
import EstadisticasMetricas from '../../components/estadisticas/EstadisticasMetricas';
import EstadisticasChartsGrid from '../../components/estadisticas/EstadisticasChartsGrid';
import EstadisticasEmptyState from '../../components/estadisticas/EstadisticasEmptyState';
import EstadisticasLoading from '../../components/estadisticas/EstadisticasLoading';
import '../../styles/CinematicPages.css';

const EstadisticasPage: React.FC = () => {
  // Usar el hook personalizado para manejar toda la lógica de datos
  const {
    loading,
    error,
    expedientesPorFuerza,
    expedientesPorEstado,
    filteredExpedientesPorFuerza,
    filteredExpedientesPorEstado,
    estadisticasGenerales,
    activeFuerzaIndex,
    activeEstadoIndex,
    activeFilter,
    fetchData,
    handlePieClick,
    clearFilters
  } = useEstadisticasData();

  // Función para navegar a crear expediente
  const handleCreateExpediente = () => {
    window.location.href = '/expedientes/crear';
  };

  return (
    <CinematicLayout
      title="Estadísticas Generales"
      subtitle="Distribución de expedientes por estado y fuerza asignada"
      icon={<PieChartIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
    >
      <Box className="cinematic-fade-in">
        {/* Controles superiores */}
        <EstadisticasControls
          onRefresh={fetchData}
          loading={loading}
        />

        {/* Mensaje de error */}
        {error && (
          <Alert
            severity="error"
            className="cinematic-alert error"
            sx={{
              mb: 3,
              backgroundColor: 'rgba(255, 107, 53, 0.1)',
              border: '1px solid var(--cc-alert)',
              color: 'var(--cc-text-primary)'
            }}
          >
            {error}
          </Alert>
        )}

        {/* Panel de información de filtro activo */}
        <EstadisticasFilters
          activeFilter={activeFilter}
          onClearFilter={clearFilters}
        />

        {/* Resumen de estadísticas generales */}
        <EstadisticasMetricas
          estadisticasGenerales={estadisticasGenerales}
          expedientesPorEstado={expedientesPorEstado}
          loading={loading}
        />

        {/* Gráficos principales */}
        {loading && !expedientesPorFuerza.length ? (
          <EstadisticasLoading />
        ) : (
          <EstadisticasChartsGrid
            filteredExpedientesPorFuerza={filteredExpedientesPorFuerza}
            filteredExpedientesPorEstado={filteredExpedientesPorEstado}
            loading={loading}
            activeFuerzaIndex={activeFuerzaIndex}
            activeEstadoIndex={activeEstadoIndex}
            onPieClick={handlePieClick}
          />
        )}

        {/* Mensaje cuando no hay expedientes */}
        {!loading && expedientesPorFuerza.length === 0 && (
          <EstadisticasEmptyState
            onCreateExpediente={handleCreateExpediente}
          />
        )}
      </Box>
    </CinematicLayout>
  );
};

export default EstadisticasPage;