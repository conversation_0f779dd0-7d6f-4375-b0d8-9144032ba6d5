import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Al<PERSON>,
  Button
} from '@mui/material';
import GavelIcon from '@mui/icons-material/Gavel';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Cell } from 'recharts';
import estadisticaService from '../../api/estadisticaService';
import CinematicLayout from '../../components/layout/CinematicLayout';
import '../../styles/CinematicPages.css';

const FUERZAS = [
  'TODAS',
  'PFA',
  'GNA',
  'PSA',
  'PNA',
  'SPF',
  'INTERPOOL',
  'CUFRE',
  'SIN DATO'
];
const ESTADOS = [
  'TODOS',
  'DETENIDO',
  'CAPTURA VIGENTE',
  'SIN EFECTO',
  'SIN DATO'
];
const COLORS = [
  '#1976d2', '#388e3c', '#bfa16c', '#8e24aa', '#ffd600', '#d32f2f', '#757575', '#4fc3f7', '#222', '#424242'
];

// Componente cinematográfico para el tooltip
const CustomTooltip = (props: any) => {
  const { active, payload, label } = props;
  
  if (active && payload && payload.length > 0) {
    const data = payload[0].payload;
    return (
      <Box className="cinematic-tooltip">
        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'var(--cc-cufre)', mb: 1 }}>
          {data.delitoOriginal}
        </Typography>
        <Typography variant="body2" sx={{ color: 'var(--cc-text-primary)' }}>
          <b>Cantidad:</b> {data.count}
        </Typography>
      </Box>
    );
  }
  
  return null;
};

const RankingDelitosPage = () => {
  const [ranking, setRanking] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [fuerza, setFuerza] = useState('TODAS');
  const [estado, setEstado] = useState('TODOS');
  const [showAll, setShowAll] = useState(false);
  const [topLimit] = useState(10);

  useEffect(() => {
    setLoading(true);
    estadisticaService.getDelitosPorTipo()
      .then((res: any) => {
        // El endpoint debe devolver [{ delito: 'Robo', cantidad: 15 }, ...]
        setRanking(Array.isArray(res) ? res : (res.data || []));
        setLoading(false);
      })
      .catch(() => {
        setError('No se pudo obtener el ranking de delitos.');
        setLoading(false);
      });
  }, []);

  // Si el endpoint no soporta fuerza/estado, solo mostramos el ranking general
  // Si en el futuro el endpoint soporta filtros, aquí se pueden aplicar
  const allData = ranking
    .map((item: any) => {
      const delitoOriginal = item.delito || item.nombre || 'SIN DATO';
      
      // Truncar nombres largos para mejor visualización en el gráfico
      // Guardamos el nombre original para mostrarlo completo en el tooltip
      return {
        delitoOriginal,
        delito: delitoOriginal.length > 18 ? delitoOriginal.substring(0, 15) + '...' : delitoOriginal,
        count: item.cantidad || item.value || 0
      };
    })
    .sort((a, b) => b.count - a.count);

  // Aplicar límite si no se está mostrando todo
  const data = showAll ? allData : allData.slice(0, topLimit);

  return (
    <CinematicLayout
      title="Ranking de Delitos"
      subtitle="Expedientes agrupados por tipo de delito"
      icon={<GavelIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
    >
      <Box className="cinematic-fade-in">
        <Box className="cinematic-main-panel">
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3
          }}>
            <Typography className="cinematic-subtitle">
              {!showAll && allData.length > topLimit && `Mostrando top ${topLimit} de ${allData.length} delitos.`}
            </Typography>
            {allData.length > topLimit && (
              <Button
                variant={showAll ? "outlined" : "contained"}
                onClick={() => setShowAll(!showAll)}
                className={showAll ? "cinematic-button" : "cinematic-button primary"}
                sx={{
                  borderRadius: 3,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  letterSpacing: 1
                }}
              >
                {showAll ? `Mostrar Top ${topLimit}` : 'Ver Todos'}
              </Button>
            )}
          </Box>

          <Box className="cinematic-chart-container">
            {loading ? (
              <Box className="cinematic-loading">
                <CircularProgress sx={{ color: 'var(--cc-cufre)' }} />
                <Typography className="cinematic-loading-text">
                  Cargando ranking...
                </Typography>
              </Box>
            ) : error ? (
              <Alert
                severity="error"
                className="cinematic-alert error"
                sx={{
                  backgroundColor: 'rgba(255, 107, 53, 0.1)',
                  border: '1px solid var(--cc-alert)',
                  color: 'var(--cc-text-primary)'
                }}
              >
                {error}
              </Alert>
            ) : data.length === 0 ? (
              <Alert
                severity="info"
                className="cinematic-alert info"
                sx={{
                  backgroundColor: 'rgba(255, 214, 0, 0.1)',
                  border: '1px solid var(--cc-cufre)',
                  color: 'var(--cc-text-primary)'
                }}
              >
                No hay datos para mostrar.
              </Alert>
            ) : (
              <Box sx={{ height: data.length > 8 ? 600 : 500 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={data}
                    margin={{ top: 20, right: 40, left: 40, bottom: 60 }}
                    barGap={5}
                    barCategoryGap={10}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      opacity={0.2}
                      stroke="var(--cc-border)"
                    />
                    <XAxis
                      dataKey="delito"
                      angle={-35}
                      textAnchor="end"
                      interval={0}
                      height={100}
                      tick={{
                        fontWeight: 600,
                        fontSize: 12,
                        fill: 'var(--cc-text-primary)'
                      }}
                      tickMargin={8}
                      stroke="var(--cc-text-secondary)"
                    />
                    <YAxis
                      tick={{
                        fontWeight: 600,
                        fontSize: 15,
                        fill: 'var(--cc-text-primary)'
                      }}
                      allowDecimals={false}
                      stroke="var(--cc-text-secondary)"
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar dataKey="count" radius={[8, 8, 0, 0]}>
                      {data.map((entry, idx) => (
                        <Cell key={entry.delito} fill={COLORS[idx % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </CinematicLayout>
  );
};

export default RankingDelitosPage; 