import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Alert, Paper, Switch, FormControlLabel, Checkbox, FormGroup, FormControl, FormLabel } from '@mui/material';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, GeoJSON } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L, { LatLngTuple, Icon } from 'leaflet';
import PlaceRoundedIcon from '@mui/icons-material/PlaceRounded';
import ListAltRoundedIcon from '@mui/icons-material/ListAltRounded';
import MapRoundedIcon from '@mui/icons-material/MapRounded';
import expedienteService from '../../api/expedienteService';
// @ts-ignore
import { HeatmapLayer } from 'react-leaflet-heatmap-layer-v3';
const arGeoJson = require('../../data/ar.json');

// Fix para los iconos de Leaflet en React
const iconUrl = require('leaflet/dist/images/marker-icon.png');
const iconShadow = require('leaflet/dist/images/marker-shadow.png');

const iconFuga = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});
const iconDetencion = new L.Icon({
  iconUrl: process.env.PUBLIC_URL + '/images/marker-icon-green.png',
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const MAP_CENTER: LatLngTuple = [-38.4161, -63.6167]; // Centro de Argentina
const MAP_ZOOM = 4.5;

// Función para normalizar nombres de provincia (mayúsculas, sin espacios, sin tildes)
const normalizarProvincia = (str: string) => {
  return (str || '')
    .trim()
    .toUpperCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Elimina tildes/acentos
    .replace(/\s+/g, ' '); // Normaliza espacios múltiples
};

// Mapeo de equivalencias para nombres especiales
const equivalenciasProvincia: { [key: string]: string } = {
  'CIUDAD DE BUENOS AIRES': 'CABA',
  'CIUDAD AUTONOMA DE BUENOS AIRES': 'CABA',
  'CABA': 'CABA',
  'BUENOS AIRES': 'BUENOS AIRES',
  'TIERRA DEL FUEGO': 'TIERRA DEL FUEGO',
  'TIERRA DEL FUEGO ANTARTIDA E ISLAS DEL ATLANTICO SUR': 'TIERRA DEL FUEGO',
  'ENTRE RIOS': 'ENTRE RIOS',
  'NEUQUEN': 'NEUQUEN',
  'SANTIAGO DEL ESTERO': 'SANTIAGO DEL ESTERO',
  'RIO NEGRO': 'RIO NEGRO',
  'CORDOBA': 'CORDOBA',
  'SANTA FE': 'SANTA FE',
  'MENDOZA': 'MENDOZA',
  'TUCUMAN': 'TUCUMAN',
  'SALTA': 'SALTA',
  'MISIONES': 'MISIONES',
  'CHACO': 'CHACO',
  'CORRIENTES': 'CORRIENTES',
  'FORMOSA': 'FORMOSA',
  'SAN JUAN': 'SAN JUAN',
  'JUJUY': 'JUJUY',
  'LA PAMPA': 'LA PAMPA',
  'LA RIOJA': 'LA RIOJA',
  'SAN LUIS': 'SAN LUIS',
  'CATAMARCA': 'CATAMARCA',
  'CHUBUT': 'CHUBUT',
  'SANTA CRUZ': 'SANTA CRUZ'
};

// Función para obtener la clave de backend a partir del nombre del GeoJSON
const getClaveProvincia = (nombreGeoJson: string) => {
  const norm = normalizarProvincia(nombreGeoJson);
  // Si hay equivalencia, usarla; si no, usar el normalizado
  const clave = equivalenciasProvincia[norm] || norm;
  console.log(`Mapeando provincia: "${nombreGeoJson}" -> "${norm}" -> "${clave}"`);
  return clave;
};

const MapaGeneralPage: React.FC = () => {
  const [expedientes, setExpedientes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFuga, setShowFuga] = useState(true);
  const [showDetencion, setShowDetencion] = useState(true);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [showExpedientesProvincia, setShowExpedientesProvincia] = useState(false);
  const [expedientesPorProvincia, setExpedientesPorProvincia] = useState<{ [key: string]: number }>({});

  useEffect(() => {
    const fetchExpedientes = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await expedienteService.getAll();
        setExpedientes(data);
      } catch (err: any) {
        setError('Error al cargar los expedientes');
      } finally {
        setLoading(false);
      }
    };
    fetchExpedientes();
  }, []);

  useEffect(() => {
    if (showExpedientesProvincia) {
      import('../../api/estadisticaService').then(({ default: estadisticaService }) => {
        estadisticaService.getExpedientesPorProvincia().then((data: any) => {
          console.log('Datos recibidos de expedientes por provincia:', data);
          setExpedientesPorProvincia(data);
        }).catch(error => {
          console.error('Error al obtener expedientes por provincia:', error);
          setError('Error al cargar datos de provincias');
        });
      }).catch(error => {
        console.error('Error al importar el servicio de estadísticas:', error);
        setError('Error al cargar el servicio de estadísticas');
      });
    }
  }, [showExpedientesProvincia]);

  // Filtrar puntos según selección
  const puntosFuga = showFuga ? expedientes.filter((exp: any) => exp.fugaLatitud && exp.fugaLongitud) : [];
  const puntosDetencion = showDetencion ? expedientes.filter((exp: any) => exp.detencionLatitud && exp.detencionLongitud) : [];

  // Datos para el heatmap (solo puntos seleccionados)
  const heatmapPoints = [
    ...puntosFuga.map((exp: any) => [Number(exp.fugaLatitud), Number(exp.fugaLongitud), 1]),
    ...puntosDetencion.map((exp: any) => [Number(exp.detencionLatitud), Number(exp.detencionLongitud), 1])
  ];

  return (
    <Box>
      {/* Fila superior: título a la izquierda, controles a la derecha */}
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: { xs: 'stretch', md: 'center' },
        justifyContent: { xs: 'flex-start', md: 'space-between' },
        gap: 2,
        mt: 2,
        mb: 3,
        flexWrap: 'wrap',
      }}>
        {/* Título alineado a la izquierda */}
        <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 220, mb: { xs: 2, md: 0 } }}>
          <PlaceRoundedIcon sx={{ color: '#1976d2', fontSize: 40, minWidth: 40, mr: 1 }} />
          <Typography variant="h3" sx={{ fontWeight: 'bold', color: '#1976d2', textAlign: 'left', fontSize: { xs: 28, sm: 34, md: 40 } }}>
            Mapa
          </Typography>
        </Box>
        {/* Controles y tarjetas a la derecha */}
        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' }, alignItems: 'center', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
          <FormControl component="fieldset" sx={{ minWidth: 220, background: '#f5faff', borderRadius: 2, px: 2, py: 1, boxShadow: 1, display: 'flex', alignItems: 'center', flex: '1 1 220px', maxWidth: 340 }}>
            <FormLabel component="legend" sx={{ color: '#1976d2', fontWeight: 600, mb: 1, fontSize: 16, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', alignSelf: 'flex-start' }}>Mostrar:</FormLabel>
            <FormGroup row sx={{ flexWrap: 'wrap', gap: 1 }}>
              <FormControlLabel
                control={<Checkbox checked={showFuga && !showExpedientesProvincia} onChange={() => { setShowFuga(v => !v); setShowExpedientesProvincia(false); }} sx={{ color: '#d32f2f' }} />}
                label={<span style={{ color: '#d32f2f', fontWeight: 500, fontSize: 15 }}>Puntos de Fuga</span>}
              />
              <FormControlLabel
                control={<Checkbox checked={showDetencion && !showExpedientesProvincia} onChange={() => { setShowDetencion(v => !v); setShowExpedientesProvincia(false); }} sx={{ color: '#388e3c' }} />}
                label={<span style={{ color: '#388e3c', fontWeight: 500, fontSize: 15 }}>Puntos de Detención</span>}
              />
              <FormControlLabel
                control={<Checkbox checked={showExpedientesProvincia} onChange={() => { setShowExpedientesProvincia(v => !v); setShowFuga(false); setShowDetencion(false); setShowHeatmap(false); }} sx={{ color: '#b71c1c' }} />}
                label={<span style={{ color: '#b71c1c', fontWeight: 500, fontSize: 15 }}>Expedientes por provincia</span>}
              />
            </FormGroup>
          </FormControl>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Paper elevation={4} sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1.5, borderRadius: 3, background: '#f5faff', minWidth: 110, maxWidth: 140, flex: '1 1 110px', boxShadow: 2 }}>
              <ListAltRoundedIcon sx={{ color: '#d32f2f', fontSize: 28, mr: 1 }} />
              <Box sx={{ minWidth: 0 }}>
                <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, lineHeight: 1, fontSize: 22, wordBreak: 'break-word' }}>
                  {puntosFuga.length}
                </Typography>
                <Typography variant="body2" sx={{ color: '#d32f2f', fontWeight: 500, fontSize: 13, wordBreak: 'break-word' }}>
                  fuga
                </Typography>
              </Box>
            </Paper>
            <Paper elevation={4} sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1.5, borderRadius: 3, background: '#f5faff', minWidth: 110, maxWidth: 140, flex: '1 1 110px', boxShadow: 2 }}>
              <ListAltRoundedIcon sx={{ color: '#388e3c', fontSize: 28, mr: 1 }} />
              <Box sx={{ minWidth: 0 }}>
                <Typography variant="h6" sx={{ color: '#388e3c', fontWeight: 700, lineHeight: 1, fontSize: 22, wordBreak: 'break-word' }}>
                  {puntosDetencion.length}
                </Typography>
                <Typography variant="body2" sx={{ color: '#388e3c', fontWeight: 500, fontSize: 13, wordBreak: 'break-word' }}>
                  detención
                </Typography>
              </Box>
            </Paper>
            <Paper elevation={4} sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1.5, borderRadius: 3, background: '#f5faff', minWidth: 180, maxWidth: 220, flex: '1 1 180px', boxShadow: 2 }}>
              <MapRoundedIcon sx={{ color: '#1976d2', fontSize: 28, mr: 1 }} />
              <FormControlLabel
                control={
                  <Switch
                    checked={showHeatmap}
                    onChange={() => setShowHeatmap(v => !v)}
                    color="primary"
                  />
                }
                label={<span style={{ fontWeight: 500, fontSize: 14 }}>{showHeatmap ? 'Ver como marcadores' : 'Ver como mapa de calor'}</span>}
                sx={{ fontWeight: 500, ml: 1 }}
              />
            </Paper>
          </Box>
        </Box>
      </Box>
      {loading && <CircularProgress sx={{ mt: 4 }} />}
      {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
      {!loading && !error && (
        <Box sx={{ height: { xs: 400, sm: 500, md: 600 }, width: '100%', mt: 3, boxShadow: 3, borderRadius: 3, overflow: 'hidden', border: '1px solid #e0e0e0', background: '#fafdff' }}>
          <MapContainer center={MAP_CENTER} zoom={MAP_ZOOM} style={{ height: '100%', width: '100%' }}>
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            {showExpedientesProvincia && (
              <GeoJSON
                data={arGeoJson}
                style={feature => {
                  if (!feature) return {};
                  const provincia = feature.properties?.name || feature.properties?.nombre || feature.properties?.NOMBRE || '';
                  const claveProvincia = getClaveProvincia(provincia);
                  const count = expedientesPorProvincia[claveProvincia] || 0;
                  // Calcular el valor máximo para normalizar la intensidad del color
                  const maxCount = Math.max(1, ...Object.values(expedientesPorProvincia));
                  console.log('Conteo máximo de expedientes por provincia:', maxCount);
                  const intensity = count / maxCount;
                  const color = `rgba(183, 28, 28, ${0.2 + 0.7 * intensity})`;
                  return {
                    fillColor: color,
                    color: '#b71c1c',
                    weight: 1,
                    fillOpacity: 0.7,
                  };
                }}
                onEachFeature={(feature, layer) => {
                  if (!feature) return;
                  const provincia = feature.properties?.name || feature.properties?.nombre || feature.properties?.NOMBRE || '';
                  const claveProvincia = getClaveProvincia(provincia);
                  const count = expedientesPorProvincia[claveProvincia] || 0;
                  
                  // Agregar un console.log para depuración
                  console.log(`Provincia: ${provincia}, Clave: ${claveProvincia}, Expedientes: ${count}`);
                  
                  // Crear un tooltip más informativo
                  layer.bindTooltip(
                    `<div style="text-align: center; font-family: Arial, sans-serif;">
                      <strong style="font-size: 14px; color: #b71c1c;">${provincia}</strong>
                      <br/>
                      <span style="font-size: 13px;">Expedientes: <b>${count}</b></span>
                    </div>`, 
                    { sticky: true }
                  );
                }}
              />
            )}
            {showHeatmap ? (
              <HeatmapLayer
                fitBoundsOnLoad
                fitBoundsOnUpdate
                points={heatmapPoints}
                longitudeExtractor={(m: number[]) => m[1]}
                latitudeExtractor={(m: number[]) => m[0]}
                intensityExtractor={(m: number[]) => m[2]}
                radius={20}
                blur={15}
                max={1}
              />
            ) : (
              <>
                {puntosFuga.map((exp: any, idx: number) => {
                  const lat = Number(exp.fugaLatitud);
                  const lng = Number(exp.fugaLongitud);
                  if (isNaN(lat) || isNaN(lng)) return null;
                  const position: LatLngTuple = [lat, lng];
                  // Obtener nombre del prófugo como en ExpedienteDetallePage
                  let nombreProfugo = 'Sin dato';
                  if (Array.isArray(exp.personaExpedientes)) {
                    const imputado = exp.personaExpedientes.find((p: any) => (p.tipoRelacion || '').toLowerCase() === 'imputado');
                    if (imputado) {
                      if (imputado.persona) {
                        nombreProfugo = `${imputado.persona.nombre || ''} ${imputado.persona.apellido || ''}`.trim();
                      } else {
                        nombreProfugo = `${imputado.nombre || ''} ${imputado.apellido || ''}`.trim();
                      }
                    }
                    if (!nombreProfugo) nombreProfugo = 'Sin dato';
                  }
                  return (
                    <Marker
                      key={`fuga-${exp.id || idx}`}
                      position={position}
                      icon={iconFuga}
                    >
                      <Tooltip direction="top" offset={[0, -10]} opacity={1} permanent={false}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                          Número Expediente: {exp.numero || 'Sin número'}
                        </Typography>
                        <Typography variant="body2">
                          Nombre Prófugo: {nombreProfugo}
                        </Typography>
                        <Typography variant="body2">
                          Lugar Fuga: {exp.fugaLugar || 'Sin dato'}
                        </Typography>
                      </Tooltip>
                      <Popup>
                        <Typography variant="subtitle2" gutterBottom>
                          <b>Expediente:</b> {exp.numero || 'Sin número'}
                        </Typography>
                        <Typography variant="body2">
                          <b>Lugar de fuga:</b> {exp.fugaLugar || 'Sin dato'}
                        </Typography>
                      </Popup>
                    </Marker>
                  );
                })}
                {puntosDetencion.map((exp: any, idx: number) => {
                  const lat = Number(exp.detencionLatitud);
                  const lng = Number(exp.detencionLongitud);
                  if (isNaN(lat) || isNaN(lng)) return null;
                  const position: LatLngTuple = [lat, lng];
                  // Obtener nombre del detenido como en ExpedienteDetallePage
                  let nombreDetenido = 'Sin dato';
                  if (Array.isArray(exp.personaExpedientes)) {
                    const imputado = exp.personaExpedientes.find((p: any) => (p.tipoRelacion || '').toLowerCase() === 'imputado');
                    if (imputado) {
                      if (imputado.persona) {
                        nombreDetenido = `${imputado.persona.nombre || ''} ${imputado.persona.apellido || ''}`.trim();
                      } else {
                        nombreDetenido = `${imputado.nombre || ''} ${imputado.apellido || ''}`.trim();
                      }
                    }
                    if (!nombreDetenido) nombreDetenido = 'Sin dato';
                  }
                  return (
                    <Marker
                      key={`detencion-${exp.id || idx}`}
                      position={position}
                      icon={iconDetencion}
                    >
                      <Tooltip direction="top" offset={[0, -10]} opacity={1} permanent={false}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 700 }}>
                          Número Expediente: {exp.numero || 'Sin número'}
                        </Typography>
                        <Typography variant="body2">
                          Nombre Prófugo: {nombreDetenido}
                        </Typography>
                        <Typography variant="body2">
                          Lugar Detención: {exp.detencionLugar || 'Sin dato'}
                        </Typography>
                      </Tooltip>
                      <Popup>
                        <Typography variant="subtitle2" gutterBottom>
                          <b>Expediente:</b> {exp.numero || 'Sin número'}
                        </Typography>
                        <Typography variant="body2">
                          <b>Lugar de detención:</b> {exp.detencionLugar || 'Sin dato'}
                        </Typography>
                      </Popup>
                    </Marker>
                  );
                })}
              </>
            )}
          </MapContainer>
        </Box>
      )}
    </Box>
  );
};

export default MapaGeneralPage;