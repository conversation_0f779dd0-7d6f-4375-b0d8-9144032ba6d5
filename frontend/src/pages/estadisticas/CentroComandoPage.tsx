import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Typography, IconButton, Tooltip, Chip } from '@mui/material';
import {
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Dashboard as DashboardIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate, useLocation, useOutletContext } from 'react-router-dom';
import useAutoRefresh from '../../hooks/useAutoRefresh';
import estadisticaService from '../../api/estadisticaService';
import PanelMetricas from '../../components/estadisticas/PanelMetricas';
import PanelGraficoDona from '../../components/estadisticas/PanelGraficoDona';
import PanelDetenidosCompacto from '../../components/estadisticas/PanelDetenidosCompacto';
import PanelLineaTemporal from '../../components/estadisticas/PanelLineaTemporal';
import PanelRanking from '../../components/estadisticas/PanelRanking';
import PanelCasosDestacados from '../../components/estadisticas/PanelCasosDestacados';
import DrillDownModal from '../../components/estadisticas/DrillDownModal';
import CentroEstadisticasLoadingModal from '../../components/modals/CentroEstadisticasLoadingModal';
import { EstadisticasFilterProvider, useEstadisticasFilter } from '../../context/EstadisticasFilterContext';
import '../../styles/CentroComando.css';

// Colores para las fuerzas (mismo esquema que EstadisticasPage)
const COLORS_FUERZA = {
  'PFA': '#1976d2',
  'GNA': '#388e3c',
  'PSA': '#222',
  'PNA': '#bfa16c',
  'SPF': '#4fc3f7',
  'INTERPOOL': '#8e24aa',
  'CUFRE': '#ffd600',
  'SIN DATOS': '#424242'
};

// Colores para los estados
const COLORS_ESTADO = {
  'DETENIDO': '#388e3c',
  'CAPTURA VIGENTE': '#d32f2f',
  'SIN EFECTO': '#757575',
  'SIN DATOS': '#424242'
};

interface OutletContextType {
  onFullscreenChange?: (fullscreen: boolean) => void;
}

// Componente interno que usa el contexto de filtros
const CentroComandoContent: React.FC = () => {
  const { filters, setFilter, clearFilters } = useEstadisticasFilter();
  const navigate = useNavigate();
  const location = useLocation();
  const { onFullscreenChange } = useOutletContext<OutletContextType>() || {};
  const [isFullscreen, setIsFullscreen] = useState(true); // Por defecto true
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [showLoadingModal, setShowLoadingModal] = useState(false);
  const [drillDownData, setDrillDownData] = useState<{
    title: string;
    type: 'table' | 'chart';
    data: any[];
    columns?: string[];
    chartType?: 'pie' | 'bar';
  } | null>(null);
  
  // Estados para los datos
  const [metricas, setMetricas] = useState<any[]>([]);
  const [expedientesPorEstado, setExpedientesPorEstado] = useState<any[]>([]);
  const [expedientesPorFuerza, setExpedientesPorFuerza] = useState<any[]>([]);
  const [detenidosPorFuerza, setDetenidosPorFuerza] = useState<any[]>([]);
  const [evolucionTemporal, setEvolucionTemporal] = useState<any[]>([]);
  const [rankingDelitos, setRankingDelitos] = useState<any[]>([]);
  const [casosDestacados, setCasosDestacados] = useState<any>({
    casoMasNuevo: null,
    casoMasAntiguo: null,
    top3Prioridad: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Función para cargar todos los datos
  const fetchAllData = useCallback(async () => {
    try {
      setError(null);
      
      // Primero obtener las métricas clave
      const metricasClave = await estadisticaService.getMetricasClave();
      
      // Luego obtener los datos con filtros si existen
      const [
        estadoData,
        expedientesPorFuerzaData,
        fuerzaData,
        evolucionData,
        delitosData
      ] = await Promise.all([
        filters.fuerza || filters.tipoCaptura
          ? estadisticaService.getExpedientesPorEstadoFiltrado(filters)
          : estadisticaService.getExpedientesPorEstado(),
        filters.estado || filters.tipoCaptura
          ? estadisticaService.getExpedientesPorFuerzaFiltrado(filters)
          : estadisticaService.getExpedientesPorFuerza(),
        filters.estado || filters.tipoCaptura
          ? estadisticaService.getExpedientesPorFuerzaFiltrado(filters)
          : estadisticaService.getDetenidosPorFuerza(),
        filters.estado || filters.fuerza || filters.tipoCaptura
          ? estadisticaService.getExpedientesPorMesFiltrado(filters)
          : estadisticaService.getExpedientesPorMes(),
        filters.estado || filters.fuerza || filters.tipoCaptura
          ? estadisticaService.getDelitosPorTipoFiltrado(filters)
          : estadisticaService.getDelitosPorTipo()
      ]);

      // Procesar métricas principales
      const metricasData = [
        {
          label: 'Total Expedientes',
          value: metricasClave?.totalExpedientes || 0,
          trend: { value: 12, type: 'positive' as const, period: 'este mes' }
        },
        {
          label: 'Total Usuarios',
          value: metricasClave?.totalUsuarios || 0,
          trend: { value: 5, type: 'positive' as const, period: 'este mes' }
        },
        {
          label: 'Personas Vinculadas',
          value: metricasClave?.totalPersonasVinculadas || 0,
          trend: { value: 8, type: 'positive' as const, period: 'este mes' }
        },
        {
          label: 'Total Delitos',
          value: metricasClave?.totalDelitos || 0,
          trend: { value: 3, type: 'positive' as const, period: 'este mes' }
        },
        {
          label: 'Fuerzas de Seguridad',
          value: metricasClave?.totalFuerzasSeguridad || 0,
          format: 'number' as const,
          trend: { value: 0, type: 'neutral' as const, period: 'sin cambios' }
        }
      ];

      // Agregar información adicional de casos
      const casosDestacados = {
        casoMasNuevo: metricasClave?.casoMasNuevo || null,
        casoMasAntiguo: metricasClave?.casoMasAntiguo || null,
        top3Prioridad: metricasClave?.top3Prioridad || []
      };

      // Función helper para normalizar nombres
      const normalizeName = (name: string | null | undefined): string => {
        if (!name) return 'SIN DATOS';
        const upperName = name.toUpperCase().trim();
        // Normalizar variaciones de "sin datos"
        if (upperName === 'SIN DATO' || upperName === 'S/D' || upperName === 'SIN DATOS' || upperName === 'Sin datos') {
          return 'SIN DATOS';
        }
        return name;
      };

      // Función helper para agrupar datos duplicados
      const groupData = (data: any[]) => {
        const grouped = data.reduce((acc: any, item: any) => {
          const normalizedName = normalizeName(item.name);
          if (acc[normalizedName]) {
            acc[normalizedName].value += item.value;
          } else {
            acc[normalizedName] = { ...item, name: normalizedName };
          }
          return acc;
        }, {});
        return Object.values(grouped);
      };

      // Procesar expedientes por estado
      const estadoRaw = Array.isArray(estadoData) ? estadoData.map((item: any) => {
        const normalizedName = normalizeName(item.name || item.estadoSituacion);
        const estadoKey = normalizedName.toUpperCase() as keyof typeof COLORS_ESTADO;
        return {
          name: normalizedName,
          value: item.value || item.cantidad || 0,
          color: COLORS_ESTADO[estadoKey] || '#757575'
        };
      }) : [];
      const estadoProcessed = groupData(estadoRaw);

      // Procesar expedientes por fuerza
      const expedientesPorFuerzaRaw = Array.isArray(expedientesPorFuerzaData) ? expedientesPorFuerzaData.map((item: any) => {
        const normalizedName = normalizeName(item.name || item.fuerza || item.fuerzaAsignada);
        const fuerzaKey = normalizedName.toUpperCase() as keyof typeof COLORS_FUERZA;
        return {
          name: normalizedName,
          value: item.value || item.cantidad || 0,
          color: COLORS_FUERZA[fuerzaKey] || '#757575'
        };
      }) : [];
      const expedientesPorFuerzaProcessed = groupData(expedientesPorFuerzaRaw);

      // Procesar detenidos por fuerza
      const fuerzaRaw = Array.isArray(fuerzaData) ? fuerzaData.map((item: any) => {
        const normalizedName = normalizeName(item.name || item.fuerza || item.fuerzaAsignada);
        const fuerzaKey = normalizedName.toUpperCase() as keyof typeof COLORS_FUERZA;
        return {
          name: normalizedName,
          value: item.value || item.cantidad || 0,
          color: COLORS_FUERZA[fuerzaKey] || '#757575'
        };
      }) : [];
      const fuerzaProcessed = groupData(fuerzaRaw);

      // Procesar evolución temporal
      const evolucionProcessed = Array.isArray(evolucionData) ? evolucionData.map((item: any) => ({
        month: item.month || item.mes || item.periodo || 'N/A',
        value: item.value || item.cantidad || 0,
        fecha: item.fecha || item.month || item.mes
      })) : [];

      // Procesar ranking de delitos
      const delitosProcessed = Array.isArray(delitosData) ? delitosData.map((item: any, index: number) => ({
        name: item.delito || item.nombre || item.name || 'Delito desconocido',
        value: item.cantidad || item.value || item.count || 0,
        color: `hsl(${(index * 137.5) % 360}, 70%, 50%)`
      })).sort((a, b) => b.value - a.value) : [];

      setMetricas(metricasData);
      setExpedientesPorEstado(estadoProcessed);
      setExpedientesPorFuerza(expedientesPorFuerzaProcessed);
      setDetenidosPorFuerza(fuerzaProcessed);
      setEvolucionTemporal(evolucionProcessed);
      setRankingDelitos(delitosProcessed);
      setCasosDestacados(casosDestacados);

    } catch (err: any) {
      console.error('Error al cargar datos del centro de comando:', err);
      setError('Error al cargar los datos del centro de comando');
      
      // Datos de ejemplo en caso de error
      setMetricas([
        { label: 'Total Expedientes', value: 0, trend: { value: 0, type: 'neutral', period: 'sin datos' } },
        { label: 'Captura Vigente', value: 0, trend: { value: 0, type: 'neutral', period: 'sin datos' } },
        { label: 'Detenidos', value: 0, trend: { value: 0, type: 'neutral', period: 'sin datos' } },
        { label: 'Eficiencia', value: 0, format: 'percentage', trend: { value: 0, type: 'neutral', period: 'sin datos' } }
      ]);
      setExpedientesPorEstado([]);
      setExpedientesPorFuerza([]);
      setDetenidosPorFuerza([]);
      setEvolucionTemporal([]);
      setRankingDelitos([]);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Configurar auto-refresh
  const { lastUpdate, isRefreshing, refreshNow } = useAutoRefresh({
    interval: 30000, // 30 segundos
    enabled: true,
    onRefresh: fetchAllData
  });

  // Recargar datos cuando cambien los filtros
  useEffect(() => {
    fetchAllData();
  }, [filters]);

  // Controlar cuándo mostrar el modal de carga
  useEffect(() => {
    const navigationState = location.state as any;
    
    // Mostrar modal SOLO si:
    // 1. Viene desde sidebar (fromSidebar: true), O
    // 2. Es acceso directo (sin navigationState)
    // NO mostrar si viene de returnTo (navegación desde detalle)
    const shouldShowModal = !navigationState ||
      (navigationState.fromSidebar && !navigationState.returnTo);
    
    if (shouldShowModal) {
      setShowLoadingModal(true);
    }
  }, [location.state]);

  // Auto-activar fullscreen al montar
  useEffect(() => {
    // Notificar al MainLayout que estamos en fullscreen
    if (onFullscreenChange) {
      onFullscreenChange(true);
    }
    
    // Limpiar al desmontar
    return () => {
      if (onFullscreenChange) {
        onFullscreenChange(false);
      }
    };
  }, [onFullscreenChange]);

  // Manejar tecla ESC para salir de fullscreen
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
        if (onFullscreenChange) {
          onFullscreenChange(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen, onFullscreenChange]);

  // Manejar pantalla completa del navegador
  const toggleBrowserFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  };

  // Manejar toggle de modo fullscreen (ocultar/mostrar barras)
  const toggleFullscreen = () => {
    const newState = !isFullscreen;
    setIsFullscreen(newState);
    if (onFullscreenChange) {
      onFullscreenChange(newState);
    }
  };

  // Manejar salida de fullscreen (mostrar menú principal)
  const exitFullscreen = () => {
    setIsFullscreen(false);
    setSidebarVisible(true);
    if (onFullscreenChange) {
      onFullscreenChange(false);
    }
  };

  // Navegación a páginas específicas con filtros
  const handlePanelClick = (route: string) => {
    // Pasar filtros activos y ruta de retorno
    navigate(route, {
      state: {
        filters: filters,
        returnTo: '/estadisticas/centro-comando',
        timestamp: Date.now()
      }
    });
  };

  // Funciones de drill-down
  const handleDrillDown = (title: string, data: any[], type: 'table' | 'chart' = 'table', chartType?: 'pie' | 'bar') => {
    setDrillDownData({
      title,
      type,
      data,
      chartType
    });
  };

  const closeDrillDown = () => {
    setDrillDownData(null);
  };

  return (
    <>
      {/* Modal de carga */}
      <CentroEstadisticasLoadingModal
        open={showLoadingModal}
        onClose={() => setShowLoadingModal(false)}
      />
      
      <Box className={`centro-comando-container ${isFullscreen ? 'fullscreen' : ''}`}>

      {/* Header del centro de comando */}
      <Box className="centro-comando-header">
        <Box>
          <Typography className="centro-comando-title">
            <DashboardIcon sx={{ mr: 2, fontSize: '2.5rem' }} />
            CENTRO DE ESTADÍSTICAS CUFRE
          </Typography>
          <Typography className="centro-comando-subtitle">
            Tablero de control unificado - Actualización automática cada 30 segundos
          </Typography>
          
          {/* Mostrar filtros activos */}
          {(filters.estado || filters.fuerza || filters.tipoCaptura) && (
            <Box sx={{ mt: 1, display: 'flex', gap: 1, alignItems: 'center' }}>
              <FilterListIcon sx={{ color: 'var(--cc-text-secondary)', fontSize: '1.2rem' }} />
              {filters.estado && (
                <Chip
                  label={`Estado: ${filters.estado}`}
                  size="small"
                  onDelete={() => setFilter('estado', undefined)}
                  sx={{
                    backgroundColor: 'var(--cc-bg-highlight)',
                    color: 'var(--cc-text-primary)',
                    '& .MuiChip-deleteIcon': {
                      color: 'var(--cc-text-secondary)'
                    }
                  }}
                />
              )}
              {filters.fuerza && (
                <Chip
                  label={`Fuerza: ${filters.fuerza}`}
                  size="small"
                  onDelete={() => setFilter('fuerza', undefined)}
                  sx={{
                    backgroundColor: 'var(--cc-bg-highlight)',
                    color: 'var(--cc-text-primary)',
                    '& .MuiChip-deleteIcon': {
                      color: 'var(--cc-text-secondary)'
                    }
                  }}
                />
              )}
              {filters.tipoCaptura && (
                <Chip
                  label={`Tipo: ${filters.tipoCaptura}`}
                  size="small"
                  onDelete={() => setFilter('tipoCaptura', undefined)}
                  sx={{
                    backgroundColor: 'var(--cc-bg-highlight)',
                    color: 'var(--cc-text-primary)',
                    '& .MuiChip-deleteIcon': {
                      color: 'var(--cc-text-secondary)'
                    }
                  }}
                />
              )}
              <IconButton
                size="small"
                onClick={clearFilters}
                sx={{ color: 'var(--cc-text-secondary)' }}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            </Box>
          )}
        </Box>
        
        <Box className="centro-comando-controls">
          <Tooltip title="Volver al Dashboard">
            <IconButton
              className="control-button"
              onClick={() => navigate('/dashboard')}
            >
              <HomeIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Actualizar datos">
            <IconButton
              className="control-button"
              onClick={refreshNow}
              disabled={isRefreshing}
            >
              <RefreshIcon sx={{
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Pantalla completa del navegador">
            <IconButton
              className="control-button"
              onClick={toggleBrowserFullscreen}
            >
              <FullscreenIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Grid principal 2x3 */}
      <Box className="centro-comando-grid">
        {/* Panel de casos destacados - ocupa 2 filas */}
        <Box sx={{ gridArea: 'casos-destacados' }}>
          <PanelCasosDestacados
            casoMasNuevo={casosDestacados.casoMasNuevo}
            casoMasAntiguo={casosDestacados.casoMasAntiguo}
            top3Prioridad={casosDestacados.top3Prioridad}
            isLoading={loading}
            lastUpdate={lastUpdate}
            isRefreshing={isRefreshing}
            onRefresh={refreshNow}
          />
        </Box>

        {/* Panel de expedientes por estado */}
        <Box sx={{ gridArea: 'expedientes-estado' }}>
          <PanelGraficoDona
            title="Expedientes por Estado"
            data={expedientesPorEstado}
            isLoading={loading}
            lastUpdate={lastUpdate}
            isRefreshing={isRefreshing}
            onClick={() => handlePanelClick('/estadisticas')}
            onRefresh={refreshNow}
            onSegmentClick={(data) => {
              // Si ya está filtrado por este estado, quitar el filtro
              if (filters.estado === data.name) {
                setFilter('estado', undefined);
              } else {
                setFilter('estado', data.name);
              }
            }}
          />
        </Box>

        {/* NUEVO: Panel de expedientes por fuerza */}
        <Box sx={{ gridArea: 'expedientes-fuerza' }}>
          <PanelGraficoDona
            title="Expedientes por Fuerza"
            data={expedientesPorFuerza}
            isLoading={loading}
            lastUpdate={lastUpdate}
            isRefreshing={isRefreshing}
            onClick={() => handlePanelClick('/estadisticas')}
            onRefresh={refreshNow}
            onSegmentClick={(data) => {
              // Si ya está filtrado por esta fuerza, quitar el filtro
              if (filters.fuerza === data.name) {
                setFilter('fuerza', undefined);
              } else {
                setFilter('fuerza', data.name);
              }
            }}
          />
        </Box>

        {/* Panel de detenidos por fuerza */}
        <Box sx={{ gridArea: 'detenidos-fuerza' }}>
          <PanelDetenidosCompacto
            title="Detenidos por Fuerza"
            data={detenidosPorFuerza}
            isLoading={loading}
            lastUpdate={lastUpdate}
            isRefreshing={isRefreshing}
            onClick={() => handlePanelClick('/estadisticas/detenidos-por-fuerza')}
            onRefresh={refreshNow}
            onItemClick={(data) => {
              // Si ya está filtrado por esta fuerza, quitar el filtro
              if (filters.fuerza === data.name) {
                setFilter('fuerza', undefined);
              } else {
                setFilter('fuerza', data.name);
              }
            }}
          />
        </Box>

        {/* Panel de evolución temporal */}
        <Box sx={{ gridArea: 'evolucion-temporal' }}>
          <PanelLineaTemporal
            title="Evolución Temporal"
            data={evolucionTemporal}
            isLoading={loading}
            lastUpdate={lastUpdate}
            isRefreshing={isRefreshing}
            onClick={() => handlePanelClick('/estadisticas/evolucion-expedientes')}
            onRefresh={refreshNow}
          />
        </Box>
      </Box>

      {/* Panel de métricas clave */}
      <Box className="centro-comando-metrics" sx={{ px: 3, pb: 2 }}>
        <PanelMetricas
          metricas={metricas}
          isLoading={loading}
          lastUpdate={lastUpdate}
          isRefreshing={isRefreshing}
          onClick={() => handlePanelClick('/estadisticas')}
          onRefresh={refreshNow}
        />
      </Box>

      {/* Panel extendido de ranking */}
      <Box className="centro-comando-extended">
        <PanelRanking
          title="Ranking de Delitos"
          data={rankingDelitos}
          isLoading={loading}
          lastUpdate={lastUpdate}
          isRefreshing={isRefreshing}
          onClick={() => handlePanelClick('/estadisticas/ranking-delitos')}
          onRefresh={refreshNow}
          maxItems={8}
          showPercentage={true}
        />
      </Box>

      {/* Indicador de estado */}
      {error && (
        <Box
          sx={{
            position: 'fixed',
            bottom: '100px',
            right: '2rem',
            background: 'var(--cc-alert)',
            color: 'white',
            padding: '1rem',
            borderRadius: '8px',
            maxWidth: '300px',
            zIndex: 1000
          }}
        >
          <Typography variant="body2">{error}</Typography>
        </Box>
      )}

        {/* Modal de drill-down */}
        <DrillDownModal
          open={!!drillDownData}
          onClose={closeDrillDown}
          data={drillDownData}
        />
      </Box>
    </>
  );
};

// Componente principal que provee el contexto
const CentroComandoPage: React.FC = () => {
  return (
    <EstadisticasFilterProvider>
      <CentroComandoContent />
    </EstadisticasFilterProvider>
  );
};

export default CentroComandoPage;