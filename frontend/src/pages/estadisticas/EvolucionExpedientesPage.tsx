import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import TimelineIcon from '@mui/icons-material/Timeline';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from 'recharts';
import estadisticaService from '../../api/estadisticaService';
import CinematicLayout from '../../components/layout/CinematicLayout';
import '../../styles/CinematicPages.css';

// Utilidad para agrupar por mes
function groupByMonth(expedientes: any[]) {
  const counts: Record<string, number> = {};
  expedientes.forEach(exp => {
    const fecha = exp.created_at || exp.fechaIngreso || exp.fechaCreacion;
    if (!fecha) return;
    // Obtener año-mes (YYYY-MM)
    const date = new Date(fecha);
    if (isNaN(date.getTime())) return;
    const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    counts[key] = (counts[key] || 0) + 1;
  });
  // Ordenar por fecha ascendente
  return Object.entries(counts)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([month, value]) => ({ month, value }));
}

const EvolucionExpedientesPage: React.FC = () => {
  const [data, setData] = useState<{ month: string, value: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const chartData = await estadisticaService.getExpedientesPorMes();
        setData(chartData as { month: string; value: number }[]);
      } catch (err: any) {
        setError('Error al cargar la evolución de expedientes');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Tooltip cinematográfico personalizado
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const d = payload[0].payload;
      return (
        <Box className="cinematic-tooltip">
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'var(--cc-cufre)', mb: 1 }}>
            {d.month}
          </Typography>
          <Typography variant="body2" sx={{ color: 'var(--cc-text-primary)' }}>
            <b>Expedientes:</b> {d.value}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <CinematicLayout
      title="Evolución Temporal de Expedientes"
      subtitle="Cantidad de expedientes cargados en el sistema agrupados por mes de creación"
      icon={<TimelineIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
    >
      <Box className="cinematic-fade-in">
        <Box className="cinematic-main-panel">
          <Box className="cinematic-chart-container">
            {loading ? (
              <Box className="cinematic-loading">
                <CircularProgress sx={{ color: 'var(--cc-cufre)' }} />
                <Typography className="cinematic-loading-text">
                  Cargando evolución temporal...
                </Typography>
              </Box>
            ) : error ? (
              <Alert
                severity="error"
                className="cinematic-alert error"
                sx={{
                  backgroundColor: 'rgba(255, 107, 53, 0.1)',
                  border: '1px solid var(--cc-alert)',
                  color: 'var(--cc-text-primary)'
                }}
              >
                {error}
              </Alert>
            ) : data.length === 0 ? (
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: 300,
                flexDirection: 'column'
              }}>
                <Typography variant="h6" sx={{ color: 'var(--cc-text-secondary)', textAlign: 'center' }}>
                  No hay expedientes registrados en el sistema.
                </Typography>
              </Box>
            ) : (
              <ResponsiveContainer width="100%" height={500}>
                <LineChart data={data} margin={{ top: 20, right: 40, left: 40, bottom: 60 }}>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="var(--cc-border)"
                    opacity={0.3}
                  />
                  <XAxis
                    dataKey="month"
                    stroke="var(--cc-text-secondary)"
                    fontSize={15}
                    angle={-35}
                    textAnchor="end"
                    height={60}
                    tick={{ fill: 'var(--cc-text-primary)' }}
                  />
                  <YAxis
                    allowDecimals={false}
                    stroke="var(--cc-text-secondary)"
                    fontSize={15}
                    tick={{ fill: 'var(--cc-text-primary)' }}
                  />
                  <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(255, 214, 0, 0.1)' }} />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="var(--cc-cufre)"
                    strokeWidth={3}
                    dot={{ r: 6, fill: 'var(--cc-cufre)', stroke: 'var(--cc-bg-primary)', strokeWidth: 2 }}
                    activeDot={{ r: 10, fill: 'var(--cc-cufre)', stroke: 'var(--cc-bg-primary)', strokeWidth: 3 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </Box>
        </Box>
      </Box>
    </CinematicLayout>
  );
};

export default EvolucionExpedientesPage; 