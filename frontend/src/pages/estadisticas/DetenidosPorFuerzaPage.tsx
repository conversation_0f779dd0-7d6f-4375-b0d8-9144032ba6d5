import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Avatar
} from '@mui/material';
import BarChartIcon from '@mui/icons-material/BarChart';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import estadisticaService from '../../api/estadisticaService';
import CinematicLayout from '../../components/layout/CinematicLayout';
import '../../styles/CinematicPages.css';

// Mapeo de logos de fuerza (igual que en ExpedienteDetallePage)
const fuerzaIconos: Record<string, { src: string; alt: string }> = {
  PFA: { src: '/images/icon1.png', alt: 'Policía Federal Argentina' },
  GNA: { src: '/images/Insignia_de_la_Gendarmería_de_Argentina.svg.png', alt: 'Gendarmería Nacional Argentina' },
  PNA: { src: '/images/icon3.png', alt: 'Prefectura Naval Argentina' },
  PSA: { src: '/images/icon4.png', alt: 'Policía de Seguridad Aeroportuaria' },
  INTERPOOL: { src: '/images/interpol.png', alt: 'Interpol' },
  SPF: { src: '/images/Logo_SPF.png', alt: 'Servicio Penitenciario Federal' },
  CUFRE: { src: '/images/logo-cufre-2.png', alt: 'CUFRE' },
};

// Colores para las fuerzas (mismo esquema que en EstadisticasPage)
const COLORS_FUERZA = {
  'PFA': '#1976d2', // Azul
  'GNA': '#388e3c', // Verde
  'PSA': '#222', // Negro
  'PNA': '#bfa16c', // Marrón clarito
  'SPF': '#4fc3f7', // Celeste
  'INTERPOOL': '#8e24aa', // Violeta
  'CUFRE': '#ffd600',  // Amarillo
  'SIN DATOS': '#ff6b35', // Naranja distintivo para destacar que requiere atención
  'POL LOC': '#ff9800' // Naranja (para Policía Local)
};

// Color por defecto para fuerzas no mapeadas
const DEFAULT_COLOR = '#757575';

const DetenidosPorFuerzaPage: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const chartData = await estadisticaService.getDetenidosPorFuerza();
        // Agrega logo y color según fuerza
        const dataWithIcons = (chartData as any[]).map((item: any) => {
          // Normalizar el nombre de la fuerza a mayúsculas para consistencia
          const fuerzaNombre = (item.fuerza || 'SIN DATOS').toUpperCase();
          
          // Asignar color según el mapa de colores o usar color por defecto
          const color = fuerzaNombre in COLORS_FUERZA 
            ? COLORS_FUERZA[fuerzaNombre as keyof typeof COLORS_FUERZA]
            : DEFAULT_COLOR;
            
          return {
            ...item,
            fuerza: fuerzaNombre, // Normalizar el nombre para mostrar
            logo: fuerzaIconos[fuerzaNombre]?.src,
            alt: fuerzaIconos[fuerzaNombre]?.alt,
            color: color
          };
        });
        setData(dataWithIcons);
      } catch (err: any) {
        setError('Error al cargar los datos de detenidos por fuerza');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // Tooltip cinematográfico personalizado
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const d = payload[0].payload;
      return (
        <Box className="cinematic-tooltip">
          <Box display="flex" alignItems="center" mb={1}>
            {d.logo && <Avatar src={d.logo} alt={d.alt} sx={{ width: 32, height: 32, mr: 1, bgcolor: '#fff' }} />}
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'var(--cc-cufre)' }}>{d.fuerza}</Typography>
          </Box>
          <Typography variant="body2" sx={{ color: 'var(--cc-text-primary)' }}>
            <b>Detenidos:</b> {d.value}
          </Typography>
          {d.fuerza === 'SIN DATOS' && (
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic', opacity: 0.9, color: 'var(--cc-text-secondary)' }}>
              Detenidos sin fuerza asignada
            </Typography>
          )}
        </Box>
      );
    }
    return null;
  };

  return (
    <CinematicLayout
      title="Detenidos por Fuerza"
      subtitle="Cantidad de expedientes en estado DETENIDO agrupados por fuerza asignada"
      icon={<BarChartIcon sx={{ mr: 2, fontSize: '2.5rem' }} />}
    >
      <Box className="cinematic-fade-in">
        <Typography className="cinematic-description">
          <strong>Nota:</strong> La categoría "SIN DATOS" representa detenidos a los que aún no se les ha asignado una fuerza.
        </Typography>
        
        <Box className="cinematic-main-panel">
          <Box className="cinematic-chart-container">
            {loading ? (
              <Box className="cinematic-loading">
                <CircularProgress sx={{ color: 'var(--cc-cufre)' }} />
                <Typography className="cinematic-loading-text">
                  Cargando datos...
                </Typography>
              </Box>
            ) : error ? (
              <Alert
                severity="error"
                className="cinematic-alert error"
                sx={{
                  backgroundColor: 'rgba(255, 107, 53, 0.1)',
                  border: '1px solid var(--cc-alert)',
                  color: 'var(--cc-text-primary)'
                }}
              >
                {error}
              </Alert>
            ) : data.length === 0 ? (
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: 300,
                flexDirection: 'column'
              }}>
                <Typography variant="h6" sx={{ color: 'var(--cc-text-secondary)', textAlign: 'center' }}>
                  No hay detenidos registrados en el sistema.
                </Typography>
              </Box>
            ) : (
              <ResponsiveContainer width="100%" height={500}>
                <BarChart
                  data={data}
                  layout="vertical"
                  margin={{ top: 20, right: 40, left: 40, bottom: 20 }}
                  barCategoryGap={32}
                >
                  <XAxis
                    type="number"
                    allowDecimals={false}
                    stroke="var(--cc-text-secondary)"
                    fontSize={15}
                    tick={{ fill: 'var(--cc-text-secondary)' }}
                  />
                  <YAxis
                    dataKey="fuerza"
                    type="category"
                    tick={({ x, y, payload }) => {
                      const d = data.find(f => f.fuerza === payload.value);
                      return (
                        <g transform={`translate(${x - 55},${y})`}>
                          {d?.logo && <image href={d.logo} x={-38} y={-16} height={32} width={32} style={{ filter: 'drop-shadow(0 2px 6px rgba(255, 214, 0, 0.3))' }} />}
                          <text x={0} y={0} dy={8} fill="var(--cc-text-primary)" fontWeight="bold" fontSize={17}>{payload.value}</text>
                        </g>
                      );
                    }}
                    width={180}
                  />
                  <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(255, 214, 0, 0.1)' }} />
                  <Bar dataKey="value" radius={[8, 8, 8, 8]}>
                    {data.map((entry, idx) => (
                      <Cell key={`bar-${entry.fuerza}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            )}
          </Box>
        </Box>
      </Box>
    </CinematicLayout>
  );
};

export default DetenidosPorFuerzaPage; 