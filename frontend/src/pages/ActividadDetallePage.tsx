import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Breadcrumbs,
  Link,
  Alert,
  CircularProgress,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Computer as ComputerIcon,
  Code as CodeIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';
import { ActividadSistema, ActividadDetalle, TipoDetalle } from '../types/actividad.types';
import actividadSistemaService from '../api/actividadSistemaService';
import { isLegacyRecord, formatLegacyField } from '../utils/legacyUtils';
import LegacyBadge from '../components/actividad/LegacyBadge';

const ActividadDetallePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [actividad, setActividad] = useState<ActividadSistema | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      cargarDetalle(parseInt(id));
    }
  }, [id]);

  const cargarDetalle = async (actividadId: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await actividadSistemaService.getActividadById(actividadId);
      setActividad(data);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Error al cargar el detalle');
      console.error('Error al cargar detalle:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatearFechaHora = (fechaString: string) => {
    try {
      return new Date(fechaString).toLocaleString('es-AR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      return fechaString;
    }
  };

  const formatearDuracion = (duracionMs?: number) => {
    if (!duracionMs) return 'N/A';
    if (duracionMs < 1000) return `${duracionMs} ms`;
    if (duracionMs < 60000) return `${(duracionMs / 1000).toFixed(2)} segundos`;
    return `${(duracionMs / 60000).toFixed(2)} minutos`;
  };

  const getEstadoIcon = (estado?: string) => {
    switch (estado) {
      case 'SUCCESS':
        return <CheckCircleIcon color="success" />;
      case 'ERROR':
        return <ErrorIcon color="error" />;
      case 'WARNING':
        return <WarningIcon color="warning" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getEstadoColor = (estado?: string) => {
    switch (estado) {
      case 'SUCCESS':
        return 'success';
      case 'ERROR':
        return 'error';
      case 'WARNING':
        return 'warning';
      default:
        return 'info';
    }
  };

  const formatearJSON = (jsonString?: string) => {
    if (!jsonString) return 'N/A';
    try {
      const obj = JSON.parse(jsonString);
      return JSON.stringify(obj, null, 2);
    } catch {
      return jsonString;
    }
  };

  const renderDetalleSeccion = (detalle: ActividadDetalle) => {
    const getTipoIcon = (tipo: string) => {
      switch (tipo) {
        case TipoDetalle.REQUEST:
          return <ComputerIcon color="primary" />;
        case TipoDetalle.RESPONSE:
          return <CheckCircleIcon color="success" />;
        case TipoDetalle.ERROR:
          return <ErrorIcon color="error" />;
        default:
          return <InfoIcon color="info" />;
      }
    };

    const getTipoColor = (tipo: string) => {
      switch (tipo) {
        case TipoDetalle.REQUEST:
          return 'primary';
        case TipoDetalle.RESPONSE:
          return 'success';
        case TipoDetalle.ERROR:
          return 'error';
        default:
          return 'info';
      }
    };

    return (
      <Accordion key={detalle.id}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getTipoIcon(detalle.tipoDetalle)}
            <Typography variant="h6">
              {detalle.tipoDetalle.replace('_', ' ')}
            </Typography>
            <Chip 
              label={detalle.tipoDetalle} 
              color={getTipoColor(detalle.tipoDetalle) as any}
              size="small" 
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Fecha de creación: {formatearFechaHora(detalle.fechaCreacion)}
            </Typography>
          </Box>
          
          {detalle.contenidoJson ? (
            <Paper sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
              <Typography variant="subtitle2" gutterBottom>
                Contenido JSON:
              </Typography>
              <Box 
                component="pre" 
                sx={{ 
                  fontSize: '0.75rem',
                  overflow: 'auto',
                  maxHeight: 300,
                  fontFamily: 'monospace',
                  whiteSpace: 'pre-wrap'
                }}
              >
                {formatearJSON(detalle.contenidoJson)}
              </Box>
            </Paper>
          ) : (
            <Typography variant="body2" color="textSecondary">
              No hay contenido JSON disponible
            </Typography>
          )}
        </AccordionDetails>
      </Accordion>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ p: 3 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          <IconButton onClick={() => navigate('/actividad-sistema')}>
            <ArrowBackIcon />
          </IconButton>
        </Box>
      </Container>
    );
  }

  if (!actividad) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ p: 3 }}>
          <Alert severity="warning">
            No se encontró la actividad solicitada.
          </Alert>
        </Box>
      </Container>
    );
  }

  const isLegacy = isLegacyRecord(actividad);

  return (
    <Container maxWidth="lg">
      <Box sx={{ p: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs separator={<NavigateNextIcon fontSize="small" />} sx={{ mb: 3 }}>
          <Link 
            color="inherit" 
            href="#" 
            onClick={() => navigate('/actividad-sistema')}
            sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
          >
            <ArrowBackIcon fontSize="small" />
            Actividad del Sistema
          </Link>
          <Typography color="text.primary">
            Detalle #{actividad.id}
          </Typography>
        </Breadcrumbs>

        {/* Header */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h4" component="h1" gutterBottom>
                  Detalle de Actividad #{actividad.id}
                </Typography>
                {isLegacy && <LegacyBadge />}
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getEstadoIcon(actividad.estadoRespuesta)}
                <Chip
                  label={actividad.estadoRespuesta || 'N/A'}
                  color={getEstadoColor(actividad.estadoRespuesta) as any}
                  variant="filled"
                />
              </Box>
            </Box>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <Chip 
                icon={<PersonIcon />}
                label={actividad.usuario} 
                color="primary" 
                variant="outlined" 
              />
              <Chip 
                label={actividad.tipoAccion} 
                color="secondary" 
                variant="outlined" 
              />
              {actividad.modulo && (
                <Chip 
                  label={actividad.modulo} 
                  color="info" 
                  variant="outlined" 
                />
              )}
            </Stack>
          </CardContent>
        </Card>

        {/* Información General */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <InfoIcon color="primary" />
              Información General
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Usuario
                        </TableCell>
                        <TableCell>{actividad.usuario}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Fecha y Hora
                        </TableCell>
                        <TableCell>{formatearFechaHora(actividad.fechaHora)}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Tipo de Acción
                        </TableCell>
                        <TableCell>{actividad.tipoAccion}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Categoría
                        </TableCell>
                        <TableCell
                          sx={{
                            fontStyle: isLegacy && !actividad.categoriaAccion ? 'italic' : 'normal',
                            color: isLegacy && !actividad.categoriaAccion ? 'text.secondary' : 'inherit'
                          }}
                        >
                          {formatLegacyField(actividad.categoriaAccion, 'categoriaAccion')}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Módulo
                        </TableCell>
                        <TableCell
                          sx={{
                            fontStyle: isLegacy && !actividad.modulo ? 'italic' : 'normal',
                            color: isLegacy && !actividad.modulo ? 'text.secondary' : 'inherit'
                          }}
                        >
                          {formatLegacyField(actividad.modulo, 'modulo')}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
              
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          IP Cliente
                        </TableCell>
                        <TableCell
                          sx={{
                            fontStyle: isLegacy && !actividad.ipCliente ? 'italic' : 'normal',
                            color: isLegacy && !actividad.ipCliente ? 'text.secondary' : 'inherit'
                          }}
                        >
                          {formatLegacyField(actividad.ipCliente, 'ipCliente')}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          ID de Sesión
                        </TableCell>
                        <TableCell
                          sx={{
                            fontFamily: 'monospace',
                            fontSize: '0.75rem',
                            fontStyle: isLegacy && !actividad.sessionId ? 'italic' : 'normal',
                            color: isLegacy && !actividad.sessionId ? 'text.secondary' : 'inherit'
                          }}
                        >
                          {formatLegacyField(actividad.sessionId, 'sessionId')}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Duración
                        </TableCell>
                        <TableCell>{formatearDuracion(actividad.duracionMs)}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row" sx={{ fontWeight: 'bold' }}>
                          Estado de Respuesta
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={actividad.estadoRespuesta || 'N/A'} 
                            color={getEstadoColor(actividad.estadoRespuesta) as any}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Box>

            {actividad.detalles && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Detalles Adicionales
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: '#f9f9f9' }}>
                  <Typography variant="body2">
                    {actividad.detalles}
                  </Typography>
                </Paper>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Detalles Técnicos */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ComputerIcon color="primary" />
              Detalles Técnicos
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Endpoint
                </Typography>
                <Paper sx={{ p: 1, backgroundColor: '#f5f5f5', fontFamily: 'monospace' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontStyle: isLegacy && !actividad.endpoint ? 'italic' : 'normal',
                      color: isLegacy && !actividad.endpoint ? 'text.secondary' : 'inherit'
                    }}
                  >
                    {actividad.endpoint
                      ? `${actividad.metodoHttp || 'GET'} ${actividad.endpoint}`
                      : formatLegacyField(actividad.endpoint, 'endpoint')
                    }
                  </Typography>
                </Paper>
              </Box>
              
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <Typography variant="subtitle2" gutterBottom>
                  User Agent
                </Typography>
                <Paper sx={{ p: 1, backgroundColor: '#f5f5f5' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: '0.75rem',
                      wordBreak: 'break-all',
                      fontStyle: isLegacy && !actividad.userAgent ? 'italic' : 'normal',
                      color: isLegacy && !actividad.userAgent ? 'text.secondary' : 'inherit'
                    }}
                  >
                    {formatLegacyField(actividad.userAgent, 'userAgent')}
                  </Typography>
                </Paper>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Request/Response y Cambios Realizados */}
        {actividad.actividadDetalles && actividad.actividadDetalles.length > 0 && (
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CodeIcon color="primary" />
                Request/Response y Cambios Realizados
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              {actividad.actividadDetalles.map(detalle => renderDetalleSeccion(detalle))}
            </CardContent>
          </Card>
        )}
      </Box>
    </Container>
  );
};

export default ActividadDetallePage;