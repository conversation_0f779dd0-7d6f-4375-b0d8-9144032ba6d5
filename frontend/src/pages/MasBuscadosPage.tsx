import React, { useEffect, useState, useCallback } from 'react';
import expedienteService from '../api/expedienteService';
import { Expediente } from '../types/expediente.types';
import ModalWanted from '../components/expedientes/ModalWanted';
import { useOutletContext, useNavigate } from 'react-router-dom';
import { useModalContext } from '../context/ModalContext';
import '../styles/MasBuscadosPage.css';
import { Button, Box, Typography, CircularProgress, ToggleButtonGroup, ToggleButton, ButtonGroup, Tooltip } from '@mui/material';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import RefreshIcon from '@mui/icons-material/Refresh';
const logoFallback = '/images/logo-cufre-2.png';

// Tipos de expediente (ajusta según tu backend)
// type Expediente = { ... } // Ya importado

type ExpedienteDetalle = Expediente & {
  // Puedes agregar más campos si el detalle trae más info
  descripcion?: string;
};

// Modal simple (puedes reemplazarlo por uno reutilizable si existe)
function Modal({ children, onClose }: { children: React.ReactNode; onClose: () => void }) {
  return (
    <div style={{
      position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',
      background: 'rgba(0,0,0,0.5)', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 1000
    }}>
      <div style={{ background: '#fff', padding: 24, borderRadius: 8, minWidth: 320, maxWidth: 500, position: 'relative' }}>
        <button onClick={onClose} style={{ position: 'absolute', top: 8, right: 8, fontSize: 18, background: 'none', border: 'none', cursor: 'pointer' }}>×</button>
        {children}
      </div>
    </div>
  );
}

// Función utilitaria para obtener la URL absoluta de la foto principal
const getFotoUrl = (foto: any) => {
  if (!foto || !foto.rutaArchivo) return ''; // Si no hay foto o ruta, devuelve cadena vacía
  if (foto.rutaArchivo.startsWith('http')) {
    return foto.rutaArchivo; // Ya es una URL absoluta
  }
  
  // Comprobar si estamos en el entorno de producción (servidor)
  // En producción, NO necesitamos añadir /api porque Nginx ya lo hace
  const isProduction = window.location.hostname === '************' || 
                     !window.location.hostname.includes('localhost');
                     
  // Asegurarse de que la ruta tenga el prefijo /api/ necesario para uploads
  if (foto.rutaArchivo.startsWith('/uploads/')) {
    return isProduction ? foto.rutaArchivo : `/api${foto.rutaArchivo}`;
  }
  // Usar ruta relativa que será manejada por NGINX
  return foto.rutaArchivo.startsWith('/') ? foto.rutaArchivo : '/' + foto.rutaArchivo;
};

// Función para obtener el color según la posición
const getColorByIndex = (index: number) => {
  if (index < 3) return '#e53935'; // rojo
  if (index < 7) return '#fb8c00'; // naranja
  if (index < 10) return '#fbc02d'; // amarillo
  return '#03a9f4'; // celeste estándar para 11+
};

const getPriorityClass = (priority: number, prefix: string = 'bg') => {
  if (priority === 1) return `${prefix}-danger`;
  if (priority <= 3) return `${prefix}-orange`;
  if (priority <= 10) return `${prefix}-warning`;
  return `${prefix}-navy`;
};

const getBorderColorByIndex = (index: number) => {
  if (index < 3) return '#e53935'; // rojo
  if (index < 7) return '#fb8c00'; // naranja
  if (index < 10) return '#fbc02d'; // amarillo
  return '#03a9f4'; // celeste estándar para 11+
};

const MasBuscadosPage: React.FC = () => {
  const [expedientes, setExpedientes] = useState<Expediente[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [limit, setLimit] = useState(10);
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
  const [expedienteSeleccionado, setExpedienteSeleccionado] = useState<ExpedienteDetalle | null>(null);
  const [loadingDetalle, setLoadingDetalle] = useState(false);
  const { masBuscadosModalOpen, setMasBuscadosModalOpen } = useModalContext();
  const [expedienteModal, setExpedienteModal] = useState<Expediente | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const navigate = useNavigate();

  // Función para cargar los expedientes más buscados (usando useCallback para evitar recreaciones)
  const fetchMasBuscados = useCallback(() => {
    setLoading(true);
    setError(null);
    expedienteService.getMasBuscados(limit)
      .then(data => {
        setExpedientes(data);
      })
      .catch(() => setError('No se pudieron cargar los más buscados.'))
      .finally(() => setLoading(false));
  }, [limit]);
  
  // Cargar expedientes al montar o cambiar el límite
  useEffect(() => {
    fetchMasBuscados();
  }, [limit, fetchMasBuscados]);

  // Manejar click en "Ver más"
  const handleVerMas = (id: number) => {
    setLoadingDetalle(true);
    expedienteService.getExpedienteDetalle(id)
      .then(data => setExpedienteSeleccionado(data))
      .catch(() => setError('No se pudo cargar el detalle del expediente.'))
      .finally(() => setLoadingDetalle(false));
  };

  // Al hacer clic en la tarjeta
  const handleCardClick = (exp: Expediente) => {
    setExpedienteModal(exp);
    setMasBuscadosModalOpen(true);
  };

  // --- NUEVA VISTA DE TARJETAS ---
  const TarjetasView = ({ expedientes }: { expedientes: Expediente[] }) => (
    <div className="tarjetas-grid">
      {expedientes.map((exp, index) => {
        const fotoPrincipal = exp.fotografias?.find(f => f.id === exp.fotoPrincipalId);
        const nombreCompleto = ((exp.personaExpedientes?.[0]?.persona?.nombre || '') + ' ' + (exp.personaExpedientes?.[0]?.persona?.apellido || '')).trim().toUpperCase() || 'PRÓFUGO NO ESPECIFICADO';
        const priority = index + 1;
        const borderColor = getBorderColorByIndex(index);
        const bgColor = getColorByIndex(index);
        return (
          <div key={exp.id}>
            <div
              className="tarjeta-mas-buscado text-center"
              onClick={() => handleCardClick(exp)}
              style={{
                cursor: 'pointer',
                border: `3px solid ${borderColor}`,
                borderRadius: 16,
                width: 170,
                minHeight: 260,
                background: bgColor,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'flex-start',
                margin: '0.5rem',
                boxShadow: '0 2px 8px rgba(0,0,0,0.07)',
                position: 'relative'
              }}
            >
              <span style={{ position: 'absolute', top: 8, left: 12, fontWeight: 700, color: '#111', fontSize: 18, background: '#fff', padding: '0 4px', borderRadius: 4 }}>
                #{priority}
              </span>
              <img
                src={getFotoUrl(fotoPrincipal) || logoFallback}
                alt={`Foto de ${nombreCompleto}`}
                className="tarjeta-imagen-mas-buscado"
                style={{ width: 100, height: 100, objectFit: 'cover', borderRadius: '50%', margin: '32px 0 12px 0', border: `2.5px solid ${borderColor}` }}
                onError={(e) => { (e.target as HTMLImageElement).src = logoFallback; }}
              />
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center', width: '100%' }}>
                <h3 className="wanted-name" style={{ fontSize: 18, fontWeight: 700, margin: 0, color: '#111', textTransform: 'uppercase', letterSpacing: 1, textAlign: 'center' }}>{nombreCompleto}</h3>
                <div className="text-muted small" style={{ fontSize: 13, marginTop: 6, color: '#111', textAlign: 'center' }}>Expediente: {exp.numero || 'N/A'}</div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  // --- NUEVA VISTA DE LISTA ---
  const ListView = ({ expedientes }: { expedientes: Expediente[] }) => (
    <div>
      {expedientes.map((exp, idx) => {
        const fotoPrincipal = exp.fotografias?.find(f => f.id === exp.fotoPrincipalId);
        const nombreCompleto = ((exp.personaExpedientes?.[0]?.persona?.nombre || '') + ' ' + (exp.personaExpedientes?.[0]?.persona?.apellido || '')).trim().toUpperCase() || 'PRÓFUGO NO ESPECIFICADO';
        const priority = idx + 1;
        const borderColor = getBorderColorByIndex(idx);
        const bgColor = getColorByIndex(idx);
        const isTop3 = idx < 3;
        const isTop7 = idx < 7 && idx >= 3;
        const isTop10 = idx < 10 && idx >= 7;
        let delitoLabel = 'Sin delito';
        if (exp.delitos && exp.delitos.length > 0) {
          const primerDelito = (exp.delitos[0] && 'nombre' in exp.delitos[0] && exp.delitos[0].nombre) ? String(exp.delitos[0].nombre) : 'Delito';
          if (exp.delitos.length === 1) {
            delitoLabel = primerDelito;
          } else {
            delitoLabel = `${primerDelito} y TANTOS (${exp.delitos.length - 1}) más`;
          }
        }
        return (
          <div
            key={exp.id}
            className="d-flex align-items-center mb-2"
            style={{
              border: `2.5px solid ${borderColor}`,
              borderRadius: 12,
              background: bgColor,
              padding: '10px 18px',
              margin: '10px 0',
              boxShadow: '0 1px 4px rgba(0,0,0,0.06)',
              minHeight: 80,
              position: 'relative',
              cursor: 'pointer',
              transition: 'box-shadow 0.2s',
            }}
            onClick={() => handleCardClick(exp)}
          >
            <span style={{ fontWeight: 700, fontSize: 20, color: '#111', marginRight: 18, minWidth: 36, textAlign: 'center' }}>#{priority}</span>
            <img
              src={getFotoUrl(fotoPrincipal) || logoFallback}
              alt={`Foto de ${nombreCompleto}`}
              className="imagen-perfil-lista me-3 shadow-sm"
              style={{ width: 56, height: 56, objectFit: 'cover', borderRadius: '50%', border: `2px solid ${borderColor}` }}
              onError={(e) => { (e.target as HTMLImageElement).src = logoFallback; }}
            />
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              <span style={{ fontWeight: 700, fontSize: 17, color: '#111', textTransform: 'uppercase', letterSpacing: 1 }}>{nombreCompleto}</span>
              <span style={{ fontSize: 14, color: '#111', marginTop: 2 }}>Expediente: {exp.numero || 'N/A'}</span>
              <span style={{ fontSize: 13, color: '#333', marginTop: 2 }}>{delitoLabel}</span>
            </div>
          </div>
        );
      })}
    </div>
  );

  // Función para actualizar la lista de expedientes
  const handleRefresh = () => {
    setRefreshing(true);
    fetchMasBuscados();
    // Simulamos un pequeño retraso para mostrar el indicador de carga
    setTimeout(() => {
      setRefreshing(false);
    }, 800);
  };

  // Manejador para cambiar el tipo de vista
  const handleViewTypeChange = (_event: React.MouseEvent<HTMLElement>, newViewType: 'grid' | 'list' | null) => {
    if (newViewType !== null) {
      setViewType(newViewType);
    }
  };

  // Manejador para cambiar el límite de expedientes
  const handleLimitChange = (newLimit: number) => {
    if (newLimit !== limit) {
      setLimit(newLimit);
      // Recargar los expedientes con el nuevo límite
      fetchMasBuscados();
    }
  };

  return (
    <div style={{ maxWidth: 1100, margin: '0 auto', padding: 24 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">Los Más Buscados</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {/* Controles de vista y límite */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Selector de cantidad */}
            <ButtonGroup size="small" aria-label="cantidad de expedientes">
              <Tooltip title="Mostrar 10 expedientes">
                <Button 
                  variant={limit === 10 ? "contained" : "outlined"}
                  onClick={() => handleLimitChange(10)}
                >
                  10
                </Button>
              </Tooltip>
              <Tooltip title="Mostrar 50 expedientes">
                <Button 
                  variant={limit === 50 ? "contained" : "outlined"}
                  onClick={() => handleLimitChange(50)}
                >
                  50
                </Button>
              </Tooltip>
              <Tooltip title="Mostrar 100 expedientes">
                <Button 
                  variant={limit === 100 ? "contained" : "outlined"}
                  onClick={() => handleLimitChange(100)}
                >
                  100
                </Button>
              </Tooltip>
            </ButtonGroup>
            
            {/* Selector de tipo de vista */}
            <ToggleButtonGroup
              value={viewType}
              exclusive
              onChange={handleViewTypeChange}
              aria-label="tipo de vista"
              size="small"
            >
              <ToggleButton value="grid" aria-label="vista de tarjetas">
                <Tooltip title="Vista de tarjetas">
                  <ViewModuleIcon />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="list" aria-label="vista de lista">
                <Tooltip title="Vista de lista">
                  <ViewListIcon />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
          
          {/* Botón de actualizar */}
          <Button
            variant="contained"
            color="primary"
            startIcon={refreshing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
            onClick={handleRefresh}
            disabled={refreshing || loading}
          >
            {refreshing ? 'Actualizando...' : 'Actualizar lista'}
          </Button>
        </Box>
      </Box>

      {loading && <div style={{ textAlign: 'center', margin: 40 }}><span>Cargando...</span></div>}
      {error && <div style={{ color: 'red', margin: 20 }}>{error}</div>}
      {!loading && !error && expedientes.length === 0 && <div style={{ margin: 20 }}>No hay expedientes para mostrar.</div>}

      {!loading && !error && expedientes.length > 0 && (
        <div>
          {viewType === 'grid'
            ? <TarjetasView expedientes={expedientes} />
            : <ListView expedientes={expedientes} />}
        </div>
      )}

      {/* Modal de detalle rápido */}
      {expedienteSeleccionado && (
        <Modal onClose={() => setExpedienteSeleccionado(null)}>
          {loadingDetalle ? (
            <div style={{ textAlign: 'center', margin: 40 }}>Cargando...</div>
          ) : (
            <div style={{ textAlign: 'center' }}>
              <img src="/images/logo_ministerio_seguridad_nacional.png" alt="Logo" style={{ width: 80, marginBottom: 12 }} />
              <img
                src={getFotoUrl(expedienteSeleccionado.fotografias?.find(f => f.id === expedienteSeleccionado.fotoPrincipalId)) || '/img/default.jpg'}
                alt="Foto grande"
                style={{ width: 120, height: 120, objectFit: 'cover', borderRadius: '50%', marginBottom: 12 }}
              />
              <h2 style={{ margin: '8px 0' }}>{expedienteSeleccionado.profugoNombreCompleto || expedienteSeleccionado.numero}</h2>
              <ul style={{ listStyle: 'none', padding: 0, margin: '8px 0' }}>
                {expedienteSeleccionado.delitos && Array.isArray(expedienteSeleccionado.delitos)
                  ? expedienteSeleccionado.delitos.map((d: any) => <li key={d.id || d.nombre}>{d.nombre || d.id}</li>)
                  : null}
              </ul>
              {expedienteSeleccionado.recompensa && expedienteSeleccionado.montoRecompensa && <p style={{ color: '#1565c0', fontWeight: 500 }}>Recompensa: ${expedienteSeleccionado.montoRecompensa}</p>}
              <button onClick={() => {
                setExpedienteSeleccionado(null);
                navigate(`/expedientes/${expedienteSeleccionado.id}`);
              }}
                style={{ marginTop: 16, padding: '8px 18px', borderRadius: 6, border: 'none', background: '#e53935', color: '#fff', cursor: 'pointer', fontWeight: 'bold' }}>
                Ver expediente completo
              </button>
            </div>
          )}
        </Modal>
      )}

      {/* Modal Wanted */}
      <ModalWanted
        expediente={expedienteModal}
        open={masBuscadosModalOpen}
        onClose={() => setMasBuscadosModalOpen(false)}
        onDetalle={() => {
          if (expedienteModal) {
            // Cerrar el modal y navegar a la página de detalle
            setMasBuscadosModalOpen(false);
            navigate(`/expedientes/${expedienteModal.id}`);
          }
        }}
      />
    </div>
  );
};

export default MasBuscadosPage; 