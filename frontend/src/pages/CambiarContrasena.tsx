import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './CambiarContrasena.css';
import usuarioService from '../api/usuarioService';

const CambiarContrasena: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const emailFromState = location.state?.email || '';
  const [email, setEmail] = useState(emailFromState);
  const [currentPassword, setCurrentPassword] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  // Estados para controlar la visibilidad de las contraseñas
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Funciones para alternar la visibilidad de las contraseñas
  const toggleCurrentPasswordVisibility = () => setShowCurrentPassword(!showCurrentPassword);
  const toggleNewPasswordVisibility = () => setShowNewPassword(!showNewPassword);
  const toggleConfirmPasswordVisibility = () => setShowConfirmPassword(!showConfirmPassword);

  useEffect(() => {
    if (!emailFromState) {
      // Si no viene el email por navegación, intentar obtenerlo autenticado
      usuarioService.getMe()
        .then(usuario => setEmail(usuario.email))
        .catch(() => setEmail(''));
    }
  }, [emailFromState]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!email) {
      setError('Debe ingresar su email.');
      return;
    }
    
    if (!currentPassword) {
      setError('Debe ingresar su contraseña actual.');
      return;
    }
    
    if (password.length < 8) {
      setError('La nueva contraseña debe tener al menos 8 caracteres.');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }
    
    try {
      // Usamos el nuevo método que no requiere autenticación
      const resp = await usuarioService.primerCambioContrasena(email, currentPassword, password);
      setSuccess(true);
      if (resp && resp.action === 'activar_2fa') {
        if (resp.temp_token) {
          localStorage.setItem('temp_token', resp.temp_token);
        }
        setTimeout(() => navigate('/activar-2fa', { state: { email } }), 1500);
      } else {
        setTimeout(() => navigate('/login'), 1500);
      }
    } catch (err: any) {
      console.error(err);
      setError('Error al cambiar la contraseña. Verifique sus credenciales.');
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: 24 }}>
          <img src="/img/logo-minseg.png" alt="Logo Ministerio de Seguridad" style={{ height: 64, marginBottom: 12 }} />
          <h2 style={{ margin: 0, fontWeight: 600, color: '#222' }}>Cambiar Contraseña</h2>
          <div style={{ marginTop: 8, marginBottom: 24, fontSize: 18, color: '#444', fontWeight: 500, background: '#f5f6fa', borderRadius: 8, padding: '6px 18px' }}>
            <span style={{ letterSpacing: 0.5 }}>Email: {email}</span>
          </div>
        </div>
        <form onSubmit={handleSubmit} style={{ width: '100%' }}>
          <div className="form-group">
            <label>Contraseña actual</label>
            <div style={{ position: 'relative' }}>
              <input 
                type={showCurrentPassword ? "text" : "password"} 
                value={currentPassword} 
                onChange={e => setCurrentPassword(e.target.value)} 
                placeholder="Ingrese su contraseña actual" 
                required 
                style={{ paddingRight: '40px' }} 
              />
              <div 
                onClick={toggleCurrentPasswordVisibility} 
                style={{ 
                  position: 'absolute', 
                  right: '10px', 
                  top: '50%', 
                  transform: 'translateY(-50%)', 
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                {showCurrentPassword ? "Ocultar" : "Mostrar"}
              </div>
            </div>
          </div>
          <div className="form-group">
            <label>Nueva contraseña</label>
            <div style={{ position: 'relative' }}>
              <input 
                type={showNewPassword ? "text" : "password"} 
                value={password} 
                onChange={e => setPassword(e.target.value)} 
                placeholder="Ingrese nueva contraseña" 
                required 
                style={{ paddingRight: '40px' }} 
              />
              <div 
                onClick={toggleNewPasswordVisibility} 
                style={{ 
                  position: 'absolute', 
                  right: '10px', 
                  top: '50%', 
                  transform: 'translateY(-50%)', 
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                {showNewPassword ? "Ocultar" : "Mostrar"}
              </div>
            </div>
          </div>
          <div className="form-group">
            <label>Confirmar nueva contraseña</label>
            <div style={{ position: 'relative' }}>
              <input 
                type={showConfirmPassword ? "text" : "password"} 
                value={confirmPassword} 
                onChange={e => setConfirmPassword(e.target.value)} 
                placeholder="Confirme nueva contraseña" 
                required 
                style={{ paddingRight: '40px' }} 
              />
              <div 
                onClick={toggleConfirmPasswordVisibility} 
                style={{ 
                  position: 'absolute', 
                  right: '10px', 
                  top: '50%', 
                  transform: 'translateY(-50%)', 
                  cursor: 'pointer',
                  color: '#666'
                }}
              >
                {showConfirmPassword ? "Ocultar" : "Mostrar"}
              </div>
            </div>
          </div>
          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">Contraseña cambiada con éxito. Redirigiendo...</div>}
          <button type="submit" className="btn-primary" disabled={success} style={{ marginTop: 16, fontWeight: 600, fontSize: 16, padding: '12px 0' }}>Cambiar</button>
        </form>
      </div>
    </div>
  );
};

export default CambiarContrasena; 