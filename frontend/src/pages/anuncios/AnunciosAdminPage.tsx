import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Alert,
  CircularProgress,
  Snackbar,
  TextField,
  FormControlLabel,
  Switch,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Announcement as AnnouncementIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import anuncioService from '../../api/anuncioService';
import { Anuncio, CrearAnuncioRequest } from '../../types/anuncio.types';
import ProfessionalHeader from '../../components/common/ProfessionalHeader';

// Editor de texto enriquecido simple (placeholder para react-quill)
interface SimpleEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const SimpleEditor: React.FC<SimpleEditorProps> = ({ value, onChange, placeholder }) => {
  return (
    <TextField
      multiline
      rows={6}
      fullWidth
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      variant="outlined"
      helperText="Nota: En una implementación completa, aquí iría un editor WYSIWYG como react-quill"
    />
  );
};

const AnunciosAdminPage: React.FC = () => {
  const { user } = useAuth();
  const [anuncios, setAnuncios] = useState<Anuncio[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Estados para el modal de crear/editar
  const [modalOpen, setModalOpen] = useState(false);
  const [editingAnuncio, setEditingAnuncio] = useState<Anuncio | null>(null);
  const [formData, setFormData] = useState<CrearAnuncioRequest>({
    titulo: '',
    contenido: '',
    activo: false
  });
  const [submitting, setSubmitting] = useState(false);

  // Estados para confirmación de eliminación
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [anuncioToDelete, setAnuncioToDelete] = useState<Anuncio | null>(null);

  useEffect(() => {
    cargarAnuncios();
  }, []);

  const cargarAnuncios = async () => {
    try {
      setLoading(true);
      const data = await anuncioService.getAll();
      // Asegurar que data sea un array válido
      setAnuncios(Array.isArray(data) ? data : []);
      setError(null);
    } catch (err: any) {
      console.error('Error al cargar anuncios:', err);
      setError('Error al cargar los anuncios: ' + (err.response?.data?.error || err.message));
      // En caso de error, asegurar que anuncios sea un array vacío
      setAnuncios([]);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenModal = (anuncio?: Anuncio) => {
    if (anuncio) {
      setEditingAnuncio(anuncio);
      setFormData({
        titulo: anuncio.titulo,
        contenido: anuncio.contenido,
        activo: anuncio.activo
      });
    } else {
      setEditingAnuncio(null);
      setFormData({
        titulo: '',
        contenido: '',
        activo: false
      });
    }
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setEditingAnuncio(null);
    setFormData({
      titulo: '',
      contenido: '',
      activo: false
    });
  };

  const handleSubmit = async () => {
    if (!formData.titulo.trim() || !formData.contenido.trim()) {
      setError('El título y el contenido son obligatorios');
      return;
    }

    try {
      setSubmitting(true);
      
      if (editingAnuncio) {
        // Reemplazar el TODO con la llamada al servicio de actualización
        await anuncioService.update(editingAnuncio.id, formData);
        setSuccess('Anuncio actualizado exitosamente');
      } else {
        await anuncioService.create(formData);
        setSuccess('Anuncio creado exitosamente');
      }
      
      handleCloseModal();
      cargarAnuncios();
    } catch (err: any) {
      setError('Error al guardar el anuncio: ' + (err.response?.data?.error || err.message));
    } finally {
      setSubmitting(false);
    }
  };

  const handleToggleActivo = async (anuncio: Anuncio) => {
    try {
      if (anuncio.activo) {
        await anuncioService.desactivar(anuncio.id);
        setSuccess('Anuncio desactivado');
      } else {
        await anuncioService.activar(anuncio.id);
        setSuccess('Anuncio activado');
      }
      cargarAnuncios();
    } catch (err: any) {
      setError('Error al cambiar el estado del anuncio: ' + (err.response?.data?.error || err.message));
    }
  };

  const handleDeleteClick = (anuncio: Anuncio) => {
    setAnuncioToDelete(anuncio);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!anuncioToDelete) return;

    try {
      await anuncioService.delete(anuncioToDelete.id);
      setSuccess('Anuncio eliminado exitosamente');
      setDeleteDialogOpen(false);
      setAnuncioToDelete(null);
      cargarAnuncios();
    } catch (err: any) {
      setError('Error al eliminar el anuncio: ' + (err.response?.data?.error || err.message));
    }
  };

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-AR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <ProfessionalHeader
        title="Anuncios"
        buttonText="Crear Anuncio"
        onButtonClick={() => handleOpenModal()}
        showButton={true}
      />
      
      <Box sx={{ p: 3 }}>

      {/* Estadísticas rápidas */}
      <Box sx={{ display: 'flex', gap: 3, mb: 3, flexWrap: 'wrap' }}>
        <Card sx={{ minWidth: 200, flex: 1 }}>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Total de Anuncios
            </Typography>
            <Typography variant="h4">
              {Array.isArray(anuncios) ? anuncios.length : 0}
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ minWidth: 200, flex: 1 }}>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Anuncios Activos
            </Typography>
            <Typography variant="h4" color="success.main">
              {Array.isArray(anuncios) ? anuncios.filter(a => a.activo).length : 0}
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Tabla de anuncios */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Título</TableCell>
              <TableCell>Estado</TableCell>
              <TableCell>Creado por</TableCell>
              <TableCell>Fecha de Creación</TableCell>
              <TableCell align="center">Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {!Array.isArray(anuncios) || anuncios.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography variant="body2" color="textSecondary">
                    {!Array.isArray(anuncios) ? 'Error al cargar los datos' : 'No hay anuncios creados'}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              anuncios.map((anuncio) => (
                <TableRow key={anuncio.id} hover>
                  <TableCell>
                    <Typography variant="subtitle2">
                      {anuncio.titulo}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" noWrap>
                      {anuncio.contenido.substring(0, 100)}...
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={anuncio.activo ? 'Activo' : 'Inactivo'}
                      color={anuncio.activo ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{anuncio.creadoPorNombre || 'N/A'}</TableCell>
                  <TableCell>{formatFecha(anuncio.fechaCreacion)}</TableCell>
                  <TableCell align="center">
                    <IconButton
                      onClick={() => handleOpenModal(anuncio)}
                      color="primary"
                      title="Editar"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      onClick={() => handleToggleActivo(anuncio)}
                      color={anuncio.activo ? 'warning' : 'success'}
                      title={anuncio.activo ? 'Desactivar' : 'Activar'}
                    >
                      {anuncio.activo ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                    <IconButton
                      onClick={() => handleDeleteClick(anuncio)}
                      color="error"
                      title="Eliminar"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Paper>

      {/* Modal de crear/editar anuncio */}
      <Dialog open={modalOpen} onClose={handleCloseModal} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingAnuncio ? 'Editar Anuncio' : 'Crear Nuevo Anuncio'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Título del Anuncio"
              value={formData.titulo}
              onChange={(e) => setFormData({ ...formData, titulo: e.target.value })}
              margin="normal"
              required
            />
            
            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom>
                Contenido del Anuncio *
              </Typography>
              <SimpleEditor
                value={formData.contenido}
                onChange={(value) => setFormData({ ...formData, contenido: value })}
                placeholder="Escribe el contenido del anuncio aquí..."
              />
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={formData.activo}
                  onChange={(e) => setFormData({ ...formData, activo: e.target.checked })}
                />
              }
              label="Activar este anuncio al guardar"
              sx={{ mt: 2 }}
            />
            
            {formData.activo && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Al activar este anuncio, todos los demás anuncios se desactivarán automáticamente.
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} startIcon={<CancelIcon />}>
            Cancelar
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            {submitting ? 'Guardando...' : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de confirmación de eliminación */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirmar Eliminación</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas eliminar el anuncio "{anuncioToDelete?.titulo}"?
            Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbars para mensajes */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
      </Box>
    </Box>
  );
};

export default AnunciosAdminPage;