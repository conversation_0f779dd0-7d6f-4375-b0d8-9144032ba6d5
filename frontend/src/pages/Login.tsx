import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Avatar,
  Alert,
  CircularProgress,
  Link,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { useAuth } from '../context/AuthContext';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { login, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirigir si ya está autenticado
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!username.trim() || !password) {
      setError('Por favor ingrese usuario y contraseña');
      return;
    }

    try {
      // Guardar credenciales para el proceso 2FA si fuera necesario
      if (rememberMe) {
        localStorage.setItem('last_email', username);
        localStorage.setItem('last_password', password);
      }

      const response = await login({ email: username, password });

      if ('action' in response) {
        if (response.action === 'cambiar_contrasena') {
          navigate('/cambiar-contrasena', { state: { email: username } });
        } else if (response.action === 'activar_2fa') {
          navigate('/activar-2fa');
        } else if (response.action === 'validar_2fa') {
          localStorage.setItem('last_email', username);
          localStorage.setItem('last_password', password);
          navigate('/validar-2fa');
        } else {
          setError('Acción desconocida recibida');
          console.error('Respuesta de acción desconocida:', response);
        }
      } else if ('token' in response) {
        if (!rememberMe) {
          localStorage.removeItem('last_email');
          localStorage.removeItem('last_password');
        }
      } else {
        setError('Respuesta inesperada del servidor');
      }
    } catch (err: any) {
      // Manejo de errores específicos
      if (err.response) {
        if (err.response.status === 401) {
          setError('Credenciales inválidas. Por favor, verifica tu usuario y contraseña.');
        } else if (err.response.status === 403) {
          setError('No tienes permisos para acceder.');
        } else if (err.response.status >= 500) {
          setError('Error interno del servidor. Intenta más tarde.');
        } else {
          setError(err.response.data?.message || 'Error desconocido al iniciar sesión.');
        }
      } else if (err.request) {
        setError('No se pudo conectar con el servidor. Verifica tu conexión a internet.');
      } else {
        setError('Error inesperado al iniciar sesión.');
      }
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%', maxWidth: 400 }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mb: 3
            }}
          >
            <img src="/logo-cufre-2.png" alt="CUFRE Logo" style={{ height: 64, marginBottom: 8, marginTop: 8 }} />
            <Typography component="h1" variant="h5">
              Iniciar Sesión
            </Typography>
            <Typography variant="body2" color="textSecondary" align="center" sx={{ mt: 1 }}>
              Comando Unificado Federal de Recaptura de Evadidos
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} noValidate>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Usuario"
              name="username"
              autoComplete="username"
              autoFocus
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={loading}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Contraseña"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
            <FormControlLabel
              control={
                <Checkbox
                  value="remember"
                  color="primary"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  disabled={loading}
                />
              }
              label="Recordarme"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Ingresar'}
            </Button>
          </Box>
        </Paper>
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 2 }}>
          {'© '}
          {new Date().getFullYear()}
          {' CUFRE - Sistema de Gestión de Expedientes'}
        </Typography>
      </Box>
    </Container>
  );
};

export default Login; 