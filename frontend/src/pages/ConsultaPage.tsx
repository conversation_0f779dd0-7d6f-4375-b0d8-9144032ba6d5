import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Divider,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Link
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import FolderIcon from '@mui/icons-material/Folder';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { useNavigate } from 'react-router-dom';
import expedienteService from '../api/expedienteService';

// Definir interfaces para los resultados
interface ResultadoBusqueda {
  id: number;
  tipo: 'expediente' | 'persona';
  nombre?: string;
  apellido?: string;
  numeroExpediente?: string;
  caratula?: string;
  numeroIdentificacion?: string;
  tipoIdentificacion?: string;
  estado?: string;
  fechaCreacion?: string;
  detalle?: string;
}

const ConsultaPage: React.FC = () => {
  const navigate = useNavigate();
  const [tipoBusqueda, setTipoBusqueda] = useState<'expediente' | 'persona' | 'ambos'>('ambos');
  const [nombre, setNombre] = useState('');
  const [apellido, setApellido] = useState('');
  const [expediente, setExpediente] = useState('');
  const [numeroIdentificacion, setNumeroIdentificacion] = useState('');
  const [loading, setLoading] = useState(false);
  const [resultados, setResultados] = useState<ResultadoBusqueda[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [busquedaRealizada, setBusquedaRealizada] = useState(false);

  // Limpiar formulario
  const handleLimpiar = () => {
    setNombre('');
    setApellido('');
    setExpediente('');
    setNumeroIdentificacion('');
    setTipoBusqueda('ambos');
    setResultados([]);
    setError(null);
    setBusquedaRealizada(false);
  };

  // Verificar si hay al menos un criterio de búsqueda
  const hayFiltros = (): boolean => {
    return !!nombre || !!apellido || !!expediente || !!numeroIdentificacion;
  };

  // Función para realizar la búsqueda
  const handleBuscar = async () => {
    if (!hayFiltros()) {
      setError('Debe ingresar al menos un criterio de búsqueda.');
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const data = await expedienteService.buscarAvanzado({
        nombre,
        apellido,
        numeroExpediente: expediente,
        tipoBusqueda,
        numeroIdentificacion,
      });
      
      // Transformar los datos para mostrarlos correctamente
      const resultadosFormateados: ResultadoBusqueda[] = data.map((item: any) => {
        // Determinar si es un expediente o una persona basado en los campos disponibles
        const tipo = item.numero || item.caratula ? 'expediente' : 'persona';
        
        return {
          id: item.id,
          tipo,
          nombre: item.nombre || (tipo === 'expediente' ? null : item.nombres),
          apellido: item.apellido || (tipo === 'expediente' ? null : item.apellidos),
          numeroExpediente: item.numero,
          caratula: item.caratula,
          numeroIdentificacion: item.numeroDocumento || item.numeroIdentificacion,
          tipoIdentificacion: item.tipoDocumento,
          estado: item.estado,
          fechaCreacion: item.fechaCreacion,
          detalle: tipo === 'expediente' 
            ? `${item.caratula || 'Sin carátula'} - ${item.estado || 'Estado desconocido'}` 
            : `${item.tipoDocumento || ''} ${item.numeroDocumento || ''}`
        };
      });
      
      setResultados(resultadosFormateados);
      setBusquedaRealizada(true);
    } catch (e: any) {
      console.error('Error en búsqueda:', e);
      setResultados([]);
      setError(`Error al realizar la búsqueda: ${e.message || 'Error desconocido'}`);
      setBusquedaRealizada(true);
    } finally {
      setLoading(false);
    }
  };
  
  // Función para navegar al detalle
  const navegarADetalle = (item: ResultadoBusqueda) => {
    if (item.tipo === 'expediente') {
      navigate(`/expedientes/${item.id}`);
    } else {
      navigate(`/personas/${item.id}`);
    }
  };

  return (
    <Box sx={{ maxWidth: 1100, mx: 'auto', mt: 4, mb: 4 }}>
      {/* Encabezado */}
      <Paper elevation={3} sx={{ p: 3, mb: 3, borderRadius: 3, background: 'linear-gradient(90deg, #1976d2 0%, #2196f3 100%)', color: 'white' }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          Consulta de Expedientes y Personas
        </Typography>
        <Typography variant="subtitle1" sx={{ opacity: 0.92 }}>
          Utiliza los filtros para buscar expedientes o personas en el sistema.
        </Typography>
      </Paper>

      {/* Filtros de búsqueda */}
      <Paper sx={{ p: 3, borderRadius: 3, mb: 3 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
          Filtros de búsqueda
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <TextField
            label="Nombre"
            value={nombre}
            onChange={e => setNombre(e.target.value)}
            sx={{ flex: 1, minWidth: 180 }}
            variant="outlined"
            size="small"
            placeholder="Ej: Juan"
            inputProps={{ maxLength: 50 }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleBuscar();
              }
            }}
          />
          <TextField
            label="Apellido"
            value={apellido}
            onChange={e => setApellido(e.target.value)}
            sx={{ flex: 1, minWidth: 180 }}
            variant="outlined"
            size="small"
            placeholder="Ej: Pérez"
            inputProps={{ maxLength: 50 }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleBuscar();
              }
            }}
          />
          <TextField
            label="N° Expediente"
            value={expediente}
            onChange={e => setExpediente(e.target.value)}
            sx={{ flex: 1, minWidth: 180 }}
            variant="outlined"
            size="small"
            placeholder="Ej: 123/2023"
            inputProps={{ maxLength: 20 }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleBuscar();
              }
            }}
          />
          <TextField
            label="N° Identificación"
            value={numeroIdentificacion}
            onChange={e => setNumeroIdentificacion(e.target.value)}
            sx={{ flex: 1, minWidth: 180 }}
            variant="outlined"
            size="small"
            placeholder="Ej: 12345678"
            inputProps={{ maxLength: 20 }}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleBuscar();
              }
            }}
          />
          <FormControl sx={{ flex: 1, minWidth: 180 }} size="small">
            <InputLabel>Tipo de Búsqueda</InputLabel>
            <Select
              value={tipoBusqueda}
              label="Tipo de Búsqueda"
              onChange={e => setTipoBusqueda(e.target.value as any)}
            >
              <MenuItem value="ambos">Ambos</MenuItem>
              <MenuItem value="expediente">Solo Expedientes</MenuItem>
              <MenuItem value="persona">Solo Personas</MenuItem>
            </Select>
          </FormControl>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={loading ? undefined : <SearchIcon />}
              onClick={handleBuscar}
              sx={{ minWidth: 120, height: 40 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={22} color="inherit" /> : 'Buscar'}
            </Button>
            <Button
              variant="outlined"
              onClick={handleLimpiar}
              sx={{ height: 40 }}
              disabled={loading}
            >
              Limpiar
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Resultados */}
      <Paper sx={{ p: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Resultados {resultados.length > 0 && `(${resultados.length})`}
          </Typography>
          {busquedaRealizada && resultados.length > 0 && (
            <Chip 
              label={`${resultados.length} resultado${resultados.length !== 1 ? 's' : ''} encontrado${resultados.length !== 1 ? 's' : ''}`} 
              color="primary" 
              variant="outlined" 
              size="small" 
            />
          )}
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        )}
        
        {busquedaRealizada && resultados.length === 0 && !error && (
          <Alert severity="info" sx={{ mb: 2 }}>
            No se encontraron resultados para los criterios de búsqueda especificados.
          </Alert>
        )}
        
        {!busquedaRealizada && !error && (
          <Alert severity="info" sx={{ mb: 2 }}>
            Utilice los filtros de búsqueda para encontrar expedientes o personas en el sistema.
          </Alert>
        )}
        
        {resultados.length > 0 && (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell width="10%">Tipo</TableCell>
                  <TableCell width="30%">Identificación</TableCell>
                  <TableCell width="50%">Detalle</TableCell>
                  <TableCell width="10%" align="center">Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {resultados.map((row) => (
                  <TableRow key={`${row.tipo}-${row.id}`} hover>
                    <TableCell>
                      <Chip
                        icon={row.tipo === 'expediente' ? <FolderIcon /> : <PersonIcon />}
                        label={row.tipo === 'expediente' ? 'Expediente' : 'Persona'}
                        color={row.tipo === 'expediente' ? 'primary' : 'secondary'}
                        variant="outlined"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {row.tipo === 'expediente' ? (
                        <Typography variant="body2">
                          <strong>Expediente:</strong> {row.numeroExpediente || 'Sin número'}
                        </Typography>
                      ) : (
                        <Typography variant="body2">
                          <strong>{row.apellido || ''}, {row.nombre || ''}</strong>
                          {row.numeroIdentificacion && (
                            <Box component="span" sx={{ display: 'block', mt: 0.5 }}>
                              <strong>{row.tipoIdentificacion || 'Doc'}:</strong> {row.numeroIdentificacion}
                            </Box>
                          )}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {row.tipo === 'expediente' ? (
                        <>
                          <Typography variant="body2">
                            <strong>Carátula:</strong> {row.caratula || 'Sin carátula'}
                          </Typography>
                          {row.estado && (
                            <Chip 
                              label={row.estado} 
                              size="small" 
                              sx={{ mt: 1 }}
                              color={row.estado === 'ACTIVO' ? 'success' : 'default'}
                            />
                          )}
                        </>
                      ) : (
                        <Typography variant="body2">{row.detalle || 'Sin detalles'}</Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Ver detalle">
                        <IconButton 
                          size="small" 
                          color="primary" 
                          onClick={() => navegarADetalle(row)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>
    </Box>
  );
};

export default ConsultaPage; 