import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import apiClient from '../api/axiosClient';
import {
  Typography,
  Box,
  Container,
  Alert,
  Card,
  CardContent,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Snackbar
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  DeleteOutline as DeleteIcon
} from '@mui/icons-material';
import { FiltroActividad } from '../types/actividad.types';
import useActividadSistema from '../hooks/useActividadSistema';
import FiltrosAvanzados from '../components/actividad/FiltrosAvanzados';
import TablaActividad from '../components/actividad/TablaActividad';

const ActividadSistemaPage: React.FC = () => {
  const navigate = useNavigate();
  const [filtrosActuales, setFiltrosActuales] = useState<FiltroActividad>({});
  const [menuEliminarAnchor, setMenuEliminarAnchor] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [eliminandoLogs, setEliminandoLogs] = useState(false);
  
  const {
    actividades,
    totalElements,
    totalPages,
    currentPage,
    pageSize,
    loading,
    error,
    cargarActividades,
    cambiarPagina,
    cambiarTamanoPagina,
    aplicarFiltros,
    limpiarFiltros,
    refrescar
  } = useActividadSistema();

  // Marcar notificaciones como vistas al acceder a la página
  useEffect(() => {
    const markNotificationsAsViewed = async () => {
      try {
        await apiClient.post('/api/actividad-sistema/notificaciones/marcar-vistas');
      } catch (error) {
        console.error('Error marking notifications as viewed:', error);
      }
    };

    markNotificationsAsViewed();
  }, []);

  const handleFiltrosChange = (nuevosFiltros: FiltroActividad) => {
    setFiltrosActuales(nuevosFiltros);
    aplicarFiltros(nuevosFiltros);
  };

  const handleLimpiarFiltros = () => {
    setFiltrosActuales({});
    limpiarFiltros();
  };

  const handleVerDetalle = (id: number) => {
    navigate(`/actividad-sistema/${id}`);
  };


  const handleAbrirMenuEliminar = (event: React.MouseEvent<HTMLElement>) => {
    setMenuEliminarAnchor(event.currentTarget);
  };

  const handleCerrarMenuEliminar = () => {
    setMenuEliminarAnchor(null);
  };

  const handleEliminarLogs = async (dias: number) => {
    setEliminandoLogs(true);
    setMenuEliminarAnchor(null);
    
    try {
      const response = await apiClient.delete('/api/actividad-sistema/eliminar', {
        params: { dias }
      });
      
      const eliminados = response.data.eliminados || 0;
      setSnackbarMessage(`Se eliminaron ${eliminados} registros de actividad anteriores a ${dias} días`);
      setSnackbarOpen(true);
      
      // Refrescar la tabla
      refrescar();
    } catch (error: any) {
      console.error('Error eliminando logs:', error);
      setSnackbarMessage(error.response?.data?.message || 'Error al eliminar los registros');
      setSnackbarOpen(true);
    } finally {
      setEliminandoLogs(false);
    }
  };

  const handleCerrarSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SecurityIcon sx={{ fontSize: 40, color: 'white' }} />
                <Box>
                  <Typography variant="h4" fontWeight={700} sx={{ color: 'white' }}>
                    Actividad del Sistema
                  </Typography>
                  <Typography variant="subtitle1" sx={{ color: 'rgba(255,255,255,0.85)' }}>
                    Sistema de auditoría y monitoreo de actividades
                  </Typography>
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Tooltip title="Refrescar datos">
                  <IconButton
                    onClick={refrescar}
                    disabled={loading}
                    sx={{ color: 'white' }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title="Eliminar registros antiguos">
                  <IconButton
                    sx={{ color: 'white' }}
                    onClick={handleAbrirMenuEliminar}
                    disabled={eliminandoLogs}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            
            {/* Estadísticas rápidas */}
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Chip
                label={`${totalElements} registros totales`}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 500
                }}
              />
              <Chip
                label={`Página ${currentPage + 1} de ${totalPages}`}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 500
                }}
              />
              {Object.keys(filtrosActuales).length > 0 && (
                <Chip
                  label="Filtros aplicados"
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontWeight: 500
                  }}
                />
              )}
            </Stack>
          </CardContent>
        </Card>

        {/* Filtros */}
        <FiltrosAvanzados
          filtros={filtrosActuales}
          onFiltrosChange={handleFiltrosChange}
          onLimpiarFiltros={handleLimpiarFiltros}
          loading={loading}
        />

        {/* Error */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Tabla */}
        <TablaActividad
          actividades={actividades}
          loading={loading}
          totalElements={totalElements}
          page={currentPage}
          pageSize={pageSize}
          onPageChange={cambiarPagina}
          onPageSizeChange={cambiarTamanoPagina}
          onVerDetalle={handleVerDetalle}
        />

        {/* Menú de Eliminación */}
        <Menu
          anchorEl={menuEliminarAnchor}
          open={Boolean(menuEliminarAnchor)}
          onClose={handleCerrarMenuEliminar}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={() => handleEliminarLogs(5)} disabled={eliminandoLogs}>
            Eliminar registros anteriores a 5 días
          </MenuItem>
          <MenuItem onClick={() => handleEliminarLogs(10)} disabled={eliminandoLogs}>
            Eliminar registros anteriores a 10 días
          </MenuItem>
          <MenuItem onClick={() => handleEliminarLogs(15)} disabled={eliminandoLogs}>
            Eliminar registros anteriores a 15 días
          </MenuItem>
          <MenuItem onClick={() => handleEliminarLogs(30)} disabled={eliminandoLogs}>
            Eliminar registros anteriores a 30 días
          </MenuItem>
        </Menu>

        {/* Snackbar para notificaciones */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleCerrarSnackbar}
          message={snackbarMessage}
        />
      </Box>
    </Container>
  );
};

export default ActividadSistemaPage;