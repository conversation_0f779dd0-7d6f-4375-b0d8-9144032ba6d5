import React from 'react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './context/AuthContext';
import AppRoutes from './routes/AppRoutes';
import { ModalProvider } from './context/ModalContext';
import { SessionManager } from './components/SessionManager';
import { ScreenSaverProvider } from './context/ScreenSaverContext';
import ScreenSaver from './components/ScreenSaver';

// Definir el tema personalizado
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 4,
          textTransform: 'none',
          fontWeight: 'bold',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
});

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <ModalProvider>
          <ScreenSaverProvider>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              {/* Ocultar elementos no imprimibles en PDF/impresión */}
              <style>{`
                @media print {
                  .no-print { display: none !important; }
                }
              `}</style>
              <SessionManager>
                <AppRoutes />
              </SessionManager>
              {/* Salvapantallas de seguridad */}
              <ScreenSaver />
            </ThemeProvider>
          </ScreenSaverProvider>
        </ModalProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App; 