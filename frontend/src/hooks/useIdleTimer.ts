import { useEffect, useRef, useCallback } from 'react';
import { SCREENSAVER_CONFIG, ActivityEvent } from '../config/screensaver';

interface IdleTimerConfig {
  timeout: number;
  onIdle: () => void;
  onActive: () => void;
  events?: readonly ActivityEvent[];
  enabled?: boolean;
}

interface IdleTimerReturn {
  reset: () => void;
  pause: () => void;
  resume: () => void;
  isIdle: () => boolean;
}

export const useIdleTimer = ({
  timeout = SCREENSAVER_CONFIG.IDLE_TIMEOUT,
  onIdle,
  onActive,
  events = SCREENSAVER_CONFIG.ACTIVITY_EVENTS,
  enabled = true
}: IdleTimerConfig): IdleTimerReturn => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isIdleRef = useRef<boolean>(false);
  const lastActivityRef = useRef<number>(Date.now());
  const enabledRef = useRef<boolean>(enabled);

  // Actualizar la referencia de enabled
  useEffect(() => {
    enabledRef.current = enabled;
  }, [enabled]);

  // Función para resetear el timer
  const reset = useCallback(() => {
    if (!enabledRef.current) return;

    lastActivityRef.current = Date.now();
    
    // Limpiar timeout existente
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Si estaba idle, llamar onActive
    if (isIdleRef.current) {
      isIdleRef.current = false;
      onActive();
    }

    // Configurar nuevo timeout
    timeoutRef.current = setTimeout(() => {
      if (enabledRef.current && !isIdleRef.current) {
        isIdleRef.current = true;
        onIdle();
      }
    }, timeout);
  }, [timeout, onIdle, onActive]);

  // Función para pausar el timer
  const pause = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Función para reanudar el timer
  const resume = useCallback(() => {
    if (enabledRef.current && !timeoutRef.current) {
      const timeSinceLastActivity = Date.now() - lastActivityRef.current;
      const remainingTime = Math.max(0, timeout - timeSinceLastActivity);
      
      if (remainingTime === 0) {
        // Ya debería estar idle
        if (!isIdleRef.current) {
          isIdleRef.current = true;
          onIdle();
        }
      } else {
        // Configurar timeout con el tiempo restante
        timeoutRef.current = setTimeout(() => {
          if (enabledRef.current && !isIdleRef.current) {
            isIdleRef.current = true;
            onIdle();
          }
        }, remainingTime);
      }
    }
  }, [timeout, onIdle]);

  // Función para verificar si está idle
  const isIdle = useCallback(() => {
    return isIdleRef.current;
  }, []);

  // Handler de eventos de actividad
  const handleActivity = useCallback((event: Event) => {
    // Evitar resetear en eventos sintéticos o irrelevantes
    if (!enabledRef.current || !event.isTrusted) return;
    
    reset();
  }, [reset]);

  // Configurar event listeners
  useEffect(() => {
    if (!enabled) {
      pause();
      return;
    }

    // Agregar event listeners
    events.forEach(eventType => {
      document.addEventListener(eventType, handleActivity, {
        passive: true,
        capture: true
      });
    });

    // Inicializar el timer
    reset();

    // Cleanup
    return () => {
      events.forEach(eventType => {
        document.removeEventListener(eventType, handleActivity, true);
      });
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [enabled, events, handleActivity, reset, pause]);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    reset,
    pause,
    resume,
    isIdle
  };
};

export default useIdleTimer;