import { useState, useCallback } from 'react';
import apiClient from '../api/axiosClient';

interface EstadisticaItem {
  nombre: string;
  cantidad: number;
}

interface EstadisticasData {
  porModulo: EstadisticaItem[];
  porUsuario: EstadisticaItem[];
  actividadesRecientes: any[];
}

interface UseEstadisticasActividad {
  data: EstadisticasData | null;
  loading: boolean;
  error: string | null;
  cargarEstadisticas: () => Promise<void>;
}

const useEstadisticasActividad = (): UseEstadisticasActividad => {
  const [data, setData] = useState<EstadisticasData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cargarEstadisticas = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get('/api/actividad-sistema/estadisticas');
      
      // Log para depuración - ver estructura real de los datos
      // TODO: Eliminar este console.log antes del commit final
      console.log('Estadísticas RAW', response.data);
      
      // Función helper para normalización segura de arrays
      const toArray = (val: any): any[] => Array.isArray(val) ? val : [];
      
      // Transformar los datos del backend al formato esperado
      const estadisticas: EstadisticasData = {
        porModulo: toArray(response.data.porModulo)
          .map((item: any[]) => ({
            nombre: item[0] || 'Sin módulo',
            cantidad: item[1] || 0
          }))
          .slice(0, 5),
        porUsuario: toArray(response.data.porUsuario)
          .map((item: any[]) => ({
            nombre: item[0] || 'Sin usuario',
            cantidad: item[1] || 0
          }))
          .slice(0, 5),
        actividadesRecientes: toArray(response.data.actividadesRecientes)
      };
      
      setData(estadisticas);
    } catch (err: any) {
      console.error('Error cargando estadísticas:', err);
      setError(err.response?.data?.message || 'Error al cargar las estadísticas');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    data,
    loading,
    error,
    cargarEstadisticas
  };
};

export default useEstadisticasActividad;