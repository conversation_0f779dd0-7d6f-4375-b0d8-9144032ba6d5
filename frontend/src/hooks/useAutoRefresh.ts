import { useState, useEffect, useCallback, useRef } from 'react';

interface UseAutoRefreshOptions {
  interval?: number;
  enabled?: boolean;
  onRefresh?: () => Promise<void>;
}

interface UseAutoRefreshReturn {
  lastUpdate: Date | null;
  isRefreshing: boolean;
  refreshNow: () => Promise<void>;
  toggleAutoRefresh: () => void;
  isEnabled: boolean;
}

export const useAutoRefresh = ({
  interval = 30000, // 30 segundos por defecto
  enabled = true,
  onRefresh
}: UseAutoRefreshOptions = {}): UseAutoRefreshReturn => {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const refreshNow = useCallback(async () => {
    if (isRefreshing || !onRefresh) return;
    
    try {
      setIsRefreshing(true);
      await onRefresh();
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error durante el refresh automático:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, onRefresh]);

  const toggleAutoRefresh = useCallback(() => {
    setIsEnabled(prev => !prev);
  }, []);

  useEffect(() => {
    if (!isEnabled || !onRefresh) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Ejecutar inmediatamente al montar
    refreshNow();

    // Configurar intervalo
    intervalRef.current = setInterval(refreshNow, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isEnabled, interval, onRefresh]); // Removido refreshNow para evitar ciclo infinito

  return {
    lastUpdate,
    isRefreshing,
    refreshNow,
    toggleAutoRefresh,
    isEnabled
  };
};

export default useAutoRefresh;