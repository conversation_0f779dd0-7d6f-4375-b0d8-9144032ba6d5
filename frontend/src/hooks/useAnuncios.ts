import { useState, useEffect } from 'react';
import anuncioService from '../api/anuncioService';
import { Anuncio } from '../types/anuncio.types';

/**
 * Hook personalizado para manejar la lógica de anuncios
 */
export const useAnuncios = () => {
  const [anuncioActivo, setAnuncioActivo] = useState<Anuncio | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Verifica si hay un anuncio activo para mostrar al usuario
   */
  const verificarAnuncioActivo = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const anuncio = await anuncioService.obtenerActivo();
      
      if (anuncio) {
        setAnuncioActivo(anuncio);
        setModalOpen(true);
      } else {
        setAnuncioActivo(null);
        setModalOpen(false);
      }
    } catch (err: any) {
      console.error('Error al verificar anuncio activo:', err);
      setError('Error al cargar anuncio');
      setAnuncioActivo(null);
      setModalOpen(false);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Marca el anuncio como visto y cierra el modal
   */
  const marcarComoVisto = async () => {
    if (!anuncioActivo) return;

    try {
      await anuncioService.marcarVisto(anuncioActivo.id);
      setModalOpen(false);
      setAnuncioActivo(null);
    } catch (err: any) {
      console.error('Error al marcar anuncio como visto:', err);
      // Aún así cerramos el modal para no bloquear al usuario
      setModalOpen(false);
      setAnuncioActivo(null);
    }
  };

  /**
   * Cierra el modal sin marcar como visto (para casos de error)
   */
  const cerrarModal = () => {
    setModalOpen(false);
    setAnuncioActivo(null);
  };

  // Verificar anuncio activo al montar el componente
  useEffect(() => {
    verificarAnuncioActivo();
  }, []);

  return {
    anuncioActivo,
    modalOpen,
    loading,
    error,
    marcarComoVisto,
    cerrarModal,
    verificarAnuncioActivo
  };
};

export default useAnuncios;