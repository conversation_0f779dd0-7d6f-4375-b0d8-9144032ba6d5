import { useState, useEffect, useCallback } from 'react';
import { ActividadSistema } from '../types/actividad.types';
import apiClient from '../api/axiosClient';

interface NotificationsResponse {
  notifications: ActividadSistema[];
  count: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsViewed: () => Promise<void>;
}

export const useNotifications = (): NotificationsResponse => {
  const [notifications, setNotifications] = useState<ActividadSistema[]>([]);
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      const [notificationsResponse, countResponse] = await Promise.all([
        apiClient.get('/api/actividad-sistema/notificaciones?limite=10'),
        apiClient.get('/api/actividad-sistema/notificaciones/count')
      ]);
      
      setNotifications(notificationsResponse.data);
      setCount(countResponse.data.count);
      setError(null);
    } catch (err: any) {
      setError('Error al cargar notificaciones');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const markAsViewed = useCallback(async () => {
    try {
      await apiClient.post('/api/actividad-sistema/notificaciones/marcar-vistas');
      setCount(0);
      await fetchNotifications();
    } catch (err: any) {
      setError('Error al marcar notificaciones como vistas');
      console.error('Error marking notifications as viewed:', err);
    }
  }, [fetchNotifications]);

  // Auto-actualización cada 30 segundos
  useEffect(() => {
    fetchNotifications();
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  return {
    notifications,
    count,
    loading,
    error,
    fetchNotifications,
    markAsViewed
  };
};