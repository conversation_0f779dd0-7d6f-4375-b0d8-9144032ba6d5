import { useState, useEffect, useCallback } from 'react';
import actividadSistemaService from '../api/actividadSistemaService';
import { 
  ActividadSistema, 
  FiltroActividad, 
  PaginatedResponse, 
  EstadisticasActividad 
} from '../types/actividad.types';

interface UseActividadSistemaResult {
  // Estados principales
  actividades: ActividadSistema[];
  actividadDetalle: ActividadSistema | null;
  estadisticas: EstadisticasActividad | null;
  
  // Estados de paginación
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  
  // Estados de carga y error
  loading: boolean;
  loadingDetalle: boolean;
  loadingEstadisticas: boolean;
  error: string | null;
  
  // Funciones
  cargarActividades: (filtros?: FiltroActividad) => Promise<void>;
  cargarActividadDetalle: (id: number) => Promise<void>;
  cargarEstadisticas: () => Promise<void>;
  cambiarPagina: (page: number) => void;
  cambiarTamanoPagina: (size: number) => void;
  aplicarFiltros: (filtros: FiltroActividad) => void;
  limpiarFiltros: () => void;
  refrescar: () => void;
}

const filtrosIniciales: FiltroActividad = {
  page: 0,
  size: 20,
  sortBy: 'fechaHora',
  sortDirection: 'desc'
};

export const useActividadSistema = (): UseActividadSistemaResult => {
  // Estados principales
  const [actividades, setActividades] = useState<ActividadSistema[]>([]);
  const [actividadDetalle, setActividadDetalle] = useState<ActividadSistema | null>(null);
  const [estadisticas, setEstadisticas] = useState<EstadisticasActividad | null>(null);
  
  // Estados de paginación
  const [totalElements, setTotalElements] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  
  // Estados de carga y error
  const [loading, setLoading] = useState(false);
  const [loadingDetalle, setLoadingDetalle] = useState(false);
  const [loadingEstadisticas, setLoadingEstadisticas] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filtros actuales
  const [filtrosActuales, setFiltrosActuales] = useState<FiltroActividad>(filtrosIniciales);

  /**
   * Cargar actividades con filtros
   */
  const cargarActividades = useCallback(async (filtros: FiltroActividad = filtrosActuales) => {
    setLoading(true);
    setError(null);
    
    try {
      const response: PaginatedResponse<ActividadSistema> = await actividadSistemaService.getActividades(filtros);
      
      setActividades(response.content);
      setTotalElements(response.totalElements);
      setTotalPages(response.totalPages);
      setCurrentPage(response.number);
      setPageSize(response.size);
      
      // Actualizar filtros actuales
      setFiltrosActuales(filtros);
      
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Error al cargar actividades');
      console.error('Error al cargar actividades:', err);
    } finally {
      setLoading(false);
    }
  }, [filtrosActuales]);

  /**
   * Cargar detalle de una actividad específica
   */
  const cargarActividadDetalle = useCallback(async (id: number) => {
    setLoadingDetalle(true);
    setError(null);
    
    try {
      const actividad = await actividadSistemaService.getActividadById(id);
      setActividadDetalle(actividad);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Error al cargar detalle de actividad');
      console.error('Error al cargar detalle de actividad:', err);
    } finally {
      setLoadingDetalle(false);
    }
  }, []);

  /**
   * Cargar estadísticas
   */
  const cargarEstadisticas = useCallback(async () => {
    setLoadingEstadisticas(true);
    setError(null);
    
    try {
      const stats = await actividadSistemaService.getEstadisticas();
      setEstadisticas(stats);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Error al cargar estadísticas');
      console.error('Error al cargar estadísticas:', err);
    } finally {
      setLoadingEstadisticas(false);
    }
  }, []);

  /**
   * Cambiar página
   */
  const cambiarPagina = useCallback((page: number) => {
    const nuevosFiltros = { ...filtrosActuales, page };
    cargarActividades(nuevosFiltros);
  }, [filtrosActuales, cargarActividades]);

  /**
   * Cambiar tamaño de página
   */
  const cambiarTamanoPagina = useCallback((size: number) => {
    const nuevosFiltros = { ...filtrosActuales, size, page: 0 };
    cargarActividades(nuevosFiltros);
  }, [filtrosActuales, cargarActividades]);

  /**
   * Aplicar filtros
   */
  const aplicarFiltros = useCallback((filtros: FiltroActividad) => {
    const nuevosFiltros = { 
      ...filtrosActuales, 
      ...filtros, 
      page: 0 // Resetear a primera página al aplicar filtros
    };
    cargarActividades(nuevosFiltros);
  }, [filtrosActuales, cargarActividades]);

  /**
   * Limpiar filtros
   */
  const limpiarFiltros = useCallback(() => {
    cargarActividades(filtrosIniciales);
  }, [cargarActividades]);

  /**
   * Refrescar datos actuales
   */
  const refrescar = useCallback(() => {
    cargarActividades(filtrosActuales);
  }, [filtrosActuales, cargarActividades]);

  // Cargar datos iniciales
  useEffect(() => {
    cargarActividades();
  }, []);

  return {
    // Estados principales
    actividades,
    actividadDetalle,
    estadisticas,
    
    // Estados de paginación
    totalElements,
    totalPages,
    currentPage,
    pageSize,
    
    // Estados de carga y error
    loading,
    loadingDetalle,
    loadingEstadisticas,
    error,
    
    // Funciones
    cargarActividades,
    cargarActividadDetalle,
    cargarEstadisticas,
    cambiarPagina,
    cambiarTamanoPagina,
    aplicarFiltros,
    limpiarFiltros,
    refrescar
  };
};

export default useActividadSistema;