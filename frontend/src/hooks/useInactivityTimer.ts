import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { InactivityService } from '../services/InactivityService';
import { SESSION_CONFIG, EXCLUDED_ROUTES } from '../config/session.config';
import { ActivityLogger } from '../services/activityLogger';

interface UseInactivityTimerReturn {
  showWarning: boolean;
  countdown: number;
  extendSession: () => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  isActive: boolean;
}

/**
 * Hook personalizado para gestionar el timer de inactividad y cierre automático de sesión
 */
export const useInactivityTimer = (
  onLogout: () => void,
  isAuthenticated: boolean = true
): UseInactivityTimerReturn => {
  const [showWarning, setShowWarning] = useState(false);
  const [countdown, setCountdown] = useState(5 * 60); // 5 minutos en segundos
  const [isActive, setIsActive] = useState(true);
  
  const navigate = useNavigate();
  const location = useLocation();
  const inactivityServiceRef = useRef<InactivityService | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Maneja el cierre de sesión por inactividad
   */
  const handleLogout = useCallback(async () => {
    console.log('🔒 Cerrando sesión por inactividad');
    setShowWarning(false);
    
    // Registrar actividad de logout por inactividad
    await ActivityLogger.logInactivityLogout();
    
    // Limpiar datos de sesión
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('temp_token');
    
    // Llamar al callback de logout
    onLogout();
    
    // Redirigir al login
    navigate('/login', { replace: true });
  }, [onLogout, navigate]);

  /**
   * Maneja la advertencia de sesión próxima a expirar
   */
  const handleWarning = useCallback(() => {
    console.log('⚠️ Mostrando advertencia de sesión próxima a expirar');
    setShowWarning(true);
    setCountdown(5 * 60); // Resetear countdown a 5 minutos
    
    // Iniciar countdown
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }
    
    countdownIntervalRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          // Tiempo agotado, cerrar sesión
          if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
          }
          handleLogout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [handleLogout]);

  /**
   * Maneja la actividad del usuario
   */
  const handleActivity = useCallback(() => {
    // Solo log en desarrollo para evitar spam
    if (process.env.NODE_ENV === 'development') {
      console.log('👆 Actividad detectada');
    }
  }, []);

  /**
   * Extiende la sesión cuando el usuario responde a la advertencia
   */
  const extendSession = useCallback(async () => {
    console.log('✅ Sesión extendida por el usuario');
    setShowWarning(false);
    
    // Registrar extensión de sesión
    await ActivityLogger.logSessionExtended();
    
    // Limpiar countdown
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    
    // Resetear timer de inactividad
    if (inactivityServiceRef.current) {
      inactivityServiceRef.current.extendSession();
    }
  }, []);

  /**
   * Pausa el timer de inactividad
   */
  const pauseTimer = useCallback(() => {
    console.log('⏸️ Timer de inactividad pausado');
    setIsActive(false);
    if (inactivityServiceRef.current) {
      inactivityServiceRef.current.pause();
    }
  }, []);

  /**
   * Reanuda el timer de inactividad
   */
  const resumeTimer = useCallback(() => {
    console.log('▶️ Timer de inactividad reanudado');
    setIsActive(true);
    if (inactivityServiceRef.current) {
      inactivityServiceRef.current.resume();
    }
  }, []);

  /**
   * Verifica si la ruta actual debe ser excluida del seguimiento
   */
  const shouldExcludeRoute = useCallback((pathname: string): boolean => {
    return EXCLUDED_ROUTES.some(route => pathname.startsWith(route));
  }, []);

  /**
   * Efecto principal para inicializar y gestionar el servicio de inactividad
   */
  useEffect(() => {
    // No inicializar si no está autenticado o está en ruta excluida
    if (!isAuthenticated || shouldExcludeRoute(location.pathname)) {
      return;
    }

    console.log('🚀 Inicializando servicio de inactividad');
    
    // Crear nueva instancia del servicio
    inactivityServiceRef.current = new InactivityService(
      handleLogout,
      handleWarning,
      handleActivity
    );

    // Cleanup al desmontar
    return () => {
      console.log('🧹 Limpiando servicio de inactividad');
      if (inactivityServiceRef.current) {
        inactivityServiceRef.current.destroy();
        inactivityServiceRef.current = null;
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
    };
  }, [isAuthenticated, location.pathname, handleLogout, handleWarning, handleActivity, shouldExcludeRoute]);

  /**
   * Efecto para manejar cambios de ruta
   */
  useEffect(() => {
    if (!inactivityServiceRef.current) return;

    if (shouldExcludeRoute(location.pathname)) {
      // Pausar en rutas excluidas
      pauseTimer();
    } else if (isAuthenticated) {
      // Reanudar en rutas normales si está autenticado
      resumeTimer();
    }
  }, [location.pathname, isAuthenticated, shouldExcludeRoute, pauseTimer, resumeTimer]);

  /**
   * Efecto para limpiar countdown cuando se oculta la advertencia
   */
  useEffect(() => {
    if (!showWarning && countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
  }, [showWarning]);

  return {
    showWarning,
    countdown,
    extendSession,
    pauseTimer,
    resumeTimer,
    isActive
  };
};

export default useInactivityTimer;