import { useState, useEffect, useCallback } from 'react';
import { PerfilUsuario, ActualizarPerfilRequest } from '../types/perfil.types';
import { perfilService } from '../services/perfilService';

interface UsePerfilUsuarioState {
  perfil: PerfilUsuario | null;
  loading: boolean;
  error: string | null;
  success: string | null;
}

interface UsePerfilUsuarioActions {
  obtenerPerfil: () => Promise<void>;
  actualizarPerfil: (datos: ActualizarPerfilRequest) => Promise<void>;
  obtenerPerfilPorId: (id: number) => Promise<void>;
  actualizarPerfilPorId: (id: number, datos: ActualizarPerfilRequest) => Promise<void>;
  limpiarMensajes: () => void;
  resetear: () => void;
}

export const usePerfilUsuario = (): UsePerfilUsuarioState & UsePerfilUsuarioActions => {
  const [state, setState] = useState<UsePerfilUsuarioState>({
    perfil: null,
    loading: false,
    error: null,
    success: null
  });

  // Limpiar mensajes después de un tiempo
  useEffect(() => {
    if (state.success || state.error) {
      const timer = setTimeout(() => {
        setState(prev => ({
          ...prev,
          success: null,
          error: null
        }));
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [state.success, state.error]);

  const obtenerPerfil = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const perfil = await perfilService.obtenerPerfilActual();
      setState(prev => ({
        ...prev,
        perfil,
        loading: false
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Error al obtener el perfil'
      }));
    }
  }, []);

  const actualizarPerfil = useCallback(async (datos: ActualizarPerfilRequest) => {
    setState(prev => ({ ...prev, loading: true, error: null, success: null }));
    
    try {
      const perfilActualizado = await perfilService.actualizarPerfilActual(datos);
      setState(prev => ({
        ...prev,
        perfil: perfilActualizado,
        loading: false,
        success: 'Perfil actualizado correctamente'
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Error al actualizar el perfil'
      }));
    }
  }, []);

  const obtenerPerfilPorId = useCallback(async (id: number) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const perfil = await perfilService.obtenerPerfilPorId(id);
      setState(prev => ({
        ...prev,
        perfil,
        loading: false
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Error al obtener el perfil'
      }));
    }
  }, []);

  const actualizarPerfilPorId = useCallback(async (id: number, datos: ActualizarPerfilRequest) => {
    setState(prev => ({ ...prev, loading: true, error: null, success: null }));
    
    try {
      const perfilActualizado = await perfilService.actualizarPerfilPorId(id, datos);
      setState(prev => ({
        ...prev,
        perfil: perfilActualizado,
        loading: false,
        success: 'Perfil actualizado correctamente'
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.response?.data?.message || 'Error al actualizar el perfil'
      }));
    }
  }, []);

  const limpiarMensajes = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      success: null
    }));
  }, []);

  const resetear = useCallback(() => {
    setState({
      perfil: null,
      loading: false,
      error: null,
      success: null
    });
  }, []);

  return {
    ...state,
    obtenerPerfil,
    actualizarPerfil,
    obtenerPerfilPorId,
    actualizarPerfilPorId,
    limpiarMensajes,
    resetear
  };
};

export default usePerfilUsuario;