import { useState, useEffect, useCallback } from 'react';
import estadisticaService from '../api/estadisticaService';
import { 
  ExpedienteData, 
  normalizarDatos, 
  getExampleFuerzaData, 
  getExampleEstadoData 
} from '../utils/estadisticasUtils';

interface UseEstadisticasDataReturn {
  // Estados
  loading: boolean;
  error: string | null;
  expedientesPorFuerza: ExpedienteData[];
  expedientesPorEstado: ExpedienteData[];
  filteredExpedientesPorFuerza: ExpedienteData[];
  filteredExpedientesPorEstado: ExpedienteData[];
  estadisticasGenerales: any;
  activeFuerzaIndex: number | null;
  activeEstadoIndex: number | null;
  activeFilter: { type: 'fuerza' | 'estado' | null, value: string | null };
  
  // Funciones
  fetchData: () => Promise<void>;
  handlePieClick: (data: ExpedienteData, type: 'fuerza' | 'estado', index: number) => Promise<void>;
  clearFilters: () => void;
}

export const useEstadisticasData = (): UseEstadisticasDataReturn => {
  // Estados para los datos
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [expedientesPorFuerza, setExpedientesPorFuerza] = useState<ExpedienteData[]>([]);
  const [expedientesPorEstado, setExpedientesPorEstado] = useState<ExpedienteData[]>([]);
  const [filteredExpedientesPorFuerza, setFilteredExpedientesPorFuerza] = useState<ExpedienteData[]>([]);
  const [filteredExpedientesPorEstado, setFilteredExpedientesPorEstado] = useState<ExpedienteData[]>([]);
  const [estadisticasGenerales, setEstadisticasGenerales] = useState<any>({});
  
  // Estados para la interactividad
  const [activeFuerzaIndex, setActiveFuerzaIndex] = useState<number | null>(null);
  const [activeEstadoIndex, setActiveEstadoIndex] = useState<number | null>(null);
  const [activeFilter, setActiveFilter] = useState<{ type: 'fuerza' | 'estado' | null, value: string | null }>({ type: null, value: null });

  // Función para cargar los datos iniciales
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Reiniciar filtros activos
      setActiveFuerzaIndex(null);
      setActiveEstadoIndex(null);
      setActiveFilter({ type: null, value: null });

      // Obtener datos del servicio
      const [fuerzaData, estadoData, generalesData] = await Promise.all([
        estadisticaService.getExpedientesPorFuerza(),
        estadisticaService.getExpedientesPorEstado(),
        estadisticaService.getEstadisticasGenerales()
      ]);

      // Normalizar datos
      const formattedFuerzaData = normalizarDatos(fuerzaData as any[], 'fuerza');
      const formattedEstadoData = normalizarDatos(estadoData as any[], 'estado');

      // Verificar si hay datos válidos
      if (formattedFuerzaData.length === 0 || formattedEstadoData.length === 0) {
        console.warn('No se encontraron datos válidos en la respuesta del backend');

        // Generar datos de ejemplo solo si no hay datos reales
        const exampleFuerzaData = getExampleFuerzaData();
        const exampleEstadoData = getExampleEstadoData();

        // Actualizar con datos de ejemplo si no hay datos reales
        setExpedientesPorFuerza(formattedFuerzaData.length > 0 ? formattedFuerzaData : exampleFuerzaData);
        setExpedientesPorEstado(formattedEstadoData.length > 0 ? formattedEstadoData : exampleEstadoData);
        setFilteredExpedientesPorFuerza(formattedFuerzaData.length > 0 ? formattedFuerzaData : exampleFuerzaData);
        setFilteredExpedientesPorEstado(formattedEstadoData.length > 0 ? formattedEstadoData : exampleEstadoData);
      } else {
        // Actualizar con datos reales
        setExpedientesPorFuerza(formattedFuerzaData);
        setExpedientesPorEstado(formattedEstadoData);
        setFilteredExpedientesPorFuerza(formattedFuerzaData);
        setFilteredExpedientesPorEstado(formattedEstadoData);
      }

      // Actualizar estadísticas generales
      setEstadisticasGenerales(generalesData || {
        totalExpedientes: formattedFuerzaData.reduce((acc, curr) => acc + curr.value, 0)
      });
    } catch (err: any) {
      console.error('Error al cargar estadísticas:', err);
      setError(err.response?.data?.message || 'Error al cargar las estadísticas');

      // Usar datos de ejemplo en caso de error
      const exampleFuerzaData = getExampleFuerzaData();
      const exampleEstadoData = getExampleEstadoData();

      setExpedientesPorFuerza(exampleFuerzaData);
      setExpedientesPorEstado(exampleEstadoData);
      setFilteredExpedientesPorFuerza(exampleFuerzaData);
      setFilteredExpedientesPorEstado(exampleEstadoData);
      setEstadisticasGenerales({
        totalExpedientes: 0
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Función para filtrar datos cuando se hace clic en un segmento
  const handlePieClick = useCallback(async (data: ExpedienteData, type: 'fuerza' | 'estado', index: number) => {
    // Si ya está seleccionado, reiniciar filtros
    if ((type === 'fuerza' && activeFuerzaIndex === index) ||
        (type === 'estado' && activeEstadoIndex === index)) {
      setActiveFuerzaIndex(null);
      setActiveEstadoIndex(null);
      setActiveFilter({ type: null, value: null });
      setFilteredExpedientesPorFuerza(expedientesPorFuerza);
      setFilteredExpedientesPorEstado(expedientesPorEstado);
      return;
    }

    // Mostrar carga mientras se obtienen los datos filtrados
    setLoading(true);

    try {
      // Actualizar índice activo según el tipo
      if (type === 'fuerza') {
        setActiveFuerzaIndex(index);
        setActiveFilter({ type: 'fuerza', value: data.name });

        // Intentar obtener datos del backend
        try {
          // Llamar a la API para obtener datos filtrados por fuerza
          const filteredData = await estadisticaService.getExpedientesPorEstadoYFuerza(data.name);
          // Normalizar los datos recibidos
          const formattedData = normalizarDatos(filteredData as any[], 'estado');

          // Si hay datos válidos, usarlos
          if (formattedData && formattedData.length > 0) {
            setFilteredExpedientesPorEstado(formattedData);
            setActiveEstadoIndex(null);
            return;
          }
        } catch (error) {
          console.warn(`No se pudieron obtener datos filtrados para la fuerza ${data.name}:`, error);
          // Continuar con la lógica de fallback si hay error
        }

        // Fallback: Generar datos simulados si no hay API disponible o hay error
        // En un sistema en producción, esto eventualmente será reemplazado por datos reales
        const filteredEstados = expedientesPorEstado.map(estado => {
          // Esto es solo un fallback temporal hasta que el backend implemente los endpoints
          const filteredValue = Math.max(1, Math.floor(estado.value * (Math.random() * 0.6 + 0.2)));
          return {
            ...estado,
            value: filteredValue
          };
        });

        setFilteredExpedientesPorEstado(filteredEstados);
        setActiveEstadoIndex(null);
      } else {
        // Caso de filtrado por estado
        setActiveEstadoIndex(index);
        setActiveFilter({ type: 'estado', value: data.name });

        // Intentar obtener datos del backend
        try {
          // Llamar a la API para obtener datos filtrados por estado
          const filteredData = await estadisticaService.getExpedientesPorFuerzaYEstado(data.name);
          // Normalizar los datos recibidos
          const formattedData = normalizarDatos(filteredData as any[], 'fuerza');

          // Si hay datos válidos, usarlos
          if (formattedData && formattedData.length > 0) {
            setFilteredExpedientesPorFuerza(formattedData);
            setActiveFuerzaIndex(null);
            return;
          }
        } catch (error) {
          console.warn(`No se pudieron obtener datos filtrados para el estado ${data.name}:`, error);
          // Continuar con la lógica de fallback si hay error
        }

        // Fallback: Generar datos simulados si no hay API disponible o hay error
        const filteredFuerzas = expedientesPorFuerza.map(fuerza => {
          // Esto es solo un fallback temporal hasta que el backend implemente los endpoints
          const filteredValue = Math.max(1, Math.floor(fuerza.value * (Math.random() * 0.6 + 0.2)));
          return {
            ...fuerza,
            value: filteredValue
          };
        });

        setFilteredExpedientesPorFuerza(filteredFuerzas);
        setActiveFuerzaIndex(null);
      }
    } catch (err) {
      console.error('Error al filtrar datos:', err);
      setError('Error al filtrar los datos por ' + (type === 'fuerza' ? 'fuerza' : 'estado'));
    } finally {
      setLoading(false);
    }
  }, [activeFuerzaIndex, activeEstadoIndex, expedientesPorFuerza, expedientesPorEstado]);

  // Función para limpiar filtros
  const clearFilters = useCallback(() => {
    setActiveFuerzaIndex(null);
    setActiveEstadoIndex(null);
    setActiveFilter({ type: null, value: null });
    setFilteredExpedientesPorFuerza(expedientesPorFuerza);
    setFilteredExpedientesPorEstado(expedientesPorEstado);
  }, [expedientesPorFuerza, expedientesPorEstado]);

  // Cargar datos al montar el hook
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    // Estados
    loading,
    error,
    expedientesPorFuerza,
    expedientesPorEstado,
    filteredExpedientesPorFuerza,
    filteredExpedientesPorEstado,
    estadisticasGenerales,
    activeFuerzaIndex,
    activeEstadoIndex,
    activeFilter,
    
    // Funciones
    fetchData,
    handlePieClick,
    clearFilters
  };
};