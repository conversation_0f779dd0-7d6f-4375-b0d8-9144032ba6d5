import { PerfilUsuario, ActualizarPerfilRequest } from '../types/perfil.types';
import apiClient from '../api/axiosClient';

export const perfilService = {
  /**
   * Obtiene el perfil completo del usuario actual
   */
  obtenerPerfilActual: async (): Promise<PerfilUsuario> => {
    const response = await apiClient.get('/api/usuarios/me/perfil');
    return response.data;
  },

  /**
   * Actualiza el perfil del usuario actual
   */
  actualizarPerfilActual: async (perfil: ActualizarPerfilRequest): Promise<PerfilUsuario> => {
    const response = await apiClient.put('/api/usuarios/me/perfil', perfil);
    return response.data;
  },

  /**
   * Obtiene el perfil de otro usuario (solo SUPERUSUARIO)
   */
  obtenerPerfilPorId: async (id: number): Promise<PerfilUsuario> => {
    const response = await apiClient.get(`/api/usuarios/${id}/perfil`);
    return response.data;
  },

  /**
   * Actualiza el perfil de otro usuario (solo SUPERUSUARIO)
   */
  actualizarPerfilPorId: async (id: number, perfil: ActualizarPerfilRequest): Promise<PerfilUsuario> => {
    const response = await apiClient.put(`/api/usuarios/${id}/perfil`, perfil);
    return response.data;
  },

  /**
   * Valida el formato de teléfono argentino
   */
  validarTelefonoArgentino: (telefono: string): { esValido: boolean; mensaje?: string } => {
    if (!telefono) {
      return { esValido: true }; // Campo opcional
    }

    const regex = /^\+54\s9\s\d{2}\s\d{4}-\d{4}$/;
    
    if (!regex.test(telefono)) {
      return {
        esValido: false,
        mensaje: 'El teléfono debe tener el formato: +54 9 xx xxxx-xxxx'
      };
    }

    return { esValido: true };
  },

  /**
   * Formatea un número de teléfono al formato argentino
   */
  formatearTelefonoArgentino: (telefono: string): string => {
    // Remover todo excepto números
    const numeros = telefono.replace(/\D/g, '');
    
    // Si tiene 10 dígitos, asumir que es sin código de país
    if (numeros.length === 10) {
      const area = numeros.slice(0, 2);
      const primera = numeros.slice(2, 6);
      const segunda = numeros.slice(6, 10);
      return `+54 9 ${area} ${primera}-${segunda}`;
    }
    
    // Si tiene 12 dígitos y empieza con 54
    if (numeros.length === 12 && numeros.startsWith('54')) {
      const codigo = numeros.slice(0, 2); // 54
      const nueve = numeros.slice(2, 3); // 9
      const area = numeros.slice(3, 5); // xx
      const primera = numeros.slice(5, 9); // xxxx
      const segunda = numeros.slice(9, 13); // xxxx
      
      return `+${codigo} ${nueve} ${area} ${primera}-${segunda}`;
    }
    
    // Si tiene 13 dígitos y empieza con 549
    if (numeros.length === 13 && numeros.startsWith('549')) {
      const codigo = numeros.slice(0, 2); // 54
      const nueve = numeros.slice(2, 3); // 9
      const area = numeros.slice(3, 5); // xx
      const primera = numeros.slice(5, 9); // xxxx
      const segunda = numeros.slice(9, 13); // xxxx
      
      return `+${codigo} ${nueve} ${area} ${primera}-${segunda}`;
    }
    
    return telefono; // Devolver sin cambios si no coincide con ningún patrón
  },

  /**
   * Valida todos los campos del perfil
   */
  validarPerfil: (perfil: Partial<PerfilUsuario>): Record<string, string> => {
    const errores: Record<string, string> = {};

    // Validar nombre
    if (perfil.nombre !== undefined && !perfil.nombre.trim()) {
      errores.nombre = 'El nombre es obligatorio';
    }

    // Validar apellido
    if (perfil.apellido !== undefined && !perfil.apellido.trim()) {
      errores.apellido = 'El apellido es obligatorio';
    }

    // Validar email
    if (perfil.email !== undefined) {
      if (!perfil.email.trim()) {
        errores.email = 'El email es obligatorio';
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(perfil.email)) {
          errores.email = 'El formato del email no es válido';
        }
      }
    }

    // Validar teléfono
    if (perfil.telefonoMovil !== undefined && perfil.telefonoMovil) {
      const validacionTelefono = perfilService.validarTelefonoArgentino(perfil.telefonoMovil);
      if (!validacionTelefono.esValido) {
        errores.telefonoMovil = validacionTelefono.mensaje || 'Teléfono inválido';
      }
    }

    return errores;
  }
};

export default perfilService;