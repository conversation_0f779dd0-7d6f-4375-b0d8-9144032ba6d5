import axiosClient from '../api/axiosClient';

/**
 * Servicio para registrar actividades del sistema relacionadas con la sesión
 */
export class ActivityLogger {
  
  /**
   * Registra el logout por inactividad en el backend
   */
  static async logInactivityLogout(): Promise<void> {
    try {
      // Intentar registrar la actividad antes de limpiar la sesión
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'LOGOUT_INACTIVIDAD',
        detalles: 'Sesión cerrada automáticamente por inactividad de 30 minutos'
      });
      
      console.log('📝 Actividad de logout por inactividad registrada');
    } catch (error) {
      // No fallar si no se puede registrar la actividad
      console.warn('⚠️ No se pudo registrar la actividad de logout por inactividad:', error);
    }
  }

  /**
   * Registra cuando el usuario extiende su sesión
   */
  static async logSessionExtended(): Promise<void> {
    try {
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'SESION_EXTENDIDA',
        detalles: 'Usuario extendió su sesión respondiendo a la advertencia de inactividad'
      });
      
      console.log('📝 Extensión de sesión registrada');
    } catch (error) {
      console.warn('⚠️ No se pudo registrar la extensión de sesión:', error);
    }
  }

  /**
   * Registra cuando se refresca un token automáticamente
   */
  static async logTokenRefresh(): Promise<void> {
    try {
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'TOKEN_REFRESH',
        detalles: 'Token JWT refrescado automáticamente'
      });
      
      console.log('📝 Refresh de token registrado');
    } catch (error) {
      console.warn('⚠️ No se pudo registrar el refresh de token:', error);
    }
  }

  /**
   * Registra cuando falla el refresh de un token
   */
  static async logTokenRefreshFailed(reason: string): Promise<void> {
    try {
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'TOKEN_REFRESH_FAILED',
        detalles: `Falló el refresh de token: ${reason}`
      });
      
      console.log('📝 Fallo de refresh de token registrado');
    } catch (error) {
      console.warn('⚠️ No se pudo registrar el fallo de refresh de token:', error);
    }
  }

  /**
   * Registra cuando se detecta actividad sospechosa
   */
  static async logSuspiciousActivity(details: string): Promise<void> {
    try {
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'ACTIVIDAD_SOSPECHOSA',
        detalles: details
      });
      
      console.log('📝 Actividad sospechosa registrada');
    } catch (error) {
      console.warn('⚠️ No se pudo registrar la actividad sospechosa:', error);
    }
  }

  /**
   * Registra el inicio de sesión exitoso
   */
  static async logSuccessfulLogin(): Promise<void> {
    try {
      await axiosClient.post('/actividad-sistema', {
        tipoAccion: 'LOGIN_EXITOSO',
        detalles: 'Inicio de sesión exitoso con sistema de inactividad activado'
      });
      
      console.log('📝 Login exitoso registrado');
    } catch (error) {
      console.warn('⚠️ No se pudo registrar el login exitoso:', error);
    }
  }
}

export default ActivityLogger;