import { SESSION_CONFIG, ACTIVITY_EVENTS } from '../config/session.config';

/**
 * Servicio para gestionar la inactividad del usuario y el cierre automático de sesión
 */
export class InactivityService {
  private timeoutId: NodeJS.Timeout | null = null;
  private warningTimeoutId: NodeJS.Timeout | null = null;
  private isActive: boolean = true;
  private lastActivity: number = Date.now();
  
  private onLogout: () => void;
  private onWarning: () => void;
  private onActivity?: () => void;

  constructor(
    onLogout: () => void, 
    onWarning: () => void,
    onActivity?: () => void
  ) {
    this.onLogout = onLogout;
    this.onWarning = onWarning;
    this.onActivity = onActivity;
    this.setupActivityListeners();
    this.startTimer();
  }

  /**
   * Configura los listeners para detectar actividad del usuario
   */
  private setupActivityListeners(): void {
    ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, this.handleActivity, true);
    });

    // También detectar cambios de visibilidad de la página
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // Detectar cambios de foco en la ventana
    window.addEventListener('focus', this.handleActivity);
    window.addEventListener('blur', this.handleActivity);
  }

  /**
   * Maneja los eventos de actividad del usuario
   */
  private handleActivity = (): void => {
    if (!this.isActive) return;
    
    this.lastActivity = Date.now();
    this.resetTimer();
    
    // Notificar actividad si hay callback
    if (this.onActivity) {
      this.onActivity();
    }
  };

  /**
   * Maneja cambios de visibilidad de la página
   */
  private handleVisibilityChange = (): void => {
    if (document.visibilityState === 'visible') {
      // Verificar si ha pasado mucho tiempo desde la última actividad
      const timeSinceLastActivity = Date.now() - this.lastActivity;
      
      if (timeSinceLastActivity >= SESSION_CONFIG.INACTIVITY_TIMEOUT) {
        // Si ha pasado el tiempo límite, cerrar sesión inmediatamente
        this.forceLogout();
      } else {
        // Si no, resetear el timer
        this.handleActivity();
      }
    }
  };

  /**
   * Resetea los timers de inactividad
   */
  public resetTimer(): void {
    this.clearTimers();
    if (this.isActive) {
      this.startTimer();
    }
  }

  /**
   * Inicia los timers de advertencia y logout
   */
  private startTimer(): void {
    // Timer para mostrar advertencia
    this.warningTimeoutId = setTimeout(() => {
      if (this.isActive) {
        this.onWarning();
      }
    }, SESSION_CONFIG.WARNING_TIME);

    // Timer para logout automático
    this.timeoutId = setTimeout(() => {
      if (this.isActive) {
        this.forceLogout();
      }
    }, SESSION_CONFIG.INACTIVITY_TIMEOUT);
  }

  /**
   * Limpia todos los timers activos
   */
  private clearTimers(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
      this.warningTimeoutId = null;
    }
  }

  /**
   * Fuerza el cierre de sesión
   */
  private forceLogout(): void {
    this.clearTimers();
    this.onLogout();
  }

  /**
   * Extiende la sesión (llamado cuando el usuario responde a la advertencia)
   */
  public extendSession(): void {
    this.lastActivity = Date.now();
    this.resetTimer();
  }

  /**
   * Pausa el servicio de inactividad
   */
  public pause(): void {
    this.isActive = false;
    this.clearTimers();
  }

  /**
   * Reanuda el servicio de inactividad
   */
  public resume(): void {
    this.isActive = true;
    this.lastActivity = Date.now();
    this.resetTimer();
  }

  /**
   * Obtiene el tiempo transcurrido desde la última actividad
   */
  public getTimeSinceLastActivity(): number {
    return Date.now() - this.lastActivity;
  }

  /**
   * Verifica si el servicio está activo
   */
  public getIsActive(): boolean {
    return this.isActive;
  }

  /**
   * Destruye el servicio y limpia todos los listeners
   */
  public destroy(): void {
    this.isActive = false;
    this.clearTimers();
    
    // Remover listeners de actividad
    ACTIVITY_EVENTS.forEach(event => {
      document.removeEventListener(event, this.handleActivity, true);
    });
    
    // Remover otros listeners
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('focus', this.handleActivity);
    window.removeEventListener('blur', this.handleActivity);
  }
}

export default InactivityService;