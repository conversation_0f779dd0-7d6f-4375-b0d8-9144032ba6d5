<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de URLs de Documentos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .test-button {
            margin: 5px;
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 3px solid #4CAF50;
        }
    </style>
</head>
<body>
    <h1>Prueba de URLs de Documentos</h1>
    <p>Esta página te permite probar diferentes formatos de URL para acceder a documentos.</p>

    <div class="test-container">
        <h2>Ingresa el ID del documento</h2>
        <input type="number" id="documentId" value="84" placeholder="ID del documento">
        
        <h2>Prueba las siguientes URLs:</h2>
        
        <div>
            <button class="test-button" onclick="testUrl('/api/archivos/documentos/')">Probar con /api/archivos/documentos/</button>
            <div id="result1" class="result"></div>
        </div>
        
        <div>
            <button class="test-button" onclick="testUrl('/archivos/documentos/')">Probar con /archivos/documentos/</button>
            <div id="result2" class="result"></div>
        </div>
        
        <div>
            <button class="test-button" onclick="testUrl('http://localhost:8080/api/archivos/documentos/')">Probar con http://localhost:8080/api/archivos/documentos/</button>
            <div id="result3" class="result"></div>
        </div>
        
        <div>
            <button class="test-button" onclick="testUrl('http://localhost:8080/archivos/documentos/')">Probar con http://localhost:8080/archivos/documentos/</button>
            <div id="result4" class="result"></div>
        </div>
    </div>

    <script>
        function testUrl(baseUrl) {
            const documentId = document.getElementById('documentId').value;
            if (!documentId) {
                alert('Por favor ingresa un ID de documento');
                return;
            }
            
            const url = baseUrl + documentId;
            const resultId = baseUrl === '/api/archivos/documentos/' ? 'result1' : 
                            baseUrl === '/archivos/documentos/' ? 'result2' : 
                            baseUrl === 'http://localhost:8080/api/archivos/documentos/' ? 'result3' : 'result4';
            
            document.getElementById(resultId).innerHTML = `Probando URL: ${url}...`;
            
            // Intentar abrir la URL en una nueva pestaña
            const newWindow = window.open(url, '_blank');
            
            // Verificar si la ventana se abrió correctamente
            if (newWindow) {
                document.getElementById(resultId).innerHTML += '<br>Se abrió una nueva pestaña con la URL.';
            } else {
                document.getElementById(resultId).innerHTML += '<br>No se pudo abrir la URL (posiblemente bloqueado por el navegador).';
            }
        }
    </script>
</body>
</html>
