import { saveAs } from 'file-saver';
import { Usuario, Rol } from '../types/usuario.types';

const rolLabels: Record<Rol, string> = {
  [Rol.SUPERUSUARIO]: 'Superusuario',
  [Rol.ADMINISTRADOR]: 'Administrador',
  [Rol.USUARIOCARGA]: 'Usuario Carga',
  [Rol.USUARIOCONSULTA]: 'Usuario Consulta',
};

export const exportUsersToCSV = (users: Usuario[], filename?: string) => {
  const headers = [
    'ID',
    'Nombre',
    'Apellido',
    'Email',
    'Usuario',
    'Rol',
    'Dependencia'
  ];

  const csvContent = [
    headers.join(','),
    ...users.map(user => [
      user.id || '',
      `"${user.nombre}"`,
      `"${user.apellido}"`,
      `"${user.email}"`,
      `"${user.username}"`,
      `"${rolLabels[user.rol] || user.rol}"`,
      `"${user.dependencia || ''}"`
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const defaultFilename = `usuarios_${new Date().toISOString().split('T')[0]}.csv`;
  saveAs(blob, filename || defaultFilename);
};

export const exportUsersToJSON = (users: Usuario[], filename?: string) => {
  const exportData = users.map(user => ({
    id: user.id,
    nombre: user.nombre,
    apellido: user.apellido,
    email: user.email,
    username: user.username,
    rol: user.rol,
    rolLabel: rolLabels[user.rol] || user.rol,
    dependencia: user.dependencia || '',
    exportDate: new Date().toISOString()
  }));

  const jsonContent = JSON.stringify(exportData, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  const defaultFilename = `usuarios_${new Date().toISOString().split('T')[0]}.json`;
  saveAs(blob, filename || defaultFilename);
};

export const generateUserReport = (users: Usuario[]) => {
  const totalUsers = users.length;
  const roleDistribution = users.reduce((acc, user) => {
    acc[user.rol] = (acc[user.rol] || 0) + 1;
    return acc;
  }, {} as Record<Rol, number>);

  const dependenciaDistribution = users.reduce((acc, user) => {
    const dep = user.dependencia || 'Sin dependencia';
    acc[dep] = (acc[dep] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const report = {
    resumen: {
      totalUsuarios: totalUsers,
      fechaReporte: new Date().toISOString(),
    },
    distribucionPorRol: Object.entries(roleDistribution).map(([rol, cantidad]) => ({
      rol: rolLabels[rol as Rol] || rol,
      cantidad,
      porcentaje: ((cantidad / totalUsers) * 100).toFixed(1)
    })),
    distribucionPorDependencia: Object.entries(dependenciaDistribution).map(([dependencia, cantidad]) => ({
      dependencia,
      cantidad,
      porcentaje: ((cantidad / totalUsers) * 100).toFixed(1)
    })),
    usuarios: users.map(user => ({
      nombre: user.nombre,
      apellido: user.apellido,
      email: user.email,
      rol: rolLabels[user.rol] || user.rol,
      dependencia: user.dependencia || 'Sin dependencia'
    }))
  };

  const jsonContent = JSON.stringify(report, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  const filename = `reporte_usuarios_${new Date().toISOString().split('T')[0]}.json`;
  saveAs(blob, filename);

  return report;
};