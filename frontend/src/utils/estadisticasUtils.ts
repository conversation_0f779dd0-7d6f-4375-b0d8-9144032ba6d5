// Colores para las fuerzas (mismo esquema que EstadisticasPage)
export const COLORS_FUERZA = {
  'PFA': '#1976d2', // Azul
  'GNA': '#388e3c', // Verde
  'PSA': '#222', // Negro
  'PNA': '#bfa16c', // <PERSON><PERSON><PERSON> clarito
  'SPF': '#4fc3f7', // Celeste
  'INTERPOOL': '#8e24aa', // <PERSON>a
  'CUFRE': '#ffd600',  // Amarillo
  'SIN DATOS': '#424242' // Gris oscuro
};

// Colores para los estados
export const COLORS_ESTADO = {
  'DETENIDO': '#388e3c', // Verde
  'CAPTURA VIGENTE': '#d32f2f', // Rojo
  'SIN EFECTO': '#757575', // Gris
  'SIN DATOS': '#424242' // Gris oscuro
};

// Colores por defecto para opciones no mapeadas
export const DEFAULT_COLORS = [
  '#3f51b5', '#f44336', '#4caf50', '#ff9800', '#9c27b0', 
  '#009688', '#795548', '#607d8b', '#e91e63', '#673ab7'
];

// Tipos de datos
export interface ExpedienteData {
  name: string;
  value: number;
  color?: string;
}

// Función helper para normalizar nombres
const normalizeName = (name: string | null | undefined): string => {
  if (!name) return 'SIN DATOS';
  const upperName = name.toUpperCase().trim();
  // Normalizar variaciones de "sin datos"
  if (upperName === 'SIN DATO' || upperName === 'S/D' || upperName === 'SIN DATOS' || upperName === 'Sin datos') {
    return 'SIN DATOS';
  }
  return name;
};

// Función para normalizar datos
export const normalizarDatos = (datos: any[], tipoMapa: 'fuerza' | 'estado'): ExpedienteData[] => {
  if (!datos || !Array.isArray(datos) || datos.length === 0) {
    return [];
  }

  // Determinar el mapa de colores y campos a usar
  const colorMap = tipoMapa === 'fuerza' ? COLORS_FUERZA : COLORS_ESTADO;

  // Paso 1: Mapear y normalizar los datos
  const normalizedData = datos.map((item: any, index: number) => {
    // Extraer nombre según diferentes posibles estructuras de datos del backend
    let rawName = '';
    if (tipoMapa === 'fuerza') {
      rawName = item.name || item.fuerza || item.fuerzaAsignada;
    } else {
      rawName = item.name || item.estado || item.estadoSituacion;
    }

    // Normalizar el nombre
    const name = normalizeName(rawName);

    // Extraer valor numérico
    const value = parseInt(item.value || item.cantidad || '0', 10);

    // Asignar color según el mapa o usar color por defecto
    let color = DEFAULT_COLORS[index % DEFAULT_COLORS.length];

    // Intentar acceder a colorMap de manera segura
    if (tipoMapa === 'fuerza' && name in COLORS_FUERZA) {
      color = COLORS_FUERZA[name as keyof typeof COLORS_FUERZA];
    } else if (tipoMapa === 'estado' && name in COLORS_ESTADO) {
      color = COLORS_ESTADO[name as keyof typeof COLORS_ESTADO];
    }

    return { name, value, color };
  });
  
  // Paso 2: Agrupar por nombre para evitar duplicados
  const groupedData: Record<string, ExpedienteData> = {};
  
  normalizedData.forEach(item => {
    if (item.value <= 0) return; // Ignorar items con valor cero
    
    if (groupedData[item.name]) {
      // Si ya existe este nombre, sumar el valor
      groupedData[item.name].value += item.value;
    } else {
      // Si es la primera vez que vemos este nombre
      groupedData[item.name] = { ...item };
    }
  });
  
  // Paso 3: Convertir el objeto agrupado de vuelta a un array
  return Object.values(groupedData);
};

// Función para obtener el total de un conjunto de datos
export const getPieTotal = (data: ExpedienteData[]) => {
  return data.reduce((sum, item) => sum + item.value, 0);
};

// Datos de ejemplo para cuando no hay datos reales
export const getExampleFuerzaData = (): ExpedienteData[] => [
  { name: 'PFA', value: 0, color: COLORS_FUERZA['PFA'] },
  { name: 'GNA', value: 0, color: COLORS_FUERZA['GNA'] },
  { name: 'PNA', value: 0, color: COLORS_FUERZA['PNA'] },
  { name: 'PSA', value: 0, color: COLORS_FUERZA['PSA'] },
  { name: 'SPF', value: 0, color: COLORS_FUERZA['SPF'] },
  { name: 'INTERPOOL', value: 0, color: COLORS_FUERZA['INTERPOOL'] },
  { name: 'CUFRE', value: 0, color: COLORS_FUERZA['CUFRE'] },
  { name: 'SIN DATOS', value: 0, color: COLORS_FUERZA['SIN DATOS'] }
];

export const getExampleEstadoData = (): ExpedienteData[] => [
  { name: 'CAPTURA VIGENTE', value: 0, color: COLORS_ESTADO['CAPTURA VIGENTE'] },
  { name: 'DETENIDO', value: 0, color: COLORS_ESTADO['DETENIDO'] },
  { name: 'SIN EFECTO', value: 0, color: COLORS_ESTADO['SIN EFECTO'] },
  { name: 'SIN DATOS', value: 0, color: COLORS_ESTADO['SIN DATOS'] }
];