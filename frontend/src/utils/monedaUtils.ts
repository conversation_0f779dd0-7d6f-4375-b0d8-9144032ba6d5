export interface Moneda {
  codigo: string;
  simbolo: string;
  nombre: string;
}

export const MONEDAS: Moneda[] = [
  { codigo: 'ARS', simbolo: '$', nombre: 'Pesos Argentinos' },
  { codigo: 'USD', simbolo: 'USD', nombre: '<PERSON><PERSON>lares Estadouniden<PERSON>' },
  { codigo: 'EUR', simbolo: '€', nombre: 'Euros' },
  { codigo: 'BRL', simbolo: 'R$', nombre: 'Real Brasileño' },
  { codigo: 'CLP', simbolo: '$', nombre: 'Peso Chileno' },
  { codigo: 'UYU', simbolo: '$U', nombre: 'Peso Uruguayo' },
  { codigo: 'BOB', simbolo: 'Bs', nombre: 'Boliviano' },
  { codigo: 'PYG', simbolo: '₲', nombre: 'Guaraní Paraguayo' }
];

/**
 * Formatea el monto de recompensa con el símbolo de moneda correspondiente
 * Ejemplo: formatearRecompensa("500000", "ARS") => "$500.000"
 */
export const formatearRecompensa = (monto: string, moneda: string = 'ARS'): string => {
  if (!monto) return '';
  
  const monedaConfig = MONEDAS.find(m => m.codigo === moneda);
  const simbolo = monedaConfig?.simbolo || '$';
  
  // Formatear número con separadores de miles
  const montoNumerico = Number(monto.replace(/[^\d]/g, ''));
  const montoFormateado = montoNumerico.toLocaleString('es-AR');
  
  return `${simbolo}${montoFormateado}`;
};

/**
 * Extrae el monto numérico de un string formateado
 */
export const extraerMonto = (montoFormateado: string): string => {
  return montoFormateado.replace(/[^\d]/g, '');
};

/**
 * Obtiene la moneda por código
 */
export const obtenerMonedaPorCodigo = (codigo: string): Moneda | undefined => {
  return MONEDAS.find(m => m.codigo === codigo);
};

export {};