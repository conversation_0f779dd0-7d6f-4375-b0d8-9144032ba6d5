/**
 * Utilidad para manejar URLs de API en diferentes entornos
 * Soluciona el problema de rutas relativas vs absolutas para desarrollo y producción
 */

/**
 * Devuelve la URL correcta para acceder a recursos de la API según el entorno
 * Siempre usa rutas relativas para que pase por el proxy NGINX
 */
export const getApiUrl = (path: string): string => {
  // Asegurarse de que el path comience con /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // Usar rutas relativas que pasan por el proxy NGINX
  return normalizedPath;
};

/**
 * Devuelve la URL correcta para descargar un documento
 */
export const getDocumentoUrl = (id: number): string => {
  // Comprobar si estamos en el entorno de producción (servidor)
  const isProduction = window.location.hostname === '************' || 
                     !window.location.hostname.includes('localhost');
  
  if (isProduction) {
    // En producción, usar una URL relativa que pase por el proxy de Nginx
    return `/api/archivos/documentos/${id}`;
  } else {
    // En desarrollo local, usar la URL absoluta para el backend directo
    return `http://localhost:8080/api/archivos/documentos/${id}`;
  }
};

/**
 * Devuelve la URL correcta para descargar una fotografía
 */
export const getFotografiaUrl = (id: number): string => {
  return getApiUrl(`/archivos/fotografias/${id}`);
};

/**
 * Devuelve la URL correcta para una ruta de archivo de fotografía
 */
export const getFotografiaRutaUrl = (ruta: string): string => {
  if (!ruta) return '';
  if (ruta.startsWith('http')) return ruta;
  
  // Comprobar si estamos en el entorno de producción (servidor)
  // En producción, NO necesitamos añadir /api porque Nginx ya lo hace
  const isProduction = window.location.hostname === '************' || 
                       !window.location.hostname.includes('localhost');

  // En desarrollo, añadimos /api para uploads
  if (ruta.startsWith('/uploads/')) {
    return isProduction ? ruta : `/api${ruta}`;
  }
  
  // En desarrollo, añadimos /api para archivos
  if (ruta.startsWith('/archivos/')) {
    return isProduction ? ruta : `/api${ruta}`;
  }
  
  // Usar rutas relativas que pasan por el proxy NGINX
  return ruta.startsWith('/') ? ruta : `/${ruta}`;
};
