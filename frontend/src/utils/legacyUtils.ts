import { ActividadSistema } from '../types/actividad.types';

/**
 * Detecta si un registro de actividad es legacy (anterior a la implementación de auditoría completa)
 * Un registro se considera legacy si no tiene los campos de auditoría modernos
 */
export const isLegacyRecord = (actividad: ActividadSistema): boolean => {
  // Un registro se considera legacy si carece de la mayoría de campos de auditoría modernos
  // Contamos cuántos campos de auditoría están presentes
  const auditFields = [
    actividad.ipCliente,
    actividad.userAgent,
    actividad.endpoint,
    actividad.sessionId
  ];
  
  const presentFields = auditFields.filter(field => field && field.trim() !== '').length;
  
  // Si tiene menos de 2 campos de auditoría, se considera legacy
  // Esto permite cierta flexibilidad para registros parcialmente capturados
  return presentFields < 2;
};

/**
 * Formatea campos legacy con mensajes descriptivos en lugar de "N/A"
 */
export const formatLegacyField = (value: any, fieldType: string): string => {
  if (value) return value;
  
  switch (fieldType) {
    case 'ipCliente':
      return 'No registrada (registro legacy)';
    case 'userAgent':
      return 'Información no disponible';
    case 'endpoint':
      return 'Endpoint no capturado';
    case 'duracion':
      return 'Tiempo no medido';
    case 'sessionId':
      return 'Sesión no rastreada';
    case 'metodoHttp':
      return 'Método no registrado';
    case 'modulo':
      return 'Módulo no especificado';
    case 'categoriaAccion':
      return 'Categoría no definida';
    case 'estadoRespuesta':
      return 'Estado no capturado';
    default:
      return 'No disponible';
  }
};

/**
 * Genera tooltips explicativos para campos legacy
 */
export const getLegacyTooltip = (fieldType: string): string => {
  const tooltips: Record<string, string> = {
    ipCliente: 'Este registro fue creado antes de implementar el seguimiento de IP del cliente',
    userAgent: 'La información del navegador no se capturaba en registros anteriores al sistema de auditoría',
    endpoint: 'El endpoint de la petición no se registraba en el sistema legacy',
    sessionId: 'El ID de sesión no se rastreaba en versiones anteriores del sistema',
    duracion: 'El tiempo de respuesta no se medía en registros legacy',
    metodoHttp: 'El método HTTP no se capturaba en el sistema anterior',
    modulo: 'El módulo no se especificaba en registros antiguos',
    categoriaAccion: 'La categorización de acciones se implementó posteriormente',
    estadoRespuesta: 'El estado de respuesta no se registraba en versiones anteriores'
  };
  
  return tooltips[fieldType] || 'Campo no disponible en registros legacy debido a limitaciones del sistema anterior';
};

/**
 * Obtiene un mensaje explicativo general para registros legacy
 */
export const getLegacyExplanation = (): string => {
  return 'Los registros legacy corresponden a actividades anteriores a la implementación del sistema de auditoría completo. ' +
         'Algunos campos técnicos como IP, User Agent, endpoint y duración no están disponibles para estos registros.';
};

/**
 * Determina si se debe mostrar información de legacy en la interfaz
 */
export const shouldShowLegacyInfo = (actividades: ActividadSistema[]): boolean => {
  return actividades.some(actividad => isLegacyRecord(actividad));
};

/**
 * Cuenta la cantidad de registros legacy en una lista
 */
export const countLegacyRecords = (actividades: ActividadSistema[]): number => {
  return actividades.filter(actividad => isLegacyRecord(actividad)).length;
};

/**
 * Separa registros en legacy y modernos
 */
export const separateRecordsByType = (actividades: ActividadSistema[]) => {
  const legacy: ActividadSistema[] = [];
  const modern: ActividadSistema[] = [];
  
  actividades.forEach(actividad => {
    if (isLegacyRecord(actividad)) {
      legacy.push(actividad);
    } else {
      modern.push(actividad);
    }
  });
  
  return { legacy, modern };
};