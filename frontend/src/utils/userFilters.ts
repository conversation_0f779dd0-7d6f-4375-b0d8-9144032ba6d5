import { Usuario, Rol } from '../types/usuario.types';

export interface FilterOptions {
  searchTerm: string;
  selectedRoles: Rol[];
  selectedDependencia: string;
}

export const filterUsers = (users: Usuario[], filters: FilterOptions): Usuario[] => {
  return users.filter(usuario => {
    // Filtro de búsqueda por texto
    if (filters.searchTerm) {
      const searchTermLower = filters.searchTerm.toLowerCase();
      const matchesSearch = 
        usuario.nombre.toLowerCase().includes(searchTermLower) ||
        usuario.apellido.toLowerCase().includes(searchTermLower) ||
        usuario.email.toLowerCase().includes(searchTermLower) ||
        usuario.username.toLowerCase().includes(searchTermLower) ||
        usuario.rol.toLowerCase().includes(searchTermLower) ||
        (usuario.dependencia && usuario.dependencia.toLowerCase().includes(searchTermLower));
      
      if (!matchesSearch) return false;
    }

    // Filtro por roles
    if (filters.selectedRoles.length > 0) {
      if (!filters.selectedRoles.includes(usuario.rol)) {
        return false;
      }
    }

    // Filtro por dependencia
    if (filters.selectedDependencia) {
      if (usuario.dependencia !== filters.selectedDependencia) {
        return false;
      }
    }

    return true;
  });
};

export const sortUsers = (users: Usuario[], sortBy: string, sortOrder: 'asc' | 'desc' = 'asc'): Usuario[] => {
  return [...users].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortBy) {
      case 'nombre':
        aValue = a.nombre.toLowerCase();
        bValue = b.nombre.toLowerCase();
        break;
      case 'apellido':
        aValue = a.apellido.toLowerCase();
        bValue = b.apellido.toLowerCase();
        break;
      case 'email':
        aValue = a.email.toLowerCase();
        bValue = b.email.toLowerCase();
        break;
      case 'rol':
        aValue = a.rol;
        bValue = b.rol;
        break;
      case 'dependencia':
        aValue = (a.dependencia || '').toLowerCase();
        bValue = (b.dependencia || '').toLowerCase();
        break;
      default:
        return 0;
    }

    if (aValue < bValue) {
      return sortOrder === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOrder === 'asc' ? 1 : -1;
    }
    return 0;
  });
};

export const getUniqueValues = (users: Usuario[], field: keyof Usuario): string[] => {
  const values = users
    .map(user => user[field])
    .filter((value): value is string => typeof value === 'string' && value.trim() !== '')
    .map(value => value.trim());
  
  return Array.from(new Set(values)).sort();
};

export const getUserStats = (users: Usuario[]) => {
  const total = users.length;
  
  if (total === 0) {
    return {
      total: 0,
      roleStats: {} as Record<Rol, number>,
      dependenciaStats: {} as Record<string, number>,
      mostCommonRole: null,
      mostCommonDependencia: null
    };
  }
  
  const roleStats = users.reduce((acc, user) => {
    acc[user.rol] = (acc[user.rol] || 0) + 1;
    return acc;
  }, {} as Record<Rol, number>);

  const dependenciaStats = users.reduce((acc, user) => {
    const dep = user.dependencia || 'Sin dependencia';
    acc[dep] = (acc[dep] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const roleEntries = Object.entries(roleStats);
  const dependenciaEntries = Object.entries(dependenciaStats);

  return {
    total,
    roleStats,
    dependenciaStats,
    mostCommonRole: roleEntries.length > 0
      ? roleEntries.reduce((a, b) =>
          roleStats[a[0] as Rol] > roleStats[b[0] as Rol] ? a : b
        )[0] as Rol
      : null,
    mostCommonDependencia: dependenciaEntries.length > 0
      ? dependenciaEntries.reduce((a, b) =>
          dependenciaStats[a[0]] > dependenciaStats[b[0]] ? a : b
        )[0]
      : null
  };
};

export const saveFiltersToStorage = (filters: FilterOptions) => {
  try {
    localStorage.setItem('userFilters', JSON.stringify(filters));
  } catch (error) {
    console.warn('No se pudieron guardar los filtros en localStorage:', error);
  }
};

export const loadFiltersFromStorage = (): Partial<FilterOptions> => {
  try {
    const saved = localStorage.getItem('userFilters');
    return saved ? JSON.parse(saved) : {};
  } catch (error) {
    console.warn('No se pudieron cargar los filtros desde localStorage:', error);
    return {};
  }
};

export const clearFiltersFromStorage = () => {
  try {
    localStorage.removeItem('userFilters');
  } catch (error) {
    console.warn('No se pudieron limpiar los filtros de localStorage:', error);
  }
};

// Filtros predefinidos
export const PREDEFINED_FILTERS = {
  TODOS: {
    searchTerm: '',
    selectedRoles: [],
    selectedDependencia: ''
  },
  SUPERUSUARIOS: {
    searchTerm: '',
    selectedRoles: [Rol.SUPERUSUARIO],
    selectedDependencia: ''
  },
  ADMINISTRADORES: {
    searchTerm: '',
    selectedRoles: [Rol.ADMINISTRADOR],
    selectedDependencia: ''
  },
  USUARIOS_OPERATIVOS: {
    searchTerm: '',
    selectedRoles: [Rol.USUARIOCARGA, Rol.USUARIOCONSULTA],
    selectedDependencia: ''
  }
};