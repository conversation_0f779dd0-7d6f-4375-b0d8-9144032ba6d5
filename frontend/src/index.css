/* Estilos globales de la aplicación */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Estilos para la navegación */
.active-link {
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.08);
}

/* Estilos personalizados para tablas */
.table-container {
  overflow-x: auto;
}

/* Ajustes de impresión */
@media print {
  .no-print {
    display: none !important;
  }
}

body.app-fullscreen-mode .MuiAppBar-root,
body.app-fullscreen-mode footer {
  display: none !important;
} 