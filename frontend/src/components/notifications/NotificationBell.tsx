import React, { useState } from 'react';
import {
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  CircularProgress,
  ListItemText
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { useNotifications } from '../../hooks/useNotifications';
import { useNavigate } from 'react-router-dom';
import { ActividadSistema } from '../../types/actividad.types';

const NotificationBell: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { notifications, count, loading, markAsViewed } = useNotifications();
  const navigate = useNavigate();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = (actividadId: number) => {
    handleClose();
    navigate(`/actividad-sistema/${actividadId}`);
  };

  const handleViewAll = async () => {
    await markAsViewed();
    handleClose();
    navigate('/actividad-sistema');
  };

  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'hace unos segundos';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `hace ${minutes} minuto${minutes > 1 ? 's' : ''}`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `hace ${days} día${days > 1 ? 's' : ''}`;
    }
  };

  const formatNotificationText = (actividad: ActividadSistema) => {
    const timeAgo = formatTimeAgo(actividad.fechaHora);
    return `${actividad.usuario} realizó ${actividad.tipoAccion} - ${timeAgo}`;
  };

  const getActionDescription = (tipoAccion: string) => {
    const actionMap: { [key: string]: string } = {
      'CREAR_EXPEDIENTE': 'creó un expediente',
      'EDITAR_EXPEDIENTE': 'editó un expediente',
      'ELIMINAR_EXPEDIENTE': 'eliminó un expediente',
      'AGREGAR_PERSONA': 'agregó una persona',
      'EDITAR_PERSONA': 'editó una persona',
      'AGREGAR_DELITO': 'agregó un delito',
      'EDITAR_DELITO': 'editó un delito',
      'SUBIR_FOTOGRAFIA': 'subió una fotografía',
      'ELIMINAR_FOTOGRAFIA': 'eliminó una fotografía'
    };
    return actionMap[tipoAccion] || tipoAccion.toLowerCase().replace('_', ' ');
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        aria-label="notificaciones"
        sx={{ 
          '&:hover': { 
            backgroundColor: 'rgba(255, 255, 255, 0.1)' 
          }
        }}
      >
        <Badge 
          badgeContent={count} 
          color="error"
          sx={{
            '& .MuiBadge-badge': {
              fontSize: '0.75rem',
              minWidth: '18px',
              height: '18px'
            }
          }}
        >
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: { 
            width: 420, 
            maxHeight: 500,
            mt: 1,
            '& .MuiMenuItem-root': {
              whiteSpace: 'normal',
              alignItems: 'flex-start',
              py: 1.5
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Notificaciones {count > 0 && `(${count})`}
          </Typography>
        </Box>
        
        <Divider />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : notifications.length === 0 ? (
          <MenuItem disabled>
            <ListItemText
              primary="No hay notificaciones nuevas"
              primaryTypographyProps={{
                color: 'textSecondary',
                textAlign: 'center'
              }}
            />
          </MenuItem>
        ) : (
          notifications.map((notification) => (
            <MenuItem
              key={notification.id}
              onClick={() => handleNotificationClick(notification.id)}
              sx={{ 
                borderLeft: '3px solid',
                borderLeftColor: 'primary.main',
                '&:hover': { 
                  backgroundColor: 'action.hover',
                  borderLeftColor: 'primary.dark'
                }
              }}
            >
              <ListItemText
                primary={
                  <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                    {notification.usuario} {getActionDescription(notification.tipoAccion)}
                  </Typography>
                }
                secondary={
                  <Box>
                    <Typography variant="caption" color="textSecondary" display="block">
                      {formatTimeAgo(notification.fechaHora)}
                    </Typography>
                    {notification.detalles && (
                      <Typography 
                        variant="caption" 
                        color="textSecondary" 
                        sx={{ 
                          display: 'block',
                          mt: 0.5,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: '300px'
                        }}
                      >
                        {notification.detalles}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </MenuItem>
          ))
        )}

        {notifications.length > 0 && (
          <>
            <Divider />
            <Box sx={{ p: 1 }}>
              <Button
                fullWidth
                variant="text"
                onClick={handleViewAll}
                sx={{ 
                  textTransform: 'none',
                  fontWeight: 500,
                  color: 'primary.main'
                }}
              >
                Ver todas las actividades
              </Button>
            </Box>
          </>
        )}
      </Menu>
    </>
  );
};

export default NotificationBell;