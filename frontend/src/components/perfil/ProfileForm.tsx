import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  Tooltip,
  IconButton
} from '@mui/material';
import { Save, Phone, Email, Person, Business, Info } from '@mui/icons-material';
import { PerfilUsuario, REGEX_TELEFONO_ARGENTINO } from '../../types/perfil.types';

interface ProfileFormProps {
  perfil: PerfilUsuario;
  onGuardar: (perfilActualizado: PerfilUsuario) => void;
  loading?: boolean;
  error?: string;
  success?: string;
  esEdicionCompleta?: boolean; // true para SUPERUSUARIO, false para usuario normal
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  perfil,
  onGuardar,
  loading = false,
  error,
  success,
  esEdicionCompleta = false
}) => {
  const [formData, setFormData] = useState<PerfilUsuario>(perfil);
  const [erroresValidacion, setErroresValidacion] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Actualizar form cuando cambie el perfil
  useEffect(() => {
    setFormData(perfil);
    setHasChanges(false);
  }, [perfil]);

  const validarTelefono = (telefono: string): string | null => {
    if (!telefono) return null; // Campo opcional
    
    if (!REGEX_TELEFONO_ARGENTINO.test(telefono)) {
      return 'El teléfono debe tener el formato: +54 9 xx xxxx-xxxx';
    }
    
    return null;
  };

  const validarEmail = (email: string): string | null => {
    if (!email) return 'El email es obligatorio';
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'El formato del email no es válido';
    }
    
    return null;
  };

  const validarCampo = (campo: string, valor: string): string | null => {
    switch (campo) {
      case 'nombre':
        return !valor ? 'El nombre es obligatorio' : null;
      case 'apellido':
        return !valor ? 'El apellido es obligatorio' : null;
      case 'email':
        return validarEmail(valor);
      case 'telefonoMovil':
        return validarTelefono(valor);
      default:
        return null;
    }
  };

  const handleInputChange = (campo: keyof PerfilUsuario, valor: string) => {
    const nuevoFormData = { ...formData, [campo]: valor };
    setFormData(nuevoFormData);
    
    // Validar campo
    const error = validarCampo(campo, valor);
    setErroresValidacion(prev => ({
      ...prev,
      [campo]: error || ''
    }));
    
    // Verificar si hay cambios
    const haycambios = JSON.stringify(nuevoFormData) !== JSON.stringify(perfil);
    setHasChanges(haycambios);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validar todos los campos
    const errores: Record<string, string> = {};
    
    Object.keys(formData).forEach(campo => {
      const valor = formData[campo as keyof PerfilUsuario] as string;
      const error = validarCampo(campo, valor || '');
      if (error) {
        errores[campo] = error;
      }
    });
    
    setErroresValidacion(errores);
    
    // Si no hay errores, enviar
    if (Object.keys(errores).length === 0) {
      onGuardar(formData);
    }
  };

  const formatearTelefono = (valor: string): string => {
    // Remover todo excepto números
    const numeros = valor.replace(/\D/g, '');
    
    // Si empieza con 54, formatear como +54 9 xx xxxx-xxxx
    if (numeros.startsWith('54') && numeros.length >= 12) {
      const codigo = numeros.slice(0, 2); // 54
      const nueve = numeros.slice(2, 3); // 9
      const area = numeros.slice(3, 5); // xx
      const primera = numeros.slice(5, 9); // xxxx
      const segunda = numeros.slice(9, 13); // xxxx
      
      return `+${codigo} ${nueve} ${area} ${primera}-${segunda}`;
    }
    
    return valor;
  };

  const handleTelefonoChange = (valor: string) => {
    const telefonoFormateado = formatearTelefono(valor);
    handleInputChange('telefonoMovil', telefonoFormateado);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ maxWidth: 600 }}>
      <Typography variant="h6" gutterBottom>
        Información Personal
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Nombre */}
        <TextField
          label="Nombre"
          value={formData.nombre || ''}
          onChange={(e) => handleInputChange('nombre', e.target.value)}
          disabled={!esEdicionCompleta || loading}
          error={!!erroresValidacion.nombre}
          helperText={erroresValidacion.nombre || (!esEdicionCompleta ? 'Solo editable por SUPERUSUARIO' : '')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Person />
              </InputAdornment>
            ),
          }}
          fullWidth
          required
        />

        {/* Apellido */}
        <TextField
          label="Apellido"
          value={formData.apellido || ''}
          onChange={(e) => handleInputChange('apellido', e.target.value)}
          disabled={!esEdicionCompleta || loading}
          error={!!erroresValidacion.apellido}
          helperText={erroresValidacion.apellido || (!esEdicionCompleta ? 'Solo editable por SUPERUSUARIO' : '')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Person />
              </InputAdornment>
            ),
          }}
          fullWidth
          required
        />

        {/* Email */}
        <TextField
          label="Email"
          type="email"
          value={formData.email || ''}
          disabled={true}
          error={!!erroresValidacion.email}
          helperText="El email no se puede modificar por razones de seguridad"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Email />
              </InputAdornment>
            ),
            readOnly: true,
          }}
          fullWidth
          required
        />

        {/* Dependencia */}
        <TextField
          label="Dependencia"
          value={formData.dependencia || ''}
          onChange={(e) => handleInputChange('dependencia', e.target.value)}
          disabled={!esEdicionCompleta || loading}
          helperText={!esEdicionCompleta ? 'Solo editable por SUPERUSUARIO' : ''}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Business />
              </InputAdornment>
            ),
          }}
          fullWidth
        />

        {/* Teléfono Móvil */}
        <TextField
          label="Teléfono Móvil"
          value={formData.telefonoMovil || ''}
          onChange={(e) => handleTelefonoChange(e.target.value)}
          disabled={loading}
          error={!!erroresValidacion.telefonoMovil}
          helperText={erroresValidacion.telefonoMovil || 'Formato: +54 9 xx xxxx-xxxx (opcional)'}
          placeholder="+54 9 11 1234-5678"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Phone />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <Tooltip title="Formato argentino: +54 9 seguido del código de área y número">
                  <IconButton size="small">
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              </InputAdornment>
            ),
          }}
          fullWidth
        />

        {/* Rol (solo lectura) */}
        <TextField
          label="Rol"
          value={formData.rol || ''}
          disabled
          helperText="El rol solo puede ser modificado por un SUPERUSUARIO desde la gestión de usuarios"
          fullWidth
        />

        {/* Botón Guardar */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button
            type="submit"
            variant="contained"
            disabled={!hasChanges || loading || Object.keys(erroresValidacion).some(key => erroresValidacion[key])}
            startIcon={loading ? <CircularProgress size={20} /> : <Save />}
            size="large"
          >
            {loading ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ProfileForm;