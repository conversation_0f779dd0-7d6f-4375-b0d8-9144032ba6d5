import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery,
  Alert,
  CircularProgress
} from '@mui/material';
import { Close, Edit, Visibility, OpenInNew } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PerfilUsuario } from '../../types/perfil.types';
import { Usuario } from '../../types/usuario.types';
import UserAvatar from '../usuarios/UserAvatar';
import ProfileForm from './ProfileForm';

interface PerfilModalProps {
  open: boolean;
  onClose: () => void;
  usuario: Usuario;
  onPerfilActualizado?: (perfil: PerfilUsuario) => void;
  loading?: boolean;
  error?: string;
}

const PerfilModal: React.FC<PerfilModalProps> = ({
  open,
  onClose,
  usuario,
  onPerfilActualizado,
  loading = false,
  error
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [modoEdicion, setModoEdicion] = useState(false);
  const [guardando, setGuardando] = useState(false);
  const [mensaje, setMensaje] = useState<{ tipo: 'success' | 'error'; texto: string } | null>(null);

  // Convertir Usuario a PerfilUsuario
  const perfilUsuario: PerfilUsuario = {
    id: usuario.id,
    nombre: usuario.nombre,
    apellido: usuario.apellido,
    email: usuario.email,
    dependencia: usuario.dependencia,
    telefonoMovil: usuario.telefonoMovil,
    rol: usuario.rol
  };

  // Limpiar mensajes cuando se cierra el modal
  useEffect(() => {
    if (!open) {
      setModoEdicion(false);
      setMensaje(null);
    }
  }, [open]);

  const handleGuardarPerfil = async (perfilActualizado: PerfilUsuario) => {
    setGuardando(true);
    setMensaje(null);

    try {
      // Aquí se haría la llamada al servicio
      // await perfilService.actualizarPerfil(perfilActualizado);
      
      // Simular delay para mostrar loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMensaje({ tipo: 'success', texto: 'Perfil actualizado correctamente' });
      setModoEdicion(false);
      
      if (onPerfilActualizado) {
        onPerfilActualizado(perfilActualizado);
      }
      
      // Cerrar modal después de un momento
      setTimeout(() => {
        onClose();
      }, 1500);
      
    } catch (error) {
      setMensaje({ 
        tipo: 'error', 
        texto: 'Error al actualizar el perfil. Inténtalo de nuevo.' 
      });
    } finally {
      setGuardando(false);
    }
  };

  const handleVerPerfilCompleto = () => {
    onClose();
    navigate('/perfil');
  };

  const renderVisualizacion = () => (
    <Box>
      {/* Header con avatar y nombre */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <UserAvatar usuario={usuario} size="large" />
        <Box sx={{ ml: 2, flex: 1 }}>
          <Typography variant="h6" fontWeight="bold">
            {usuario.nombre} {usuario.apellido}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {usuario.dependencia || 'Sin dependencia asignada'}
          </Typography>
          <Typography variant="caption" color="primary">
            {usuario.rol}
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Información de contacto */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom color="text.secondary">
          Información de Contacto
        </Typography>
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              Email:
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {usuario.email}
            </Typography>
          </Box>
          
          {usuario.telefonoMovil && (
            <Box>
              <Typography variant="body2" fontWeight="medium">
                Teléfono Móvil:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {usuario.telefonoMovil}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Acciones rápidas */}
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Button
          variant="outlined"
          startIcon={<Edit />}
          onClick={() => setModoEdicion(true)}
          size="small"
        >
          Editar
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<OpenInNew />}
          onClick={handleVerPerfilCompleto}
          size="small"
        >
          Ver Completo
        </Button>
      </Box>
    </Box>
  );

  const renderEdicion = () => (
    <ProfileForm
      perfil={perfilUsuario}
      onGuardar={handleGuardarPerfil}
      loading={guardando}
      error={mensaje?.tipo === 'error' ? mensaje.texto : undefined}
      success={mensaje?.tipo === 'success' ? mensaje.texto : undefined}
      esEdicionCompleta={false} // En modal rápido, solo campos básicos
    />
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          minHeight: isMobile ? '100vh' : 400,
          maxHeight: isMobile ? '100vh' : '80vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        pb: 1
      }}>
        <Typography variant="h6">
          {modoEdicion ? 'Editar Perfil' : 'Mi Perfil'}
        </Typography>
        
        <IconButton
          onClick={onClose}
          size="small"
          disabled={guardando}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : modoEdicion ? (
          renderEdicion()
        ) : (
          renderVisualizacion()
        )}
      </DialogContent>

      {!modoEdicion && !loading && (
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={onClose} disabled={guardando}>
            Cerrar
          </Button>
        </DialogActions>
      )}

      {modoEdicion && (
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button 
            onClick={() => setModoEdicion(false)} 
            disabled={guardando}
          >
            Cancelar
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default PerfilModal;