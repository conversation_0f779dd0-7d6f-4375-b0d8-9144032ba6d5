import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Announcement as AnnouncementIcon
} from '@mui/icons-material';
import { Anuncio } from '../../types/anuncio.types';

interface AnuncioModalProps {
  anuncio: Anuncio | null;
  open: boolean;
  onClose: () => void;
}

const AnuncioModal: React.FC<AnuncioModalProps> = ({ anuncio, open, onClose }) => {
  if (!anuncio) return null;

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-AR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
        }
      }}
    >
      {/* Header del modal */}
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
          pt: 3,
          px: 3,
          background: '#002856',
          color: 'white',
          position: 'relative'
        }}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <AnnouncementIcon sx={{ fontSize: 32 }} />
          <Typography variant="h5" component="div" fontWeight="bold" sx={{ letterSpacing: 0.5 }}>
            {anuncio.titulo}
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.1)'
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* Contenido del modal */}
      <DialogContent sx={{ pt: 8, pb: 3, px: 4 }}>
        <Box sx={{ mt: 2 }}>
          {/* Contenido del anuncio */}
          <Box
            sx={{
              fontSize: '1.1rem',
              lineHeight: 1.7,
              '& p': { mb: 2.5 },
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                mb: 1.5,
                mt: 2.5,
                fontWeight: 'bold',
                '&:first-of-type': { mt: 0 }
              },
              '& ul, & ol': { mb: 2.5, pl: 3 },
              '& li': { mb: 0.8 },
              '& strong, & b': { fontWeight: 'bold', color: 'text.primary' },
              '& em, & i': { fontStyle: 'italic' },
              '& a': {
                color: 'primary.main',
                textDecoration: 'underline',
                fontWeight: 500,
                '&:hover': { textDecoration: 'none' }
              }
            }}
          >
            {/* Renderizar contenido HTML de forma segura */}
            <div dangerouslySetInnerHTML={{ __html: anuncio.contenido }} />
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Información adicional */}
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
              Publicado el {formatFecha(anuncio.fechaCreacion)}
            </Typography>
            {anuncio.creadoPorNombre && (
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                Por: {anuncio.creadoPorNombre}
              </Typography>
            )}
          </Box>
        </Box>
      </DialogContent>

      {/* Acciones del modal */}
      <DialogActions sx={{ px: 4, pb: 4, pt: 2 }}>
        <Button
          onClick={onClose}
          variant="contained"
          size="large"
          fullWidth
          sx={{
            py: 2,
            fontWeight: 'bold',
            textTransform: 'none',
            borderRadius: 3,
            fontSize: '1.1rem',
            background: '#002856',
            '&:hover': {
              background: '#003366'
            }
          }}
        >
          Entendido
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AnuncioModal;