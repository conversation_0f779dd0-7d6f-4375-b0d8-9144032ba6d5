import React from 'react';
import { Chip, Tooltip } from '@mui/material';
import { History as HistoryIcon } from '@mui/icons-material';

interface LegacyBadgeProps {
  size?: 'small' | 'medium';
  variant?: 'filled' | 'outlined';
  showTooltip?: boolean;
  tooltipText?: string;
}

const LegacyBadge: React.FC<LegacyBadgeProps> = ({ 
  size = 'small', 
  variant = 'outlined',
  showTooltip = true,
  tooltipText = 'Registro legacy - Algunos campos técnicos no están disponibles debido a limitaciones del sistema anterior'
}) => {
  const badge = (
    <Chip
      icon={<HistoryIcon />}
      label="Legacy"
      color="warning"
      size={size}
      variant={variant}
      sx={{ 
        fontWeight: 500,
        fontSize: size === 'small' ? '0.75rem' : '0.875rem',
        '& .MuiChip-icon': { 
          fontSize: size === 'small' ? '0.875rem' : '1rem',
          color: 'warning.main'
        },
        '& .MuiChip-label': {
          fontWeight: 600
        },
        backgroundColor: variant === 'filled' ? 'warning.light' : 'transparent',
        borderColor: 'warning.main',
        color: 'warning.dark'
      }}
    />
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <Tooltip 
      title={tooltipText}
      arrow
      placement="top"
    >
      {badge}
    </Tooltip>
  );
};

export default LegacyBadge;