import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Stack,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
  Computer as ComputerIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { EstadisticasActividad as EstadisticasType } from '../../types/actividad.types';
import actividadSistemaService from '../../api/actividadSistemaService';

interface EstadisticasActividadProps {
  onClose?: () => void;
}

const EstadisticasActividad: React.FC<EstadisticasActividadProps> = ({ onClose }) => {
  const [estadisticas, setEstadisticas] = useState<EstadisticasType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    cargarEstadisticas();
  }, []);

  const cargarEstadisticas = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await actividadSistemaService.getEstadisticas();
      setEstadisticas(data);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Error al cargar estadísticas');
      console.error('Error al cargar estadísticas:', err);
    } finally {
      setLoading(false);
    }
  };

  const calcularPorcentaje = (valor: number, total: number) => {
    return total > 0 ? Math.round((valor / total) * 100) : 0;
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Cargando estadísticas...
          </Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  if (error || !estadisticas) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" color="error">
            Error al cargar estadísticas
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {error || 'No se pudieron cargar las estadísticas'}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  const totalPorModulo = estadisticas.porModulo.reduce((sum, [, count]) => sum + count, 0);
  const totalPorUsuario = estadisticas.porUsuario.reduce((sum, [, count]) => sum + count, 0);

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <TrendingUpIcon color="primary" />
        Estadísticas de Actividad
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', mb: 3 }}>
        {/* Actividad por Módulo */}
        <Box sx={{ flex: 1, minWidth: 400 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ComputerIcon color="primary" />
                Actividad por Módulo
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Stack spacing={2}>
                {estadisticas.porModulo.map(([modulo, count]) => {
                  const porcentaje = calcularPorcentaje(count, totalPorModulo);
                  return (
                    <Box key={modulo}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" fontWeight={500}>
                          {modulo}
                        </Typography>
                        <Chip 
                          label={`${count} (${porcentaje}%)`} 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={porcentaje} 
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  );
                })}
              </Stack>
              
              <Box sx={{ mt: 2, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                <Typography variant="caption" color="textSecondary">
                  Total de actividades: {totalPorModulo}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Usuarios más Activos */}
        <Box sx={{ flex: 1, minWidth: 400 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PersonIcon color="primary" />
                Usuarios más Activos
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Stack spacing={2}>
                {estadisticas.porUsuario.slice(0, 5).map(([usuario, count], index) => {
                  const porcentaje = calcularPorcentaje(count, totalPorUsuario);
                  return (
                    <Box key={usuario}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box>
                          <Typography variant="body2" fontWeight={500}>
                            #{index + 1} {usuario.split('@')[0]}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {usuario.includes('@') ? usuario.split('@')[1] : ''}
                          </Typography>
                        </Box>
                        <Chip 
                          label={`${count} (${porcentaje}%)`} 
                          size="small" 
                          color="secondary" 
                          variant="outlined"
                        />
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={porcentaje} 
                        color="secondary"
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  );
                })}
              </Stack>
              
              <Box sx={{ mt: 2, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                <Typography variant="caption" color="textSecondary">
                  Total de usuarios activos: {estadisticas.porUsuario.length}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Actividades Recientes */}
      <Box sx={{ mt: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckCircleIcon color="primary" />
              Actividades Recientes (Últimas 24 horas)
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {estadisticas.actividadesRecientes.length === 0 ? (
              <Typography variant="body2" color="textSecondary" align="center" sx={{ py: 2 }}>
                No hay actividades recientes en las últimas 24 horas
              </Typography>
            ) : (
              <Stack spacing={1}>
                {estadisticas.actividadesRecientes.slice(0, 10).map((actividad) => (
                  <Box 
                    key={actividad.id} 
                    sx={{ 
                      p: 1, 
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      '&:hover': { backgroundColor: '#f9f9f9' }
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {actividad.usuario} - {actividad.tipoAccion}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {new Date(actividad.fechaHora).toLocaleString('es-AR')}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {actividad.modulo && (
                          <Chip 
                            label={actividad.modulo} 
                            size="small" 
                            variant="outlined"
                          />
                        )}
                        {actividad.estadoRespuesta && (
                          <Chip 
                            label={actividad.estadoRespuesta} 
                            size="small" 
                            color={
                              actividad.estadoRespuesta === 'SUCCESS' ? 'success' :
                              actividad.estadoRespuesta === 'ERROR' ? 'error' : 'warning'
                            }
                          />
                        )}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Stack>
            )}
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default EstadisticasActividad;