import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Typography,
  Box,
  TablePagination,
  Avatar,
  Stack
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Computer as ComputerIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationOnIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { ActividadSistema, ModuloActividad, EstadoRespuesta } from '../../types/actividad.types';
import { isLegacyRecord, formatLegacyField } from '../../utils/legacyUtils';
import LegacyBadge from './LegacyBadge';

interface TablaActividadProps {
  actividades: ActividadSistema[];
  loading: boolean;
  totalElements: number;
  page: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onVerDetalle: (id: number) => void;
}

const TablaActividad: React.FC<TablaActividadProps> = ({
  actividades,
  loading,
  totalElements,
  page,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onVerDetalle
}) => {

  // Configuración de colores y iconos por tipo de acción
  const getAccionConfig = (tipoAccion: string, estadoRespuesta?: string) => {
    if (estadoRespuesta === EstadoRespuesta.ERROR) {
      return { color: 'error' as const, icon: <ErrorIcon fontSize="small" /> };
    }
    
    if (tipoAccion.includes('LOGIN')) {
      return { color: 'success' as const, icon: <CheckCircleIcon fontSize="small" /> };
    }
    if (tipoAccion.includes('LOGOUT')) {
      return { color: 'warning' as const, icon: <WarningIcon fontSize="small" /> };
    }
    if (tipoAccion.includes('CREAR')) {
      return { color: 'primary' as const, icon: <CheckCircleIcon fontSize="small" /> };
    }
    if (tipoAccion.includes('EDITAR')) {
      return { color: 'info' as const, icon: <CheckCircleIcon fontSize="small" /> };
    }
    if (tipoAccion.includes('ELIMINAR')) {
      return { color: 'error' as const, icon: <ErrorIcon fontSize="small" /> };
    }
    
    return { color: 'default' as const, icon: <CheckCircleIcon fontSize="small" /> };
  };

  // Configuración de colores por módulo
  const getModuloColor = (modulo?: string) => {
    switch (modulo) {
      case ModuloActividad.EXPEDIENTES:
        return 'primary';
      case ModuloActividad.USUARIOS:
        return 'secondary';
      case ModuloActividad.SISTEMA:
        return 'info';
      case ModuloActividad.REPORTES:
        return 'warning';
      default:
        return 'default';
    }
  };

  // Formatear fecha/hora
  const formatearFechaHora = (fechaString: string) => {
    try {
      const fecha = new Date(fechaString);
      return {
        fecha: fecha.toLocaleDateString('es-AR'),
        hora: fecha.toLocaleTimeString('es-AR', { 
          hour: '2-digit', 
          minute: '2-digit',
          second: '2-digit'
        })
      };
    } catch (error) {
      return { fecha: 'N/A', hora: 'N/A' };
    }
  };

  // Obtener iniciales del usuario
  const getIniciales = (usuario: string) => {
    if (!usuario) return 'U';
    const partes = usuario.split('@')[0].split('.');
    if (partes.length >= 2) {
      return (partes[0][0] + partes[1][0]).toUpperCase();
    }
    return usuario.substring(0, 2).toUpperCase();
  };

  // Formatear duración
  const formatearDuracion = (duracionMs?: number) => {
    if (!duracionMs) return 'N/A';
    if (duracionMs < 1000) return `${duracionMs}ms`;
    if (duracionMs < 60000) return `${(duracionMs / 1000).toFixed(1)}s`;
    return `${(duracionMs / 60000).toFixed(1)}m`;
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onPageSizeChange(parseInt(event.target.value, 10));
  };

  if (loading) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Cargando actividades...</Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Usuario
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Acción
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Módulo
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Fecha/Hora
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Información Técnica
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                Estado
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', textAlign: 'center' }}>
                Acciones
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {actividades.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography variant="body2" color="textSecondary">
                    No hay actividades registradas con los filtros aplicados.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              actividades.map((actividad) => {
                const { fecha, hora } = formatearFechaHora(actividad.fechaHora);
                const accionConfig = getAccionConfig(actividad.tipoAccion, actividad.estadoRespuesta);
                const isLegacy = isLegacyRecord(actividad);
                
                return (
                  <TableRow
                    key={actividad.id}
                    hover
                    sx={{
                      '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                      cursor: 'pointer',
                      backgroundColor: isLegacy ? 'rgba(255, 193, 7, 0.05)' : 'inherit'
                    }}
                    onClick={() => onVerDetalle(actividad.id)}
                  >
                    {/* Usuario */}
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar 
                          sx={{ 
                            width: 32, 
                            height: 32, 
                            fontSize: '0.75rem',
                            bgcolor: 'primary.main'
                          }}
                        >
                          {getIniciales(actividad.usuario)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight={500}>
                            {actividad.usuario.split('@')[0]}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {actividad.usuario.includes('@') ? actividad.usuario.split('@')[1] : ''}
                          </Typography>
                          {isLegacy && (
                            <Box sx={{ mt: 0.5 }}>
                              <LegacyBadge />
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </TableCell>

                    {/* Acción */}
                    <TableCell>
                      <Chip
                        icon={accionConfig.icon}
                        label={actividad.tipoAccion}
                        color={accionConfig.color}
                        size="small"
                        variant="outlined"
                        sx={{ fontWeight: 500 }}
                      />
                      {actividad.detalles && (
                        <Typography variant="caption" display="block" color="textSecondary" sx={{ mt: 0.5 }}>
                          {actividad.detalles.length > 50 
                            ? `${actividad.detalles.substring(0, 50)}...` 
                            : actividad.detalles}
                        </Typography>
                      )}
                    </TableCell>

                    {/* Módulo */}
                    <TableCell>
                      {actividad.modulo && (
                        <Chip
                          label={actividad.modulo}
                          color={getModuloColor(actividad.modulo) as any}
                          size="small"
                          variant="filled"
                        />
                      )}
                    </TableCell>

                    {/* Fecha/Hora */}
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {fecha}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {hora}
                        </Typography>
                      </Box>
                    </TableCell>

                    {/* Información Técnica */}
                    <TableCell>
                      <Stack spacing={0.5}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <LocationOnIcon fontSize="small" color="action" />
                          <Typography
                            variant="caption"
                            sx={{
                              fontStyle: isLegacy && !actividad.ipCliente ? 'italic' : 'normal',
                              color: isLegacy && !actividad.ipCliente ? 'text.secondary' : 'inherit'
                            }}
                          >
                            {formatLegacyField(actividad.ipCliente, 'ipCliente')}
                          </Typography>
                        </Box>
                        
                        {actividad.duracionMs && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <ScheduleIcon fontSize="small" color="action" />
                            <Typography variant="caption">
                              {formatearDuracion(actividad.duracionMs)}
                            </Typography>
                          </Box>
                        )}
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <ComputerIcon fontSize="small" color="action" />
                          <Typography
                            variant="caption"
                            sx={{
                              fontStyle: isLegacy && !actividad.endpoint ? 'italic' : 'normal',
                              color: isLegacy && !actividad.endpoint ? 'text.secondary' : 'inherit'
                            }}
                          >
                            {actividad.endpoint
                              ? `${actividad.metodoHttp || 'GET'} ${actividad.endpoint.length > 20
                                  ? `${actividad.endpoint.substring(0, 20)}...`
                                  : actividad.endpoint}`
                              : formatLegacyField(actividad.endpoint, 'endpoint')
                            }
                          </Typography>
                        </Box>
                      </Stack>
                    </TableCell>

                    {/* Estado */}
                    <TableCell>
                      {actividad.estadoRespuesta && (
                        <Chip
                          label={actividad.estadoRespuesta}
                          color={
                            actividad.estadoRespuesta === EstadoRespuesta.SUCCESS ? 'success' :
                            actividad.estadoRespuesta === EstadoRespuesta.ERROR ? 'error' :
                            'warning'
                          }
                          size="small"
                          variant="filled"
                        />
                      )}
                    </TableCell>

                    {/* Acciones */}
                    <TableCell align="center">
                      <Tooltip title="Ver detalles completos">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onVerDetalle(actividad.id);
                          }}
                          color="primary"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Paginación */}
      <TablePagination
        rowsPerPageOptions={[10, 20, 50, 100]}
        component="div"
        count={totalElements}
        rowsPerPage={pageSize}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Filas por página:"
        labelDisplayedRows={({ from, to, count }) => 
          `${from}-${to} de ${count !== -1 ? count : `más de ${to}`}`
        }
      />
    </Paper>
  );
};

export default TablaActividad;