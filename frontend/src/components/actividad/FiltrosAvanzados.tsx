import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Typography,
  Collapse,
  IconButton,
  Autocomplete,
  Stack
} from '@mui/material';
import {
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { es } from 'date-fns/locale';
import { 
  FiltroActividad, 
  ModuloActividad, 
  EstadoRespuesta,
  FiltroRapido 
} from '../../types/actividad.types';

interface FiltrosAvanzadosProps {
  filtros: FiltroActividad;
  onFiltrosChange: (filtros: FiltroActividad) => void;
  onLimpiarFiltros: () => void;
  loading?: boolean;
}

const FiltrosAvanzados: React.FC<FiltrosAvanzadosProps> = ({
  filtros,
  onFiltrosChange,
  onLimpiarFiltros,
  loading = false
}) => {
  const [expanded, setExpanded] = useState(false);
  const [filtrosLocales, setFiltrosLocales] = useState<FiltroActividad>(filtros);
  const [usuariosDisponibles] = useState<string[]>([
    // TODO: Cargar desde API
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ]);

  // Filtros rápidos predefinidos
  const filtrosRapidos: FiltroRapido[] = [
    {
      label: 'Últimas 24 horas',
      value: {
        fechaInicio: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      },
      color: 'primary'
    },
    {
      label: 'Esta semana',
      value: {
        fechaInicio: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      color: 'secondary'
    },
    {
      label: 'Solo errores',
      value: {
        estadoRespuesta: EstadoRespuesta.ERROR
      },
      color: 'error'
    },
    {
      label: 'Expedientes',
      value: {
        modulo: ModuloActividad.EXPEDIENTES
      },
      color: 'info'
    },
    {
      label: 'Usuarios',
      value: {
        modulo: ModuloActividad.USUARIOS
      },
      color: 'warning'
    }
  ];

  useEffect(() => {
    setFiltrosLocales(filtros);
  }, [filtros]);

  const handleInputChange = (field: keyof FiltroActividad, value: any) => {
    setFiltrosLocales(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFechaChange = (field: 'fechaInicio' | 'fechaFin', date: Date | null) => {
    setFiltrosLocales(prev => ({
      ...prev,
      [field]: date ? date.toISOString() : undefined
    }));
  };

  const aplicarFiltros = () => {
    onFiltrosChange(filtrosLocales);
  };

  const aplicarFiltroRapido = (filtroRapido: FiltroRapido) => {
    const nuevosFiltros = { ...filtrosLocales, ...filtroRapido.value };
    setFiltrosLocales(nuevosFiltros);
    onFiltrosChange(nuevosFiltros);
  };

  const limpiarFiltros = () => {
    setFiltrosLocales({});
    onLimpiarFiltros();
  };

  const tienesFiltrosActivos = () => {
    return Object.keys(filtrosLocales).some(key => 
      key !== 'page' && key !== 'size' && key !== 'sortBy' && key !== 'sortDirection' &&
      filtrosLocales[key as keyof FiltroActividad] !== undefined &&
      filtrosLocales[key as keyof FiltroActividad] !== ''
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          {/* Header con filtros rápidos */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FilterListIcon color="primary" />
              <Typography variant="h6" component="h2">
                Filtros de Búsqueda
              </Typography>
              {tienesFiltrosActivos() && (
                <Chip 
                  label="Filtros activos" 
                  color="primary" 
                  size="small" 
                  variant="outlined" 
                />
              )}
            </Box>
            <IconButton 
              onClick={() => setExpanded(!expanded)}
              size="small"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>

          {/* Filtros rápidos */}
          <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
            {filtrosRapidos.map((filtro, index) => (
              <Chip
                key={index}
                label={filtro.label}
                onClick={() => aplicarFiltroRapido(filtro)}
                color={filtro.color as any}
                variant="outlined"
                size="small"
                sx={{ cursor: 'pointer' }}
              />
            ))}
          </Stack>

          {/* Filtros avanzados colapsables */}
          <Collapse in={expanded}>
            <Box sx={{ mt: 2 }}>
              {/* Primera fila */}
              <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
                {/* Usuario */}
                <Box sx={{ minWidth: 200, flex: 1 }}>
                  <Autocomplete
                    options={usuariosDisponibles}
                    value={filtrosLocales.usuario || ''}
                    onChange={(_, value) => handleInputChange('usuario', value)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Usuario"
                        variant="outlined"
                        size="small"
                        fullWidth
                      />
                    )}
                    freeSolo
                  />
                </Box>

                {/* Módulo */}
                <Box sx={{ minWidth: 150, flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Módulo</InputLabel>
                    <Select
                      value={filtrosLocales.modulo || ''}
                      onChange={(e) => handleInputChange('modulo', e.target.value)}
                      label="Módulo"
                    >
                      <MenuItem value="">Todos</MenuItem>
                      {Object.values(ModuloActividad).map(modulo => (
                        <MenuItem key={modulo} value={modulo}>
                          {modulo}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {/* Estado de Respuesta */}
                <Box sx={{ minWidth: 150, flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Estado</InputLabel>
                    <Select
                      value={filtrosLocales.estadoRespuesta || ''}
                      onChange={(e) => handleInputChange('estadoRespuesta', e.target.value)}
                      label="Estado"
                    >
                      <MenuItem value="">Todos</MenuItem>
                      {Object.values(EstadoRespuesta).map(estado => (
                        <MenuItem key={estado} value={estado}>
                          {estado}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {/* IP Cliente */}
                <Box sx={{ minWidth: 150, flex: 1 }}>
                  <TextField
                    label="IP Cliente"
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={filtrosLocales.ipCliente || ''}
                    onChange={(e) => handleInputChange('ipCliente', e.target.value)}
                    placeholder="***********"
                  />
                </Box>
              </Box>

              {/* Segunda fila */}
              <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
                {/* Fecha Inicio */}
                <Box sx={{ minWidth: 200, flex: 1 }}>
                  <DateTimePicker
                    label="Fecha Inicio"
                    value={filtrosLocales.fechaInicio ? new Date(filtrosLocales.fechaInicio) : null}
                    onChange={(date) => handleFechaChange('fechaInicio', date)}
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true
                      }
                    }}
                  />
                </Box>

                {/* Fecha Fin */}
                <Box sx={{ minWidth: 200, flex: 1 }}>
                  <DateTimePicker
                    label="Fecha Fin"
                    value={filtrosLocales.fechaFin ? new Date(filtrosLocales.fechaFin) : null}
                    onChange={(date) => handleFechaChange('fechaFin', date)}
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true
                      }
                    }}
                  />
                </Box>

                {/* Categoría de Acción */}
                <Box sx={{ minWidth: 200, flex: 1 }}>
                  <TextField
                    label="Categoría de Acción"
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={filtrosLocales.categoriaAccion || ''}
                    onChange={(e) => handleInputChange('categoriaAccion', e.target.value)}
                    placeholder="LOGIN_EXITOSO"
                  />
                </Box>

                {/* Botones de acción */}
                <Box sx={{ minWidth: 200, display: 'flex', gap: 1, alignItems: 'center' }}>
                  <Button
                    variant="contained"
                    startIcon={<SearchIcon />}
                    onClick={aplicarFiltros}
                    disabled={loading}
                    size="small"
                  >
                    Buscar
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<ClearIcon />}
                    onClick={limpiarFiltros}
                    disabled={loading}
                    size="small"
                  >
                    Limpiar
                  </Button>
                </Box>
              </Box>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
};

export default FiltrosAvanzados;