import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  IconButton
} from '@mui/material';
import Grid from '@mui/material/Grid';
import {
  Close as CloseIcon,
  Bar<PERSON>hart as BarChartIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { <PERSON>Chart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import useEstadisticasActividad from '../../hooks/useEstadisticasActividad';

interface EstadisticasActividadModalProps {
  open: boolean;
  onClose: () => void;
}

const COLORS = ['#1976d2', '#1565c0', '#0d47a1', '#42a5f5', '#64b5f6'];

const EstadisticasActividadModal: React.FC<EstadisticasActividadModalProps> = ({
  open,
  onClose
}) => {
  const { data, loading, error, cargarEstadisticas } = useEstadisticasActividad();

  useEffect(() => {
    if (open && !data && !loading) {
      cargarEstadisticas();
    }
  }, [open, data, loading, cargarEstadisticas]);

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '600px' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BarChartIcon color="primary" />
          <Typography variant="h6">Estadísticas de Actividad del Sistema</Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {data && !loading && (
          <Grid container spacing={3}>
            {/* Gráfico por Módulo */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <BarChartIcon color="primary" />
                    <Typography variant="h6">Actividades por Módulo</Typography>
                  </Box>
                  
                  {data.porModulo.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={data.porModulo} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <XAxis 
                          dataKey="nombre" 
                          angle={-45}
                          textAnchor="end"
                          height={80}
                          fontSize={12}
                        />
                        <YAxis />
                        <Tooltip 
                          formatter={(value) => [value, 'Actividades']}
                          labelStyle={{ color: '#333' }}
                        />
                        <Bar dataKey="cantidad" radius={[4, 4, 0, 0]}>
                          {data.porModulo.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                      No hay datos disponibles
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico por Usuario */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <PersonIcon color="primary" />
                    <Typography variant="h6">Actividades por Usuario</Typography>
                  </Box>
                  
                  {data.porUsuario.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={data.porUsuario} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <XAxis 
                          dataKey="nombre" 
                          angle={-45}
                          textAnchor="end"
                          height={80}
                          fontSize={12}
                        />
                        <YAxis />
                        <Tooltip 
                          formatter={(value) => [value, 'Actividades']}
                          labelStyle={{ color: '#333' }}
                        />
                        <Bar dataKey="cantidad" radius={[4, 4, 0, 0]}>
                          {data.porUsuario.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                      No hay datos disponibles
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Resumen de actividades recientes */}
            <Grid size={{ xs: 12 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Resumen de Actividades Recientes
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {data.actividadesRecientes.length > 0 
                      ? `Se encontraron ${data.actividadesRecientes.length} actividades en las últimas 24 horas`
                      : 'No hay actividades recientes registradas'
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} variant="contained">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EstadisticasActividadModal;