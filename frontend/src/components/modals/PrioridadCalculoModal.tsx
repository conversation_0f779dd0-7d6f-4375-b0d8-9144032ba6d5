import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  Button, 
  Divider, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper 
} from '@mui/material';
import { Expediente } from '../../types/expediente.types';

interface PrioridadCalculoModalProps {
  open: boolean;
  onClose: () => void;
  expediente: Expediente | null;
}

const PrioridadCalculoModal: React.FC<PrioridadCalculoModalProps> = ({ open, onClose, expediente }) => {
  if (!expediente) return null;

  // Función para calcular los puntos de un factor específico
  const calcularPuntosFactor = (factor: string, valor: any): number => {
    switch (factor) {
      case 'profesion':
        if (valor === 'FUERZA_SEGURIDAD') return 800;
        if (valor === 'FUERZA_ARMADA') return 900;
        if (valor === 'SERVICIO_INTELIGENCIA') return 1000;
        if (valor === 'PROFESIONAL') return 800;
        if (valor === 'COMERCIANTE') return 800;
        if (valor === 'OFICIO') return 500;
        if (valor === 'EMPLEADO') return 500;
        if (valor === 'DESOCUPADO') return 100;
        return 0;
      case 'detenciones':
        if (valor === 0) return 0;
        if (valor <= 2) return 200;
        if (valor <= 5) return 750;
        return 1000;
      case 'complices':
        if (valor === 0) return 0;
        if (valor === 1) return 200;
        if (valor <= 3) return 500;
        return 1000;
      case 'tipoCaptura':
        if (valor === 'NACIONAL') return 500;
        if (valor === 'INTERNACIONAL') return 1000;
        if (valor === 'NACIONAL E INTERNACIONAL') return 1500;
        return 0;
      case 'tipoVictima':
        if (valor === 'MENOR') return 800;
        if (valor === 'MUJER') return 250;
        if (valor === 'ANCIANO_JUBILADO') return 500;
        if (valor === 'POLITICO') return 800;
        if (valor === 'JUEZ' || valor === 'FISCAL') return 1000;
        if (valor === 'OTROS') return 250;
        return 0;
      case 'nivelOrganizacion':
        if (valor === 'SIMPLE') return 250;
        if (valor === 'COMPLEJA') return 800;
        return 0;
      case 'ambitoBanda':
        if (valor === 'NACIONAL') return 750;
        if (valor === 'PROVINCIAL') return 500;
        if (valor === 'BARRIAL') return 250;
        if (valor === 'INTERNACIONAL') return 1000;
        return 0;
      case 'capacidadOperativa':
        if (valor === 'ALTA') return 1000;
        if (valor === 'BAJA') return 500;
        return 0;
      case 'impactoSocial':
        if (valor === 'ALTO') return 500;
        if (valor === 'BAJO') return 250;
        return 0;
      case 'tipoDano':
        if (valor === 'FISICO') return 250;
        if (valor === 'PSICOLOGICO') return 150;
        if (valor === 'MATERIAL') return 50;
        return 0;
      case 'flag':
        return valor ? 500 : 0;
      case 'reincidente':
        return valor ? 800 : 0;
      case 'reiterante':
        return valor ? 500 : 0;
      case 'terrorismo':
        return valor ? 1000 : 0;
      case 'armasFuego':
        return valor ? 500 : 0;
      case 'armasBlancas':
        return valor ? 250 : 0;
      case 'recompensa':
        return valor ? 500 : 0;
      case 'recursosLimitados':
        if (valor === 'SI') return 500;
        if (valor === 'NO') return 200;
        return 0;
      case 'areaFronteriza':
        if (valor === 'SI') return 500;
        if (valor === 'NO') return 100;
        return 0;
      case 'impactoPercepcion':
        if (valor === 'ALTA') return 500;
        if (valor === 'MEDIA') return 250;
        if (valor === 'BAJA') return 100;
        return 0;
      default:
        return 0;
    }
  };

  // Crear un array de todos los factores que contribuyen a la prioridad
  const factores = [
    // Delitos (no incluidos aquí porque requieren lógica especial)
    { nombre: 'Profesión/Ocupación', valor: expediente.profugoProfesionOcupacion, puntos: calcularPuntosFactor('profesion', expediente.profugoProfesionOcupacion), mostrarSiempre: true },
    { nombre: 'Detenciones previas', valor: expediente.profugoNumeroDetencionesPrevias, puntos: calcularPuntosFactor('detenciones', expediente.profugoNumeroDetencionesPrevias), mostrarSiempre: true },
    { nombre: 'Número de cómplices', valor: expediente.numeroComplices, puntos: calcularPuntosFactor('complices', expediente.numeroComplices), mostrarSiempre: true },
    { nombre: 'Tipo de captura', valor: expediente.tipoCaptura, puntos: calcularPuntosFactor('tipoCaptura', expediente.tipoCaptura), mostrarSiempre: true },
    { nombre: 'Tipo de víctima', valor: expediente.tipoVictima, puntos: calcularPuntosFactor('tipoVictima', expediente.tipoVictima), mostrarSiempre: true },
    { nombre: 'Caso mediático', valor: expediente.mediaticoFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('flag', expediente.mediaticoFlag), mostrarSiempre: true },
    { nombre: 'Prófugo reincidente', valor: expediente.reincicenteFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('reincidente', expediente.reincicenteFlag), mostrarSiempre: true },
    { nombre: 'Prófugo reiterante', valor: expediente.reiteranteFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('reiterante', expediente.reiteranteFlag), mostrarSiempre: true },
    { nombre: 'Involucra banda', valor: expediente.bandaFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('flag', expediente.bandaFlag), mostrarSiempre: true },
    { nombre: 'Involucra terrorismo', valor: expediente.terrorismoFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('terrorismo', expediente.terrorismoFlag), mostrarSiempre: true },
    { nombre: 'Nivel de organización', valor: expediente.nivelOrganizacion, puntos: calcularPuntosFactor('nivelOrganizacion', expediente.nivelOrganizacion), mostrarSiempre: true },
    { nombre: 'Ámbito de la banda', valor: expediente.ambitoBanda, puntos: calcularPuntosFactor('ambitoBanda', expediente.ambitoBanda), mostrarSiempre: true },
    { nombre: 'Capacidad operativa', valor: expediente.capacidadOperativa, puntos: calcularPuntosFactor('capacidadOperativa', expediente.capacidadOperativa), mostrarSiempre: true },
    { nombre: 'Hubo planificación', valor: expediente.planificacionFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('flag', expediente.planificacionFlag), mostrarSiempre: true },
    { nombre: 'Conexiones con otras actividades', valor: expediente.conexionesOtrasActividadesFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('flag', expediente.conexionesOtrasActividadesFlag), mostrarSiempre: true },
    { nombre: 'Impacto social', valor: expediente.impactoSocial, puntos: calcularPuntosFactor('impactoSocial', expediente.impactoSocial), mostrarSiempre: true },
    { nombre: 'Tipo de daño', valor: expediente.tipoDano, puntos: calcularPuntosFactor('tipoDano', expediente.tipoDano), mostrarSiempre: true },
    { nombre: 'Uso de armas de fuego', valor: expediente.usoArmasFuegoFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('armasFuego', expediente.usoArmasFuegoFlag), mostrarSiempre: true },
    { nombre: 'Uso de armas blancas', valor: expediente.usoArmasBlancasFlag ? 'Sí' : 'No', puntos: calcularPuntosFactor('armasBlancas', expediente.usoArmasBlancasFlag), mostrarSiempre: true },
    { nombre: 'Recursos limitados', valor: expediente.recursosLimitados, puntos: calcularPuntosFactor('recursosLimitados', expediente.recursosLimitados), mostrarSiempre: true },
    { nombre: 'Área fronteriza', valor: expediente.areaFronteriza, puntos: calcularPuntosFactor('areaFronteriza', expediente.areaFronteriza), mostrarSiempre: true },
    { nombre: 'Impacto en percepción de seguridad', valor: expediente.impactoPercepcion, puntos: calcularPuntosFactor('impactoPercepcion', expediente.impactoPercepcion), mostrarSiempre: true },
    { nombre: 'Tiene recompensa', valor: expediente.recompensa ? 'Sí' : 'No', puntos: calcularPuntosFactor('recompensa', expediente.recompensa), mostrarSiempre: true },
  ];

  // Filtrar para mostrar solo factores con valor o que deben mostrarse siempre
  const factoresFiltrados = factores.filter(f => 
    (f.valor !== undefined && f.valor !== null && f.valor !== '' && f.valor !== 'No' && f.valor !== 0) || 
    (f.puntos > 0) || 
    f.mostrarSiempre
  );

  // Calcular el total de puntos de todos los factores excepto delitos
  const puntosSinDelitos = factoresFiltrados.reduce((total, factor) => total + factor.puntos, 0);
  
  // Calcular los puntos de delitos como la diferencia entre la prioridad total y los demás factores
  // Esto asume que la prioridad total ya incluye los puntos de los delitos
  const puntosDelitos = Math.max(0, (expediente.prioridad || 0) - puntosSinDelitos);

  // Calcular el total de puntos mostrados
  const totalPuntosMostrados = factoresFiltrados.reduce((total, factor) => total + factor.puntos, 0) + puntosDelitos;

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-prioridad-title"
    >
      <Box sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: { xs: '90%', sm: '80%', md: '70%', lg: '60%' },
        maxWidth: 800,
        maxHeight: '90vh',
        bgcolor: 'background.paper',
        boxShadow: 24,
        p: 4,
        borderRadius: 2,
        overflow: 'auto'
      }}>
        <Typography id="modal-prioridad-title" variant="h5" component="h2" sx={{ mb: 3, fontWeight: 'bold' }}>
          Cálculo de Prioridad: {expediente.prioridad || 0}
        </Typography>
        
        <Divider sx={{ mb: 3 }} />
        
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
          Factores que contribuyen a la prioridad:
        </Typography>
        
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Factor</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Valor</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Puntos</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {/* Delitos */}
              {expediente.delitos && expediente.delitos.length > 0 && (
                <TableRow sx={{ backgroundColor: puntosDelitos > 0 ? 'rgba(144, 238, 144, 0.1)' : 'inherit' }}>
                  <TableCell>Delitos asociados</TableCell>
                  <TableCell>{expediente.delitos.length} delito(s)</TableCell>
                  <TableCell>{puntosDelitos} puntos</TableCell>
                </TableRow>
              )}
              
              {/* Mostrar todos los factores filtrados */}
              {factoresFiltrados.map((factor, index) => (
                <TableRow key={index} sx={{ backgroundColor: factor.puntos > 0 ? 'rgba(144, 238, 144, 0.1)' : 'inherit' }}>
                  <TableCell>{factor.nombre}</TableCell>
                  <TableCell>{factor.valor !== undefined && factor.valor !== null ? factor.valor : 'No especificado'}</TableCell>
                  <TableCell>{factor.puntos} puntos</TableCell>
                </TableRow>
              ))}
              
              {/* Mostrar total */}
              <TableRow sx={{ backgroundColor: 'rgba(173, 216, 230, 0.2)' }}>
                <TableCell sx={{ fontWeight: 'bold' }}>Total mostrado</TableCell>
                <TableCell></TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>{totalPuntosMostrados} puntos</TableCell>
              </TableRow>
              
              {/* Mostrar diferencia si existe */}
              {expediente.prioridad !== totalPuntosMostrados && (
                <TableRow sx={{ backgroundColor: 'rgba(255, 182, 193, 0.2)' }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>Otros factores no mostrados</TableCell>
                  <TableCell></TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>{(expediente.prioridad || 0) - totalPuntosMostrados} puntos</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
          Nota: La prioridad se calcula sumando los puntos de cada factor relevante. Los delitos tienen una valoración base según su gravedad.
          {expediente.prioridad !== totalPuntosMostrados && (
            <> Algunos factores pueden no mostrarse si no tienen valor asignado o son valores por defecto.</>
          )}
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button onClick={onClose} variant="contained" color="primary">
            Cerrar
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default PrioridadCalculoModal;
