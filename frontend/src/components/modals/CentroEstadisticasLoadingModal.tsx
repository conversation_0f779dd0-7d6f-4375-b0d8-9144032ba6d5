import React, { useState, useEffect } from 'react';
import { Box, Typography, LinearProgress, Modal, Fade } from '@mui/material';
import { Dashboard as DashboardIcon } from '@mui/icons-material';

interface CentroEstadisticasLoadingModalProps {
  open: boolean;
  onClose: () => void;
}

const CentroEstadisticasLoadingModal: React.FC<CentroEstadisticasLoadingModalProps> = ({
  open,
  onClose
}) => {
  const [progress, setProgress] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const fullText = 'Ingresando al centro de estadísticas';
  
  useEffect(() => {
    if (!open) return;

    // Reset states when modal opens
    setProgress(0);
    setDisplayText('');

    // Progress animation
    const progressTimer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(progressTimer);
          return 100;
        }
        return prev + 2; // 100% in 5 seconds (2% every 100ms)
      });
    }, 100);

    // Typing animation
    let textIndex = 0;
    const typingTimer = setInterval(() => {
      if (textIndex < fullText.length) {
        setDisplayText(fullText.substring(0, textIndex + 1));
        textIndex++;
      } else {
        clearInterval(typingTimer);
      }
    }, 150); // Typing speed

    // Auto close after 5 seconds
    const closeTimer = setTimeout(() => {
      onClose();
    }, 6000);

    return () => {
      clearInterval(progressTimer);
      clearInterval(typingTimer);
      clearTimeout(closeTimer);
    };
  }, [open, onClose, fullText]);

  return (
    <Modal
      open={open}
      onClose={() => {}} // Prevent manual close
      closeAfterTransition
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backdropFilter: 'blur(10px)',
      }}
    >
      <Fade in={open} timeout={500}>
        <Box
          sx={{
            position: 'relative',
            width: '500px',
            height: '300px',
            background: 'linear-gradient(135deg, var(--cc-bg-primary) 0%, #0d1117 50%, var(--cc-bg-primary) 100%)',
            border: '2px solid var(--cc-cufre)',
            borderRadius: '20px',
            padding: '3rem',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: `
              0 25px 80px rgba(0, 0, 0, 0.8),
              0 0 40px var(--cc-glow),
              inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at 20% 80%, rgba(255, 214, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%)
              `,
              animation: 'backgroundPulse 3s ease-in-out infinite alternate',
              pointerEvents: 'none',
              zIndex: 0,
            },
            '@keyframes backgroundPulse': {
              '0%': { opacity: 0.3 },
              '100%': { opacity: 0.7 }
            }
          }}
        >
          {/* Matrix-style background effect */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                repeating-linear-gradient(
                  90deg,
                  transparent,
                  transparent 98px,
                  rgba(255, 214, 0, 0.03) 100px
                ),
                repeating-linear-gradient(
                  0deg,
                  transparent,
                  transparent 98px,
                  rgba(255, 214, 0, 0.03) 100px
                )
              `,
              animation: 'matrixMove 4s linear infinite',
              pointerEvents: 'none',
              zIndex: 1,
              '@keyframes matrixMove': {
                '0%': { transform: 'translate(0, 0)' },
                '100%': { transform: 'translate(100px, 100px)' }
              }
            }}
          />

          {/* Content */}
          <Box sx={{ position: 'relative', zIndex: 2, textAlign: 'center', width: '100%' }}>
            {/* Icon */}
            <DashboardIcon
              sx={{
                fontSize: '4rem',
                color: 'var(--cc-cufre)',
                mb: 2,
                filter: 'drop-shadow(0 0 20px var(--cc-glow))',
                animation: 'iconPulse 2s ease-in-out infinite alternate',
                '@keyframes iconPulse': {
                  '0%': { transform: 'scale(1)', filter: 'drop-shadow(0 0 20px var(--cc-glow))' },
                  '100%': { transform: 'scale(1.1)', filter: 'drop-shadow(0 0 30px var(--cc-glow))' }
                }
              }}
            />

            {/* Typing text */}
            <Typography
              variant="h5"
              sx={{
                color: 'var(--cc-text-primary)',
                fontFamily: 'Orbitron, monospace',
                fontWeight: 600,
                mb: 3,
                minHeight: '2rem',
                textTransform: 'uppercase',
                letterSpacing: '2px',
                textShadow: '0 0 15px rgba(255, 255, 255, 0.3)',
                '&::after': {
                  content: '"|"',
                  color: 'var(--cc-cufre)',
                  animation: 'blink 1s infinite',
                  '@keyframes blink': {
                    '0%, 50%': { opacity: 1 },
                    '51%, 100%': { opacity: 0 }
                  }
                }
              }}
            >
              {displayText}
            </Typography>

            {/* Progress bar */}
            <Box sx={{ width: '100%', mb: 2 }}>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: '8px',
                  borderRadius: '4px',
                  backgroundColor: 'var(--cc-bg-accent)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: 'var(--cc-cufre)',
                    borderRadius: '4px',
                    boxShadow: '0 0 15px var(--cc-glow)',
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
                      animation: 'shimmer 2s infinite',
                      '@keyframes shimmer': {
                        '0%': { transform: 'translateX(-100%)' },
                        '100%': { transform: 'translateX(100%)' }
                      }
                    }
                  }
                }}
              />
            </Box>

            {/* Progress percentage */}
            <Typography
              variant="body2"
              sx={{
                color: 'var(--cc-text-secondary)',
                fontFamily: 'Orbitron, monospace',
                fontWeight: 500,
                letterSpacing: '1px'
              }}
            >
              {Math.round(progress)}%
            </Typography>
          </Box>

          {/* Floating particles effect */}
          {[...Array(6)].map((_, index) => (
            <Box
              key={index}
              sx={{
                position: 'absolute',
                width: '4px',
                height: '4px',
                backgroundColor: 'var(--cc-cufre)',
                borderRadius: '50%',
                opacity: 0.6,
                animation: `float${index} ${3 + index * 0.5}s ease-in-out infinite`,
                left: `${20 + index * 12}%`,
                top: `${30 + (index % 2) * 40}%`,
                [`@keyframes float${index}`]: {
                  '0%, 100%': { 
                    transform: 'translateY(0px) scale(1)',
                    opacity: 0.6
                  },
                  '50%': { 
                    transform: `translateY(${-20 - index * 5}px) scale(1.2)`,
                    opacity: 1
                  }
                }
              }}
            />
          ))}
        </Box>
      </Fade>
    </Modal>
  );
};

export default CentroEstadisticasLoadingModal;