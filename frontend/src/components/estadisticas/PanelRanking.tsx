import React from 'react';
import { Box, Typography, List, ListItem, ListItemText, Chip } from '@mui/material';
import { Gavel as GavelIcon, TrendingUp as TrendingUpIcon } from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

interface RankingData {
  name: string;
  value: number;
  percentage?: number;
  trend?: {
    value: number;
    type: 'positive' | 'negative' | 'neutral';
  };
  color?: string;
}

interface PanelRankingProps {
  title: string;
  data: RankingData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  onItemClick?: (data: RankingData, index: number) => void;
  maxItems?: number;
  showTrend?: boolean;
  showPercentage?: boolean;
}

const PanelRanking: React.FC<PanelRankingProps> = ({
  title,
  data,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  onItemClick,
  maxItems = 10,
  showTrend = false,
  showPercentage = true
}) => {
  const displayData = data.slice(0, maxItems);
  const totalValue = data.reduce((sum, item) => sum + item.value, 0);

  const handleItemClick = (item: RankingData, index: number) => {
    if (onItemClick) {
      onItemClick(item, index);
    }
  };

  const getItemPercentage = (value: number) => {
    return totalValue > 0 ? ((value / totalValue) * 100) : 0;
  };

  const getRankColor = (index: number) => {
    switch (index) {
      case 0: return 'var(--cc-cufre)'; // Oro para el primero
      case 1: return '#c0c0c0'; // Plata para el segundo
      case 2: return '#cd7f32'; // Bronce para el tercero
      default: return 'var(--cc-text-secondary)';
    }
  };

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0: return '🥇';
      case 1: return '🥈';
      case 2: return '🥉';
      default: return `${index + 1}°`;
    }
  };

  return (
    <PanelEstadisticas
      title={title}
      icon={<GavelIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
      className="panel-ranking-extended"
    >
      {data.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'var(--cc-text-secondary)'
          }}
        >
          <GavelIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" sx={{ textAlign: 'center' }}>
            No hay datos de ranking disponibles
          </Typography>
        </Box>
      ) : (
        <Box sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* Resumen superior */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
              p: 2,
              background: 'rgba(255, 255, 255, 0.05)',
              borderRadius: 2,
              border: '1px solid var(--cc-border)',
              flexShrink: 0
            }}
          >
            <Box>
              <Typography
                variant="h4"
                sx={{
                  color: 'var(--cc-cufre)',
                  fontWeight: 700,
                  fontFamily: 'Orbitron, monospace'
                }}
              >
                {totalValue.toLocaleString('es-AR')}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  color: 'var(--cc-text-secondary)',
                  textTransform: 'uppercase',
                  letterSpacing: '1px'
                }}
              >
                Total de casos
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography
                variant="h6"
                sx={{
                  color: 'var(--cc-text-primary)',
                  fontWeight: 600
                }}
              >
                {data.length}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  color: 'var(--cc-text-secondary)',
                  textTransform: 'uppercase',
                  letterSpacing: '1px'
                }}
              >
                Tipos de delitos
              </Typography>
            </Box>
          </Box>

          {/* Lista de ranking con scroll */}
          <Box sx={{
            flex: 1,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'var(--cc-bg-accent)',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'var(--cc-cufre)',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: '#ffed4e',
            }
          }}>
            <List sx={{ padding: 0 }}>
              {data.map((item, index) => {
                const percentage = getItemPercentage(item.value);
                return (
                  <ListItem
                    key={index}
                    onClick={() => handleItemClick(item, index)}
                    sx={{
                      mb: 1,
                      borderRadius: 2,
                      background: 'rgba(255, 255, 255, 0.03)',
                      border: '1px solid var(--cc-border)',
                      cursor: onItemClick ? 'pointer' : 'default',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(255, 214, 0, 0.1)',
                        border: '1px solid rgba(255, 214, 0, 0.3)',
                        transform: onItemClick ? 'translateX(4px)' : 'none'
                      }
                    }}
                  >
                    {/* Posición en el ranking */}
                    <Box
                      sx={{
                        minWidth: '60px',
                        textAlign: 'center',
                        mr: 2
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          color: getRankColor(index),
                          fontWeight: 700,
                          fontSize: index < 3 ? '1.5rem' : '1.2rem'
                        }}
                      >
                        {getRankIcon(index)}
                      </Typography>
                    </Box>

                    {/* Contenido principal */}
                    <ListItemText
                      primary={
                        <Typography
                          variant="subtitle1"
                          sx={{
                            color: 'var(--cc-text-primary)',
                            fontWeight: 600,
                            mb: 0.5
                          }}
                        >
                          {item.name}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{ color: 'var(--cc-text-secondary)' }}
                          >
                            {item.value.toLocaleString('es-AR')} casos
                          </Typography>
                          {showPercentage && (
                            <Chip
                              label={`${percentage.toFixed(1)}%`}
                              size="small"
                              sx={{
                                backgroundColor: 'var(--cc-bg-highlight)',
                                color: 'var(--cc-text-primary)',
                                fontSize: '0.75rem',
                                height: '20px'
                              }}
                            />
                          )}
                          {showTrend && item.trend && (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                color: item.trend.type === 'positive' ? 'var(--cc-success)' :
                                       item.trend.type === 'negative' ? 'var(--cc-alert)' : 'var(--cc-text-secondary)'
                              }}
                            >
                              <TrendingUpIcon
                                fontSize="small"
                                sx={{
                                  transform: item.trend.type === 'negative' ? 'rotate(180deg)' : 'none'
                                }}
                              />
                              <Typography variant="caption">
                                {item.trend.value > 0 ? '+' : ''}{item.trend.value}%
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      }
                    />

                    {/* Barra de progreso visual */}
                    <Box
                      sx={{
                        width: '100px',
                        height: '6px',
                        backgroundColor: 'var(--cc-bg-accent)',
                        borderRadius: '3px',
                        overflow: 'hidden',
                        ml: 2
                      }}
                    >
                      <Box
                        sx={{
                          width: `${percentage}%`,
                          height: '100%',
                          backgroundColor: item.color || getRankColor(index),
                          borderRadius: '3px',
                          transition: 'width 0.5s ease'
                        }}
                      />
                    </Box>
                  </ListItem>
                );
              })}
            </List>
          </Box>

          {/* Indicador de más elementos */}
          {data.length > maxItems && (
            <Box
              sx={{
                textAlign: 'center',
                mt: 2,
                p: 1,
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: 1,
                border: '1px solid var(--cc-border)',
                flexShrink: 0
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  color: 'var(--cc-text-secondary)',
                  fontStyle: 'italic'
                }}
              >
                Mostrando {Math.min(maxItems, data.length)} de {data.length} delitos
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </PanelEstadisticas>
  );
};

export default PanelRanking;