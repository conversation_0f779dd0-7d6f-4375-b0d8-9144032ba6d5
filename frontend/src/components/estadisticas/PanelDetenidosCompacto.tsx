import React from 'react';
import { Box, Typography, Avatar, Chip } from '@mui/material';
import { BarChart as BarChartIcon } from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

// Función para obtener el logo de cada fuerza
const getFuerzaLogo = (fuerza: string): string | null => {
  const fuerzaUpper = fuerza.toUpperCase();
  switch (fuerzaUpper) {
    case 'PFA':
      return '/images/pfa.png';
    case 'GNA':
      return '/images/Insignia_de_la_Gendarmería_de_Argentina.svg.png';
    case 'PNA':
      return '/images/Logo_de_la_Prefectura_Naval_Argentina.svg.png';
    case 'PSA':
      return '/images/Logo-PSA.png';
    case 'SPF':
      return '/images/Logo_SPF.png';
    case 'CUFRE':
      return '/images/logo-cufre.png';
    default:
      return null;
  }
};

interface DetenidoData {
  name: string;
  value: number;
  color: string;
}

interface PanelDetenidosCompactoProps {
  title: string;
  data: DetenidoData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  onItemClick?: (data: DetenidoData, index: number) => void;
}

const PanelDetenidosCompacto: React.FC<PanelDetenidosCompactoProps> = ({
  title,
  data,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  onItemClick
}) => {
  // Filtrar datos con valor > 0 y ordenar por valor descendente
  const filteredData = data.filter(item => item.value > 0).sort((a, b) => b.value - a.value);
  
  const handleItemClick = (item: DetenidoData, index: number) => {
    if (onItemClick) {
      onItemClick(item, index);
    }
  };

  return (
    <PanelEstadisticas
      title={title}
      icon={<BarChartIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
    >
      {filteredData.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'var(--cc-text-secondary)'
          }}
        >
          <BarChartIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" sx={{ textAlign: 'center' }}>
            No hay detenidos registrados
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 1.5,
            height: '100%',
            overflow: 'auto',
            padding: '0.5rem',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'var(--cc-bg-accent)',
              borderRadius: '3px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'var(--cc-cufre)',
              borderRadius: '3px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: '#ffed4e',
            }
          }}
        >
          {filteredData.map((item, index) => {
            const logo = getFuerzaLogo(item.name);
            return (
              <Box
                key={item.name}
                onClick={() => handleItemClick(item, index)}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '0.75rem 1rem',
                  backgroundColor: 'var(--cc-bg-accent)',
                  border: '1px solid var(--cc-border)',
                  borderRadius: '8px',
                  cursor: onItemClick ? 'pointer' : 'default',
                  transition: 'all 0.3s ease',
                  '&:hover': onItemClick ? {
                    borderColor: 'var(--cc-cufre)',
                    backgroundColor: 'var(--cc-bg-highlight)',
                    transform: 'translateX(4px)',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
                  } : {}
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  {logo ? (
                    <Avatar
                      src={logo}
                      alt={item.name}
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: '#fff',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                        border: '1px solid rgba(255, 255, 255, 0.1)'
                      }}
                    />
                  ) : (
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        color: '#fff'
                      }}
                    >
                      {item.name.substring(0, 2)}
                    </Box>
                  )}
                  <Typography
                    variant="body1"
                    sx={{
                      color: 'var(--cc-text-primary)',
                      fontWeight: 600,
                      fontSize: '0.9rem'
                    }}
                  >
                    {item.name}
                  </Typography>
                </Box>
                
                <Chip
                  label={item.value}
                  size="small"
                  sx={{
                    backgroundColor: item.color,
                    color: '#fff',
                    fontWeight: 'bold',
                    fontSize: '0.8rem',
                    minWidth: '40px',
                    '& .MuiChip-label': {
                      padding: '0 8px'
                    }
                  }}
                />
              </Box>
            );
          })}
          
          {/* Mostrar total si hay más de una fuerza */}
          {filteredData.length > 1 && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '0.75rem 1rem',
                backgroundColor: 'var(--cc-bg-highlight)',
                border: '2px solid var(--cc-cufre)',
                borderRadius: '8px',
                marginTop: '0.5rem'
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  color: 'var(--cc-cufre)',
                  fontWeight: 700,
                  fontSize: '0.9rem'
                }}
              >
                TOTAL
              </Typography>
              
              <Chip
                label={filteredData.reduce((sum, item) => sum + item.value, 0)}
                size="small"
                sx={{
                  backgroundColor: 'var(--cc-cufre)',
                  color: 'var(--cc-bg-primary)',
                  fontWeight: 'bold',
                  fontSize: '0.8rem',
                  minWidth: '40px',
                  '& .MuiChip-label': {
                    padding: '0 8px'
                  }
                }}
              />
            </Box>
          )}
        </Box>
      )}
    </PanelEstadisticas>
  );
};

export default PanelDetenidosCompacto;