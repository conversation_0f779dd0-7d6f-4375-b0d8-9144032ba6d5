import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import DonutLargeIcon from '@mui/icons-material/DonutLarge';
import { ExpedienteData, getPieTotal } from '../../utils/estadisticasUtils';

interface EstadisticasMetricasProps {
  estadisticasGenerales: any;
  expedientesPorEstado: ExpedienteData[];
  loading: boolean;
}

const EstadisticasMetricas: React.FC<EstadisticasMetricasProps> = ({
  estadisticasGenerales,
  expedientesPorEstado,
  loading
}) => {
  return (
    <Box className="cinematic-main-panel" sx={{ mb: 4 }}>
      <Typography className="cinematic-title" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <DonutLargeIcon sx={{ mr: 2, color: 'var(--cc-cufre)' }} />
        Resumen de Expedientes
      </Typography>
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', lg: '1fr 1fr 1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <Box className="cinematic-chart-container" sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="subtitle1" sx={{ color: 'var(--cc-text-secondary)', mb: 2 }}>
            Total de Expedientes
          </Typography>
          <Typography variant="h3" sx={{
            fontFamily: 'Orbitron, monospace',
            fontWeight: 'bold',
            color: 'var(--cc-cufre)',
            textShadow: '0 0 15px var(--cc-glow)'
          }}>
            {loading ? (
              <CircularProgress sx={{ color: 'var(--cc-cufre)' }} size={40} />
            ) : (
              estadisticasGenerales.totalExpedientes || getPieTotal(expedientesPorEstado)
            )}
          </Typography>
        </Box>
        
        <Box className="cinematic-chart-container" sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="subtitle1" sx={{ color: 'var(--cc-text-secondary)', mb: 2 }}>
            Capturas Vigentes
          </Typography>
          <Typography variant="h3" sx={{
            fontFamily: 'Orbitron, monospace',
            fontWeight: 'bold',
            color: '#d32f2f',
            textShadow: '0 0 15px rgba(211, 47, 47, 0.3)'
          }}>
            {loading ? (
              <CircularProgress sx={{ color: 'var(--cc-cufre)' }} size={40} />
            ) : (
              expedientesPorEstado.find(item => item.name === 'CAPTURA VIGENTE')?.value || 0
            )}
          </Typography>
        </Box>

        <Box className="cinematic-chart-container" sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="subtitle1" sx={{ color: 'var(--cc-text-secondary)', mb: 2 }}>
            Detenidos
          </Typography>
          <Typography variant="h3" sx={{
            fontFamily: 'Orbitron, monospace',
            fontWeight: 'bold',
            color: '#388e3c',
            textShadow: '0 0 15px rgba(56, 142, 60, 0.3)'
          }}>
            {loading ? (
              <CircularProgress sx={{ color: 'var(--cc-cufre)' }} size={40} />
            ) : (
              expedientesPorEstado.find(item => item.name === 'DETENIDO')?.value || 0
            )}
          </Typography>
        </Box>

        <Box className="cinematic-chart-container" sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="subtitle1" sx={{ color: 'var(--cc-text-secondary)', mb: 2 }}>
            Sin Efecto
          </Typography>
          <Typography variant="h3" sx={{
            fontFamily: 'Orbitron, monospace',
            fontWeight: 'bold',
            color: '#757575',
            textShadow: '0 0 15px rgba(117, 117, 117, 0.3)'
          }}>
            {loading ? (
              <CircularProgress sx={{ color: 'var(--cc-cufre)' }} size={40} />
            ) : (
              expedientesPorEstado.find(item => item.name === 'SIN EFECTO')?.value || 0
            )}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default EstadisticasMetricas;