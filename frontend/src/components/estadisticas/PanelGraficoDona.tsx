import React from 'react';
import { Box, Typography } from '@mui/material';
import { <PERSON><PERSON><PERSON>, Pie, ResponsiveContainer, Cell, Tooltip as RechartsTooltip, Legend } from 'recharts';
import { DonutLarge as DonutIcon } from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

interface DonaData {
  name: string;
  value: number;
  color: string;
}

interface PanelGraficoDonaProps {
  title: string;
  data: DonaData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  onSegmentClick?: (data: DonaData, index: number) => void;
}

const PanelGraficoDona: React.FC<PanelGraficoDonaProps> = ({
  title,
  data,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  onSegmentClick
}) => {
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const total = getTotalValue();
      const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : '0';
      
      return (
        <Box
          className="custom-tooltip"
          sx={{
            background: 'rgba(26, 26, 46, 0.95) !important',
            border: '1px solid var(--cc-cufre) !important',
            borderRadius: '8px !important',
            padding: '1rem !important',
            color: 'var(--cc-text-primary) !important',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.4) !important',
            backdropFilter: 'blur(10px) !important'
          }}
        >
          <Typography 
            variant="subtitle2" 
            sx={{ 
              fontWeight: 'bold', 
              color: data.color,
              mb: 0.5
            }}
          >
            {data.name}
          </Typography>
          <Typography variant="body2">
            <strong>Cantidad:</strong> {data.value.toLocaleString('es-AR')}
          </Typography>
          <Typography variant="body2">
            <strong>Porcentaje:</strong> {percentage}%
          </Typography>
        </Box>
      );
    }
    return null;
  };

  const getTotalValue = () => {
    return data.reduce((sum, item) => sum + item.value, 0);
  };

  const handlePieClick = (data: any, index: number) => {
    if (onSegmentClick) {
      onSegmentClick(data, index);
    }
  };

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // No mostrar etiquetas para segmentos muy pequeños
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="600"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <PanelEstadisticas
      title={title}
      icon={<DonutIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
    >
      {data.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'var(--cc-text-secondary)'
          }}
        >
          <DonutIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" sx={{ textAlign: 'center' }}>
            No hay datos disponibles
          </Typography>
        </Box>
      ) : (
        <Box className="chart-container" sx={{ width: '100%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={80}
                innerRadius={40}
                fill="#8884d8"
                dataKey="value"
                onClick={handlePieClick}
                style={{ cursor: onSegmentClick ? 'pointer' : 'default' }}
              >
                {data.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={entry.color}
                    strokeWidth={2}
                    style={{
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                      transition: 'all 0.3s ease'
                    }}
                  />
                ))}
              </Pie>
              <RechartsTooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                iconType="circle"
                wrapperStyle={{
                  paddingTop: '10px',
                  fontSize: '12px',
                  color: 'var(--cc-text-primary)'
                }}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color, fontWeight: 600 }}>
                    {value}
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
          
          {/* Resumen en el centro */}
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="h4"
              sx={{
                color: 'var(--cc-cufre)',
                fontWeight: 700,
                fontFamily: 'Orbitron, monospace',
                textShadow: '0 0 10px var(--cc-glow)'
              }}
            >
              {getTotalValue().toLocaleString('es-AR')}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: 'var(--cc-text-secondary)',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                fontWeight: 600
              }}
            >
              Total
            </Typography>
          </Box>
        </Box>
      )}
    </PanelEstadisticas>
  );
};

export default PanelGraficoDona;