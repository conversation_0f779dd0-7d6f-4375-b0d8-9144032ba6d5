import React from 'react';
import { Box, Typography } from '@mui/material';
import { Line<PERSON>hart, Line, XAxis, YAxis, ResponsiveContainer, Tooltip as RechartsTooltip, CartesianGrid } from 'recharts';
import { Timeline as TimelineIcon } from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

interface TimelineData {
  month: string;
  value: number;
  fecha?: string; // Para mostrar fecha completa en tooltip
}

interface PanelLineaTemporalProps {
  title: string;
  data: TimelineData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  onPointClick?: (data: TimelineData, index: number) => void;
  lineColor?: string;
  fillGradient?: boolean;
}

const PanelLineaTemporal: React.FC<PanelLineaTemporalProps> = ({
  title,
  data,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  onPointClick,
  lineColor = 'var(--cc-cufre)',
  fillGradient = true
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          className="custom-tooltip"
          sx={{
            background: 'rgba(26, 26, 46, 0.95) !important',
            border: '1px solid var(--cc-cufre) !important',
            borderRadius: '8px !important',
            padding: '1rem !important',
            color: 'var(--cc-text-primary) !important',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.4) !important',
            backdropFilter: 'blur(10px) !important'
          }}
        >
          <Typography 
            variant="subtitle2" 
            sx={{ 
              fontWeight: 'bold', 
              color: lineColor,
              mb: 0.5
            }}
          >
            {data.fecha || data.month}
          </Typography>
          <Typography variant="body2">
            <strong>Expedientes:</strong> {data.value.toLocaleString('es-AR')}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  const handlePointClick = (data: any, index: number) => {
    if (onPointClick) {
      onPointClick(data, index);
    }
  };

  const getMaxValue = () => {
    return Math.max(...data.map(item => item.value));
  };

  const getMinValue = () => {
    return Math.min(...data.map(item => item.value));
  };

  const getTrend = () => {
    if (data.length < 2) return 'neutral';
    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;
    
    if (lastValue > firstValue) return 'positive';
    if (lastValue < firstValue) return 'negative';
    return 'neutral';
  };

  const getTrendPercentage = () => {
    if (data.length < 2) return 0;
    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;
    
    if (firstValue === 0) return 0;
    return ((lastValue - firstValue) / firstValue * 100);
  };

  const formatAxisLabel = (value: string) => {
    // Formatear etiquetas del eje X para mejor visualización
    return value.length > 6 ? value.substring(0, 6) : value;
  };

  return (
    <PanelEstadisticas
      title={title}
      icon={<TimelineIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
    >
      {data.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'var(--cc-text-secondary)'
          }}
        >
          <TimelineIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" sx={{ textAlign: 'center' }}>
            No hay datos temporales disponibles
          </Typography>
        </Box>
      ) : (
        <Box className="chart-container" sx={{ width: '100%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            >
              <defs>
                {fillGradient && (
                  <linearGradient id="lineGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={lineColor} stopOpacity={0.3}/>
                    <stop offset="95%" stopColor={lineColor} stopOpacity={0.05}/>
                  </linearGradient>
                )}
              </defs>
              
              <CartesianGrid 
                strokeDasharray="3 3" 
                stroke="var(--cc-border)"
                opacity={0.3}
              />
              
              <XAxis 
                dataKey="month"
                tick={{ 
                  fill: 'var(--cc-text-secondary)', 
                  fontSize: 11,
                  fontWeight: 600
                }}
                axisLine={{ stroke: 'var(--cc-border)' }}
                tickLine={{ stroke: 'var(--cc-border)' }}
                angle={-45}
                textAnchor="end"
                height={60}
                tickFormatter={formatAxisLabel}
              />
              
              <YAxis 
                allowDecimals={false}
                tick={{ 
                  fill: 'var(--cc-text-secondary)', 
                  fontSize: 12,
                  fontWeight: 600
                }}
                axisLine={{ stroke: 'var(--cc-border)' }}
                tickLine={{ stroke: 'var(--cc-border)' }}
              />
              
              <RechartsTooltip content={<CustomTooltip />} />
              
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke={lineColor}
                strokeWidth={3}
                dot={{ 
                  r: 5, 
                  fill: lineColor, 
                  stroke: '#fff', 
                  strokeWidth: 2,
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                }}
                activeDot={{ 
                  r: 8, 
                  fill: lineColor, 
                  stroke: '#fff', 
                  strokeWidth: 3,
                  filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.4))'
                }}
                style={{ cursor: onPointClick ? 'pointer' : 'default' }}
                fill={fillGradient ? 'url(#lineGradient)' : 'none'}
              />
            </LineChart>
          </ResponsiveContainer>

          {/* Estadísticas de tendencia */}
          <Box
            sx={{
              position: 'absolute',
              top: '1rem',
              right: '1rem',
              background: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '8px',
              padding: '0.75rem',
              border: '1px solid var(--cc-border)',
              minWidth: '120px'
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: 'var(--cc-text-secondary)',
                display: 'block',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                mb: 0.5
              }}
            >
              Tendencia
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography
                variant="h6"
                sx={{
                  color: getTrend() === 'positive' ? 'var(--cc-success)' : 
                         getTrend() === 'negative' ? 'var(--cc-alert)' : 'var(--cc-text-secondary)',
                  fontWeight: 700,
                  fontFamily: 'Orbitron, monospace'
                }}
              >
                {getTrendPercentage() > 0 ? '+' : ''}{getTrendPercentage().toFixed(1)}%
              </Typography>
            </Box>
            <Typography
              variant="caption"
              sx={{
                color: 'var(--cc-text-secondary)',
                display: 'block'
              }}
            >
              Máx: {getMaxValue().toLocaleString('es-AR')}
            </Typography>
          </Box>
        </Box>
      )}
    </PanelEstadisticas>
  );
};

export default PanelLineaTemporal;