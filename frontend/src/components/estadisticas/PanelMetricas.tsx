import React from 'react';
import { Box, Typography, Grid } from '@mui/material';
import {
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon
} from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

interface MetricaData {
  label: string;
  value: number | string;
  trend?: {
    value: number;
    type: 'positive' | 'negative' | 'neutral';
    period: string;
  };
  format?: 'number' | 'percentage' | 'currency';
}

interface PanelMetricasProps {
  metricas: MetricaData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
}

const PanelMetricas: React.FC<PanelMetricasProps> = ({
  metricas,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh
}) => {
  const formatValue = (value: number | string, format?: string) => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'percentage':
        return `${value}%`;
      case 'currency':
        return new Intl.NumberFormat('es-AR', {
          style: 'currency',
          currency: 'ARS'
        }).format(value);
      default:
        return new Intl.NumberFormat('es-AR').format(value);
    }
  };

  const getTrendIcon = (type: 'positive' | 'negative' | 'neutral') => {
    switch (type) {
      case 'positive':
        return <TrendingUpIcon fontSize="small" />;
      case 'negative':
        return <TrendingDownIcon fontSize="small" />;
      default:
        return <RemoveIcon fontSize="small" />;
    }
  };

  const getTrendColor = (type: 'positive' | 'negative' | 'neutral') => {
    switch (type) {
      case 'positive':
        return 'var(--cc-success)';
      case 'negative':
        return 'var(--cc-alert)';
      default:
        return 'var(--cc-text-secondary)';
    }
  };

  return (
    <PanelEstadisticas
      title="Métricas Clave"
      icon={<AssessmentIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: 2,
          height: '100%',
          alignItems: 'center',
          justifyContent: 'space-around',
          flexWrap: 'wrap'
        }}
      >
        {metricas.map((metrica, index) => (
          <Box
            key={index}
            sx={{
              flex: '1 1 auto',
              minWidth: '200px',
              maxWidth: '250px'
            }}
          >
            <Box
              sx={{
                textAlign: 'center',
                p: 1.5,
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                transition: 'all 0.3s ease',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                '&:hover': {
                  background: 'rgba(255, 214, 0, 0.1)',
                  border: '1px solid rgba(255, 214, 0, 0.3)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Typography
                className="metric-large"
                sx={{
                  fontSize: '2.2rem !important',
                  lineHeight: 1.2,
                  mb: 0.5
                }}
              >
                {formatValue(metrica.value, metrica.format)}
              </Typography>
              
              <Typography
                className="metric-label"
                sx={{
                  fontSize: '0.85rem !important',
                  mb: metrica.trend ? 0.5 : 0
                }}
              >
                {metrica.label}
              </Typography>

              {metrica.trend && (
                <Box
                  className="metric-trend"
                  sx={{
                    justifyContent: 'center',
                    color: getTrendColor(metrica.trend.type),
                    fontSize: '0.8rem !important'
                  }}
                >
                  {getTrendIcon(metrica.trend.type)}
                  <Typography
                    variant="caption"
                    sx={{
                      color: 'inherit',
                      fontWeight: 600
                    }}
                  >
                    {metrica.trend.value > 0 ? '+' : ''}{metrica.trend.value}% {metrica.trend.period}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    </PanelEstadisticas>
  );
};

export default PanelMetricas;