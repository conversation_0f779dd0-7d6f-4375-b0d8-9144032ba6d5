import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import AddCircleIcon from '@mui/icons-material/AddCircle';

interface EstadisticasEmptyStateProps {
  onCreateExpediente: () => void;
}

const EstadisticasEmptyState: React.FC<EstadisticasEmptyStateProps> = ({
  onCreateExpediente
}) => {
  return (
    <Box className="cinematic-main-panel" sx={{ textAlign: 'center', py: 6 }}>
      <Typography variant="h6" sx={{ color: 'var(--cc-text-secondary)', mb: 2 }}>
        No hay expedientes registrados en el sistema.
      </Typography>
      <Button
        variant="contained"
        className="cinematic-button primary"
        startIcon={<AddCircleIcon />}
        onClick={onCreateExpediente}
      >
        Crear nuevo expediente
      </Button>
    </Box>
  );
};

export default EstadisticasEmptyState;