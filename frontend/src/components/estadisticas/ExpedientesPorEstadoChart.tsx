import React from 'react';
import { Box, Typography } from '@mui/material';
import { <PERSON><PERSON>hart, Pie, ResponsiveContainer, Cell, Sector, Tooltip as RechartsTooltip } from 'recharts';
import DonutLargeIcon from '@mui/icons-material/DonutLarge';
import { ExpedienteData } from '../../utils/estadisticasUtils';
import CustomTooltip from './CustomTooltip';

interface ExpedientesPorEstadoChartProps {
  data: ExpedienteData[];
  loading: boolean;
  onSegmentClick: (data: ExpedienteData, index: number) => void;
  activeIndex: number | null;
}

const ExpedientesPorEstadoChart: React.FC<ExpedientesPorEstadoChartProps> = ({
  data,
  loading,
  onSegmentClick,
  activeIndex
}) => {
  // Componente de renderizado para el sector activo
  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
    return (
      <g>
        <defs>
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 12}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          filter="url(#glow)"
        />
        <text x={cx} y={cy - 5} textAnchor="middle" fill="#222" style={{ fontWeight: 'bold', fontSize: '1.2rem' }}>
          {payload.name}
        </text>
        <text x={cx} y={cy + 20} textAnchor="middle" fill="#666">
          {`${value} (${(percent * 100).toFixed(0)}%)`}
        </text>
      </g>
    );
  };

  return (
    <Box className="cinematic-main-panel">
      <Typography className="cinematic-title" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <DonutLargeIcon sx={{ mr: 2, color: 'var(--cc-cufre)' }} />
        Expedientes por Estado
      </Typography>
      <Typography className="cinematic-subtitle">
        Distribución por estado de situación
      </Typography>
      
      <Box className="cinematic-chart-container">
        {data.length === 0 ? (
          <Typography sx={{ color: 'var(--cc-text-secondary)', textAlign: 'center' }}>
            No hay datos para mostrar
          </Typography>
        ) : (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <defs>
                <filter id="glow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="value"
                activeIndex={activeIndex === null ? undefined : activeIndex}
                activeShape={renderActiveShape}
                onMouseEnter={(_, index) => {}}
                onMouseLeave={() => {}}
                onClick={(clickData, index) => onSegmentClick(clickData, index)}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <RechartsTooltip content={<CustomTooltip totalData={data} />} />
            </PieChart>
          </ResponsiveContainer>
        )}
      </Box>
    </Box>
  );
};

export default ExpedientesPorEstadoChart;