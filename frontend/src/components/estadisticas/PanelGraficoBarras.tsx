import React from 'react';
import { Box, Typography, Avatar } from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, ResponsiveContainer, Tooltip as RechartsTooltip, Cell } from 'recharts';
import { BarChart as BarChartIcon } from '@mui/icons-material';
import PanelEstadisticas from './PanelEstadisticas';

// Función para obtener el logo de cada fuerza
const getFuerzaLogo = (fuerza: string): string | null => {
  const fuerzaUpper = fuerza.toUpperCase();
  switch (fuerzaUpper) {
    case 'PFA':
      return '/images/pfa.png';
    case 'GNA':
      return '/images/Insignia_de_la_Gendarmería_de_Argentina.svg.png';
    case 'PNA':
      return '/images/Logo_de_la_Prefectura_Naval_Argentina.svg.png';
    case 'PSA':
      return '/images/Logo-PSA.png';
    case 'SPF':
      return '/images/Logo_SPF.png';
    case 'CUFRE':
      return '/images/logo-cufre.png';
    default:
      return null;
  }
};

interface BarraData {
  name: string;
  value: number;
  color: string;
}

interface PanelGraficoBarrasProps {
  title: string;
  data: BarraData[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  onBarClick?: (data: BarraData, index: number) => void;
  layout?: 'horizontal' | 'vertical';
}

const PanelGraficoBarras: React.FC<PanelGraficoBarrasProps> = ({
  title,
  data,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  onBarClick,
  layout = 'horizontal'
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const logo = getFuerzaLogo(data.name);
      return (
        <Box
          className="custom-tooltip"
          sx={{
            background: 'rgba(26, 26, 46, 0.95) !important',
            border: '1px solid var(--cc-cufre) !important',
            borderRadius: '8px !important',
            padding: '1rem !important',
            color: 'var(--cc-text-primary) !important',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.4) !important',
            backdropFilter: 'blur(10px) !important'
          }}
        >
          <Box display="flex" alignItems="center" mb={1}>
            {logo && (
              <Avatar
                src={logo}
                alt={data.name}
                sx={{
                  width: 24,
                  height: 24,
                  mr: 1,
                  bgcolor: '#fff',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                }}
              />
            )}
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 'bold',
                color: 'var(--cc-cufre)'
              }}
            >
              {data.name}
            </Typography>
          </Box>
          <Typography variant="body2">
            <strong>Cantidad:</strong> {data.value.toLocaleString('es-AR')}
          </Typography>
          {data.name === 'SIN DATOS' && (
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic', opacity: 0.9, color: 'var(--cc-text-secondary)' }}>
              Sin fuerza asignada
            </Typography>
          )}
        </Box>
      );
    }
    return null;
  };

  const handleBarClick = (data: any, index: number) => {
    if (onBarClick) {
      onBarClick(data, index);
    }
  };

  const getMaxValue = () => {
    return Math.max(...data.map(item => item.value));
  };

  const formatAxisLabel = (value: string) => {
    // Truncar nombres largos para mejor visualización
    return value.length > 8 ? value.substring(0, 6) + '...' : value;
  };

  return (
    <PanelEstadisticas
      title={title}
      icon={<BarChartIcon />}
      isLoading={isLoading}
      lastUpdate={lastUpdate}
      isRefreshing={isRefreshing}
      onClick={onClick}
      onRefresh={onRefresh}
    >
      {data.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'var(--cc-text-secondary)'
          }}
        >
          <BarChartIcon sx={{ fontSize: '4rem', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" sx={{ textAlign: 'center' }}>
            No hay datos disponibles
          </Typography>
        </Box>
      ) : (
        <Box className="chart-container" sx={{ width: '100%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            {layout === 'horizontal' ? (
              <BarChart
                data={data}
                layout="horizontal"
                margin={{ top: 20, right: 30, left: 80, bottom: 20 }}
              >
                <XAxis 
                  type="number" 
                  allowDecimals={false}
                  tick={{ 
                    fill: 'var(--cc-text-secondary)', 
                    fontSize: 12,
                    fontWeight: 600
                  }}
                  axisLine={{ stroke: 'var(--cc-border)' }}
                  tickLine={{ stroke: 'var(--cc-border)' }}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  tick={({ x, y, payload }) => {
                    const logo = getFuerzaLogo(payload.value);
                    return (
                      <g transform={`translate(${x - 45},${y})`}>
                        {logo && (
                          <image
                            href={logo}
                            x={-20}
                            y={-8}
                            height={16}
                            width={16}
                            style={{ filter: 'drop-shadow(0 1px 3px rgba(255, 214, 0, 0.3))' }}
                          />
                        )}
                        <text
                          x={0}
                          y={0}
                          dy={4}
                          fill="var(--cc-text-primary)"
                          fontWeight="600"
                          fontSize={10}
                          textAnchor="start"
                        >
                          {formatAxisLabel(payload.value)}
                        </text>
                      </g>
                    );
                  }}
                  axisLine={{ stroke: 'var(--cc-border)' }}
                  tickLine={{ stroke: 'var(--cc-border)' }}
                  width={70}
                />
                <RechartsTooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="value" 
                  radius={[0, 4, 4, 0]}
                  onClick={handleBarClick}
                  style={{ cursor: onBarClick ? 'pointer' : 'default' }}
                >
                  {data.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke={entry.color}
                      strokeWidth={1}
                      style={{
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                        transition: 'all 0.3s ease'
                      }}
                    />
                  ))}
                </Bar>
              </BarChart>
            ) : (
              <BarChart
                data={data}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
              >
                <XAxis 
                  dataKey="name"
                  tick={{ 
                    fill: 'var(--cc-text-primary)', 
                    fontSize: 12,
                    fontWeight: 600
                  }}
                  axisLine={{ stroke: 'var(--cc-border)' }}
                  tickLine={{ stroke: 'var(--cc-border)' }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  tickFormatter={formatAxisLabel}
                />
                <YAxis 
                  allowDecimals={false}
                  tick={{ 
                    fill: 'var(--cc-text-secondary)', 
                    fontSize: 12,
                    fontWeight: 600
                  }}
                  axisLine={{ stroke: 'var(--cc-border)' }}
                  tickLine={{ stroke: 'var(--cc-border)' }}
                />
                <RechartsTooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="value" 
                  radius={[4, 4, 0, 0]}
                  onClick={handleBarClick}
                  style={{ cursor: onBarClick ? 'pointer' : 'default' }}
                >
                  {data.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke={entry.color}
                      strokeWidth={1}
                      style={{
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                        transition: 'all 0.3s ease'
                      }}
                    />
                  ))}
                </Bar>
              </BarChart>
            )}
          </ResponsiveContainer>

          {/* Estadística destacada */}
          <Box
            sx={{
              position: 'absolute',
              top: '1rem',
              left: '1rem',
              background: 'rgba(0, 0, 0, 0.7)',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              border: '1px solid var(--cc-border)'
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: 'var(--cc-text-secondary)',
                display: 'block',
                textTransform: 'uppercase',
                letterSpacing: '1px'
              }}
            >
              Máximo
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'var(--cc-cufre)',
                fontWeight: 700,
                fontFamily: 'Orbitron, monospace'
              }}
            >
              {getMaxValue().toLocaleString('es-AR')}
            </Typography>
          </Box>
        </Box>
      )}
    </PanelEstadisticas>
  );
};

export default PanelGraficoBarras;