import React from 'react';
import { Box, Typography, IconButton, Tooltip } from '@mui/material';
import { RefreshRounded as RefreshIcon } from '@mui/icons-material';
import '../../styles/CentroComando.css';

interface PanelEstadisticasProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isLoading?: boolean;
  lastUpdate?: Date | null | undefined;
  isRefreshing?: boolean;
  onClick?: () => void;
  onRefresh?: () => void;
  className?: string;
}

const PanelEstadisticas: React.FC<PanelEstadisticasProps> = ({
  title,
  icon,
  children,
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onClick,
  onRefresh,
  className = ''
}) => {
  const formatLastUpdate = (date: Date | null | undefined) => {
    if (!date) return 'Sin datos';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    
    if (diffSeconds < 60) {
      return `Hace ${diffSeconds}s`;
    } else if (diffMinutes < 60) {
      return `Hace ${diffMinutes}m`;
    } else {
      return date.toLocaleTimeString('es-AR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  return (
    <Box
      className={`panel-estadisticas ${className}`}
      onClick={onClick}
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        userSelect: 'none'
      }}
    >
      {/* Indicador de actualización */}
      <Box className="update-indicator">
        <Box 
          className={`update-pulse ${isRefreshing ? 'refreshing' : ''}`}
        />
        <Typography variant="caption">
          {formatLastUpdate(lastUpdate ?? null)}
        </Typography>
        {onRefresh && (
          <Tooltip title="Actualizar datos">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onRefresh();
              }}
              sx={{
                color: 'var(--cc-text-secondary)',
                '&:hover': {
                  color: 'var(--cc-cufre)',
                  transform: 'rotate(180deg)',
                  transition: 'all 0.3s ease'
                }
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* Título del panel */}
      <Typography className="panel-title">
        <Box className="panel-title-icon">
          {icon}
        </Box>
        {title}
      </Typography>

      {/* Contenido del panel */}
      <Box className="panel-content">
        {isLoading ? (
          <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box className="loading-shimmer" sx={{ width: '60%', height: '40px' }} />
            <Box className="loading-shimmer" sx={{ width: '80%', height: '20px' }} />
            <Box className="loading-shimmer" sx={{ width: '40%', height: '20px' }} />
          </Box>
        ) : (
          children
        )}
      </Box>

      {/* Efecto de hover para indicar interactividad */}
      {onClick && (
        <Box
          sx={{
            position: 'absolute',
            bottom: '1rem',
            right: '1rem',
            opacity: 0,
            transition: 'opacity 0.3s ease',
            '.panel-estadisticas:hover &': {
              opacity: 0.7
            }
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: 'var(--cc-cufre)',
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}
          >
            Click para detalles
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default PanelEstadisticas;