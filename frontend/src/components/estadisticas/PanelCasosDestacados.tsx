import React from 'react';
import { Box, Typography, Card, CardContent, Chip, Skeleton, IconButton, Tooltip } from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  History as HistoryIcon,
  PriorityHigh as PriorityHighIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface CasoDestacado {
  id: number;
  numero: string;
  fechaIngreso: string;
  descripcion?: string;
  prioridad?: number;
}

interface PanelCasosDestacadosProps {
  casoMasNuevo?: CasoDestacado | null;
  casoMasAntiguo?: CasoDestacado | null;
  top3Prioridad?: CasoDestacado[];
  isLoading?: boolean;
  lastUpdate?: Date | null;
  isRefreshing?: boolean;
  onRefresh?: () => void;
}

const PanelCasosDestacados: React.FC<PanelCasosDestacadosProps> = ({
  casoMasNuevo,
  casoMasAntiguo,
  top3Prioridad = [],
  isLoading = false,
  lastUpdate,
  isRefreshing = false,
  onRefresh
}) => {
  const navigate = useNavigate();

  const handleCasoClick = (casoId: number) => {
    navigate(`/expedientes/${casoId}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-AR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    });
  };

  const CasoCard = ({ caso, icon, title, color }: { 
    caso?: CasoDestacado | null; 
    icon: React.ReactNode; 
    title: string; 
    color: string;
  }) => (
    <Card 
      sx={{ 
        backgroundColor: 'var(--cc-bg-accent)',
        border: '1px solid var(--cc-border)',
        borderRadius: '12px',
        cursor: caso ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        '&:hover': caso ? {
          borderColor: color,
          transform: 'translateY(-2px)',
          boxShadow: `0 4px 20px rgba(0, 0, 0, 0.3)`
        } : {}
      }}
      onClick={() => caso && handleCasoClick(caso.id)}
    >
      <CardContent sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Box sx={{ color, mr: 1 }}>{icon}</Box>
          <Typography variant="subtitle2" sx={{ color: 'var(--cc-text-secondary)' }}>
            {title}
          </Typography>
        </Box>
        {isLoading ? (
          <>
            <Skeleton variant="text" width="60%" />
            <Skeleton variant="text" width="80%" />
          </>
        ) : caso ? (
          <>
            <Typography variant="h6" sx={{ color: 'var(--cc-text-primary)', fontWeight: 600 }}>
              {caso.numero}
            </Typography>
            <Typography variant="caption" sx={{ color: 'var(--cc-text-secondary)' }}>
              {formatDate(caso.fechaIngreso)}
            </Typography>
            {caso.prioridad && (
              <Chip 
                label={`Prioridad: ${caso.prioridad}`}
                size="small"
                sx={{ 
                  mt: 1,
                  backgroundColor: 'var(--cc-bg-highlight)',
                  color: 'var(--cc-cufre)'
                }}
              />
            )}
          </>
        ) : (
          <Typography variant="body2" sx={{ color: 'var(--cc-text-secondary)' }}>
            Sin datos
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box className="panel-estadisticas panel-casos-destacados" sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <Box className="panel-header" sx={{ mb: 2, flexShrink: 0 }}>
        <Typography className="panel-title">
          <PriorityHighIcon className="panel-title-icon" />
          Casos Destacados
        </Typography>
        
        {/* Indicador de actualización */}
        {lastUpdate && (
          <Box className="update-indicator">
            <Box className={`update-pulse ${isRefreshing ? 'refreshing' : ''}`} />
            <Typography variant="caption">
              {isRefreshing ? 'Actualizando...' : `Actualizado ${lastUpdate.toLocaleTimeString()}`}
            </Typography>
          </Box>
        )}
        
        {/* Botón de actualización */}
        {onRefresh && (
          <Tooltip title="Actualizar datos">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onRefresh();
              }}
              disabled={isRefreshing}
              sx={{ ml: 'auto' }}
            >
              <RefreshIcon sx={{
                fontSize: '1.2rem',
                animation: isRefreshing ? 'spin 1s linear infinite' : 'none'
              }} />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        flex: 1,
        overflow: 'hidden'
      }}>
        {/* Caso más nuevo y más antiguo */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: 2,
          flexShrink: 0
        }}>
          <CasoCard
            caso={casoMasNuevo}
            icon={<AccessTimeIcon />}
            title="Caso más nuevo"
            color="var(--cc-success)"
          />
          <CasoCard
            caso={casoMasAntiguo}
            icon={<HistoryIcon />}
            title="Caso más antiguo"
            color="var(--cc-alert)"
          />
        </Box>

        {/* Top 3 casos con mayor prioridad */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Typography
            variant="subtitle2"
            sx={{
              color: 'var(--cc-text-secondary)',
              mb: 1,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              flexShrink: 0
            }}
          >
            <PriorityHighIcon sx={{ fontSize: '1rem' }} />
            Top 3 - Mayor Prioridad
          </Typography>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            flex: 1,
            overflow: 'visible'
          }}>
            {isLoading ? (
              <>
                <Skeleton variant="rectangular" height={60} />
                <Skeleton variant="rectangular" height={60} />
                <Skeleton variant="rectangular" height={60} />
              </>
            ) : top3Prioridad.length > 0 ? (
              top3Prioridad.map((caso, index) => (
                <Box
                  key={caso.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 1.5,
                    backgroundColor: 'var(--cc-bg-accent)',
                    border: '1px solid var(--cc-border)',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    flexShrink: 0,
                    '&:hover': {
                      borderColor: 'var(--cc-cufre)',
                      backgroundColor: 'var(--cc-bg-highlight)'
                    }
                  }}
                  onClick={() => handleCasoClick(caso.id)}
                >
                  <Typography
                    sx={{
                      fontWeight: 700,
                      fontSize: '1.5rem',
                      color: index === 0 ? 'var(--cc-cufre)' : 'var(--cc-text-secondary)',
                      mr: 2,
                      minWidth: '30px'
                    }}
                  >
                    {index + 1}°
                  </Typography>
                  <Box sx={{ flex: 1 }}>
                    <Typography sx={{ color: 'var(--cc-text-primary)', fontWeight: 600 }}>
                      {caso.numero}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'var(--cc-text-secondary)' }}>
                      Prioridad: {caso.prioridad} • {formatDate(caso.fechaIngreso)}
                    </Typography>
                  </Box>
                  <IconButton size="small" sx={{ color: 'var(--cc-text-secondary)' }}>
                    <OpenInNewIcon fontSize="small" />
                  </IconButton>
                </Box>
              ))
            ) : (
              <Typography
                variant="body2"
                sx={{
                  color: 'var(--cc-text-secondary)',
                  textAlign: 'center',
                  py: 2
                }}
              >
                No hay casos con prioridad asignada
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PanelCasosDestacados;