import React from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';

interface EstadisticasControlsProps {
  onRefresh: () => void;
  loading: boolean;
}

const EstadisticasControls: React.FC<EstadisticasControlsProps> = ({
  onRefresh,
  loading
}) => {
  return (
    <Box sx={{
      mb: 3,
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'center'
    }}>
      <Tooltip title="Actualizar datos">
        <IconButton
          onClick={onRefresh}
          disabled={loading}
          className="cinematic-button"
          sx={{
            color: 'var(--cc-cufre)',
            border: '1px solid var(--cc-border)',
            '&:hover': {
              backgroundColor: 'var(--cc-cufre)',
              color: 'var(--cc-bg-primary)'
            }
          }}
        >
          <RefreshIcon />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default EstadisticasControls;