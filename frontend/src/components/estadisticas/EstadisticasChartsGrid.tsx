import React from 'react';
import { Box } from '@mui/material';
import { ExpedienteData } from '../../utils/estadisticasUtils';
import ExpedientesPorFuerzaChart from './ExpedientesPorFuerzaChart';
import ExpedientesPorEstadoChart from './ExpedientesPorEstadoChart';

interface EstadisticasChartsGridProps {
  filteredExpedientesPorFuerza: ExpedienteData[];
  filteredExpedientesPorEstado: ExpedienteData[];
  loading: boolean;
  activeFuerzaIndex: number | null;
  activeEstadoIndex: number | null;
  onPieClick: (data: ExpedienteData, type: 'fuerza' | 'estado', index: number) => void;
}

const EstadisticasChartsGrid: React.FC<EstadisticasChartsGridProps> = ({
  filteredExpedientesPorFuerza,
  filteredExpedientesPorEstado,
  loading,
  activeFuerzaIndex,
  activeEstadoIndex,
  onPieClick
}) => {
  return (
    <Box sx={{
      display: 'grid',
      gridTemplateColumns: { xs: '1fr', lg: '1fr 1fr' },
      gap: 4
    }}>
      <ExpedientesPorFuerzaChart
        data={filteredExpedientesPorFuerza}
        loading={loading}
        activeIndex={activeFuerzaIndex}
        onSegmentClick={(data, index) => onPieClick(data, 'fuerza', index)}
      />
      
      <ExpedientesPorEstadoChart
        data={filteredExpedientesPorEstado}
        loading={loading}
        activeIndex={activeEstadoIndex}
        onSegmentClick={(data, index) => onPieClick(data, 'estado', index)}
      />
    </Box>
  );
};

export default EstadisticasChartsGrid;