import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface DrillDownData {
  title: string;
  type: 'table' | 'chart';
  data: any[];
  columns?: string[];
  chartType?: 'pie' | 'bar';
}

interface DrillDownModalProps {
  open: boolean;
  onClose: () => void;
  data: DrillDownData | null;
}

const COLORS = ['#00C49F', '#FFBB28', '#FF8042', '#0088FE', '#8884D8'];

const DrillDownModal: React.FC<DrillDownModalProps> = ({ open, onClose, data }) => {
  if (!data) return null;

  const renderChart = () => {
    if (!data.data || data.data.length === 0) return null;

    if (data.chartType === 'pie') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data.data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    if (data.chartType === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      );
    }

    return null;
  };

  const renderTable = () => {
    if (!data.data || data.data.length === 0) {
      return (
        <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 4 }}>
          No hay datos disponibles
        </Typography>
      );
    }

    const columns = data.columns || Object.keys(data.data[0] || {});

    return (
      <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column} sx={{ fontWeight: 'bold' }}>
                  {column.charAt(0).toUpperCase() + column.slice(1)}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.data.map((row, index) => (
              <TableRow key={index} hover>
                {columns.map((column) => (
                  <TableCell key={column}>
                    {typeof row[column] === 'boolean' ? (
                      <Chip
                        label={row[column] ? 'Sí' : 'No'}
                        color={row[column] ? 'success' : 'default'}
                        size="small"
                      />
                    ) : typeof row[column] === 'number' ? (
                      row[column].toLocaleString()
                    ) : (
                      row[column] || 'N/A'
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: '#1a1a1a',
          color: '#fff',
          border: '1px solid #333',
          borderRadius: 2,
          minHeight: '60vh'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #333',
          pb: 2
        }}
      >
        <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
          {data.title}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: '#fff' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ width: '100%' }}>
          {data.type === 'chart' ? renderChart() : renderTable()}
        </Box>
      </DialogContent>

      <DialogActions sx={{ borderTop: '1px solid #333', pt: 2 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            color: '#fff',
            borderColor: '#555',
            '&:hover': {
              borderColor: '#777',
              backgroundColor: 'rgba(255,255,255,0.1)'
            }
          }}
        >
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DrillDownModal;