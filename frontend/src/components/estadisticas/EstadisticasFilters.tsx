import React from 'react';
import { Box, Alert, Typography, useTheme } from '@mui/material';

interface EstadisticasFiltersProps {
  activeFilter: { type: 'fuerza' | 'estado' | null, value: string | null };
  onClearFilter: () => void;
}

const EstadisticasFilters: React.FC<EstadisticasFiltersProps> = ({
  activeFilter,
  onClearFilter
}) => {
  const theme = useTheme();

  if (!activeFilter.type) {
    return null;
  }

  return (
    <Alert
      severity="info"
      className="cinematic-alert info"
      sx={{
        mb: 3,
        backgroundColor: 'rgba(255, 214, 0, 0.1)',
        border: '1px solid var(--cc-cufre)',
        color: 'var(--cc-text-primary)',
        display: 'flex',
        alignItems: 'center',
        '& .MuiAlert-message': {
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          justifyContent: 'space-between'
        }
      }}
    >
      <Box>
        <Typography variant="subtitle1" component="span" sx={{ fontWeight: 'bold' }}>
          Filtro aplicado: 
        </Typography>{' '}
        <Typography component="span">
          {activeFilter.type === 'fuerza' ? 'Fuerza' : 'Estado'} = "{activeFilter.value}"
        </Typography>
      </Box>
      <Typography 
        variant="body2" 
        sx={{ 
          cursor: 'pointer', 
          color: theme.palette.primary.main,
          '&:hover': { textDecoration: 'underline' }
        }}
        onClick={onClearFilter}
      >
        Limpiar filtro
      </Typography>
    </Alert>
  );
};

export default EstadisticasFilters;