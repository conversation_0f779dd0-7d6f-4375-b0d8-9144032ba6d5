import React from 'react';
import { Box, Typography } from '@mui/material';
import { ExpedienteData, getPieTotal } from '../../utils/estadisticasUtils';

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  totalData: ExpedienteData[];
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, totalData }) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <Box className="cinematic-tooltip">
        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: 'var(--cc-cufre)', mb: 1 }}>
          {data.name}
        </Typography>
        <Typography variant="body2" sx={{ color: 'var(--cc-text-primary)' }}>
          <b>Cantidad:</b> {data.value} expedientes
        </Typography>
        <Typography variant="body2" sx={{ color: 'var(--cc-text-primary)' }}>
          <b>Porcentaje:</b> {((data.value / getPieTotal(totalData)) * 100).toFixed(1)}%
        </Typography>
      </Box>
    );
  }
  return null;
};

export default CustomTooltip;