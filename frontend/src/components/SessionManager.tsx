import React from 'react';
import { useAuth } from '../context/AuthContext';
import { useInactivityTimer } from '../hooks/useInactivityTimer';
import { SessionWarningModal } from './SessionWarningModal';

interface SessionManagerProps {
  children: React.ReactNode;
}

/**
 * Componente que gestiona la sesión del usuario y el cierre automático por inactividad
 * Debe envolver toda la aplicación autenticada
 */
export const SessionManager: React.FC<SessionManagerProps> = ({ children }) => {
  const { logout, isAuthenticated } = useAuth();

  // Hook para gestionar la inactividad
  const {
    showWarning,
    countdown,
    extendSession,
    isActive
  } = useInactivityTimer(logout, isAuthenticated);

  return (
    <>
      {children}
      
      {/* Modal de advertencia de sesión */}
      <SessionWarningModal
        isOpen={showWarning}
        countdown={countdown}
        onExtendSession={extendSession}
      />
      
      {/* Indicador de estado en desarrollo */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'fixed',
            bottom: '10px',
            right: '10px',
            background: isActive ? '#10b981' : '#ef4444',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 10000,
            opacity: 0.7
          }}
        >
          Session: {isActive ? 'Active' : 'Paused'}
        </div>
      )}
    </>
  );
};

export default SessionManager;