/* Overlay del modal */
.session-warning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.3s ease-out;
}

/* Modal principal */
.session-warning-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 450px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  border: 2px solid #f59e0b;
}

/* Header del modal */
.session-warning-header {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  padding: 20px;
  text-align: center;
  position: relative;
}

.session-warning-icon {
  font-size: 2.5rem;
  margin-bottom: 8px;
  display: block;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.session-warning-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Contenido del modal */
.session-warning-content {
  padding: 30px 20px;
  text-align: center;
}

.session-warning-message {
  font-size: 1.1rem;
  color: #374151;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.session-warning-subtitle {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 20px 0 0 0;
  line-height: 1.4;
}

/* Countdown */
.session-warning-countdown {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  border: 2px solid #f87171;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.countdown-time {
  font-size: 2.5rem;
  font-weight: bold;
  color: #dc2626;
  font-family: 'Courier New', monospace;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: pulse 1s infinite;
}

.countdown-label {
  font-size: 0.9rem;
  color: #991b1b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Acciones del modal */
.session-warning-actions {
  padding: 0 20px 20px 20px;
  text-align: center;
}

.btn-extend-session {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  min-width: 200px;
  justify-content: center;
}

.btn-extend-session:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.btn-extend-session:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn-extend-session:focus {
  outline: none;
  ring: 2px solid #10b981;
  ring-offset: 2px;
}

.btn-icon {
  font-size: 1.2rem;
  animation: rotate 2s linear infinite;
}

/* Footer del modal */
.session-warning-footer {
  background: #f9fafb;
  padding: 12px 20px;
  text-align: center;
  border-top: 1px solid #e5e7eb;
}

.session-warning-footer small {
  color: #6b7280;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .session-warning-modal {
    width: 95%;
    margin: 10px;
  }
  
  .session-warning-header {
    padding: 16px;
  }
  
  .session-warning-icon {
    font-size: 2rem;
  }
  
  .session-warning-title {
    font-size: 1.3rem;
  }
  
  .session-warning-content {
    padding: 20px 16px;
  }
  
  .countdown-time {
    font-size: 2rem;
  }
  
  .btn-extend-session {
    padding: 12px 20px;
    font-size: 1rem;
    min-width: 180px;
  }
}

/* Accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .session-warning-overlay,
  .session-warning-modal,
  .countdown-time,
  .btn-icon {
    animation: none;
  }
  
  .btn-extend-session:hover {
    transform: none;
  }
}

/* Modo oscuro */
@media (prefers-color-scheme: dark) {
  .session-warning-modal {
    background: #1f2937;
    border-color: #f59e0b;
  }
  
  .session-warning-message {
    color: #d1d5db;
  }
  
  .session-warning-subtitle {
    color: #9ca3af;
  }
  
  .session-warning-footer {
    background: #111827;
    border-color: #374151;
  }
  
  .session-warning-footer small {
    color: #9ca3af;
  }
}