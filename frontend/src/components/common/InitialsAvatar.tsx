import React from 'react';

interface InitialsAvatarProps {
  nombre: string;
  apellido: string;
  size?: number;
  className?: string;
}

const InitialsAvatar: React.FC<InitialsAvatarProps> = ({ 
  nombre, 
  apellido, 
  size = 40, 
  className = '' 
}) => {
  // Función para obtener las iniciales
  const getInitials = (nombre: string, apellido: string): string => {
    const nombreInicial = nombre?.charAt(0)?.toUpperCase() || '';
    const apellidoInicial = apellido?.charAt(0)?.toUpperCase() || '';
    return nombreInicial + apellidoInicial;
  };

  // Función para generar un color de fondo basado en las iniciales
  const getBackgroundColor = (initials: string): string => {
    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
      '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5'
    ];
    
    // Usar el código ASCII de las iniciales para seleccionar un color consistente
    const charSum = initials.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    return colors[charSum % colors.length];
  };

  const initials = getInitials(nombre, apellido);
  const backgroundColor = getBackgroundColor(initials);

  const avatarStyle: React.CSSProperties = {
    width: size,
    height: size,
    borderRadius: '50%',
    backgroundColor,
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: size * 0.4, // Tamaño de fuente proporcional al tamaño del avatar
    fontWeight: 'bold',
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center',
    userSelect: 'none',
    flexShrink: 0
  };

  return (
    <div 
      className={`initials-avatar ${className}`}
      style={avatarStyle}
      title={`${nombre} ${apellido}`}
    >
      {initials}
    </div>
  );
};

export default InitialsAvatar;