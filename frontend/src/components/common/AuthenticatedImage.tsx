import React, { useState, useEffect } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

interface AuthenticatedImageProps {
  src: string;
  alt: string;
  style?: React.CSSProperties;
  sx?: any;
  fallbackSrc?: string;
  onError?: () => void;
  className?: string;
  showErrorMessage?: boolean;
}

/**
 * Componente para mostrar imágenes que requieren autenticación
 * Realiza peticiones fetch con headers de autenticación y crea blob URLs
 */
const AuthenticatedImage: React.FC<AuthenticatedImageProps> = ({ 
  src, 
  alt, 
  style, 
  sx, 
  fallbackSrc,
  onError,
  className,
  showErrorMessage = true
}) => {
  const [imageError, setImageError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    const loadImage = async () => {
      if (!src) {
        setImageError(true);
        setLoading(false);
        return;
      }

      // Si la imagen no es de la API (no contiene /api/ o /uploads/), usar directamente
      if (!src.includes('/api/') && !src.includes('/uploads/')) {
        setBlobUrl(src);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setImageError(false);

        // Obtener token de autenticación
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No hay token de autenticación');
        }

        // Realizar petición autenticada
        const response = await fetch(src, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Convertir respuesta a blob
        const blob = await response.blob();
        
        // Crear URL del blob solo si el componente sigue montado
        if (isMounted) {
          const url = URL.createObjectURL(blob);
          setBlobUrl(url);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error al cargar imagen:', error);
        if (isMounted) {
          setImageError(true);
          setLoading(false);
          onError?.();
        }
      }
    };

    loadImage();

    // Función de limpieza
    return () => {
      isMounted = false;
      if (blobUrl && blobUrl.startsWith('blob:')) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [src]); // Dependencia: se ejecuta cuando cambia la URL

  // Limpieza adicional cuando el componente se desmonta
  useEffect(() => {
    return () => {
      if (blobUrl && blobUrl.startsWith('blob:')) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, []); // Solo se ejecuta al desmontar

  if (loading) {
    return (
      <Box sx={{ 
        position: 'relative', 
        width: '100%', 
        height: '100%', 
        minHeight: 40,
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        ...sx 
      }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  if (imageError) {
    if (fallbackSrc) {
      return (
        <img 
          src={fallbackSrc} 
          alt={alt}
          style={style}
          className={className}
        />
      );
    }

    if (showErrorMessage) {
      return (
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          border: '1px dashed #ccc',
          color: 'text.secondary',
          p: 1,
          textAlign: 'center',
          minHeight: 40,
          ...sx
        }}>
          <Typography variant="body2" fontSize="0.75rem">
            Error al cargar imagen
          </Typography>
        </Box>
      );
    }

    return null;
  }

  if (!blobUrl) {
    return null;
  }

  return (
    <img 
      src={blobUrl} 
      alt={alt}
      style={style}
      className={className}
      onError={() => {
        setImageError(true);
        onError?.();
      }}
    />
  );
};

export default AuthenticatedImage;
