import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';

interface ProfessionalHeaderProps {
  title: string;
  buttonText?: string;
  onButtonClick?: () => void;
  showButton?: boolean;
}

const ProfessionalHeader: React.FC<ProfessionalHeaderProps> = ({
  title,
  buttonText,
  onButtonClick,
  showButton = false
}) => {
  return (
    <Paper
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 2,
        mb: 4,
        bgcolor: '#002856',
        color: '#fff',
        borderRadius: 3,
        boxShadow: 4,
        height: '72px'
      }}
    >
      <Box sx={{ flex: '0 0 80px', display: 'flex', alignItems: 'center' }}>
        <img 
          src="/images/logo-cufre-2.png" 
          alt="Logo CUFRE" 
          style={{ height: 56, objectFit: 'contain' }} 
        />
      </Box>
      
      <Typography 
        variant="h4" 
        sx={{ 
          flex: 1, 
          textAlign: 'center', 
          fontWeight: 'bold', 
          letterSpacing: 1 
        }}
      >
        {title}
      </Typography>
      
      {showButton && buttonText && onButtonClick && (
        <Button
          variant="contained"
          color="secondary"
          onClick={onButtonClick}
          sx={{ fontWeight: 'bold', boxShadow: 2 }}
        >
          {buttonText}
        </Button>
      )}
      
      {!showButton && (
        <Box sx={{ flex: '0 0 80px' }} />
      )}
    </Paper>
  );
};

export default ProfessionalHeader;