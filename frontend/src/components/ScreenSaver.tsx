import React, { useState, useEffect, useCallback } from 'react';
import { useScreenSaver } from '../context/ScreenSaverContext';
import { SCREENSAVER_CONFIG } from '../config/screensaver';
import '../styles/ScreenSaver.css';

interface ScreenSaverProps {
  className?: string;
  showText?: boolean;
  customText?: string;
  customSubtitle?: string;
}

const ScreenSaver: React.FC<ScreenSaverProps> = ({
  className = '',
  showText = true,
  customText = 'Sistema protegido',
  customSubtitle = 'Mueva el mouse o presione cualquier tecla para continuar'
}) => {
  const { isScreenSaverActive, deactivateScreenSaver } = useScreenSaver();
  const [isVisible, setIsVisible] = useState(false);
  const [logoLoaded, setLogoLoaded] = useState(false);
  const [logoError, setLogoError] = useState(false);

  // Manejar la visibilidad con animación
  useEffect(() => {
    if (isScreenSaverActive) {
      setIsVisible(true);
      // Prevenir scroll del body cuando el salvapantallas está activo
      document.body.style.overflow = 'hidden';
    } else {
      // Delay para permitir la animación de salida
      const timer = setTimeout(() => {
        setIsVisible(false);
        document.body.style.overflow = '';
      }, SCREENSAVER_CONFIG.FADE_DURATION);
      
      return () => clearTimeout(timer);
    }
  }, [isScreenSaverActive]);

  // Manejar eventos de actividad para desactivar el salvapantallas
  const handleActivity = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (isScreenSaverActive) {
      deactivateScreenSaver();
    }
  }, [isScreenSaverActive, deactivateScreenSaver]);

  // Handler específico para eventos touch
  const handleTouchActivity = useCallback((event: React.TouchEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (isScreenSaverActive) {
      deactivateScreenSaver();
    }
  }, [isScreenSaverActive, deactivateScreenSaver]);

  // Manejar carga del logo
  const handleLogoLoad = useCallback(() => {
    setLogoLoaded(true);
    setLogoError(false);
  }, []);

  // Manejar error del logo
  const handleLogoError = useCallback(() => {
    setLogoError(true);
    setLogoLoaded(false);
    console.warn('⚠️ Error al cargar el logo del salvapantallas');
  }, []);

  // Manejar eventos de teclado globalmente
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isScreenSaverActive) {
        event.preventDefault();
        event.stopPropagation();
        deactivateScreenSaver();
      }
    };

    if (isScreenSaverActive) {
      document.addEventListener('keydown', handleKeyDown, true);
      document.addEventListener('keyup', handleKeyDown, true);
      document.addEventListener('keypress', handleKeyDown, true);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
      document.removeEventListener('keyup', handleKeyDown, true);
      document.removeEventListener('keypress', handleKeyDown, true);
    };
  }, [isScreenSaverActive, deactivateScreenSaver]);

  // Prevenir menú contextual
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    return false;
  }, []);

  // No renderizar si no está visible
  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={`screensaver-overlay ${!isScreenSaverActive ? 'fade-out' : ''} ${className}`}
      onClick={handleActivity}
      onMouseDown={handleActivity}
      onMouseMove={handleActivity}
      onTouchStart={handleTouchActivity}
      onContextMenu={handleContextMenu}
      role="dialog"
      aria-modal="true"
      aria-label="Salvapantallas de seguridad"
      tabIndex={-1}
    >
      <div className="screensaver-content">
        {/* Logo CUFRE */}
        <div style={{ position: 'relative' }}>
          {!logoError && (
            <img
              src={SCREENSAVER_CONFIG.LOGO_PATH}
              alt="Logo CUFRE"
              className={`screensaver-logo ${!SCREENSAVER_CONFIG.PULSE_ANIMATION ? 'no-animation' : ''}`}
              onLoad={handleLogoLoad}
              onError={handleLogoError}
              draggable={false}
              style={{
                opacity: logoLoaded ? 1 : 0,
                transition: 'opacity 0.3s ease-in-out'
              }}
            />
          )}
          
          {/* Fallback si el logo no carga */}
          {logoError && (
            <div
              className="screensaver-logo"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                color: '#666',
                fontSize: '1.5rem',
                fontWeight: 'bold',
                width: '300px',
                height: '200px'
              }}
            >
              CUFRE
            </div>
          )}
          
          {/* Indicador de carga */}
          {!logoLoaded && !logoError && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '40px',
                height: '40px',
                border: '3px solid #f3f3f3',
                borderTop: '3px solid #1976d2',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}
            />
          )}
        </div>

        {/* Texto informativo */}
        {showText && (
          <>
            <div className="screensaver-text">
              {customText}
            </div>
            <div className="screensaver-subtitle">
              {customSubtitle}
            </div>
          </>
        )}
      </div>

      {/* Estilos adicionales para el spinner */}
      <style>{`
        @keyframes spin {
          0% { transform: translate(-50%, -50%) rotate(0deg); }
          100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default ScreenSaver;