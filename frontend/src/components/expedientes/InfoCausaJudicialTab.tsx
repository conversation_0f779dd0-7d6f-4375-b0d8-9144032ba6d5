import React from 'react';
import {
  TextField,
  MenuItem,
  Box,
  Typography,
  Divider,
  Tooltip
} from '@mui/material';
import { Expediente } from '../../types/expediente.types';

interface InfoCausaJudicialTabProps {
  expediente: Expediente;
  onChange: (field: keyof Expediente, value: any) => void;
}

const jurisdicciones = [
  'Federal',
  'Provincial',
  'Municipal',
  'Nacional',
  'Otra'
];

const tiposCaptura = [
  'OTRO',
  'NACIONAL',
  'INTERNACIONAL',
  'NACIONAL E INTERNACIONAL'
];

const provinciasArgentinas = [
  "CABA", "Buenos Aires", "Catamarca", "Chaco", "Chubut", "Córdoba", "Corrientes", "Entre Ríos",
  "Formosa", "Jujuy", "La Pampa", "La Rioja", "Mendoza", "Misiones", "Neuquén", "Río Negro",
  "Salta", "San Juan", "San Luis", "Santa Cruz", "Santa Fe", "Santiago del Estero", "Tierra del Fuego", "Tucumán"
];

const InfoCausaJudicialTab: React.FC<InfoCausaJudicialTabProps> = ({ expediente, onChange }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;
    onChange(name as keyof Expediente, fieldValue);
  };

  return (
    <Box component="form" noValidate autoComplete="off">
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
        Información de la Causa Judicial
      </Typography>
      <Divider sx={{ mb: 3 }} />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              label="Fecha Oficio"
              name="fechaOficio"
              type="date"
              value={expediente.fechaOficio || ''}
              onChange={handleChange}
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              variant="outlined"
            />
          </Box>
          
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              label="Número de Causa"
              name="numeroCausa"
              value={expediente.numeroCausa || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
        
        <Box>
          <TextField
            fullWidth
            required
            label="Carátula"
            name="caratula"
            value={expediente.caratula || ''}
            onChange={handleChange}
            margin="normal"
            helperText="Título o descripción breve del expediente"
            size="small"
            variant="outlined"
          />
        </Box>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              label="Juzgado"
              name="juzgado"
              value={expediente.juzgado || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            />
          </Box>
          
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              label="Secretaría"
              name="secretaria"
              value={expediente.secretaria || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              label="Fiscalía"
              name="fiscalia"
              value={expediente.fiscalia || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            />
          </Box>
          
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Jurisdicción"
              name="jurisdiccion"
              value={expediente.jurisdiccion || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {jurisdicciones.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Provincia"
              name="provincia"
              value={expediente.provincia || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              <MenuItem value="">
                <em>Seleccione...</em>
              </MenuItem>
              {provinciasArgentinas.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              select
              label="Tipo de Captura"
              name="tipoCaptura"
              value={expediente.tipoCaptura || 'OTRO'}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {tiposCaptura.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 1.5rem)' } }}>
            <TextField
              fullWidth
              label="País"
              name="pais"
              value={expediente.pais || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
        
        <Box>
          <TextField
            fullWidth
            multiline
            rows={2}
            label="Motivo de Captura"
            name="motivoCaptura"
            value={expediente.motivoCaptura || ''}
            onChange={handleChange}
            margin="normal"
            size="small"
            variant="outlined"
          />
        </Box>
        
        <Box>
          <TextField
            fullWidth
            multiline
            rows={2}
            label="Disposición del Juzgado"
            name="disposicionJuzgado"
            value={expediente.disposicionJuzgado || ''}
            onChange={handleChange}
            margin="normal"
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default InfoCausaJudicialTab; 