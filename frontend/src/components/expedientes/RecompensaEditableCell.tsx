import React, { useState } from 'react';
import {
  Box,
  Checkbox,
  TextField,
  Select,
  MenuItem,
  IconButton,
  FormControl,
  Typography
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { Expediente } from '../../types/expediente.types';
import { RecompensaUpdateData } from '../../types/recompensa.types';
import { MONEDAS, formatearRecompensa } from '../../utils/monedaUtils';

interface RecompensaEditableCellProps {
  expediente: Expediente;
  onUpdate: (id: number, data: RecompensaUpdateData) => Promise<void>;
}

const RecompensaEditableCell: React.FC<RecompensaEditableCellProps> = ({ 
  expediente, 
  onUpdate 
}) => {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [recompensa, setRecompensa] = useState(expediente.recompensa || false);
  const [monto, setMonto] = useState(expediente.montoRecompensa || '');
  const [moneda, setMoneda] = useState('ARS'); // Por defecto Pesos Argentinos

  const handleEdit = () => {
    setEditing(true);
    setRecompensa(expediente.recompensa || false);
    setMonto(expediente.montoRecompensa || '');
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      await onUpdate(expediente.id!, { 
        recompensa, 
        montoRecompensa: recompensa ? monto : '', 
        moneda: recompensa ? moneda : undefined 
      });
      setEditing(false);
    } catch (error) {
      console.error('Error al actualizar recompensa:', error);
      // Aquí podrías mostrar un mensaje de error al usuario
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    setRecompensa(expediente.recompensa || false);
    setMonto(expediente.montoRecompensa || '');
    setMoneda('ARS');
  };

  if (!editing) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.08)',
            transform: 'scale(1.02)',
            transition: 'all 0.2s ease-in-out'
          },
          p: 1.5,
          borderRadius: 2,
          border: '1px solid #e0e0e0',
          backgroundColor: expediente.recompensa && expediente.montoRecompensa ? '#e8f5e8' : '#f5f5f5',
          minHeight: 48,
          width: '100%'
        }}
        onClick={handleEdit}
      >
        <Box sx={{ flex: 1 }}>
          {expediente.recompensa && expediente.montoRecompensa ? (
            <Box>
              <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                {formatearRecompensa(expediente.montoRecompensa, moneda)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Recompensa activa
              </Typography>
            </Box>
          ) : (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                Sin recompensa
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Click para editar
              </Typography>
            </Box>
          )}
        </Box>
        <IconButton
          size="small"
          sx={{
            backgroundColor: 'rgba(25, 118, 210, 0.1)',
            '&:hover': { backgroundColor: 'rgba(25, 118, 210, 0.2)' }
          }}
        >
          <EditIcon fontSize="small" color="primary" />
        </IconButton>
      </Box>
    );
  }

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      gap: 1.5,
      p: 2,
      border: '2px solid #1976d2',
      borderRadius: 2,
      backgroundColor: '#f8f9ff',
      width: '100%',
      maxWidth: '100%'
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Checkbox
          checked={recompensa}
          onChange={(e) => setRecompensa(e.target.checked)}
          size="small"
          color="primary"
        />
        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
          Tiene recompensa
        </Typography>
      </Box>
      
      {recompensa && (
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            size="small"
            value={monto}
            onChange={(e) => setMonto(e.target.value)}
            placeholder="Ingrese monto"
            type="number"
            sx={{
              flex: 1,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white'
              }
            }}
            InputProps={{
              startAdornment: <Typography sx={{ mr: 0.5, color: 'text.secondary' }}>💰</Typography>
            }}
          />
          <FormControl size="small" sx={{ minWidth: 90 }}>
            <Select
              value={moneda}
              onChange={(e) => setMoneda(e.target.value)}
              sx={{
                backgroundColor: 'white',
                '& .MuiSelect-select': {
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }
              }}
            >
              {MONEDAS.map(m => (
                <MenuItem key={m.codigo} value={m.codigo}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Typography>{m.simbolo}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {m.codigo}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}
      
      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', mt: 0.5 }}>
        <IconButton
          size="small"
          onClick={handleSave}
          disabled={loading}
          sx={{
            backgroundColor: '#4caf50',
            color: 'white',
            '&:hover': { backgroundColor: '#45a049' },
            '&:disabled': { backgroundColor: '#cccccc' }
          }}
        >
          <SaveIcon fontSize="small" />
        </IconButton>
        <IconButton
          size="small"
          onClick={handleCancel}
          disabled={loading}
          sx={{
            backgroundColor: '#f44336',
            color: 'white',
            '&:hover': { backgroundColor: '#da190b' },
            '&:disabled': { backgroundColor: '#cccccc' }
          }}
        >
          <CancelIcon fontSize="small" />
        </IconButton>
      </Box>
    </Box>
  );
};

export default RecompensaEditableCell;

export {};