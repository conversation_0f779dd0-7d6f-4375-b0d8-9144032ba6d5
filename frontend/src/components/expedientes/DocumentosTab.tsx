import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  TextField,
  MenuItem,
  IconButton,
  Stack,
  Chip,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  AttachFile as AttachFileIcon, 
  Delete as DeleteIcon,
  RemoveRedEye as RemoveRedEyeIcon,
  PictureAsPdf as PdfIcon,
  InsertDriveFile as FileIcon,
  Description as TextIcon,
  Image as ImageIcon,
  TableChart as ExcelIcon,
  Code as CodeIcon,
  Archive as ArchiveIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon
} from '@mui/icons-material';
import { Expediente, Documento } from '../../types/expediente.types';
import expedienteService from '../../api/expedienteService';

interface DocumentosTabProps {
  expediente: Expediente;
  onChange: (field: keyof Expediente, value: any) => void;
}

const tiposDocumento = [
  'Orden de Captura',
  'Orden Judicial',
  'Oficio',
  'Informe',
  'Perita<PERSON>',
  'Declaración',
  'Denuncia',
  'Sentencia',
  'Otro'
];

const DocumentosTab: React.FC<DocumentosTabProps> = ({ expediente, onChange }) => {
  const [newDocumento, setNewDocumento] = useState<Partial<Documento>>({
    url: '',
    nombre: '',
    tipo: '',
    descripcion: ''
  });
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewDocumento(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        if (event.target?.result) {
          setNewDocumento(prev => ({
            ...prev,
            file: file, // Guardar el archivo para enviar luego
            url: event.target?.result as string,
            nombre: file.name
          }));
        }
      };
      
      reader.readAsDataURL(file);
    }
  };

  const handleAddDocumento = async () => {
    if (!newDocumento.url || !newDocumento.tipo || !newDocumento.nombre || !expediente.id || !newDocumento.file) {
      setError("Falta información o el expediente no ha sido guardado aún");
      return;
    }
    
    setUploading(true);
    setError(null);
    
    try {
      // Preparar FormData para enviar al servidor
      const formData = new FormData();
      
      // Obtener el tipo MIME del archivo y asegurarse de que no exceda 50 caracteres
      const file = newDocumento.file;
      let fileType = file.type || '';
      
      // Si el tipo MIME es muy largo, truncarlo o usar una versión simplificada
      if (fileType.length > 50) {
        // Extraer la parte principal del tipo MIME (ej: 'application/pdf' de 'application/pdf;charset=UTF-8')
        const mainType = fileType.split(';')[0];
        
        // Si aún es muy largo, usar una versión genérica basada en la extensión
        if (mainType.length > 50) {
          const extension = file.name.split('.').pop()?.toLowerCase() || '';
          switch (extension) {
            case 'pdf': fileType = 'application/pdf'; break;
            case 'doc': case 'docx': fileType = 'application/msword'; break;
            case 'xls': case 'xlsx': fileType = 'application/excel'; break;
            case 'jpg': case 'jpeg': fileType = 'image/jpeg'; break;
            case 'png': fileType = 'image/png'; break;
            default: fileType = 'application/octet-stream';
          }
        } else {
          fileType = mainType;
        }
      }
      
      // Agregar el archivo con su tipo MIME limitado
      const fileWithCorrectType = new File([file], file.name, { type: fileType });
      formData.append('file', fileWithCorrectType);
      formData.append('tipo', newDocumento.tipo);
      formData.append('descripcion', newDocumento.descripcion || '');
      
      console.log('Tipo de archivo enviado:', fileType, '(longitud:', fileType.length, ')');
      
      // Llamar a la API para subir el documento
      const uploadedDoc = await expedienteService.uploadDocumento(expediente.id, formData);
      
      // Actualizar el estado con la respuesta del servidor
      const updatedDocumentos = [
        ...expediente.documentos,
        {
          id: uploadedDoc.id,
          url: uploadedDoc.rutaArchivo, // Usar la ruta devuelta por el servidor
          nombre: newDocumento.nombre,
          tipo: newDocumento.tipo,
          descripcion: newDocumento.descripcion || ''
        }
      ];
      
      onChange('documentos', updatedDocumentos);
      
      // Resetear formulario
      setNewDocumento({
        url: '',
        nombre: '',
        tipo: '',
        descripcion: ''
      });
    } catch (error) {
      console.error('Error al subir documento:', error);
      setError('Error al subir el documento. Por favor, intente nuevamente.');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteDocumento = async (id: number | undefined) => {
    if (!id || !expediente.id) return;
    setUploading(true);
    setError(null);
    try {
      await expedienteService.deleteDocumento(expediente.id, id);
      const updatedDocumentos = expediente.documentos.filter(doc => doc.id !== id);
      onChange('documentos', updatedDocumentos);
    } catch (error) {
      setError('Error al eliminar el documento. Intenta nuevamente.');
    } finally {
      setUploading(false);
    }
  };

  const handleViewDocumento = (id: number | undefined) => {
    if (!id) return;
    // Importar la utilidad para obtener la URL correcta según el entorno
    import('../../utils/urlHelper').then(module => {
      const url = module.getDocumentoUrl(id);
      window.open(url, '_blank');
    });
  };

  const getFileIcon = (fileName: string | undefined) => {
    if (!fileName) return <FileIcon />;
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (!extension) return <FileIcon />;
    
    // Documentos PDF
    if (extension === 'pdf') return <PdfIcon sx={{ color: '#d32f2f' }} />;
    
    // Documentos de texto
    if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
      return <TextIcon sx={{ color: '#1976d2' }} />;
    }
    
    // Hojas de cálculo
    if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      return <ExcelIcon sx={{ color: '#2e7d32' }} />;
    }
    
    // Imágenes
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'].includes(extension)) {
      return <ImageIcon sx={{ color: '#ff9800' }} />;
    }
    
    // Audio
    if (['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'].includes(extension)) {
      return <AudioIcon sx={{ color: '#9c27b0' }} />;
    }
    
    // Video
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension)) {
      return <VideoIcon sx={{ color: '#f44336' }} />;
    }
    
    // Archivos comprimidos
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      return <ArchiveIcon sx={{ color: '#795548' }} />;
    }
    
    // Código/programación
    if (['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'php', 'rb', 'go', 'rs'].includes(extension)) {
      return <CodeIcon sx={{ color: '#00bcd4' }} />;
    }
    
    // Archivo genérico
    return <FileIcon sx={{ color: '#757575' }} />;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Documentos
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      <Box sx={{ mb: 4, p: 2, border: '1px dashed grey', borderRadius: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Agregar Nuevo Documento
        </Typography>
        
        <Stack spacing={2}>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: { xs: 'wrap', md: 'nowrap' } }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<AttachFileIcon />}
              sx={{ flexGrow: 1, minWidth: '200px' }}
              disabled={uploading || !expediente.id}
            >
              Seleccionar Archivo
              <input
                type="file"
                hidden
                onChange={handleFileChange}
              />
            </Button>
            
            <TextField
              select
              label="Tipo de Documento"
              name="tipo"
              value={newDocumento.tipo || ''}
              onChange={handleInputChange}
              sx={{ flexGrow: 2 }}
              disabled={uploading}
            >
              {tiposDocumento.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          
          <TextField
            fullWidth
            label="Descripción"
            name="descripcion"
            value={newDocumento.descripcion || ''}
            onChange={handleInputChange}
            multiline
            rows={2}
            disabled={uploading}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {newDocumento.nombre && (
              <Typography variant="body2" color="text.secondary">
                Archivo seleccionado: {newDocumento.nombre}
              </Typography>
            )}
            
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDocumento}
              disabled={uploading || !newDocumento.url || !newDocumento.tipo || !expediente.id}
            >
              {uploading ? <CircularProgress size={24} color="inherit" /> : 'Agregar Documento'}
            </Button>
          </Box>
        </Stack>
        
        {!expediente.id && (
          <Typography color="error" sx={{ mt: 2 }}>
            Debe guardar el expediente antes de poder agregar documentos.
          </Typography>
        )}
      </Box>
      
      <Divider sx={{ my: 3 }} />
      
      <Typography variant="subtitle1" gutterBottom>
        Documentos Registrados ({expediente.documentos.length})
      </Typography>
      
      {expediente.documentos.length === 0 ? (
        <Typography color="text.secondary" sx={{ fontStyle: 'italic', mt: 2 }}>
          No hay documentos registrados para este expediente.
        </Typography>
      ) : (
        <Stack spacing={2} sx={{ mt: 2 }}>
          {expediente.documentos.map((doc) => (
            <Card key={doc.id} variant="outlined">
              <CardContent sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  {getFileIcon(doc.nombre)}
                  <Typography variant="subtitle1" noWrap sx={{ flexGrow: 1 }}>
                    {doc.nombre}
                  </Typography>
                  <Chip label={doc.tipo} size="small" color="primary" variant="outlined" />
                </Box>
                
                <Typography variant="body2" color="text.secondary">
                  {doc.descripcion || 'Sin descripción'}
                </Typography>
              </CardContent>
              
              <CardActions>
                <IconButton 
                  size="small" 
                  color="primary"
                  onClick={() => handleViewDocumento(doc.id)}
                  aria-label="Ver documento"
                >
                  <RemoveRedEyeIcon />
                </IconButton>
                
                <IconButton 
                  size="small" 
                  color="error" 
                  onClick={() => handleDeleteDocumento(doc.id)}
                  aria-label="Eliminar documento"
                >
                  <DeleteIcon />
                </IconButton>
              </CardActions>
            </Card>
          ))}
        </Stack>
      )}
    </Box>
  );
};

export default DocumentosTab; 