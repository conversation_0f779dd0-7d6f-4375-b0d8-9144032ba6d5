import React from 'react';

const logoMinisterio = '/images/logo_ministerio_seguridad_nacional.png';
const logoCufre = '/images/logo-cufre-2.png';

export interface ModalWantedProps {
  expediente: any;
  open: boolean;
  onClose: () => void;
  onDetalle: () => void;
}

const ModalWanted: React.FC<ModalWantedProps> = ({ expediente, open, onClose, onDetalle }) => {
  if (!open || !expediente) return null;

  // Buscar foto principal
  const fotoPrincipal = expediente.fotografias?.find((f: any) => f.id === expediente.fotoPrincipalId);
  
  // Obtener URL de la foto utilizando la misma lógica que en MasBuscadosPage
  const getFotoUrl = (foto: any) => {
    if (!foto || !foto.rutaArchivo) return ''; 
    if (foto.rutaArchivo.startsWith('http')) {
      return foto.rutaArchivo;
    }
    // Asegurarse de que la ruta tenga el prefijo /api/ necesario para uploads
    if (foto.rutaArchivo.startsWith('/uploads/')) {
      return `/api${foto.rutaArchivo}`;
    }
    return foto.rutaArchivo.startsWith('/') ? foto.rutaArchivo : `/${foto.rutaArchivo}`;
  };
  
  const fotoUrl = getFotoUrl(fotoPrincipal) || logoCufre;

  // Nombre del prófugo (imputado principal)
  const imputado = expediente.personaExpedientes?.find((p: any) => (p.tipoRelacion || '').toLowerCase() === 'imputado');
  const nombre = imputado && imputado.persona
    ? `${imputado.persona.nombre || ''} ${imputado.persona.apellido || ''}`.trim().toUpperCase()
    : '-';

  // Definir interfaces para manejar los delitos agrupados
  interface DelitoInfo {
    id: number;
    nombre?: string;
    codigoPenal?: string;
  }

  interface DelitoAgrupado {
    delito: DelitoInfo;
    cantidad: number;
  }

  // Agrupar delitos para evitar repeticiones
  const delitosAgrupados: DelitoAgrupado[] = [];
  
  if (expediente.delitos && expediente.delitos.length > 0) {
    // Mapa para agrupar delitos por su ID
    const delitosMap = new Map<string, DelitoAgrupado>();
    
    // Procesar cada delito del expediente
    expediente.delitos.forEach((delito: any) => {
      let delitoInfo: DelitoInfo | null = null;
      
      // Extraer la información del delito según su estructura
      if (delito.delito && typeof delito.delito === 'object') {
        // Caso: relación completa con objeto delito anidado
        delitoInfo = {
          id: delito.delito.id,
          nombre: delito.delito.nombre,
          codigoPenal: delito.delito.codigoPenal
        };
      } else if (delito.delitoId && typeof delito.delitoId === 'number') {
        // Caso: solo tiene el ID del delito
        delitoInfo = {
          id: delito.delitoId
        };
      } else if (typeof delito === 'object' && delito.id && !delito.delito) {
        // Caso: el delito es un objeto plano
        delitoInfo = {
          id: delito.id,
          nombre: delito.nombre || delito.descripcion,
          codigoPenal: delito.codigoPenal
        };
      } else if (typeof delito === 'number') {
        // Caso: el delito es solo un ID numérico
        delitoInfo = {
          id: delito
        };
      }
      
      // Si no pudimos extraer la información, omitir este delito
      if (!delitoInfo) {
        return;
      }
      
      // Usar el ID del delito como clave de agrupación
      const key = String(delitoInfo.id);
      
      // Actualizar el contador para este delito
      if (delitosMap.has(key)) {
        const grupo = delitosMap.get(key)!;
        grupo.cantidad += 1;
      } else {
        delitosMap.set(key, {
          delito: delitoInfo,
          cantidad: 1
        });
      }
    });
    
    // Convertir el mapa a un array para renderizar
    delitosAgrupados.push(...Array.from(delitosMap.values()));
  }
  
  // Extraer los nombres de los delitos para mostrar
  const delitos = delitosAgrupados.map(grupo => {
    const nombreDelito = grupo.delito.nombre || `Artículo ${grupo.delito.id || 'No especificado'}`;
    // No usamos un string simple, sino un objeto con la información necesaria para renderizar
    return {
      nombre: nombreDelito,
      cantidad: grupo.cantidad
    };
  });

  // Recompensa // Eliminada la variable local 'recompensa' no utilizada
  // const recompensa = expediente.recompensa;

  return (
    <div style={{
      position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',
      background: 'rgba(0,0,0,0.6)', display: 'flex', alignItems: 'flex-start', justifyContent: 'center', zIndex: 2000, paddingTop: 80
    }}>
      <div style={{
        background: '#fff', borderRadius: 16, boxShadow: '0 8px 32px #0008', minWidth: 500, maxWidth: 700, padding: 0, overflow: 'hidden', border: '6px solid #1a237e', position: 'relative', fontFamily: 'sans-serif'
      }}>
        {/* Encabezado */}
        <div style={{ display: 'flex', alignItems: 'center', background: '#1a237e', padding: '18px 18px 10px 18px', borderBottom: '3px solid #1a237e' }}>
          <img src={logoMinisterio} alt="Logo Ministerio" style={{ width: 60, marginRight: 18, borderRadius: 0, border: 'none', background: 'none', boxShadow: 'none' }} />
          <div style={{ flex: 1, textAlign: 'center' }}>
            <div style={{ fontSize: 32, fontWeight: 900, color: '#fff', letterSpacing: 2, fontFamily: 'serif', marginBottom: 2 }}>BUSCADO</div>
            <div style={{ fontSize: 13, fontWeight: 700, color: '#fff', marginBottom: 0 }}>POR EL MINISTERIO DE SEGURIDAD NACIONAL</div>
          </div>
        </div>
        {/* Nombre */}
        <div style={{ fontSize: 32, fontWeight: 900, color: '#1a237e', textAlign: 'center', margin: '28px 0 10px 0', textTransform: 'uppercase', letterSpacing: 1, fontFamily: 'serif' }}>{nombre}</div>
        {/* Foto */}
        <div style={{ display: 'flex', justifyContent: 'center', margin: '0 0 18px 0' }}>
          <img src={fotoUrl} alt="Foto" style={{ width: 220, height: 220, objectFit: 'cover', borderRadius: 12, border: '6px solid #fff', boxShadow: '0 4px 24px #0004', background: '#fff' }} />
        </div>
        {/* Delitos como lista */}
        {delitos.length > 0 && (
          <ul style={{
            fontSize: 18,
            color: '#222',
            fontWeight: 600,
            textAlign: 'center',
            margin: '0 32px 18px 32px',
            lineHeight: 1.5,
            listStyle: 'disc',
            padding: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 6
          }}>
            {delitos.map((delito, idx) => (
              <li key={idx} style={{ width: '100%', textAlign: 'left', margin: 0, padding: 0 }}>
                {delito.nombre}
                {delito.cantidad > 1 && (
                  <span style={{ color: '#b22222', fontWeight: 'bold' }}> ({delito.cantidad})</span>
                )}
              </li>
            ))}
          </ul>
        )}
        {/* Recompensa */}
        {expediente.recompensa && expediente.montoRecompensa && (
          <div style={{
            background: '#e3e7fd', color: '#b22222', fontWeight: 900, fontSize: 22, textAlign: 'center', borderRadius: 10, padding: '10px 0', margin: '10px 30px', border: '2px solid #1a237e', letterSpacing: 1
          }}>
            RECOMPENSA: ${expediente.montoRecompensa}
          </div>
        )}
        {/* Pie */}
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: 18, background: '#e3e7fd', borderTop: '2px solid #1a237e' }}>
          <button onClick={onDetalle} style={{
            background: '#b22222', color: '#fff', fontWeight: 700, border: 'none', borderRadius: 8, padding: '10px 22px', fontSize: 16, cursor: 'pointer', marginRight: 12
          }}>Ver Detalle Completo</button>
          <button onClick={onClose} style={{
            background: '#eee', color: '#333', fontWeight: 500, border: 'none', borderRadius: 8, padding: '8px 16px', fontSize: 14, cursor: 'pointer'
          }}>Cerrar</button>
        </div>
      </div>
    </div>
  );
};

export default ModalWanted; 