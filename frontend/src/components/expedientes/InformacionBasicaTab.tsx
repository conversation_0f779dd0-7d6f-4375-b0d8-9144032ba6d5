import React from 'react';
import {
  TextField,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Box,
  Typography,
  Divider,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { Expediente } from '../../types/expediente.types';
import expedienteService from '../../api/expedienteService';
import { useAuth } from '../../context/AuthContext';
import { Rol } from '../../types/usuario.types';

interface InformacionBasicaTabProps {
  expediente: Expediente;
  onChange: (field: keyof Expediente, value: any) => void;
  onCreateExpediente?: () => void;
  expedienteId?: number;
  loading?: boolean;
}

const estados = [
  'SIN EFECTO',
  'CAPTURA VIGENTE',
  'DETENIDO'
];

const jurisdicciones = [
  'Federal',
  'Provincial',
  'Municipal',
  'Otra'
];

const instancias = [
  'Primera Instancia',
  'Segunda Instancia',
  'Casación',
  'Corte Suprema',
  'Otra'
];

const fuerzasAsignadas = [
  'S/D',
  'GNA',
  'PFA',
  'PSA',
  'PNA',
  'SPF',
  'POL LOCAL',
  'INTERPOL',
  'AMERIPOL',
  'EUROPOL',
  'BLOQUE DE BÚSQUEDA CUFRE'
];

const InformacionBasicaTab: React.FC<InformacionBasicaTabProps> = ({ expediente, onChange, onCreateExpediente, expedienteId, loading }) => {
  const [loadingNumero, setLoadingNumero] = React.useState(false);
  const { user } = useAuth();
  
  // Determinar si el usuario puede editar el número de expediente
  const puedeEditarNumero = user?.rol === Rol.SUPERUSUARIO || user?.rol === Rol.ADMINISTRADOR;
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    let fieldValue: any = type === 'checkbox' ? checked : value;
    
    // Si el campo es estadoSituacion, guardar en mayúsculas
    if (name === 'estadoSituacion') {
      // Convertir a mayúsculas si hay un valor
      fieldValue = value ? value.toUpperCase() : null;
    }
    
    onChange(name as keyof Expediente, fieldValue);
  };

  // Asignar fecha actual para nuevos expedientes
  React.useEffect(() => {
    if (!expediente.fechaInicio) {
      const today = new Date().toISOString().split('T')[0];
      onChange('fechaInicio', today);
    }
  }, [expediente.fechaInicio, onChange]);
  
  // Estado para controlar si ya se ha solicitado un número para un nuevo expediente
  const [numeroSolicitado, setNumeroSolicitado] = React.useState<boolean>(false);
  
  // Efecto que se ejecuta una sola vez al montar el componente
  React.useEffect(() => {
    // Determinar si estamos en modo edición o creación
    const esModoEdicion = !!expedienteId;
    
    if (esModoEdicion) {
      // En modo edición, no hacemos nada con el número
      console.log('Modo edición detectado, ID:', expedienteId, 'Manteniendo número original:', expediente.numero);
    } else if (!numeroSolicitado && (!expediente.numero || expediente.numero === '')) {
      // Solo en modo creación y si no hemos solicitado un número todavía
      console.log('Modo creación detectado, solicitando nuevo número');
      
      const getNextNumber = async () => {
        try {
          setLoadingNumero(true);
          const nextNumber = await expedienteService.getNextExpedienteNumber();
          console.log('Nuevo número obtenido para expediente nuevo:', nextNumber);
          onChange('numero', nextNumber.toString());
          // Marcar que ya hemos solicitado un número para no volver a hacerlo
          setNumeroSolicitado(true);
        } catch (error) {
          console.error('Error al obtener el próximo número de expediente:', error);
          onChange('numero', '5000');
          setNumeroSolicitado(true);
        } finally {
          setLoadingNumero(false);
        }
      };
      
      getNextNumber();
    }
  // Este efecto solo debe ejecutarse una vez al montar el componente
  // o si cambia el ID del expediente (lo que indica un cambio entre creación/edición)
  }, [expedienteId, expediente.numero, onChange, numeroSolicitado]);


  return (
    <Box component="form" noValidate autoComplete="off">
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
        Información Básica del Expediente
      </Typography>
      <Divider sx={{ mb: 3 }} />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 2rem)' } }}>
            <Tooltip title="Este número es único para cada expediente" arrow>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TextField
                  fullWidth
                  required
                  label="Número de Expediente"
                  name="numero"
                  value={expediente.numero}
                  onChange={puedeEditarNumero ? handleChange : undefined}
                  InputProps={{
                    readOnly: !puedeEditarNumero,
                    endAdornment: loadingNumero ? <CircularProgress size={20} /> : null
                  }}
                  margin="normal"
                  helperText={puedeEditarNumero
                    ? "Número de expediente (editable por administradores)"
                    : "Número de expediente asignado automáticamente (a partir de 5000)"
                  }
                  size="small"
                  variant="outlined"
                />
                {!expedienteId && onCreateExpediente && (
                  <button
                    type="button"
                    style={{ height: 40 }}
                    onClick={onCreateExpediente}
                    disabled={loading || !expediente.numero || loadingNumero}
                  >
                    Crear expediente
                  </button>
                )}
              </Box>
            </Tooltip>
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 2rem)' } }}>
            <TextField
              fullWidth
              required
              label="Fecha de Inicio"
              name="fechaInicio"
              type="date"
              value={expediente.fechaInicio}
              onChange={handleChange}
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
        <Box>
          <TextField
            fullWidth
            required
            label="Carátula"
            name="caratula"
            value={expediente.caratula}
            onChange={handleChange}
            margin="normal"
            helperText="Título o descripción breve del expediente"
            size="small"
            variant="outlined"
          />
        </Box>
        <Divider sx={{ my: 2 }} />
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Estado"
              name="estadoSituacion"
              value={expediente.estadoSituacion || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {estados.map((option) => (
                <MenuItem key={option} value={option.toUpperCase()}>
                  {option.toUpperCase()}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Fuerza Asignada"
              name="fuerzaAsignada"
              value={expediente.fuerzaAsignada || 'S/D'}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {fuerzasAsignadas.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Jurisdicción"
              name="jurisdiccion"
              value={expediente.jurisdiccion || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {jurisdicciones.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Instancia"
              name="instancia"
              value={expediente.instancia || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              {instancias.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              select
              label="Autorización de Tareas"
              name="autorizacionTareas"
              value={expediente.autorizacionTareas || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
            >
              <MenuItem value="SI">SI</MenuItem>
              <MenuItem value="NO">NO</MenuItem>
            </TextField>
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(33.33% - 2rem)' } }}>
            <TextField
              fullWidth
              label="Fecha Autorización de Tareas"
              name="fechaAutorizacionTareas"
              type="date"
              value={expediente.fechaAutorizacionTareas || ''}
              onChange={handleChange}
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
        <Box>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Descripción/Observaciones"
            name="descripcion"
            value={expediente.descripcion || ''}
            onChange={handleChange}
            margin="normal"
            helperText="Describa brevemente el contenido o propósito del expediente"
            size="small"
            variant="outlined"
          />
        </Box>
        {/* Campo: ¿Tiene recompensa? */}
        <Box>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!expediente.recompensa}
                onChange={e => onChange('recompensa', e.target.checked)}
                name="recompensa"
                color="primary"
              />
            }
            label="¿Tiene recompensa?"
          />
        </Box>
        {/* Campo: Monto de la recompensa */}
        {expediente.recompensa && (
          <Box>
            <TextField
              fullWidth
              label="Monto de la recompensa"
              name="montoRecompensa"
              value={expediente.montoRecompensa || ''}
              onChange={handleChange}
              margin="normal"
              size="small"
              variant="outlined"
              helperText="Ingrese el monto solo si corresponde"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default InformacionBasicaTab; 