import React, { useEffect, useState } from 'react';
import { Box, Typography, IconButton, Tooltip } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useNavigate, useOutletContext } from 'react-router-dom';
import '../../styles/CentroComando.css';

interface CinematicLayoutProps {
  title: string;
  subtitle?: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  onBack?: () => void;
  showBackButton?: boolean;
  backRoute?: string;
}

interface OutletContextType {
  onFullscreenChange?: (fullscreen: boolean) => void;
}

const CinematicLayout: React.FC<CinematicLayoutProps> = ({
  title,
  subtitle,
  icon,
  children,
  onBack,
  showBackButton = true,
  backRoute = '/estadisticas/centro-comando'
}) => {
  const navigate = useNavigate();
  const { onFullscreenChange } = useOutletContext<OutletContextType>() || {};
  const [isFullscreen, setIsFullscreen] = useState(true);

  // Auto-activar fullscreen al montar
  useEffect(() => {
    if (onFullscreenChange) {
      onFullscreenChange(true);
    }
    
    return () => {
      if (onFullscreenChange) {
        onFullscreenChange(false);
      }
    };
  }, [onFullscreenChange]);

  // Manejar tecla ESC para salir de fullscreen
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
        if (onFullscreenChange) {
          onFullscreenChange(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen, onFullscreenChange]);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      // Si es navegación de vuelta al centro de estadísticas, marcar como returnTo
      if (backRoute === '/estadisticas/centro-comando') {
        navigate(backRoute, { state: { returnTo: 'centro-comando' } });
      } else {
        navigate(backRoute);
      }
    }
  };


  return (
    <Box className={`centro-comando-container ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Header cinematográfico */}
      <Box className="centro-comando-header">
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {showBackButton && (
            <Tooltip title="Volver al Centro de Comando" arrow>
              <IconButton
                onClick={handleBack}
                sx={{
                  color: 'var(--cc-cufre)',
                  border: '2px solid var(--cc-cufre)',
                  borderRadius: 2,
                  p: 1,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'var(--cc-cufre)',
                    color: 'var(--cc-bg-primary)',
                    transform: 'scale(1.05)',
                    boxShadow: '0 0 20px var(--cc-glow)'
                  }
                }}
              >
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
          )}
          
          <Box>
            <Typography className="centro-comando-title">
              {icon}
              {title}
            </Typography>
            {subtitle && (
              <Typography className="centro-comando-subtitle">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Contenido principal */}
      <Box sx={{ 
        flex: 1, 
        position: 'relative', 
        zIndex: 5,
        padding: '2rem 3rem',
        overflow: 'auto',
        minHeight: 0
      }}>
        {children}
      </Box>
    </Box>
  );
};

export default CinematicLayout;