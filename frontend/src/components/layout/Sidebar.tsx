import React from 'react';
import { styled } from '@mui/material/styles';
import {
  Drawer,
  List,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Button
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import UserAvatar from '../usuarios/UserAvatar';
import SpaceDashboardRoundedIcon from '@mui/icons-material/SpaceDashboardRounded';
import FolderOpenRoundedIcon from '@mui/icons-material/FolderOpenRounded';
import GavelRoundedIcon from '@mui/icons-material/GavelRounded';
import BarChartRoundedIcon from '@mui/icons-material/BarChartRounded';
import PersonSearchRoundedIcon from '@mui/icons-material/PersonSearchRounded';
import GroupRoundedIcon from '@mui/icons-material/GroupRounded';
import PlaceRoundedIcon from '@mui/icons-material/PlaceRounded';
import SearchIcon from '@mui/icons-material/Search';
import SchoolIcon from '@mui/icons-material/School';
import HelpOutlineRoundedIcon from '@mui/icons-material/HelpOutlineRounded';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import CampaignIcon from '@mui/icons-material/Campaign';
import AnnouncementIcon from '@mui/icons-material/Announcement';
import SettingsIcon from '@mui/icons-material/Settings';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { Rol } from '../../types/usuario.types';

const drawerWidth = 240;

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
  justifyContent: 'flex-end',
}));

interface SidebarProps {
  open: boolean;
  handleDrawerClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ open, handleDrawerClose }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  

  const handleNavigation = (path: string) => {
    // Si es navegación al centro de estadísticas desde sidebar, marcar el origen
    if (path === '/estadisticas/centro-comando') {
      navigate(path, { state: { fromSidebar: true } });
    } else {
      navigate(path);
    }
  };

  // Helpers de visibilidad por rol
  const isSuper = user?.rol === Rol.SUPERUSUARIO;
  const isAdmin = user?.rol === Rol.ADMINISTRADOR;
  const isCarga = user?.rol === Rol.USUARIOCARGA;
  const isConsulta = user?.rol === Rol.USUARIOCONSULTA;

  // Función para saber si el usuario puede ver un menú
  const canSee = (menu: string) => {
    if (isSuper) return true;
    if (isAdmin) {
      if (menu === 'usuarios') return false;
      return true;
    }
    if (isCarga) {
      if (menu === 'usuarios' || menu === 'actividad' || menu === 'crear-delito' || menu === 'anuncios' || menu === 'configurar-variables') return false;
      return true;
    }
    if (isConsulta) {
      return ['consulta', 'estadisticas', 'masbuscados', 'soporte', 'pn-recompensas'].includes(menu);
    }
    return false;
  };

  return (
    <Drawer
      className="no-print"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          backgroundColor: '#1c2536',
          color: '#fff',
        },
      }}
      variant="persistent"
      anchor="left"
      open={open}
    >
      <DrawerHeader>
        <IconButton onClick={handleDrawerClose} sx={{ color: '#fff' }}>
          <ChevronLeftIcon />
        </IconButton>
      </DrawerHeader>
      <Divider />
      {user && (
        <Box sx={{ p: 2 }}>
          {/* Header con avatar y nombre */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <UserAvatar
              usuario={{
                ...user,
                username: user.email
              }}
              size="large"
            />
            <Box sx={{ ml: 2, flex: 1 }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
                {user.nombre} {user.apellido}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                {user.email}
              </Typography>
              <Typography variant="caption" color="primary" sx={{ fontWeight: 500 }}>
                {user.rol}
              </Typography>
            </Box>
          </Box>

          {/* Botones de acción */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<PersonIcon />}
              onClick={() => navigate('/perfil')}
              size="small"
            >
              Mi Perfil
            </Button>
            <Button
              fullWidth
              variant="outlined"
              color="error"
              onClick={logout}
              size="small"
            >
              Cerrar Sesión
            </Button>
          </Box>
        </Box>
      )}
      <List>
        {/* Dashboard */}
        {canSee('dashboard') && (
        <ListItem disablePadding>
          <ListItemButton 
            selected={location.pathname === '/dashboard'}
            onClick={() => handleNavigation('/dashboard')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/dashboard' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <SpaceDashboardRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Dashboard" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Consulta */}
        {canSee('consulta') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/consulta'}
            onClick={() => handleNavigation('/consulta')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/consulta' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <SearchIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Consulta" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Expedientes */}
        {canSee('expedientes') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/expedientes'}
            onClick={() => handleNavigation('/expedientes')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/expedientes' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <FolderOpenRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Expedientes" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Más Buscados */}
        {canSee('masbuscados') && (
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleNavigation('/mas-buscados')}
            selected={location.pathname === '/mas-buscados'}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/mas-buscados' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <PersonSearchRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Más Buscados" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Delitos */}
        {canSee('delitos') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/delitos'}
            onClick={() => handleNavigation('/delitos')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/delitos' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <GavelRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Delitos" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Estadísticas */}
        {canSee('estadisticas') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/estadisticas/centro-comando'}
            onClick={() => handleNavigation('/estadisticas/centro-comando')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/estadisticas/centro-comando' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <BarChartRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Estadísticas" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Mapa */}
        {canSee('estadisticas') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/estadisticas/mapa'}
            onClick={() => handleNavigation('/estadisticas/mapa')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/estadisticas/mapa' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <PlaceRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Mapa" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Usuarios */}
        {canSee('usuarios') && (
        <ListItem disablePadding>
          <ListItemButton 
            selected={location.pathname.startsWith('/usuarios')}
            onClick={() => handleNavigation('/usuarios')}
            sx={{ borderRadius: 2, mb: 1, px: 2, py: 1.5 }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <GroupRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Usuarios" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Configurar Variables */}
        {canSee('configurar-variables') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/configurar-variables'}
            onClick={() => handleNavigation('/configurar-variables')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/configurar-variables' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <SettingsIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Configurar Variables" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* P.N Recompensas */}
        {canSee('pn-recompensas') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname.startsWith('/pn-recompensas')}
            onClick={() => handleNavigation('/pn-recompensas')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname.startsWith('/pn-recompensas') ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <MonetizationOnIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Recompensas" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Tutoriales */}
        {canSee('tutoriales') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname.startsWith('/tutoriales')}
            onClick={() => handleNavigation('/tutoriales')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname.startsWith('/tutoriales') ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <SchoolIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Tutoriales" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Soporte */}
        {canSee('soporte') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/soporte'}
            onClick={() => handleNavigation('/soporte')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/soporte' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <HelpOutlineRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Soporte" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Actividad del Sistema */}
        {canSee('actividad') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/actividad-sistema'}
            onClick={() => handleNavigation('/actividad-sistema')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/actividad-sistema' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <BarChartRoundedIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Actividad del Sistema" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

        {/* Anuncios */}
        {canSee('anuncios') && (
        <ListItem disablePadding>
          <ListItemButton
            selected={location.pathname === '/anuncios'}
            onClick={() => handleNavigation('/anuncios')}
            sx={{
              borderRadius: 2,
              mb: 1,
              px: 2,
              py: 1.5,
              color: '#fff',
              backgroundColor: location.pathname === '/anuncios' ? 'rgba(255,255,255,0.08)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                color: '#fff'
              }
            }}
          >
            <ListItemIcon sx={{ color: '#fff', minWidth: 0, mr: 2 }}>
              <AnnouncementIcon fontSize="medium" />
            </ListItemIcon>
            <ListItemText primary="Anuncios" primaryTypographyProps={{ fontWeight: 500, color: '#fff' }} />
          </ListItemButton>
        </ListItem>
        )}

      </List>
    </Drawer>
  );
};

export default Sidebar; 