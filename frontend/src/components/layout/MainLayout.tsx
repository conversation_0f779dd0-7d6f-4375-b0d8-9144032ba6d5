import React, { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import { Box, CssBaseline, Toolbar, IconButton, Typography, Tooltip } from '@mui/material';
import MuiAppBar, { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar';
import { Menu as MenuIcon, Security as SecurityIcon } from '@mui/icons-material';
import { Outlet, useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import { useAuth } from '../../context/AuthContext';
import { useModalContext } from '../../context/ModalContext';
import { useScreenSaver } from '../../context/ScreenSaverContext';
import NotificationBell from '../notifications/NotificationBell';
import { Rol } from '../../types/usuario.types';
import AnuncioModal from '../anuncios/AnuncioModal';
import { useAnuncios } from '../../hooks/useAnuncios';

const drawerWidth = 240;

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<AppBarProps>(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const MainLayout: React.FC = () => {
  const [open, setOpen] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { user } = useAuth();
  const { masBuscadosModalOpen } = useModalContext();
  const { activateScreenSaver } = useScreenSaver();
  const location = useLocation();
  
  // Hook para manejar anuncios
  const { anuncioActivo, modalOpen, marcarComoVisto } = useAnuncios();

  // Detectar si estamos en el Centro de Estadísticas
  const isEstadisticas = location.pathname === '/estadisticas/centro-comando';

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  const handleActivateScreenSaver = () => {
    activateScreenSaver();
  };

  // Función para manejar el cambio de fullscreen desde el componente hijo
  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  // Efecto para manejar el estado inicial cuando se navega al Centro de Estadísticas
  useEffect(() => {
    if (isEstadisticas) {
      setIsFullscreen(true);
    } else {
      setIsFullscreen(false);
    }
  }, [isEstadisticas]);

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      {/* Mostrar AppBar solo si no estamos en fullscreen */}
      {!isFullscreen && (
      <AppBar position="fixed" open={open} sx={{ backgroundColor: '#1c2536' }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={{ mr: 2, ...(open && { display: 'none' }) }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <img src="/logo-cufre-2.png" alt="CUFRE Logo" style={{ height: 40, marginRight: 16 }} />
            <Typography variant="h6" noWrap component="div">
              Comando Unificado Federal De Recaptura de Evadidos
            </Typography>
          </Box>
          
          {/* Botón de salvapantallas de seguridad */}
          <Tooltip title="Activar salvapantallas de seguridad" arrow>
            <IconButton
              color="inherit"
              onClick={handleActivateScreenSaver}
              sx={{ mr: 2 }}
              aria-label="Activar salvapantallas de seguridad"
            >
              <SecurityIcon />
            </IconButton>
          </Tooltip>

          {/* Sistema de notificaciones para ADMINISTRADOR y SUPERUSUARIO */}
          {user && (user.rol === Rol.ADMINISTRADOR || user.rol === Rol.SUPERUSUARIO) && (
            <Box sx={{ mr: 2 }}>
              <NotificationBell />
            </Box>
          )}
        </Toolbar>
      </AppBar>
      )}
      {/* Mostrar Sidebar solo si no estamos en fullscreen */}
      {!isFullscreen && (
      <Sidebar open={open} handleDrawerClose={handleDrawerClose} />
      )}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Marca de agua */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 0,
            pointerEvents: 'none',
            background: 'url("/logo-cufre-2.png") center center no-repeat',
            backgroundSize: '50% auto',
            opacity: 0.07,
          }}
        />
        {/* Solo mostrar Toolbar si no estamos en fullscreen */}
        {!isFullscreen && <Toolbar />}
        <Box sx={{ flexGrow: 1, position: 'relative', zIndex: 1 }}>
          <Outlet context={{ onFullscreenChange: handleFullscreenChange }} />
        </Box>
        {/* Solo mostrar footer si no estamos en fullscreen y no hay modal abierto */}
        {!masBuscadosModalOpen && !isFullscreen && (
          <Box component="footer" sx={{ textAlign: 'center', py: 2, color: 'text.secondary', fontSize: 14, position: 'relative', zIndex: 1 }}>
            2025 - Dirección Nacional Gestión de Bases de Datos de Seguridad
          </Box>
        )}
      </Box>
      
      {/* Modal de anuncios globales */}
      <AnuncioModal
        anuncio={anuncioActivo}
        open={modalOpen}
        onClose={marcarComoVisto}
      />
    </Box>
  );
};

export default MainLayout; 