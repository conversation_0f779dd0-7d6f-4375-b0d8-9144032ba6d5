import React from 'react';
import './SessionWarningModal.css';

interface SessionWarningModalProps {
  isOpen: boolean;
  countdown: number;
  onExtendSession: () => void;
}

/**
 * Modal de advertencia que se muestra cuando la sesión está próxima a expirar
 */
export const SessionWarningModal: React.FC<SessionWarningModalProps> = ({
  isOpen,
  countdown,
  onExtendSession
}) => {
  /**
   * Formatea el tiempo en formato MM:SS
   */
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  /**
   * Maneja el click en el botón de extender sesión
   */
  const handleExtendSession = () => {
    onExtendSession();
  };

  /**
   * Maneja el evento de teclado para extender sesión con Enter o Espacio
   */
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleExtendSession();
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="session-warning-overlay" role="dialog" aria-modal="true" aria-labelledby="session-warning-title">
      <div className="session-warning-modal">
        <div className="session-warning-header">
          <div className="session-warning-icon">
            ⚠️
          </div>
          <h3 id="session-warning-title" className="session-warning-title">
            Sesión por Expirar
          </h3>
        </div>
        
        <div className="session-warning-content">
          <p className="session-warning-message">
            Su sesión se cerrará automáticamente por inactividad en:
          </p>
          
          <div className="session-warning-countdown">
            <span className="countdown-time">{formatTime(countdown)}</span>
            <span className="countdown-label">minutos</span>
          </div>
          
          <p className="session-warning-subtitle">
            Haga clic en el botón para mantener su sesión activa
          </p>
        </div>
        
        <div className="session-warning-actions">
          <button
            type="button"
            className="btn-extend-session"
            onClick={handleExtendSession}
            onKeyDown={handleKeyDown}
            autoFocus
          >
            <span className="btn-icon">🔄</span>
            Mantener Sesión Activa
          </button>
        </div>
        
        <div className="session-warning-footer">
          <small>
            Esta ventana aparece por motivos de seguridad después de 25 minutos de inactividad
          </small>
        </div>
      </div>
    </div>
  );
};

export default SessionWarningModal;