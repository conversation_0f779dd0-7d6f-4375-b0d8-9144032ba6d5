# SessionManager - Integración del Sistema de Inactividad

## Descripción

El `SessionManager` es un componente que gestiona automáticamente el cierre de sesión por inactividad del usuario. Debe envolver toda la aplicación autenticada para funcionar correctamente.

## Integración

### 1. En el componente principal de la aplicación (App.tsx o similar):

```tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { SessionManager } from './components/SessionManager';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
// ... otros imports

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route 
            path="/*" 
            element={
              <SessionManager>
                <Dashboard />
                {/* <PERSON><PERSON><PERSON><PERSON> van todas las rutas autenticadas */}
              </SessionManager>
            } 
          />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
```

### 2. O envolviendo rutas específicas:

```tsx
import { ProtectedRoute } from './components/ProtectedRoute';
import { SessionManager } from './components/SessionManager';

<Route 
  path="/dashboard" 
  element={
    <ProtectedRoute>
      <SessionManager>
        <Dashboard />
      </SessionManager>
    </ProtectedRoute>
  } 
/>
```

## Funcionalidades

### Detección de Actividad
- **Eventos detectados**: clicks, movimiento de mouse, teclas, scroll, touch
- **Reseteo automático**: cada actividad resetea el timer de 30 minutos
- **Detección de visibilidad**: maneja cambios de pestaña/ventana

### Sistema de Advertencias
- **Advertencia a los 25 minutos**: modal con countdown de 5 minutos
- **Botón de extensión**: permite al usuario mantener la sesión activa
- **Cierre automático**: a los 30 minutos sin respuesta

### Refresh Automático de Tokens
- **Tokens de 2 horas**: configurados en el backend
- **Refresh transparente**: cuando el token está próximo a expirar
- **Manejo de errores**: logout automático si el refresh falla

## Configuración

### Tiempos configurables en `session.config.ts`:

```typescript
export const SESSION_CONFIG = {
  INACTIVITY_TIMEOUT: 30 * 60 * 1000,    // 30 minutos
  WARNING_TIME: 25 * 60 * 1000,          // 25 minutos
  TOKEN_REFRESH_TIME: 110 * 60 * 1000,   // 1h 50min
  WARNING_COUNTDOWN: 5 * 60 * 1000        // 5 minutos
};
```

### Rutas excluidas del seguimiento:

```typescript
export const EXCLUDED_ROUTES = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password'
];
```

## Estados del Sistema

### Indicador de Desarrollo
En modo desarrollo, se muestra un pequeño indicador en la esquina inferior derecha:
- **Verde**: Sistema activo
- **Rojo**: Sistema pausado

### Logs de Consola
- `🚀 Inicializando servicio de inactividad`
- `👆 Actividad detectada` (solo en desarrollo)
- `⚠️ Mostrando advertencia de sesión próxima a expirar`
- `✅ Sesión extendida por el usuario`
- `🔒 Cerrando sesión por inactividad`
- `🔄 Intentando refrescar token...`
- `✅ Token refrescado exitosamente`

## Personalización

### Estilos del Modal
Los estilos están en `SessionWarningModal.css` y pueden personalizarse:
- Colores y gradientes
- Animaciones
- Responsive design
- Modo oscuro

### Eventos de Actividad
Se pueden agregar más eventos en `session.config.ts`:

```typescript
export const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove', 
  'keypress',
  'scroll',
  'touchstart',
  'click',
  'keydown',
  'wheel'  // Agregar scroll con rueda
];
```

## Consideraciones de Seguridad

1. **Limpieza completa**: Se eliminan todos los tokens y datos de sesión
2. **Redirección forzada**: Redirige automáticamente al login
3. **Prevención de múltiples refresh**: Evita llamadas simultáneas al endpoint
4. **Manejo de errores**: Logout inmediato si hay problemas con el refresh

## Debugging

### Variables de entorno útiles:
```bash
NODE_ENV=development  # Muestra logs adicionales e indicador visual
```

### Herramientas de desarrollo:
- Abrir DevTools → Console para ver logs del sistema
- Network tab para verificar llamadas de refresh
- Application tab → Local Storage para ver tokens

## Compatibilidad

- ✅ React 18+
- ✅ TypeScript
- ✅ React Router v6
- ✅ Navegadores modernos
- ✅ Dispositivos móviles
- ✅ Modo oscuro
- ✅ Accesibilidad (ARIA)