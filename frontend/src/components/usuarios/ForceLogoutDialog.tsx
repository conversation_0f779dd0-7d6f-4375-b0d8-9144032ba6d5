import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Avatar,
  Divider
} from '@mui/material';
import {
  ExitToApp as LogoutIcon,
  Warning as WarningIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { Rol } from '../../types/usuario.types';

interface ForceLogoutDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  targetUser: {
    id: number;
    nombre: string;
    apellido: string;
    usuario: string;
    email: string;
    rol: Rol;
    activo: boolean;
  } | null;
  loading?: boolean;
}

const ForceLogoutDialog: React.FC<ForceLogoutDialogProps> = ({
  open,
  onClose,
  onConfirm,
  targetUser,
  loading = false
}) => {
  const [error, setError] = useState('');

  const handleClose = () => {
    setError('');
    onClose();
  };

  const handleConfirm = async () => {
    try {
      setError('');
      await onConfirm();
      handleClose();
    } catch (err: any) {
      setError(err.message || 'Error al forzar el cierre de sesión');
    }
  };

  const getRoleDisplayName = (rol: Rol): string => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return 'Superusuario';
      case Rol.ADMINISTRADOR: return 'Administrador';
      case Rol.USUARIOCARGA: return 'Usuario Carga';
      case Rol.USUARIOCONSULTA: return 'Usuario Consulta';
      default: return rol;
    }
  };

  const getRoleColor = (rol: Rol): string => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return '#d32f2f';
      case Rol.ADMINISTRADOR: return '#ed6c02';
      case Rol.USUARIOCARGA: return '#1976d2';
      case Rol.USUARIOCONSULTA: return '#0288d1';
      default: return '#666';
    }
  };

  const getInitials = (nombre: string, apellido: string): string => {
    return `${nombre.charAt(0)}${apellido.charAt(0)}`.toUpperCase();
  };

  if (!targetUser) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 24px 48px rgba(0,0,0,0.15)'
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
        color: 'white',
        mb: 2
      }}>
        <LogoutIcon />
        <Typography variant="h6" component="div">
          Forzar Cierre de Sesión
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ pt: 0 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <WarningIcon fontSize="small" />
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              Acción Crítica
            </Typography>
          </Box>
          <Typography variant="body2">
            Esta acción invalidará inmediatamente todas las sesiones activas del usuario 
            y lo obligará a iniciar sesión nuevamente.
          </Typography>
        </Alert>

        <Typography variant="body1" sx={{ mb: 3 }}>
          Está a punto de forzar el cierre de sesión del siguiente usuario:
        </Typography>
        
        <Box sx={{ 
          p: 3, 
          bgcolor: 'grey.50', 
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'grey.200',
          mb: 3
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar 
              sx={{ 
                bgcolor: getRoleColor(targetUser.rol),
                width: 56,
                height: 56,
                fontSize: '1.25rem',
                fontWeight: 600
              }}
            >
              {getInitials(targetUser.nombre, targetUser.apellido)}
            </Avatar>
            
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {targetUser.nombre} {targetUser.apellido}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                {targetUser.email}
              </Typography>
              <Typography 
                variant="caption" 
                sx={{ 
                  fontWeight: 600,
                  color: getRoleColor(targetUser.rol),
                  textTransform: 'uppercase',
                  letterSpacing: 0.5
                }}
              >
                {getRoleDisplayName(targetUser.rol)}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PersonIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              Usuario: <strong>{targetUser.usuario}</strong>
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Efectos de esta acción:</strong>
          </Typography>
          <Box component="ul" sx={{ mt: 1, mb: 0, pl: 2 }}>
            <li>
              <Typography variant="body2">
                Se invalidarán todos los tokens de sesión del usuario
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                El usuario será desconectado de todas las sesiones activas
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                Deberá iniciar sesión nuevamente para acceder al sistema
              </Typography>
            </li>
          </Box>
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button 
          onClick={handleClose}
          disabled={loading}
          variant="outlined"
          sx={{ minWidth: 100 }}
        >
          Cancelar
        </Button>
        <Button 
          onClick={handleConfirm}
          disabled={loading}
          variant="contained"
          color="error"
          sx={{ 
            minWidth: 140,
            background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)'
          }}
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <LogoutIcon />}
        >
          {loading ? 'Cerrando...' : 'Forzar Cierre'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ForceLogoutDialog;