import React, { useState } from 'react';
import {
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip
} from '@mui/material';
import {
  SupervisorAccount as SuperusuarioIcon,
  AdminPanelSettings as AdminIcon,
  Edit as CargaIcon,
  Visibility as ConsultaIcon
} from '@mui/icons-material';
import { Rol } from '../../types/usuario.types';

interface RoleChipProps {
  currentRole: Rol;
  canEdit: boolean;
  onRoleChange: (newRole: Rol) => void;
  disabled?: boolean;
}

const RoleChip: React.FC<RoleChipProps> = ({
  currentRole,
  canEdit,
  onRoleChange,
  disabled = false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (canEdit && !disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRoleSelect = (role: Rol) => {
    onRoleChange(role);
    handleClose();
  };

  const getChipColor = (rol: Rol): "error" | "warning" | "primary" | "info" | "default" => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return 'error';
      case Rol.ADMINISTRADOR: return 'warning';
      case Rol.USUARIOCARGA: return 'primary';
      case Rol.USUARIOCONSULTA: return 'info';
      default: return 'default';
    }
  };

  const getRoleIcon = (rol: Rol) => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return <SuperusuarioIcon fontSize="small" />;
      case Rol.ADMINISTRADOR: return <AdminIcon fontSize="small" />;
      case Rol.USUARIOCARGA: return <CargaIcon fontSize="small" />;
      case Rol.USUARIOCONSULTA: return <ConsultaIcon fontSize="small" />;
      default: return <ConsultaIcon fontSize="small" />;
    }
  };

  const getRoleDisplayName = (rol: Rol): string => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return 'Superusuario';
      case Rol.ADMINISTRADOR: return 'Administrador';
      case Rol.USUARIOCARGA: return 'Usuario Carga';
      case Rol.USUARIOCONSULTA: return 'Usuario Consulta';
      default: return rol;
    }
  };

  const availableRoles = [
    Rol.SUPERUSUARIO,
    Rol.ADMINISTRADOR,
    Rol.USUARIOCARGA,
    Rol.USUARIOCONSULTA
  ];

  const chipElement = (
    <Chip
      icon={getRoleIcon(currentRole)}
      label={getRoleDisplayName(currentRole)}
      color={getChipColor(currentRole)}
      variant="filled"
      size="small"
      onClick={handleClick}
      sx={{
        cursor: canEdit && !disabled ? 'pointer' : 'default',
        '&:hover': canEdit && !disabled ? {
          opacity: 0.8,
          transform: 'scale(1.02)'
        } : {},
        transition: 'all 0.2s ease-in-out',
        fontWeight: 600
      }}
      disabled={disabled}
    />
  );

  return (
    <>
      {canEdit && !disabled ? (
        <Tooltip title="Clic para cambiar rol" arrow>
          {chipElement}
        </Tooltip>
      ) : (
        chipElement
      )}
      
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 180,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            border: '1px solid rgba(0,0,0,0.08)'
          }
        }}
      >
        {availableRoles.map((role) => (
          <MenuItem
            key={role}
            onClick={() => handleRoleSelect(role)}
            selected={role === currentRole}
            sx={{
              py: 1.5,
              '&.Mui-selected': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText',
                '&:hover': {
                  backgroundColor: 'primary.main',
                }
              }
            }}
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              {getRoleIcon(role)}
            </ListItemIcon>
            <ListItemText 
              primary={getRoleDisplayName(role)}
              primaryTypographyProps={{
                fontWeight: role === currentRole ? 600 : 400
              }}
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default RoleChip;