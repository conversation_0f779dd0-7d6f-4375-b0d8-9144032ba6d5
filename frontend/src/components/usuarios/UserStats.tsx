import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  LinearProgress,
  Tooltip,
  Collapse,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  People as PeopleIcon,
  AdminPanelSettings as AdminIcon,
  Person as PersonIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { Usuario, Rol } from '../../types/usuario.types';
import { getUserStats } from '../../utils/userFilters';

interface UserStatsProps {
  users: Usuario[];
  filteredUsers: Usuario[];
}

const UserStats: React.FC<UserStatsProps> = ({ users, filteredUsers }) => {
  const [expanded, setExpanded] = React.useState(false);
  
  const allStats = getUserStats(users);
  const filteredStats = getUserStats(filteredUsers);

  // Manejar casos donde no hay datos
  if (allStats.total === 0) {
    return (
      <Paper elevation={1} sx={{ mb: 2, p: 2, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No hay usuarios para mostrar estadísticas
        </Typography>
      </Paper>
    );
  }

  const rolLabels: Record<Rol, string> = {
    [Rol.SUPERUSUARIO]: 'Superusuario',
    [Rol.ADMINISTRADOR]: 'Administrador',
    [Rol.USUARIOCARGA]: 'Usuario Carga',
    [Rol.USUARIOCONSULTA]: 'Usuario Consulta',
  };

  const rolColors: Record<Rol, "error" | "warning" | "primary" | "info"> = {
    [Rol.SUPERUSUARIO]: 'error',
    [Rol.ADMINISTRADOR]: 'warning',
    [Rol.USUARIOCARGA]: 'primary',
    [Rol.USUARIOCONSULTA]: 'info',
  };

  const rolIcons: Record<Rol, React.ReactNode> = {
    [Rol.SUPERUSUARIO]: <AdminIcon fontSize="small" />,
    [Rol.ADMINISTRADOR]: <AdminIcon fontSize="small" />,
    [Rol.USUARIOCARGA]: <PersonIcon fontSize="small" />,
    [Rol.USUARIOCONSULTA]: <PersonIcon fontSize="small" />,
  };

  return (
    <Paper elevation={1} sx={{ mb: 2, overflow: 'hidden' }}>
      <Box 
        sx={{ 
          p: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: 'action.hover'
          }
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <PeopleIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Estadísticas de Usuarios
          </Typography>
          <Chip 
            label={`${filteredUsers.length} de ${users.length}`}
            color="primary"
            size="small"
          />
        </Box>
        <IconButton size="small">
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{ p: 2, pt: 0, backgroundColor: 'grey.50' }}>
          {/* Distribución por Roles */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
              <AdminIcon fontSize="small" />
              Distribución por Roles
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              {Object.values(Rol).map((rol) => {
                const count = filteredStats.roleStats[rol] || 0;
                const totalCount = allStats.roleStats[rol] || 0;
                const percentage = allStats.total > 0 ? (totalCount / allStats.total) * 100 : 0;
                
                return (
                  <Box key={rol}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {rolIcons[rol]}
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {rolLabels[rol]}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          {count} / {totalCount}
                        </Typography>
                        <Chip 
                          label={`${percentage.toFixed(1)}%`}
                          color={rolColors[rol]}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                    <Tooltip title={`${count} de ${totalCount} usuarios con rol ${rolLabels[rol]}`}>
                      <LinearProgress
                        variant="determinate"
                        value={totalCount > 0 ? (count / totalCount) * 100 : 0}
                        color={rolColors[rol]}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: 'grey.200',
                        }}
                      />
                    </Tooltip>
                  </Box>
                );
              })}
            </Box>
          </Box>

          {/* Distribución por Dependencias */}
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
              <BusinessIcon fontSize="small" />
              Top Dependencias
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {Object.entries(filteredStats.dependenciaStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 6)
                .map(([dependencia, count]) => {
                  const totalCount = allStats.dependenciaStats[dependencia] || 0;
                  return (
                    <Tooltip 
                      key={dependencia}
                      title={`${count} de ${totalCount} usuarios en ${dependencia}`}
                    >
                      <Chip
                        label={`${dependencia} (${count})`}
                        variant="outlined"
                        size="small"
                        sx={{
                          '&:hover': {
                            backgroundColor: 'action.hover'
                          }
                        }}
                      />
                    </Tooltip>
                  );
                })}
            </Box>
          </Box>

          {/* Resumen rápido */}
          <Box sx={{ 
            mt: 3, 
            p: 2, 
            backgroundColor: 'primary.main', 
            color: 'primary.contrastText',
            borderRadius: 2 
          }}>
            <Typography variant="body2" sx={{ textAlign: 'center' }}>
              <strong>Resumen:</strong> {filteredUsers.length} usuarios mostrados de {users.length} totales
              {filteredUsers.length !== users.length && (
                <span> • Filtros activos</span>
              )}
            </Typography>
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

export default UserStats;