import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Security as SecurityIcon
} from '@mui/icons-material';
import { Rol } from '../../types/usuario.types';

interface RoleChangeDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (password: string) => Promise<void>;
  targetUser: {
    id: number;
    nombre: string;
    apellido: string;
    usuario: string;
    currentRole: Rol;
  } | null;
  newRole: Rol;
  loading?: boolean;
}

const RoleChangeDialog: React.FC<RoleChangeDialogProps> = ({
  open,
  onClose,
  onConfirm,
  targetUser,
  newRole,
  loading = false
}) => {
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const handleClose = () => {
    setPassword('');
    setShowPassword(false);
    setError('');
    onClose();
  };

  const handleConfirm = async () => {
    if (!password.trim()) {
      setError('La contraseña es requerida');
      return;
    }

    try {
      setError('');
      await onConfirm(password);
      handleClose();
    } catch (err: any) {
      setError(err.message || 'Error al cambiar el rol. Verifique su contraseña.');
    }
  };

  const getRoleDisplayName = (rol: Rol): string => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return 'Superusuario';
      case Rol.ADMINISTRADOR: return 'Administrador';
      case Rol.USUARIOCARGA: return 'Usuario Carga';
      case Rol.USUARIOCONSULTA: return 'Usuario Consulta';
      default: return rol;
    }
  };

  const getRoleColor = (rol: Rol): string => {
    switch (rol) {
      case Rol.SUPERUSUARIO: return '#d32f2f';
      case Rol.ADMINISTRADOR: return '#ed6c02';
      case Rol.USUARIOCARGA: return '#1976d2';
      case Rol.USUARIOCONSULTA: return '#0288d1';
      default: return '#666';
    }
  };

  if (!targetUser) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 24px 48px rgba(0,0,0,0.15)'
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        color: 'white',
        mb: 2
      }}>
        <SecurityIcon />
        <Typography variant="h6" component="div">
          Confirmar Cambio de Rol
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ pt: 0 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Está a punto de cambiar el rol del usuario:
          </Typography>
          
          <Box sx={{ 
            p: 2, 
            bgcolor: 'grey.50', 
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.200'
          }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
              {targetUser.nombre} {targetUser.apellido}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Usuario: {targetUser.usuario}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">Rol actual:</Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    fontWeight: 600,
                    color: getRoleColor(targetUser.currentRole)
                  }}
                >
                  {getRoleDisplayName(targetUser.currentRole)}
                </Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary">→</Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">Nuevo rol:</Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    fontWeight: 600,
                    color: getRoleColor(newRole)
                  }}
                >
                  {getRoleDisplayName(newRole)}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>

        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Atención:</strong> Esta acción requiere confirmación con su contraseña. 
            El cambio de rol afectará los permisos del usuario inmediatamente.
          </Typography>
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <TextField
          fullWidth
          label="Confirme su contraseña"
          type={showPassword ? 'text' : 'password'}
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter' && !loading) {
              handleConfirm();
            }
          }}
          disabled={loading}
          autoFocus
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() => setShowPassword(!showPassword)}
                  edge="end"
                  disabled={loading}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '&.Mui-focused fieldset': {
                borderColor: 'primary.main',
              },
            },
          }}
        />
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button 
          onClick={handleClose}
          disabled={loading}
          variant="outlined"
          sx={{ minWidth: 100 }}
        >
          Cancelar
        </Button>
        <Button 
          onClick={handleConfirm}
          disabled={loading || !password.trim()}
          variant="contained"
          sx={{ 
            minWidth: 120,
            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)'
          }}
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : null}
        >
          {loading ? 'Cambiando...' : 'Confirmar Cambio'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RoleChangeDialog;