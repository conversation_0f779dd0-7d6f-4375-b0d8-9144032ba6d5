import React from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  Paper,
  Typography,
  Collapse,
  IconButton
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { Rol } from '../../types/usuario.types';
import debounce from 'lodash.debounce';

export interface UserFiltersState {
  searchTerm: string;
  selectedRoles: Rol[];
  selectedDependencia: string;
  showFilters: boolean;
}

interface UserFiltersProps {
  filters: UserFiltersState;
  onFiltersChange: (filters: UserFiltersState) => void;
  dependencias: string[];
  totalResults: number;
  filteredResults: number;
}

const UserFilters: React.FC<UserFiltersProps> = ({
  filters,
  onFiltersChange,
  dependencias,
  totalResults,
  filteredResults
}) => {
  const rolLabels: Record<Rol, string> = {
    [Rol.SUPERUSUARIO]: 'Superusuario',
    [Rol.ADMINISTRADOR]: 'Administrador',
    [Rol.USUARIOCARGA]: 'Usuario Carga',
    [Rol.USUARIOCONSULTA]: 'Usuario Consulta',
  };

  const rolColors: Record<Rol, "error" | "warning" | "primary" | "info"> = {
    [Rol.SUPERUSUARIO]: 'error',
    [Rol.ADMINISTRADOR]: 'warning',
    [Rol.USUARIOCARGA]: 'primary',
    [Rol.USUARIOCONSULTA]: 'info',
  };

  // Debounced search function
  const debouncedSearch = React.useMemo(
    () => debounce((searchValue: string) => {
      onFiltersChange({
        ...filters,
        searchTerm: searchValue
      });
    }, 300),
    [filters, onFiltersChange]
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    debouncedSearch(value);
  };

  const handleRoleToggle = (role: Rol) => {
    const newSelectedRoles = filters.selectedRoles.includes(role)
      ? filters.selectedRoles.filter(r => r !== role)
      : [...filters.selectedRoles, role];
    
    onFiltersChange({
      ...filters,
      selectedRoles: newSelectedRoles
    });
  };

  const handleDependenciaChange = (event: any) => {
    onFiltersChange({
      ...filters,
      selectedDependencia: event.target.value
    });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      searchTerm: '',
      selectedRoles: [],
      selectedDependencia: '',
      showFilters: filters.showFilters
    });
  };

  const toggleFilters = () => {
    onFiltersChange({
      ...filters,
      showFilters: !filters.showFilters
    });
  };

  const hasActiveFilters = filters.searchTerm || filters.selectedRoles.length > 0 || filters.selectedDependencia;

  return (
    <Paper elevation={2} sx={{ mb: 3, overflow: 'hidden' }}>
      {/* Search Bar */}
      <Box sx={{ p: 2, pb: 1 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2,
          alignItems: { xs: 'stretch', md: 'center' }
        }}>
          <Box sx={{ flex: 1 }}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Buscar por nombre, apellido, email o rol..."
              defaultValue={filters.searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                }
              }}
            />
          </Box>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: { xs: 'center', md: 'flex-end' } }}>
            <Button
              variant={filters.showFilters ? "contained" : "outlined"}
              startIcon={<FilterIcon />}
              endIcon={filters.showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={toggleFilters}
              sx={{ borderRadius: 2 }}
            >
              Filtros
            </Button>
            {hasActiveFilters && (
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
                sx={{ borderRadius: 2 }}
              >
                Limpiar
              </Button>
            )}
          </Box>
        </Box>

        {/* Results Summary */}
        <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {filteredResults === totalResults 
              ? `${totalResults} usuarios en total`
              : `${filteredResults} de ${totalResults} usuarios`
            }
          </Typography>
          {hasActiveFilters && (
            <Typography variant="body2" color="primary">
              Filtros activos
            </Typography>
          )}
        </Box>
      </Box>

      {/* Advanced Filters */}
      <Collapse in={filters.showFilters}>
        <Box sx={{ p: 2, pt: 0, borderTop: '1px solid', borderColor: 'divider', backgroundColor: 'grey.50' }}>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: 3
          }}>
            {/* Role Filters */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                Filtrar por Rol
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.values(Rol).map((role) => (
                  <Chip
                    key={role}
                    label={rolLabels[role]}
                    color={filters.selectedRoles.includes(role) ? rolColors[role] : 'default'}
                    variant={filters.selectedRoles.includes(role) ? 'filled' : 'outlined'}
                    onClick={() => handleRoleToggle(role)}
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'scale(1.05)',
                      }
                    }}
                  />
                ))}
              </Box>
            </Box>

            {/* Dependencia Filter */}
            <Box sx={{ flex: 1 }}>
              <FormControl fullWidth>
                <InputLabel>Dependencia</InputLabel>
                <Select
                  value={filters.selectedDependencia}
                  label="Dependencia"
                  onChange={handleDependenciaChange}
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="">
                    <em>Todas las dependencias</em>
                  </MenuItem>
                  {dependencias.map((dep) => (
                    <MenuItem key={dep} value={dep}>
                      {dep}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Box>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Filtros aplicados:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {filters.searchTerm && (
                  <Chip
                    size="small"
                    label={`Búsqueda: "${filters.searchTerm}"`}
                    onDelete={() => onFiltersChange({ ...filters, searchTerm: '' })}
                    color="primary"
                    variant="outlined"
                  />
                )}
                {filters.selectedRoles.map((role) => (
                  <Chip
                    key={role}
                    size="small"
                    label={`Rol: ${rolLabels[role]}`}
                    onDelete={() => handleRoleToggle(role)}
                    color={rolColors[role]}
                    variant="outlined"
                  />
                ))}
                {filters.selectedDependencia && (
                  <Chip
                    size="small"
                    label={`Dependencia: ${filters.selectedDependencia}`}
                    onDelete={() => onFiltersChange({ ...filters, selectedDependencia: '' })}
                    color="secondary"
                    variant="outlined"
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default UserFilters;