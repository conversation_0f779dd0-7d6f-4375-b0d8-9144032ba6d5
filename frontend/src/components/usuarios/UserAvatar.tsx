import React from 'react';
import { Avatar, Tooltip } from '@mui/material';
import { Usuario } from '../../types/usuario.types';

interface UserAvatarProps {
  usuario: Usuario;
  size?: 'small' | 'medium' | 'large';
}

const UserAvatar: React.FC<UserAvatarProps> = ({ usuario, size = 'medium' }) => {
  const getInitials = (nombre: string, apellido: string) => {
    const nombreInicial = nombre && nombre.length > 0 ? nombre.charAt(0) : '';
    const apellidoInicial = apellido && apellido.length > 0 ? apellido.charAt(0) : '';
    return `${nombreInicial}${apellidoInicial}`.toUpperCase() || 'U';
  };

  const getAvatarSize = () => {
    switch (size) {
      case 'small':
        return { width: 32, height: 32, fontSize: '0.875rem' };
      case 'large':
        return { width: 56, height: 56, fontSize: '1.25rem' };
      default:
        return { width: 40, height: 40, fontSize: '1rem' };
    }
  };

  const getBackgroundColor = (nombre: string) => {
    const colors = [
      '#1976d2', '#388e3c', '#f57c00', '#d32f2f', '#7b1fa2',
      '#0288d1', '#689f38', '#fbc02d', '#e64a19', '#5e35b1'
    ];
    if (!nombre || nombre.length === 0) {
      return colors[0]; // Color por defecto
    }
    const index = nombre.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Validaciones defensivas
  if (!usuario) {
    return (
      <Avatar
        sx={{
          ...getAvatarSize(),
          bgcolor: '#1976d2',
          fontWeight: 600,
        }}
      >
        U
      </Avatar>
    );
  }

  const nombre = usuario.nombre || '';
  const apellido = usuario.apellido || '';
  const tooltipText = nombre && apellido ? `${nombre} ${apellido}` : 'Usuario';

  return (
    <Tooltip title={tooltipText}>
      <Avatar
        sx={{
          ...getAvatarSize(),
          bgcolor: getBackgroundColor(nombre),
          fontWeight: 600,
          cursor: 'pointer',
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.1)',
          }
        }}
      >
        {getInitials(nombre, apellido)}
      </Avatar>
    </Tooltip>
  );
};

export default UserAvatar;