# Salvapantallas de Seguridad CUFRE

## Descripción

El salvapantallas de seguridad es una funcionalidad implementada para proteger información sensible cuando los usuarios dejan sus estaciones de trabajo desatendidas. Se activa automáticamente después de 2 minutos de inactividad y muestra un fondo blanco con el logo de CUFRE.

## Características

- ✅ **Activación automática** después de 2 minutos de inactividad
- ✅ **Detección completa de actividad** (mouse, teclado, touch, scroll)
- ✅ **Fondo blanco** con logo CUFRE centrado
- ✅ **Desactivación inmediata** al detectar cualquier actividad
- ✅ **Animaciones suaves** de entrada y salida
- ✅ **Responsive design** para diferentes tamaños de pantalla
- ✅ **Accesibilidad** con soporte para lectores de pantalla
- ✅ **Prevención de bypass** con z-index máximo
- ✅ **Integración con autenticación** existente

## Archivos Implementados

### Configuración
- `src/config/screensaver.ts` - Configuración central del salvapantallas

### Hooks
- `src/hooks/useIdleTimer.ts` - Hook para detección de inactividad

### Contextos
- `src/context/ScreenSaverContext.tsx` - Estado global del salvapantallas

### Componentes
- `src/components/ScreenSaver.tsx` - Componente principal del salvapantallas
- `src/components/ScreenSaverTest.tsx` - Componente de prueba (temporal)

### Estilos
- `src/styles/ScreenSaver.css` - Estilos CSS del salvapantallas

### Assets
- `public/images/Logo CUFRE.jpg` - Logo oficial de CUFRE

## Configuración

### Timeout de Inactividad

Para cambiar el tiempo de inactividad, modifica el archivo `src/config/screensaver.ts`:

```typescript
export const SCREENSAVER_CONFIG = {
  IDLE_TIMEOUT: 2 * 60 * 1000, // 2 minutos en milisegundos
  // ...
};
```

### Eventos de Actividad

Los eventos que resetean el timer están configurados en:

```typescript
ACTIVITY_EVENTS: [
  'mousedown',
  'mousemove',
  'keypress',
  'scroll',
  'touchstart',
  'click',
  'wheel',
  'keydown',
  'keyup'
]
```

### Logo

Para cambiar el logo, reemplaza el archivo `public/images/Logo CUFRE.jpg` o modifica la ruta en la configuración:

```typescript
LOGO_PATH: '/images/Logo CUFRE.jpg'
```

## Uso Programático

### Hook useScreenSaver

```typescript
import { useScreenSaver } from '../context/ScreenSaverContext';

const MyComponent = () => {
  const { 
    isScreenSaverActive,
    activateScreenSaver,
    deactivateScreenSaver,
    resetTimer,
    isEnabled,
    setEnabled
  } = useScreenSaver();

  // Activar manualmente
  const handleActivate = () => {
    activateScreenSaver();
  };

  // Resetear timer
  const handleReset = () => {
    resetTimer();
  };

  // Habilitar/deshabilitar
  const handleToggle = () => {
    setEnabled(!isEnabled);
  };

  return (
    <div>
      <p>Estado: {isScreenSaverActive ? 'Activo' : 'Inactivo'}</p>
      <button onClick={handleActivate}>Activar</button>
      <button onClick={handleReset}>Reset Timer</button>
      <button onClick={handleToggle}>
        {isEnabled ? 'Deshabilitar' : 'Habilitar'}
      </button>
    </div>
  );
};
```

### Configuración del Provider

```typescript
// Con configuración por defecto
<ScreenSaverProvider>
  <App />
</ScreenSaverProvider>

// Con configuración personalizada
<ScreenSaverProvider 
  enabled={true}
  timeout={5 * 60 * 1000} // 5 minutos
>
  <App />
</ScreenSaverProvider>
```

## Integración con Autenticación

El salvapantallas está integrado con el sistema de autenticación existente:

- Se coordina con `SessionManager` para evitar conflictos
- Respeta los timeouts de JWT del backend
- Se puede pausar/reanudar según el estado de autenticación

## Accesibilidad

### Características de Accesibilidad

- **ARIA Labels**: `role="dialog"` y `aria-modal="true"`
- **Navegación por Teclado**: Cualquier tecla desactiva el salvapantallas
- **Lectores de Pantalla**: Texto descriptivo para usuarios con discapacidades visuales
- **Alto Contraste**: Soporte para modo de alto contraste del sistema
- **Reducción de Movimiento**: Respeta `prefers-reduced-motion`

### Configuración de Accesibilidad

```css
/* Alto contraste */
@media (prefers-contrast: high) {
  .screensaver-overlay {
    border: 2px solid #000000;
  }
}

/* Reducir movimiento */
@media (prefers-reduced-motion: reduce) {
  .screensaver-logo {
    animation: none;
  }
}
```

## Seguridad

### Medidas de Seguridad Implementadas

1. **Z-Index Máximo**: `z-index: 2147483647`
2. **Prevención de Selección**: `user-select: none`
3. **Bloqueo de Arrastre**: `user-drag: none`
4. **Prevención de Menú Contextual**: `onContextMenu={preventDefault}`
5. **Overflow Hidden**: Previene scroll del body
6. **Event Capture**: Captura eventos en fase de captura

### Eventos Monitoreados

```typescript
const ACTIVITY_EVENTS = [
  'mousedown',    // Clic del mouse
  'mousemove',    // Movimiento del mouse
  'keypress',     // Tecla presionada
  'scroll',       // Scroll de página
  'touchstart',   // Toque en pantalla táctil
  'click',        // Clic
  'wheel',        // Rueda del mouse
  'keydown',      // Tecla presionada (down)
  'keyup'         // Tecla liberada (up)
];
```

## Troubleshooting

### Problemas Comunes

#### El salvapantallas no se activa

1. Verificar que esté habilitado: `isEnabled === true`
2. Verificar que no haya errores en la consola
3. Verificar que los event listeners estén registrados

#### El logo no se carga

1. Verificar que el archivo existe en `public/images/Logo CUFRE.jpg`
2. Verificar la ruta en `SCREENSAVER_CONFIG.LOGO_PATH`
3. Verificar permisos del archivo

#### El salvapantallas no se desactiva

1. Verificar que los eventos estén siendo capturados
2. Verificar que no haya errores en `deactivateScreenSaver`
3. Verificar que el contexto esté disponible

### Debug

Para debug, habilita los logs en la consola:

```typescript
// En ScreenSaverContext.tsx, los logs ya están implementados:
console.log('🔒 Salvapantallas activado por inactividad');
console.log('🔓 Salvapantallas desactivado por actividad');
```

## Testing

### Componente de Prueba

El componente `ScreenSaverTest` proporciona una interfaz para probar todas las funcionalidades:

- Activar/desactivar manualmente
- Ver estado actual
- Resetear timer
- Habilitar/deshabilitar

### Pruebas Automatizadas

Para implementar pruebas automatizadas:

```typescript
// Ejemplo de test con Jest/React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ScreenSaverProvider } from '../context/ScreenSaverContext';
import ScreenSaver from '../components/ScreenSaver';

test('should activate screensaver after timeout', async () => {
  render(
    <ScreenSaverProvider timeout={1000}>
      <ScreenSaver />
    </ScreenSaverProvider>
  );

  // Esperar que se active después del timeout
  await waitFor(() => {
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  }, { timeout: 1500 });
});
```

## Deployment

### Producción

Para deployment en producción:

1. **Remover componente de prueba**:
   ```typescript
   // Comentar o remover en App.tsx
   // <ScreenSaverTest />
   ```

2. **Verificar configuración**:
   - Timeout apropiado para el entorno
   - Logo correcto
   - Eventos de actividad necesarios

3. **Testing**:
   - Probar en diferentes navegadores
   - Probar en dispositivos móviles
   - Verificar accesibilidad

### Variables de Entorno

Opcionalmente, se pueden usar variables de entorno:

```typescript
// En screensaver.ts
export const SCREENSAVER_CONFIG = {
  IDLE_TIMEOUT: process.env.REACT_APP_SCREENSAVER_TIMEOUT 
    ? parseInt(process.env.REACT_APP_SCREENSAVER_TIMEOUT) 
    : 2 * 60 * 1000,
  // ...
};
```

## Mantenimiento

### Actualizaciones

Para actualizar el salvapantallas:

1. Modificar configuración en `screensaver.ts`
2. Actualizar estilos en `ScreenSaver.css`
3. Probar funcionalidad con `ScreenSaverTest`
4. Verificar integración con autenticación

### Monitoreo

Para monitorear el uso:

```typescript
// Agregar analytics en ScreenSaverContext.tsx
const handleIdle = useCallback(() => {
  // Analytics
  analytics.track('screensaver_activated', {
    timestamp: new Date().toISOString(),
    timeout: timeout
  });
  
  if (isEnabled && !isScreenSaverActive) {
    setIsScreenSaverActive(true);
  }
}, [isEnabled, isScreenSaverActive, timeout]);
```

## Soporte

Para soporte técnico o preguntas sobre el salvapantallas:

1. Revisar este documento
2. Verificar logs en la consola del navegador
3. Probar con el componente `ScreenSaverTest`
4. Contactar al equipo de desarrollo

---

**Versión**: 1.0.0  
**Última actualización**: Diciembre 2025  
**Autor**: Equipo de Desarrollo CUFRE