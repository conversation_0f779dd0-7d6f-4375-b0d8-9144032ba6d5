/* ===== CENTRO DE COMANDO CUFRE - ESTILOS CINEMATOGRÁFICOS ===== */

/* Variables CSS para el tema cinematográfico */
:root {
  --cc-bg-primary: #0a0a0a;
  --cc-bg-panel: #1a1a2e;
  --cc-bg-accent: #16213e;
  --cc-bg-highlight: #0f3460;
  --cc-text-primary: #ffffff;
  --cc-text-secondary: #b0b0b0;
  --cc-success: #00ff88;
  --cc-alert: #ff6b35;
  --cc-cufre: #ffd600;
  --cc-glow: rgba(255, 214, 0, 0.3);
  --cc-shadow: rgba(0, 0, 0, 0.8);
  --cc-border: rgba(255, 255, 255, 0.1);
}

/* Contenedor principal del centro de comando */
.centro-comando-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--cc-bg-primary) 0%, #0d1117 50%, var(--cc-bg-primary) 100%);
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
}

/* Modo fullscreen activo */
.centro-comando-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  max-height: 100vh;
}

/* Efecto de fondo animado */
.centro-comando-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 214, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.03) 0%, transparent 50%);
  animation: backgroundPulse 8s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* Header del centro de comando */
.centro-comando-header {
  position: relative;
  z-index: 10;
  padding: 2rem 3rem;
  background: linear-gradient(90deg, var(--cc-bg-panel) 0%, var(--cc-bg-accent) 100%);
  border-bottom: 2px solid var(--cc-cufre);
  box-shadow: 0 4px 20px var(--cc-shadow);
}

.centro-comando-title {
  font-family: 'Orbitron', 'Roboto', sans-serif !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: var(--cc-cufre) !important;
  text-shadow: 0 0 20px var(--cc-glow);
  margin: 0 !important;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.centro-comando-subtitle {
  font-size: 1.1rem !important;
  color: var(--cc-text-secondary) !important;
  margin-top: 0.5rem !important;
  font-weight: 300 !important;
}

/* Controles del header */
.centro-comando-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.control-button {
  background: var(--cc-bg-highlight) !important;
  border: 1px solid var(--cc-border) !important;
  color: var(--cc-text-primary) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
}

.control-button:hover {
  background: var(--cc-cufre) !important;
  color: var(--cc-bg-primary) !important;
  box-shadow: 0 0 20px var(--cc-glow) !important;
  transform: translateY(-2px) !important;
}

/* Grid principal de paneles */
.centro-comando-grid {
  position: relative;
  z-index: 5;
  padding: 2rem 3rem;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: minmax(400px, 1fr) minmax(400px, 1fr);
  grid-template-areas:
    "casos-destacados expedientes-estado expedientes-fuerza"
    "casos-destacados detenidos-fuerza evolucion-temporal";
  gap: 2rem;
  flex: 0 0 auto;
  min-height: 0;
  max-height: calc(100vh - 200px);
  overflow: hidden;
}

/* Ajuste para modo fullscreen */
.fullscreen .centro-comando-grid {
  grid-template-rows: minmax(350px, 1fr) minmax(350px, 1fr);
  max-height: calc(100vh - 150px);
}

/* Panel extendido para ranking */
.centro-comando-extended {
  position: relative;
  z-index: 5;
  padding: 0 3rem 3rem 3rem;
  flex: 0 0 auto;
  min-height: 700px;
  max-height: 1000px;
  overflow: visible;
  contain: layout style;
}

/* Ajuste para modo fullscreen */
.fullscreen .centro-comando-extended {
  padding-bottom: 3rem;
  max-height: 950px;
  min-height: 650px;
}

/* Estilos base para paneles */
.panel-estadisticas {
  background: linear-gradient(135deg, var(--cc-bg-panel) 0%, var(--cc-bg-accent) 100%);
  border: 1px solid var(--cc-border);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  height: 100%;
  max-height: 100%;
  contain: layout style;
  will-change: transform;
  backface-visibility: hidden;
}

.panel-estadisticas::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 214, 0, 0.05) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.panel-estadisticas:hover::before {
  opacity: 1;
}

.panel-estadisticas:hover {
  transform: translate3d(0, -4px, 0);
  border-color: var(--cc-cufre);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.6),
    0 0 30px var(--cc-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Títulos de paneles */
.panel-title {
  font-family: 'Orbitron', 'Roboto', sans-serif !important;
  font-size: 1.4rem !important;
  font-weight: 600 !important;
  color: var(--cc-cufre) !important;
  margin-bottom: 1.5rem !important;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.panel-title-icon {
  font-size: 1.6rem !important;
  color: var(--cc-cufre) !important;
  filter: drop-shadow(0 0 8px var(--cc-glow));
}

/* Contenido de paneles */
.panel-content {
  height: calc(100% - 60px);
  min-height: 250px;
  max-height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  contain: size layout style;
}

/* Métricas grandes */
.metric-large {
  font-size: 3.5rem !important;
  font-weight: 700 !important;
  color: var(--cc-text-primary) !important;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  margin: 0.5rem 0 !important;
  font-family: 'Orbitron', monospace !important;
}

.metric-label {
  font-size: 1.1rem !important;
  color: var(--cc-text-secondary) !important;
  font-weight: 500 !important;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.metric-trend {
  font-size: 1rem !important;
  font-weight: 600 !important;
  margin-top: 0.5rem !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-trend.positive {
  color: var(--cc-success) !important;
}

.metric-trend.negative {
  color: var(--cc-alert) !important;
}

/* Indicador de actualización */
.update-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--cc-text-secondary);
  opacity: 0.8;
}

.update-pulse {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--cc-success);
  animation: pulse 2s infinite;
}

.update-pulse.refreshing {
  background: var(--cc-cufre);
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Botón de salida de fullscreen */
.exit-fullscreen-btn {
  position: fixed !important;
  top: 1rem;
  left: 1rem;
  z-index: 10000;
  background: var(--cc-bg-highlight) !important;
  border: 2px solid var(--cc-cufre) !important;
  color: var(--cc-cufre) !important;
  padding: 0.75rem !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    0 0 15px var(--cc-glow) !important;
}

.exit-fullscreen-btn:hover {
  background: var(--cc-cufre) !important;
  color: var(--cc-bg-primary) !important;
  transform: scale(1.1) !important;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.6),
    0 0 25px var(--cc-glow) !important;
}

/* Animación de entrada para el botón */
.exit-fullscreen-btn {
  animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Gráficos personalizados */
.chart-container {
  width: 100%;
  height: 100%;
  max-height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  overflow: hidden;
  contain: size layout;
}

.chart-container .recharts-wrapper {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Asegurar que los gráficos de dona no se corten */
.chart-container svg {
  overflow: visible !important;
}

/* Ajuste específico para gráficos de dona */
.panel-grafico-dona .chart-container {
  padding: 1rem;
  max-height: calc(100% - 2rem);
}

/* Prevenir expansión infinita en todos los paneles específicos */
.panel-casos-destacados,
.panel-grafico-dona,
.panel-grafico-barras,
.panel-linea-temporal,
.panel-metricas,
.panel-ranking {
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  contain: size layout style;
}

/* Estilos específicos para el panel de métricas */
.panel-metricas .panel-content {
  padding: 1rem;
  justify-content: center;
  align-items: stretch;
}

/* Responsive para métricas en pantallas pequeñas */
@media (max-width: 1200px) {
  .panel-metricas .panel-content > div {
    flex-wrap: wrap !important;
    gap: 1rem !important;
  }
  
  .panel-metricas .panel-content > div > div {
    min-width: 150px !important;
    max-width: 200px !important;
  }
}

@media (max-width: 768px) {
  .panel-metricas .panel-content > div {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
  
  .panel-metricas .panel-content > div > div {
    min-width: auto !important;
    max-width: none !important;
    width: 100% !important;
  }
}

/* Tooltips personalizados */
.custom-tooltip {
  background: rgba(26, 26, 46, 0.95) !important;
  border: 1px solid var(--cc-cufre) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  color: var(--cc-text-primary) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(10px) !important;
}

/* Modal de drill-down */
.drill-down-modal {
  background: var(--cc-bg-primary) !important;
  border: 2px solid var(--cc-cufre) !important;
  border-radius: 16px !important;
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.8),
    0 0 40px var(--cc-glow) !important;
}

.drill-down-header {
  background: linear-gradient(90deg, var(--cc-bg-panel) 0%, var(--cc-bg-accent) 100%);
  border-bottom: 1px solid var(--cc-border);
  padding: 1.5rem 2rem;
  border-radius: 14px 14px 0 0;
}

.drill-down-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Scrollbar personalizada */
.centro-comando-container::-webkit-scrollbar,
.drill-down-content::-webkit-scrollbar {
  width: 10px;
}

.centro-comando-container::-webkit-scrollbar-track,
.drill-down-content::-webkit-scrollbar-track {
  background: var(--cc-bg-accent);
  border-radius: 4px;
}

.centro-comando-container::-webkit-scrollbar-thumb,
.drill-down-content::-webkit-scrollbar-thumb {
  background: var(--cc-cufre);
  border-radius: 4px;
}

.centro-comando-container::-webkit-scrollbar-thumb:hover,
.drill-down-content::-webkit-scrollbar-thumb:hover {
  background: #ffed4e;
}

/* Responsive Design */
@media (max-width: 1366px) {
  .centro-comando-grid {
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 1.5rem;
    padding: 1.5rem 2rem;
  }
  
  .centro-comando-header {
    padding: 1.5rem 2rem;
  }
  
  .centro-comando-title {
    font-size: 2rem !important;
  }
}

@media (max-width: 768px) {
  .centro-comando-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(5, minmax(300px, auto));
    grid-template-areas:
      "casos-destacados"
      "expedientes-estado"
      "expedientes-fuerza"
      "detenidos-fuerza"
      "evolucion-temporal";
    gap: 1rem;
    padding: 1rem;
    overflow-y: auto;
  }

  /* En móvil, siempre fullscreen */
  .centro-comando-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
  
  .centro-comando-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .centro-comando-title {
    font-size: 1.5rem !important;
    text-align: center;
  }
  
  .centro-comando-controls {
    justify-content: center;
  }
  
  .panel-estadisticas {
    padding: 1.5rem;
    min-height: 300px;
  }
  
  .exit-fullscreen-btn {
    top: 0.5rem !important;
    left: 0.5rem !important;
    padding: 0.5rem !important;
  }
}

/* Animaciones de entrada */
.panel-estadisticas {
  animation: slideInUp 0.6s ease-out;
}

.panel-estadisticas:nth-child(1) { animation-delay: 0.1s; }
.panel-estadisticas:nth-child(2) { animation-delay: 0.2s; }
.panel-estadisticas:nth-child(3) { animation-delay: 0.3s; }
.panel-estadisticas:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estados de carga */
.loading-shimmer {
  background: linear-gradient(90deg, 
    var(--cc-bg-accent) 25%, 
    var(--cc-bg-highlight) 50%, 
    var(--cc-bg-accent) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
  height: 20px;
  margin: 0.5rem 0;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Efectos de partículas (opcional) */
.particles-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Transiciones específicas para elementos que las necesitan */
.centro-comando-container,
.panel-estadisticas,
.control-button,
.exit-fullscreen-btn {
  transition-property: opacity, transform;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out;
}

/* Regla de seguridad para prevenir expansión infinita */
.centro-comando-container * {
  max-height: inherit;
}

/* Optimización de rendimiento para animaciones */
.panel-estadisticas,
.chart-container,
.panel-content {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Prevenir scroll en body cuando está en fullscreen */
body:has(.centro-comando-container.fullscreen) {
  overflow: hidden;
}

/* Fuentes personalizadas */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');
/* Estilos específicos para el panel de ranking extendido */
.panel-ranking-extended {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-ranking-extended .panel-content {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
/* ===== ESTILOS ADICIONALES PARA MEJORAS UI ===== */

/* Estilos específicos para paneles mejorados */
.panel-casos-destacados {
  height: 100% !important;
  max-height: 100% !important;
  overflow: hidden !important;
}

.panel-ranking-extended {
  height: 100% !important;
  max-height: 100% !important;
  overflow: hidden !important;
}

/* Estilos para el modal de carga */
.centro-estadisticas-loading-modal {
  --cc-modal-bg: rgba(10, 10, 10, 0.95);
  --cc-progress-glow: rgba(255, 214, 0, 0.6);
  --cc-scroll-thumb: var(--cc-cufre);
}

/* Animaciones adicionales para el modal */
@keyframes matrixMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100px, 100px); }
}

@keyframes iconPulse {
  0% { 
    transform: scale(1); 
    filter: drop-shadow(0 0 20px var(--cc-glow)); 
  }
  100% { 
    transform: scale(1.1); 
    filter: drop-shadow(0 0 30px var(--cc-glow)); 
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Mejoras en scrollbars para paneles */
.panel-casos-destacados *::-webkit-scrollbar,
.panel-ranking-extended *::-webkit-scrollbar {
  width: 6px;
}

.panel-casos-destacados *::-webkit-scrollbar-track,
.panel-ranking-extended *::-webkit-scrollbar-track {
  background: var(--cc-bg-accent);
  border-radius: 3px;
}

.panel-casos-destacados *::-webkit-scrollbar-thumb,
.panel-ranking-extended *::-webkit-scrollbar-thumb {
  background: var(--cc-cufre);
  border-radius: 3px;
}

.panel-casos-destacados *::-webkit-scrollbar-thumb:hover,
.panel-ranking-extended *::-webkit-scrollbar-thumb:hover {
  background: #ffed4e;
}

/* Responsive Design mejorado */
@media (max-width: 1366px) {
  .centro-comando-grid {
    gap: 1.5rem;
    padding: 1.5rem 2rem;
  }
  
  .centro-comando-header {
    padding: 1.5rem 2rem;
  }
  
  .centro-comando-title {
    font-size: 2rem !important;
  }
  
  .panel-casos-destacados {
    min-height: 300px;
  }
  
  .centro-comando-extended {
    min-height: 500px;
    max-height: 700px;
  }
}

@media (max-width: 768px) {
  .centro-comando-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }
  
  .centro-comando-header {
    padding: 1rem;
  }
  
  .centro-comando-title {
    font-size: 1.5rem !important;
  }
  
  .panel-casos-destacados {
    min-height: 250px;
  }
  
  .centro-comando-extended {
    min-height: 400px;
    max-height: 550px;
  }
}