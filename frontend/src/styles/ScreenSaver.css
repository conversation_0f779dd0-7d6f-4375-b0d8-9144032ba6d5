/* Estilos para el salvapantallas de seguridad */

.screensaver-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.5s ease-in-out;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.screensaver-overlay.fade-out {
  animation: fadeOut 0.5s ease-in-out forwards;
}

.screensaver-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  max-width: 90%;
  max-height: 90%;
}

.screensaver-logo {
  max-width: 400px;
  max-height: 300px;
  width: auto;
  height: auto;
  object-fit: contain;
  animation: pulse 3s infinite ease-in-out;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  border-radius: 8px;
}

.screensaver-logo.no-animation {
  animation: none;
}

.screensaver-text {
  margin-top: 2rem;
  color: #666666;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.8;
  animation: fadeInText 1s ease-in-out 0.5s both;
}

.screensaver-subtitle {
  margin-top: 0.5rem;
  color: #999999;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 300;
  opacity: 0.6;
  animation: fadeInText 1s ease-in-out 1s both;
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes fadeInText {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 0.8;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .screensaver-logo {
    max-width: 300px;
    max-height: 200px;
  }
  
  .screensaver-text {
    font-size: 1rem;
    margin-top: 1.5rem;
  }
  
  .screensaver-subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .screensaver-logo {
    max-width: 250px;
    max-height: 150px;
  }
  
  .screensaver-text {
    font-size: 0.9rem;
    margin-top: 1rem;
  }
  
  .screensaver-subtitle {
    font-size: 0.75rem;
  }
}

/* Modo de alto contraste para accesibilidad */
@media (prefers-contrast: high) {
  .screensaver-overlay {
    background-color: #ffffff;
    border: 2px solid #000000;
  }
  
  .screensaver-text {
    color: #000000;
    opacity: 1;
  }
  
  .screensaver-subtitle {
    color: #333333;
    opacity: 1;
  }
}

/* Modo oscuro del sistema */
@media (prefers-color-scheme: dark) {
  .screensaver-overlay {
    background-color: #ffffff; /* Mantener blanco para seguridad */
  }
}

/* Reducir movimiento para usuarios con preferencias de accesibilidad */
@media (prefers-reduced-motion: reduce) {
  .screensaver-overlay,
  .screensaver-logo,
  .screensaver-text,
  .screensaver-subtitle {
    animation: none;
  }
  
  .screensaver-overlay {
    transition: opacity 0.3s ease;
  }
}

/* Estilos para impresión (ocultar salvapantallas) */
@media print {
  .screensaver-overlay {
    display: none !important;
  }
}

/* Prevenir selección de texto y arrastre de imágenes */
.screensaver-overlay * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* Asegurar que el salvapantallas esté por encima de todo */
.screensaver-overlay {
  z-index: 2147483647; /* Valor máximo de z-index */
}