/* ===== PÁGINAS CINEMATOGRÁFICAS - ESTILOS EXTENDIDOS ===== */

/* Extensiones específicas para páginas individuales */
.cinematic-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--cc-bg-primary) 0%, #0d1117 50%, var(--cc-bg-primary) 100%);
  position: relative;
  overflow-y: auto;
}

.cinematic-main-panel {
  background: linear-gradient(135deg, var(--cc-bg-panel) 0%, var(--cc-bg-accent) 100%);
  border: 1px solid var(--cc-border);
  border-radius: 16px;
  padding: 3rem;
  margin: 2rem auto;
  max-width: 1400px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.cinematic-main-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 214, 0, 0.05) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.cinematic-main-panel:hover::before {
  opacity: 1;
}

.cinematic-main-panel:hover {
  transform: translate3d(0, -2px, 0);
  border-color: var(--cc-cufre);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.6),
    0 0 20px var(--cc-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cinematic-chart-container {
  background: rgba(26, 26, 46, 0.3);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--cc-border);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-height: 400px;
}

.cinematic-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 214, 0, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.cinematic-chart-container > * {
  position: relative;
  z-index: 1;
}

/* Tooltips cinematográficos para gráficos */
.cinematic-tooltip {
  background: rgba(26, 26, 46, 0.95) !important;
  border: 2px solid var(--cc-cufre) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  color: var(--cc-text-primary) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(10px) !important;
}

/* Elementos de texto cinematográficos */
.cinematic-title {
  font-family: 'Orbitron', 'Roboto', sans-serif !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--cc-cufre) !important;
  text-shadow: 0 0 15px var(--cc-glow);
  margin-bottom: 1rem !important;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.cinematic-subtitle {
  font-size: 1.1rem !important;
  color: var(--cc-text-secondary) !important;
  margin-bottom: 2rem !important;
  font-weight: 300 !important;
}

.cinematic-description {
  font-size: 0.95rem !important;
  color: var(--cc-text-secondary) !important;
  margin-bottom: 2rem !important;
  font-style: italic;
  opacity: 0.9;
}

/* Botones cinematográficos */
.cinematic-button {
  background: var(--cc-bg-highlight) !important;
  border: 1px solid var(--cc-border) !important;
  color: var(--cc-text-primary) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cinematic-button:hover {
  background: var(--cc-cufre) !important;
  color: var(--cc-bg-primary) !important;
  box-shadow: 0 0 20px var(--cc-glow) !important;
  transform: translateY(-2px) !important;
}

.cinematic-button.primary {
  background: var(--cc-cufre) !important;
  color: var(--cc-bg-primary) !important;
  border-color: var(--cc-cufre) !important;
}

.cinematic-button.primary:hover {
  background: #ffed4e !important;
  box-shadow: 0 0 25px var(--cc-glow) !important;
  transform: translateY(-3px) scale(1.02) !important;
}

/* Alertas cinematográficas */
.cinematic-alert {
  background: rgba(26, 26, 46, 0.8) !important;
  border: 1px solid var(--cc-border) !important;
  border-radius: 8px !important;
  color: var(--cc-text-primary) !important;
  backdrop-filter: blur(10px) !important;
}

.cinematic-alert.error {
  border-color: var(--cc-alert) !important;
  background: rgba(255, 107, 53, 0.1) !important;
}

.cinematic-alert.info {
  border-color: var(--cc-cufre) !important;
  background: rgba(255, 214, 0, 0.1) !important;
}

/* Loading cinematográfico */
.cinematic-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  flex-direction: column;
  gap: 1rem;
}

.cinematic-loading .MuiCircularProgress-root {
  color: var(--cc-cufre) !important;
}

.cinematic-loading-text {
  color: var(--cc-text-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Animaciones de entrada */
.cinematic-fade-in {
  animation: cinematicFadeIn 0.6s ease-out;
}

@keyframes cinematicFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cinematic-slide-up {
  animation: cinematicSlideUp 0.8s ease-out;
}

@keyframes cinematicSlideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive para páginas cinematográficas */
@media (max-width: 1366px) {
  .cinematic-main-panel {
    padding: 2rem;
    margin: 1rem;
  }
  
  .cinematic-chart-container {
    padding: 1.5rem;
    min-height: 350px;
  }
}

@media (max-width: 768px) {
  .cinematic-main-panel {
    padding: 1.5rem;
    margin: 0.5rem;
  }
  
  .cinematic-chart-container {
    padding: 1rem;
    min-height: 300px;
  }
  
  .cinematic-title {
    font-size: 1.5rem !important;
  }
  
  .cinematic-subtitle {
    font-size: 1rem !important;
  }
}

/* Efectos especiales para gráficos */
.cinematic-chart-container .recharts-wrapper {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.cinematic-chart-container svg {
  overflow: visible !important;
}

/* Personalización de scrollbar para páginas cinematográficas */
.cinematic-page-container::-webkit-scrollbar {
  width: 10px;
}

.cinematic-page-container::-webkit-scrollbar-track {
  background: var(--cc-bg-accent);
  border-radius: 4px;
}

.cinematic-page-container::-webkit-scrollbar-thumb {
  background: var(--cc-cufre);
  border-radius: 4px;
}

.cinematic-page-container::-webkit-scrollbar-thumb:hover {
  background: #ffed4e;
}