import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useIdleTimer } from '../hooks/useIdleTimer';
import { SCREENSAVER_CONFIG } from '../config/screensaver';

interface ScreenSaverContextType {
  isScreenSaverActive: boolean;
  activateScreenSaver: () => void;
  deactivateScreenSaver: () => void;
  resetTimer: () => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

const ScreenSaverContext = createContext<ScreenSaverContextType | undefined>(undefined);

interface ScreenSaverProviderProps {
  children: ReactNode;
  enabled?: boolean;
  timeout?: number;
}

export const ScreenSaverProvider: React.FC<ScreenSaverProviderProps> = ({
  children,
  enabled = true,
  timeout = SCREENSAVER_CONFIG.IDLE_TIMEOUT
}) => {
  const [isScreenSaverActive, setIsScreenSaverActive] = useState<boolean>(false);
  const [isEnabled, setIsEnabled] = useState<boolean>(enabled);

  // Callback cuando se detecta inactividad
  const handleIdle = useCallback(() => {
    if (isEnabled && !isScreenSaverActive) {
      setIsScreenSaverActive(true);
      console.log('🔒 Salvapantallas activado por inactividad');
    }
  }, [isEnabled, isScreenSaverActive]);

  // Callback cuando se detecta actividad
  const handleActive = useCallback(() => {
    if (isScreenSaverActive) {
      setIsScreenSaverActive(false);
      console.log('🔓 Salvapantallas desactivado por actividad');
    }
  }, [isScreenSaverActive]);

  // Hook de idle timer
  const { reset, pause, resume } = useIdleTimer({
    timeout,
    onIdle: handleIdle,
    onActive: handleActive,
    enabled: isEnabled
  });

  // Función para activar manualmente el salvapantallas
  const activateScreenSaver = useCallback(() => {
    if (isEnabled && !isScreenSaverActive) {
      setIsScreenSaverActive(true);
      console.log('🔒 Salvapantallas activado manualmente');
    }
  }, [isEnabled, isScreenSaverActive]);

  // Función para desactivar manualmente el salvapantallas
  const deactivateScreenSaver = useCallback(() => {
    if (isScreenSaverActive) {
      setIsScreenSaverActive(false);
      reset(); // Resetear el timer al desactivar
      console.log('🔓 Salvapantallas desactivado manualmente');
    }
  }, [isScreenSaverActive, reset]);

  // Función para resetear el timer
  const resetTimer = useCallback(() => {
    reset();
  }, [reset]);

  // Función para pausar el timer
  const pauseTimer = useCallback(() => {
    pause();
  }, [pause]);

  // Función para reanudar el timer
  const resumeTimer = useCallback(() => {
    resume();
  }, [resume]);

  // Función para habilitar/deshabilitar el salvapantallas
  const setEnabledCallback = useCallback((newEnabled: boolean) => {
    setIsEnabled(newEnabled);
    if (!newEnabled && isScreenSaverActive) {
      // Si se deshabilita mientras está activo, desactivarlo
      setIsScreenSaverActive(false);
    }
    console.log(`🔧 Salvapantallas ${newEnabled ? 'habilitado' : 'deshabilitado'}`);
  }, [isScreenSaverActive]);

  const contextValue: ScreenSaverContextType = {
    isScreenSaverActive,
    activateScreenSaver,
    deactivateScreenSaver,
    resetTimer,
    pauseTimer,
    resumeTimer,
    isEnabled,
    setEnabled: setEnabledCallback
  };

  return (
    <ScreenSaverContext.Provider value={contextValue}>
      {children}
    </ScreenSaverContext.Provider>
  );
};

// Hook personalizado para usar el contexto
export const useScreenSaver = (): ScreenSaverContextType => {
  const context = useContext(ScreenSaverContext);
  if (context === undefined) {
    throw new Error('useScreenSaver debe ser usado dentro de un ScreenSaverProvider');
  }
  return context;
};

export default ScreenSaverContext;