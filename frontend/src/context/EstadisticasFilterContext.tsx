import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface EstadisticasFilters {
  estado?: string;
  fuerza?: string;
  tipoCaptura?: string;
}

interface EstadisticasFilterContextType {
  filters: EstadisticasFilters;
  setFilter: (key: keyof EstadisticasFilters, value: string | undefined) => void;
  clearFilters: () => void;
  clearFilter: (key: keyof EstadisticasFilters) => void;
}

const EstadisticasFilterContext = createContext<EstadisticasFilterContextType | undefined>(undefined);

export const useEstadisticasFilter = () => {
  const context = useContext(EstadisticasFilterContext);
  if (!context) {
    throw new Error('useEstadisticasFilter debe ser usado dentro de EstadisticasFilterProvider');
  }
  return context;
};

interface EstadisticasFilterProviderProps {
  children: ReactNode;
}

export const EstadisticasFilterProvider: React.FC<EstadisticasFilterProviderProps> = ({ children }) => {
  const [filters, setFilters] = useState<EstadisticasFilters>({});

  const setFilter = useCallback((key: keyof EstadisticasFilters, value: string | undefined) => {
    setFilters(prev => {
      if (value === undefined || value === '') {
        // Si el valor es undefined o vacío, eliminar el filtro
        const { [key]: _, ...rest } = prev;
        return rest;
      }
      return {
        ...prev,
        [key]: value
      };
    });
  }, []);

  const clearFilter = useCallback((key: keyof EstadisticasFilters) => {
    setFilters(prev => {
      const { [key]: _, ...rest } = prev;
      return rest;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const value: EstadisticasFilterContextType = {
    filters,
    setFilter,
    clearFilters,
    clearFilter
  };

  return (
    <EstadisticasFilterContext.Provider value={value}>
      {children}
    </EstadisticasFilterContext.Provider>
  );
};

export default EstadisticasFilterContext;