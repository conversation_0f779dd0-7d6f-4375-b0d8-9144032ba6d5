/**
 * Configuración para el sistema de gestión de sesiones e inactividad
 */
export const SESSION_CONFIG = {
  // Tiempo de inactividad antes de cerrar sesión (30 minutos)
  INACTIVITY_TIMEOUT: 30 * 60 * 1000,
  
  // Tiempo antes de mostrar advertencia (25 minutos)
  WARNING_TIME: 25 * 60 * 1000,
  
  // Tiempo para refrescar token automáticamente (1h 50min)
  TOKEN_REFRESH_TIME: 110 * 60 * 1000,
  
  // Duración del countdown de advertencia (5 minutos)
  WARNING_COUNTDOWN: 5 * 60 * 1000
};

/**
 * Eventos de actividad del usuario que resetean el timer de inactividad
 */
export const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove', 
  'keypress',
  'scroll',
  'touchstart',
  'click',
  'keydown'
] as const;

/**
 * Rutas que no requieren seguimiento de actividad
 */
export const EXCLUDED_ROUTES = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password'
] as const;