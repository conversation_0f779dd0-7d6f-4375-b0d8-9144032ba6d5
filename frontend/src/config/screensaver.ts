// Configuración del salvapantallas de seguridad
export const SCREENSAVER_CONFIG = {
  // Tiempo de inactividad en milisegundos (2 minutos)
  IDLE_TIMEOUT: 2 * 60 * 1000,
  
  // Ruta del logo CUFRE
  LOGO_PATH: '/logo-cufre-2.png',
  
  // Duración de la animación de fade en milisegundos
  FADE_DURATION: 500,
  
  // Habilitar animación de pulso en el logo
  PULSE_ANIMATION: true,
  
  // Eventos que resetean el timer de inactividad
  ACTIVITY_EVENTS: [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click',
    'wheel',
    'keydown',
    'keyup'
  ] as const,
  
  // Z-index para asegurar que esté por encima de todo
  Z_INDEX: 9999
} as const;

export type ActivityEvent = typeof SCREENSAVER_CONFIG.ACTIVITY_EVENTS[number];