#!/bin/bash

# Script para corregir URLs hardcodeadas en el código compilado
# Este script se ejecuta después del build para asegurar que no haya URLs absolutas

echo "Iniciando corrección de URLs hardcodeadas en archivos compilados..."

# Directorio donde se encuentran los archivos compilados
BUILD_DIR="./build"

# Buscar y reemplazar URLs absolutas en archivos JS
find $BUILD_DIR -type f -name "*.js" -exec sed -i '' 's|http://************:8080/api|/api|g' {} \;
find $BUILD_DIR -type f -name "*.js" -exec sed -i '' 's|http://************/api|/api|g' {} \;

# También buscar posibles duplicaciones de /api
find $BUILD_DIR -type f -name "*.js" -exec sed -i '' 's|/api/api/|/api/|g' {} \;

echo "Corrección de URLs completada."
