server {
    listen 80;
    server_name _;

    # <PERSON>io raíz donde se encuentra index.html
    root /usr/share/nginx/html;

    # Deshabilitar cache para todas las respuestas
    add_header Cache-Control "no-store, no-cache, must-revalidate" always;
    add_header Pragma "no-cache" always;
    expires -1;

    # CORS headers
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;

    # Manejo de preflight OPTIONS
    if ($request_method = OPTIONS) {
        return 204;
    }

    # Proxy para todas las rutas de la API
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin $http_origin;
        proxy_set_header Authorization $http_authorization;
        proxy_redirect off;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # IMPORTANTE: también hacer proxy para /auth/ realizando el reenvío a /api/auth/
    location /auth/ {
        proxy_pass http://backend:8080/api/auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin $http_origin;
        proxy_set_header Authorization $http_authorization;
        proxy_redirect off;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Bloquear acceso directo a rutas backend sin /api
    location ~ ^/(expedientes|usuarios|delitos|estadisticas|personas|archivos|documentos|fotografias|medios-comunicacion|domicilios|actividad-sistema) {
        return 404;
    }

    # Configuración específica para servir uploads a través del backend (alta prioridad)
    location ^~ /uploads/ {
        proxy_pass http://backend:8080/api/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Origin $http_origin;
        proxy_set_header Authorization $http_authorization;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Archivos estáticos - intentar servir directamente
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        try_files $uri =404;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        access_log off;
    }

    # Soporte para SPA: esta configuración evita el bucle infinito
    location / {
        try_files $uri $uri/ /index.html =404;
        index index.html;
    }

    # Manejo específico para solicitudes a index.html
    location = /index.html {
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
        expires -1;
    }
}
