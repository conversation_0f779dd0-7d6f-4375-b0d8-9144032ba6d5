{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "ajv": "^6.12.6", "ajv-keywords": "^3.5.2", "axios": "^1.9.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "lodash.debounce": "^4.0.8", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-leaflet": "^4.2.1", "react-leaflet-heatmap-layer-v3": "^3.0.3-beta-1", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/date-fns": "^2.5.3", "@types/file-saver": "^2.0.7", "@types/leaflet": "^1.9.17", "@types/lodash.debounce": "^4.0.9"}, "proxy": "http://localhost:8080"}