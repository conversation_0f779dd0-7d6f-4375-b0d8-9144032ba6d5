#!/bin/sh

# Script para modificar el index.html y añadir script de redirección
INDEX_PATH="/usr/share/nginx/html/index.html"
PORT_REDIRECTOR="/usr/share/nginx/html/port-redirector.js"

# Copiar el script de redirección
cp /tmp/port-redirector.js $PORT_REDIRECTOR

# Añadir script de redirección en el head del index.html
sed -i "s/<head>/<head>\n  <script src=\"\/port-redirector.js\"><\/script>/" $INDEX_PATH

# Iniciar nginx normalmente
exec nginx -g "daemon off;"
