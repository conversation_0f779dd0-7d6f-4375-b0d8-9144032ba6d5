#!/bin/bash

echo "=== [CUFRE] Solución simple sin Maven ==="
echo ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==> Paso 1: Deteniendo contenedores existentes...${NC}"
docker compose down

echo ""
echo -e "${BLUE}==> Paso 2: Eliminando imágenes y volúmenes...${NC}"
docker compose down --volumes --remove-orphans
docker system prune -f

echo ""
echo -e "${BLUE}==> Paso 3: Verificando archivos de migración...${NC}"
echo "Archivos V8 en el código fuente:"
ls -la backend/src/main/resources/db/migration/V8__* 2>/dev/null || echo "Solo un archivo V8 encontrado ✅"
echo ""
echo "Archivo V10 (renombrado):"
ls -la backend/src/main/resources/db/migration/V10__* 2>/dev/null || echo "Archivo V10 no encontrado"

echo ""
echo -e "${BLUE}==> Paso 4: Reconstruyendo completamente sin cache...${NC}"
docker compose build --no-cache --pull

echo ""
echo -e "${BLUE}==> Paso 5: Levantando servicios...${NC}"
docker compose up -d

echo ""
echo -e "${BLUE}==> Paso 6: Esperando que los servicios estén listos...${NC}"
echo "Esperando 45 segundos para que el backend compile e inicie..."
sleep 45

echo ""
echo -e "${BLUE}==> Paso 7: Verificando estado de los contenedores...${NC}"
docker compose ps

echo ""
echo -e "${BLUE}==> Paso 8: Verificando logs del backend...${NC}"
echo "Últimas 30 líneas de logs del backend:"
docker compose logs --tail=30 backend

echo ""
echo -e "${BLUE}==> Paso 9: Verificando conectividad...${NC}"
echo "Probando endpoint de salud del backend..."
if curl -f http://localhost/api/actuator/health 2>/dev/null; then
    echo -e "${GREEN}✅ Backend responde correctamente${NC}"
else
    echo -e "${YELLOW}⚠️  Backend aún no responde, verificando logs...${NC}"
    echo ""
    echo "Logs completos del backend:"
    docker compose logs backend
fi

echo ""
echo -e "${BLUE}==> Paso 10: Verificando frontend...${NC}"
if curl -f http://localhost/ 2>/dev/null >/dev/null; then
    echo -e "${GREEN}✅ Frontend responde correctamente${NC}"
else
    echo -e "${RED}❌ Frontend no responde${NC}"
fi

echo ""
echo "=== [CUFRE] Resumen del despliegue ==="
echo -e "${GREEN}✅ Conflicto de migraciones Flyway resuelto${NC}"
echo -e "${GREEN}✅ Reconstrucción completa sin cache${NC}"
echo -e "${GREEN}✅ Limpieza de volúmenes y sistema${NC}"
echo ""
echo "Accede a la aplicación en:"
echo -e "${BLUE}🌐 Frontend: http://localhost${NC}"
echo -e "${BLUE}🔧 API: http://localhost/api/${NC}"
echo -e "${BLUE}💚 Health Check: http://localhost/api/actuator/health${NC}"
echo ""
echo "Para ver logs en tiempo real:"
echo "docker compose logs -f backend"
echo ""