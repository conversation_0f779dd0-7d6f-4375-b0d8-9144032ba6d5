#!/bin/bash

# Script simplificado para iniciar la aplicación CUFRE
# Este script solo detiene contenedores existentes e inicia la aplicación

echo "======================================================="
echo "     INICIO DE APLICACIÓN CUFRE EN PRODUCCIÓN        "
echo "======================================================="

# Directorio base del proyecto
BASE_DIR="/Users/<USER>/Documents/CodigoFuente/cufre"
cd "$BASE_DIR"

# Función para registrar mensajes
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
}

log "Iniciando aplicación CUFRE..."

# 1. Verificar si hay contenedores de CUFRE en ejecución
log "Verificando contenedores existentes..."
CONTAINERS=$(docker ps -a --filter "name=cufre" --format "{{.Names}}")
if [ -n "$CONTAINERS" ]; then
    log "Deteniendo y eliminando contenedores existentes..."
    docker stop $(docker ps -a --filter "name=cufre" --format "{{.Names}}") >/dev/null 2>&1 || true
    docker rm $(docker ps -a --filter "name=cufre" --format "{{.Names}}") >/dev/null 2>&1 || true
fi

# 2. Verificar si docker-compose.yml existe o crearlo
if [ ! -f "$BASE_DIR/docker-compose.yml" ]; then
    log "Creando archivo docker-compose.yml..."
    cat > "$BASE_DIR/docker-compose.yml" << "END_COMPOSE"
version: '3'

services:
  backend:
    build: ./backend
    container_name: cufre-backend
    ports:
      - "8080:8080"
    networks:
      - cufre-network

  frontend:
    build: ./frontend
    container_name: cufre-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - cufre-network

networks:
  cufre-network:
    driver: bridge
END_COMPOSE
fi

# 3. Iniciar la aplicación con docker-compose
log "Iniciando aplicación con docker-compose..."
cd "$BASE_DIR"
docker-compose up -d

# 4. Verificar que se hayan levantado correctamente los servicios
log "Verificando estado de los servicios..."
sleep 10

BACKEND_RUNNING=$(docker ps --filter "name=cufre-backend" --format "{{.Status}}" | grep -c "Up")
if [ "$BACKEND_RUNNING" -eq 1 ]; then
    log "Backend iniciado correctamente"
else
    log "ERROR: El backend no se inició correctamente. Revisa los logs:"
    docker logs cufre-backend
fi

FRONTEND_RUNNING=$(docker ps --filter "name=cufre-frontend" --format "{{.Status}}" | grep -c "Up")
if [ "$FRONTEND_RUNNING" -eq 1 ]; then
    log "Frontend iniciado correctamente"
else
    log "ERROR: El frontend no se inició correctamente. Revisa los logs:"
    docker logs cufre-frontend
fi

log "======================================================="
log "Aplicación CUFRE iniciada exitosamente"
log "La aplicación debería estar disponible en:"
log "   http://localhost"
log ""
log "Credenciales de prueba:"
log "   Usuario: <EMAIL>"
log "   Contraseña: Minseg2025-"
log ""
log "Para detener la aplicación:"
log "   docker-compose down"
log ""
log "Para ver logs del backend:"
log "   docker logs cufre-backend"
log ""
log "Para ver logs del frontend:"
log "   docker logs cufre-frontend"
log "======================================================="
