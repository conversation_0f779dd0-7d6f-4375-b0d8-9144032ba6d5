version: '3.8'

services:
  backend:
    build:
      context: ./backend
    expose:
      - "8080"
    environment:
      - SPRING_PROFILES_ACTIVE=oracle
      - SPRING_FLYWAY_IGNORE_MIGRATION_PATTERNS=V1005__*
      - SPRING_FLYWAY_OUT_OF_ORDER=true
      - SPRING_FLYWAY_VALIDATE_ON_MIGRATE=false
      - SPRING_FLYWAY_BASELINE_ON_MIGRATE=true
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - cufre-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    build:
      context: ./frontend
    ports:
      - "80:80"
    volumes:
      - ./default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - cufre-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  cufre-network:
    driver: bridge
